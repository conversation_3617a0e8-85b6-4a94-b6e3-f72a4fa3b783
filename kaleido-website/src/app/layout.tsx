'use client';

import './globals.css';

import { useEffect, useState } from "react";

import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

import Footer from '@/components/layout/Footer';
import Navbar from '@/components/layout/Navbar';
import CookieBanner from "@/components/ui/CookieBanner";
import FloatingBookDemoButton from '@/components/ui/FloatingBookDemoButton';
import { useTranslation } from '@/hooks/useTranslation';
import { getDirection } from '@/locales';
import ClarityProvider from "@/providers/ClarityProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { locale } = useTranslation();
  const direction = getDirection(locale);
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering after component is mounted
  useEffect(() => {
    setMounted(true);
  }, []);

  // Use effect to update document attributes after hydration
  useEffect(() => {
    if (mounted) {
      document.documentElement.lang = locale;
      document.documentElement.dir = direction;
    }
  }, [mounted, locale, direction]);

  return (
    <html lang="en" dir="ltr">
      <head>
        <title>
          KaleidoTalent - AI-Powered Talent Acquisition | Where AI Meets
          Emotional Intelligence
        </title>
        <meta
          name="description"
          content="Transform your hiring with KaleidoTalent's AI-powered platform. Create tailored job descriptions in seconds, match with the right candidates, and build meaningful connections beyond CVs. Where Artificial Intelligence meets Emotional Intelligence."
        />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://kaleidotalent.com/" />
        <meta
          property="og:title"
          content="KaleidoTalent - AI-Powered Talent Acquisition | Where AI Meets Emotional Intelligence"
        />
        <meta
          property="og:description"
          content="Transform your hiring with KaleidoTalent's AI-powered platform. Create tailored job descriptions in seconds, match with the right candidates, and build meaningful connections beyond CVs. Where Artificial Intelligence meets Emotional Intelligence."
        />
        <meta
          property="og:image"
          content="/images/landing/new/landing-new-1.webp"
        />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta
          property="og:image:alt"
          content="KaleidoTalent - AI-Powered Talent Acquisition Platform"
        />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://kaleidotalent.com/" />
        <meta
          property="twitter:title"
          content="KaleidoTalent - AI-Powered Talent Acquisition | Where AI Meets Emotional Intelligence"
        />
        <meta
          property="twitter:description"
          content="Transform your hiring with KaleidoTalent's AI-powered platform. Create tailored job descriptions in seconds, match with the right candidates, and build meaningful connections beyond CVs. Where Artificial Intelligence meets Emotional Intelligence."
        />
        <meta
          property="twitter:image"
          content="/images/landing/new/landing-new-1.webp"
        />
        <meta
          property="twitter:image:alt"
          content="KaleidoTalent - AI-Powered Talent Acquisition Platform"
        />

        {/* Additional meta tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />

        {/* Microsoft Clarity Script */}
        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "sbqwiohjsj");
            `,
          }}
        />

        {/* Google tag (gtag.js) */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-SSFZ5KL5K6"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-SSFZ5KL5K6');
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}>
        {mounted && (
          <ClarityProvider>
            <Navbar />
            <main className="flex-grow">{children}</main>
            <Footer />
            <FloatingBookDemoButton />
            <CookieBanner />
          </ClarityProvider>
        )}
      </body>
    </html>
  );
}
