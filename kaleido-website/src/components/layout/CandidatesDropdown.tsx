'use client';

import { useTranslation } from "@/hooks/useTranslation";
import { ChevronDown, Shield, Users, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface CandidatesDropdownProps {
  scrolled: boolean;
}

export const CandidatesDropdown: React.FC<CandidatesDropdownProps> = ({
  scrolled,
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const appUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        className={`group relative font-semibold tracking-wide transition-all duration-300 px-6 py-3 rounded-xl flex items-center gap-2 backdrop-blur-sm border overflow-hidden ${
          scrolled
            ? "text-gray-800 hover:text-purple-600 bg-white/80 hover:bg-white/95 border-gray-200/50 hover:border-purple-300/50 shadow-sm hover:shadow-md"
            : "text-white hover:text-white bg-white/10 hover:bg-white/20 border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl"
        } transform hover:scale-105 active:scale-95`}>
        {/* Organic glassmorphic background elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div
            className="absolute -top-2 -left-2 w-8 h-8 rounded-full blur-sm group-hover:scale-150 transition-transform duration-500"
            style={{
              background:
                "radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%)",
            }}></div>
          <div
            className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full blur-sm group-hover:scale-125 transition-transform duration-700"
            style={{
              background:
                "radial-gradient(circle, rgba(168,85,247,0.2) 0%, transparent 70%)",
            }}></div>
          <div
            className="absolute top-1/2 left-1/4 w-12 h-3 rounded-full blur-md transform -rotate-12 group-hover:rotate-12 transition-transform duration-500"
            style={{
              background:
                "radial-gradient(ellipse, rgba(255,255,255,0.08) 30%, transparent 70%)",
            }}></div>
        </div>

        <span className="relative z-10 text-base font-medium tracking-tight">
          For Candidates
        </span>
        <ChevronDown
          className={`relative z-10 w-4 h-4 transition-all duration-300 ${
            isOpen ? "rotate-180 scale-110" : "group-hover:translate-y-0.5"
          }`}
        />

        {/* Enhanced glassmorphic shine effect */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        <div
          className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none"
          style={{
            background:
              "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          }}></div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full mt-3 w-[90vw] max-w-[900px] right-0 lg:right-4 rounded-3xl shadow-2xl overflow-hidden z-50"
            onMouseLeave={() => setIsOpen(false)}>
            <div className="relative bg-gradient-to-br from-slate-900/60 via-gray-900/50 to-slate-900/60 backdrop-blur-3xl border border-white/10 shadow-2xl">
            {/* Simple gradient overlays for depth */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              <div className="absolute -top-20 -left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -right-20 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
            </div>
            
            {/* Subtle dot pattern overlay */}
            <div 
              className="absolute inset-0 opacity-[0.03]"
              style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 1px)`,
                backgroundSize: '50px 50px'
              }}></div>
            
            {/* Gradient overlay for depth */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-500/5 to-transparent pointer-events-none"></div>

            <div className="relative p-6">
              <div className="text-center mb-6">
                {/* Enhanced typography with creative styling */}
                <div className="relative inline-block">
                  <h2 className="text-2xl lg:text-3xl mb-2">
                    <span className="text-[10px] font-semibold text-gray-400 uppercase tracking-[0.4em] block mb-1">
                      Welcome
                    </span>
                    <span className="relative bg-gradient-to-r from-purple-300 via-pink-300 to-purple-400 bg-clip-text text-transparent font-bold tracking-tight">
                      Candidates
                    </span>
                  </h2>
                  <p className="text-sm text-gray-300/90 max-w-md mx-auto leading-relaxed">
                    Transform your career through our innovative platform
                  </p>
                </div>
              </div>

              <div className="relative flex justify-center items-center h-[300px]">
                {/* Login Card - Flat Design */}
                <motion.div 
                  initial={{ y: 100, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  whileHover={{ 
                    scale: 1.05,
                    y: -20,
                    transition: { duration: 0.3 }
                  }}
                  className="absolute left-[15%] top-[50%] -translate-y-1/2 w-[380px] h-[240px] rounded-2xl overflow-hidden group hover:z-30 transition-all duration-500 bg-gradient-to-br from-teal-500 to-teal-600 shadow-xl hover:shadow-2xl z-10">
                  
                  <div className="relative p-6 text-white h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold">Login</h3>
                        <div className="w-10 h-0.5 bg-white/80 rounded-full mt-1"></div>
                      </div>
                      <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-teal-400/20 to-teal-500/30 flex items-center justify-center relative overflow-hidden border border-teal-400/30">
                        <Shield className="w-7 h-7 text-white relative z-10" />
                        <div className="absolute bottom-0 right-0 w-0 h-0 border-style-solid border-l-[28px] border-l-transparent border-b-[28px] border-b-white/20"></div>
                      </div>
                    </div>

                    <p className="text-xs text-teal-50/80 leading-relaxed max-w-[260px] flex-grow">
                      Securely access your account to manage applications, view schedules, and track progress.
                    </p>

                    <div className="flex items-center justify-between mt-4">
                      <Link
                        href={`${appUrl}/api/auth/login`}
                        className="inline-flex items-center justify-center bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-full transition-all duration-300 text-xs font-medium backdrop-blur-sm border border-white/20">
                        Sign In
                      </Link>
                      
                      <div className="flex items-center gap-3 text-[10px] text-teal-100/70">
                        <span className="flex items-center gap-1">
                          <Shield className="w-3 h-3" />
                          Secure
                        </span>
                        <span className="flex items-center gap-1">
                          <Zap className="w-3 h-3" />
                          Quick
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Sign Up Card - Flat Design */}
                <motion.div 
                  initial={{ y: 100, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  whileHover={{ 
                    scale: 1.05,
                    y: -25,
                    transition: { duration: 0.3 }
                  }}
                  className="absolute right-[15%] top-[50%] -translate-y-1/2 w-[400px] h-[260px] rounded-2xl overflow-hidden group hover:z-30 transition-all duration-500 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 shadow-[0_30px_60px_-15px_rgba(0,0,0,0.5)] hover:shadow-[0_40px_80px_-20px_rgba(0,0,0,0.6)] z-20">
                  
                  <div className="relative p-6 text-white h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold">Sign Up</h3>
                        <div className="w-10 h-0.5 bg-white/80 rounded-full mt-1"></div>
                      </div>
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-pink-400/20 to-orange-400/30 flex items-center justify-center relative overflow-hidden border border-white/25">
                        <Users className="w-8 h-8 text-white relative z-10" />
                        <div className="absolute bottom-0 right-0 w-0 h-0 border-style-solid border-l-[32px] border-l-transparent border-b-[32px] border-b-white/20"></div>
                      </div>
                    </div>

                    <p className="text-sm text-orange-50/90 leading-relaxed max-w-[280px] flex-grow">
                      Create a personalized job profile, discover matched positions, and elevate your career prospects.
                    </p>

                    <div className="flex items-center justify-between mt-4">
                      <Link
                        href={`${appUrl}/api/auth/signup`}
                        className="inline-flex items-center justify-center bg-white/20 hover:bg-white/30 text-white px-8 py-2.5 rounded-full transition-all duration-300 text-sm font-medium backdrop-blur-sm border border-white/20">
                        Join Now
                      </Link>
                      
                      <div className="flex items-center gap-3 text-xs text-orange-50/70">
                        <span className="flex items-center gap-1.5">
                          <Zap className="w-3.5 h-3.5" />
                          Fast
                        </span>
                        <span className="flex items-center gap-1.5">
                          <svg
                            className="w-3.5 h-3.5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          Simple
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
    </div>
  );
};
