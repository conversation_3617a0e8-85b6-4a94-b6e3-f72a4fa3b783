#!/bin/bash

# Deploy script optimized for Turbopack
# This script deploys the application with Turbopack for faster builds and better caching

# Get the environment from command line argument or default to production
ENV=${1:-production}

# Ensure pnpm is in the PATH
export PATH="$PATH:$HOME/.local/share/pnpm"

# Create cache directories if they don't exist
mkdir -p .turbo-cache
mkdir -p .next/cache

# Set permissions for cache directories
chmod -R 777 .turbo-cache
chmod -R 777 .next/cache

# Install dependencies with frozen lockfile for reproducible builds
echo "📦 Installing dependencies..."
if [ -f "pnpm-lock.yaml" ]; then
    pnpm install --frozen-lockfile
else
    npm ci
fi

# Build the Next.js app with Turbopack
echo "🏗️ Building application with Turbopack..."
TURBOPACK_CACHE_ENABLED=true TURBOPACK_CACHE_SIZE=8000 npm run build:turbo

# Log the completion
echo "✅ Turbopack build completed for $ENV environment"

# Add cache information
echo ""
echo "📁 Cache information:"
echo "Turbopack cache: $(du -sh .turbo-cache 2>/dev/null || echo 'Not available')"
echo "Next.js cache: $(du -sh .next/cache 2>/dev/null || echo 'Not available')"