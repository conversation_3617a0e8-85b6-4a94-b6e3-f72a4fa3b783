# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 marketing website for Kaleido Talent, an AI-powered talent acquisition platform. The website uses TypeScript, Tailwind CSS, and is optimized with Turbopack for faster builds.

## Essential Commands

### Development
```bash
pnpm dev              # Start development server with Turbopack
pnpm dev:clean        # Clean start (removes .next directory first)
```

### Building & Production
```bash
pnpm build            # Production build with Turbopack
pnpm start            # Start production server
pnpm deploy:turbo     # Deploy with optimized caching
```

### Testing & Code Quality
```bash
pnpm test             # Run all tests
pnpm test:watch       # Run tests in watch mode
pnpm lint             # Run ESLint
```

### Cache Management
```bash
pnpm cache:clean      # Clean all caches
pnpm cache:prune      # Remove old cache files
```

## Architecture Overview

### Directory Structure
- `/src/app/` - Next.js App Router pages and layouts
- `/src/components/` - React components organized by type:
  - `/layout/` - Page structure components (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
  - `/sections/` - Main content sections (Hero, Pricing, FAQ)
  - `/ui/` - Reusable UI components
  - `/parallax/` - Animation components
- `/src/locales/` - Internationalization files (EN/AR)
- `/src/services/` - API integration services
- `/src/hooks/` - Custom React hooks
- `/src/utils/` - Utility functions
- `/public/` - Static assets

### Key Technologies
- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4 with extensive custom configuration
- **Build Tool**: Turbopack (faster alternative to Webpack)
- **Testing**: Jest with React Testing Library
- **Analytics**: Microsoft Clarity and Google Analytics integrated

### Important Configuration Files
- `tailwind.config.ts` - Contains custom gradients, animations, and theme colors
- `next.config.ts` - Next.js configuration
- `turbopack-cache.config.js` - Turbopack caching settings

### Multi-language Support
The site supports English and Arabic with RTL layout. Language switching is handled through the `/locales/` directory and custom hooks.

### Component Patterns
- Components use TypeScript interfaces for props
- Most components are client-side rendered (use client directive)
- Animations use Framer Motion and custom Tailwind animations
- Form components integrate with phone number validation

### Testing Approach
- Unit tests use Jest and React Testing Library
- Test files are co-located with components (*.test.tsx)
- Run individual tests with: `pnpm test -- path/to/test`

### Analytics Integration
- Microsoft Clarity is integrated for user behavior tracking
- Events are tracked for key user actions (plan selection, form submissions)
- See CLARITY_SETUP.md for implementation details

### Performance Optimization
- Turbopack is configured for faster builds
- Images use Next.js Image component for optimization
- Deploy script includes caching strategies

## Development Tips
- Always use pnpm as the package manager
- Check existing components before creating new ones
- Follow the established TypeScript patterns in the codebase
- Use Tailwind classes from the custom theme when possible
- Test both English and Arabic versions when making UI changes