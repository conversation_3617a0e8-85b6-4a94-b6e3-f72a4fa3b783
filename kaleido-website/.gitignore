# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Build outputs
dist/
build/
.next/
out/
.turbo/
.vercel
.cache/

# Testing
coverage/
.nyc_output/
test-results/
test-reports/
playwright-report/
playwright/.cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Logs
logs/
*.log

# Database
*.sqlite
*.sqlite3
*.db

# Temporary files
tmp/
temp/
uploads/
*.tmp
*.temp

# OS files
Thumbs.db
.DS_Store

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Next.js
.next/
out/
next-env.d.ts

# NestJS
dist/

# Sentry
.sentryclirc

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes secrets
secrets.yaml
secrets-*.yaml

# Redis dump
dump.rdb

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Turbopack
.turbopack/
turbopack-cache/
.turbo-cache/

# Jest
jest-cache/

# Misc
.cache
.parcel-cache
.eslintcache
.stylelintcache
.webpack-cache
*.pid
*.seed
*.pid.lock