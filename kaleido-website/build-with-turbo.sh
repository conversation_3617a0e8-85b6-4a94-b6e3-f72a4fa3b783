#!/bin/bash

# This script builds the Next.js app with Turbopack
# The next.config.js is now configured to work with Turbopack directly

# Set environment variables for Turbopack
export NEXT_TURBO=true
export TURBOPACK_CACHE_ENABLED=true
export TURBOPACK_CACHE_SIZE=8000
export NODE_OPTIONS="--max-old-space-size=4096"

# Create cache directories if they don't exist
mkdir -p .turbo-cache
mkdir -p .next/cache

# Clean up any previous build artifacts
echo "🧹 Cleaning up previous build artifacts..."
rm -rf .next

# Ensure all dependencies are installed
echo "📦 Checking dependencies..."
if [ -f "pnpm-lock.yaml" ]; then
    pnpm install --no-frozen-lockfile
else
    npm install
fi

# Try building with regular Next.js build first
echo "🏗️ Building application with Next.js 15..."
npx next build

# Exit with the status of the build command
exit $?