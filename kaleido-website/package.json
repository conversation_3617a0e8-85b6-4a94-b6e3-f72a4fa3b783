{"name": "kaleido-main-website", "version": "0.1.0", "private": true, "scripts": {"dev": "NEXT_FAST_REFRESH=true NODE_ENV=development NODE_OPTIONS='--inspect' next dev --turbo", "dev:clean": "rm -rf .next && NEXT_FAST_REFRESH=true NODE_ENV=development NODE_OPTIONS='--inspect' next dev --turbo", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build --turbo", "build:webpack": "NODE_OPTIONS='--max-old-space-size=8192' next build", "build:turbo": "NODE_OPTIONS='--max-old-space-size=8192' next build --turbo", "build:analyze": "ANALYZE=true NODE_OPTIONS='--max-old-space-size=8192' next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "cache:clean": "rm -rf .turbo-cache && rm -rf .next/cache", "cache:prune": "find .turbo-cache -type f -atime +30 -delete && find .next/cache -type f -atime +30 -delete", "deploy:turbo": "./deploy-turbopack.sh"}, "dependencies": {"@heroicons/react": "^2.2.0", "@microsoft/clarity": "^1.0.0", "framer-motion": "^12.7.4", "lucide-react": "^0.511.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-scroll-parallax": "^3.4.5", "react-vertical-timeline-component": "^3.5.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-vertical-timeline-component": "^3.3.6", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "typescript": "^5"}}