/**
 * Turbopack Cache Configuration
 * 
 * This file configures how Turbopack caching works in the application.
 * It's used by the deployment scripts to ensure optimal caching.
 */

module.exports = {
  // Cache directory relative to project root
  cacheDirectory: '.turbo-cache',
  
  // Maximum memory cache size in MB
  maxMemoryCacheSize: 8000,
  
  // Persistent cache between builds
  persistentCache: true,
  
  // Cache invalidation strategy
  // 'content' - invalidate based on content changes
  // 'mtime' - invalidate based on file modification time
  invalidation: 'content',
  
  // Files to exclude from caching
  exclude: [
    // Temporary files
    '**/.DS_Store',
    '**/Thumbs.db',
    
    // Log files
    '**/*.log',
    
    // Environment files (contain secrets)
    '**/.env*',
    
    // Test files
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
  ],
  
  // Cache compression level (0-9, where 9 is maximum compression)
  compressionLevel: 6,
  
  // Cache TTL in seconds (30 days)
  ttl: 30 * 24 * 60 * 60,
};