# Migration Commands

This project provides several migration commands with different levels of verbosity and feedback.

## Available Commands

### 1. `npm run migration:run`
**Default command with basic feedback**
- Shows a confirmation message when migrations are up to date
- Displays simple success/failure messages
- Good for everyday use

Example output:
```
🚀 Running database migrations...
✅ All migrations are already up to date!
```

### 2. `npm run migration:run:pretty`
**Enhanced visual feedback with colors**
- Shows pending migrations before running
- Displays recent executed migrations
- Color-coded output (green for success, yellow for warnings, red for errors)
- Shows migration count and status
- Provides helpful error hints

Example output:
```
🚀 Running database migrations...

ℹ Checking for pending migrations...
✅ All migrations are up to date!
ℹ Total executed migrations: 3

Recent migrations:
  ✓ InitialSetup1753530618821
  ✓ AddDraftStatusToJob1753965000000
  ✓ AddCompanyTeamManagement1754047900537

No migrations to run.
```

### 3. `npm run migration:run:verbose`
**Detailed TypeScript-based runner**
- Shows database connection details
- Displays all executed migrations
- Provides detailed error information
- Includes database-specific error codes and hints
- Shows stack traces in development mode

Example output:
```
🚀 Starting migration process...

ℹ Checking database connection...
→ Host: localhost
→ Database: kaleido-talent-db
→ User: postgres
ℹ Initializing data source...
✓ Data source initialized successfully
...
```

### 4. `npm run migration:run:raw`
**Raw TypeORM output**
- Direct TypeORM migration command
- No additional formatting or feedback
- Useful for debugging or CI/CD pipelines

### Other Migration Commands

- `npm run migration:show` - Display all migrations and their status
- `npm run migration:generate -- -n MigrationName` - Generate a new migration
- `npm run migration:create -- -n MigrationName` - Create an empty migration
- `npm run migration:revert` - Revert the last executed migration

## Error Handling

All enhanced commands provide helpful hints for common errors:

- **Enum type changes**: Suggests dropping default values before altering
- **Missing tables/columns**: Advises checking references
- **Connection errors**: Lists things to check (PostgreSQL running, credentials, etc.)
- **Authentication failures**: Points to username/password issues

## When to Use Each Command

- **Daily development**: Use `migration:run` or `migration:run:pretty`
- **Debugging issues**: Use `migration:run:verbose`
- **CI/CD pipelines**: Use `migration:run:raw` for clean output
- **Quick status check**: Use `migration:show`