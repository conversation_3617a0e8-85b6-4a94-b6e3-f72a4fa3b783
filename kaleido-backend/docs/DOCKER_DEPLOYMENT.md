# Docker Deployment Guide

This guide explains how migrations are handled during Docker deployment.

## Overview

Migrations are automatically run during Docker container startup to ensure your database schema is always up to date.

## How It Works

### 1. Docker Build Process

Both `Dockerfile` and `Dockerfile.prod` include:
- TypeScript source files for migrations
- Migration scripts
- TypeORM and ts-node for running migrations

### 2. Container Startup Process

The `docker-entrypoint.sh` script handles the startup sequence:

1. **Redis Startup**: Starts Redis server
2. **Database Migrations**: Runs pending migrations
3. **Application Startup**: Starts the NestJS application

### 3. Migration Execution

#### Development Mode
- Uses `migration-wrapper.sh` for colorful, detailed output
- Shows pending migrations before running
- Displays progress and errors clearly

#### Production Mode
- Uses `run-migrations-prod.js` for robust error handling
- Ensures PostgreSQL schema is set correctly
- Logs detailed information for debugging

## Environment Variables

### Migration Control

- `SKIP_MIGRATIONS`: Set to `"true"` to skip migrations entirely
- `FORCE_START_ON_MIGRATION_FAILURE`: Set to `"true"` to start the app even if migrations fail (production only)
- `NODE_ENV`: Determines which migration mode to use (`production` or `development`)

### Database Configuration

Required environment variables:
- `DB_HOST`: Database host
- `DB_PORT`: Database port (default: 5432)
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name
- `DB_SSL`: Set to `"require"` for SSL connections

## Docker Commands

### Local Development

```bash
# Build and run with docker-compose
docker-compose up --build

# Run in detached mode
docker-compose up -d

# View logs
docker-compose logs -f app
```

### Production Deployment

```bash
# Build production image
docker build -f Dockerfile.prod -t myapp:prod .

# Run with environment variables
docker run -d \
  --name myapp \
  -p 8080:8080 \
  --env-file .env.production \
  myapp:prod

# Using docker-compose for production
docker-compose -f docker-compose.prod.yml up -d
```

## Migration Scenarios

### 1. Normal Deployment (Migrations Run Successfully)

```
🚀 Starting container services...
🔄 Starting Redis...
🔄 Checking Redis status...
🔄 Running database migrations...
📋 Starting migration process...
✅ All migrations are up to date!
📊 Total executed migrations: 5
✅ Migrations completed successfully
🚀 Starting application on port 8080...
```

### 2. Deployment with Pending Migrations

```
🚀 Starting container services...
🔄 Running database migrations...
📋 Starting migration process...
⚠ Found 2 pending migration(s)
▶ Running migration: AddUserTable20240101000000
✓ Migration AddUserTable20240101000000 completed successfully
▶ Running migration: AddIndexes20240102000000
✓ Migration AddIndexes20240102000000 completed successfully
✅ Migrations completed successfully
🚀 Starting application on port 8080...
```

### 3. Migration Failure (Development)

```
🔄 Running database migrations...
❌ Migration AddUserTable20240101000000 failed!
Error Details:
─────────────────────────────────
Error Message:
relation "users" already exists
─────────────────────────────────
❌ Stopping deployment due to migration failure
💡 Set FORCE_START_ON_MIGRATION_FAILURE=true to continue despite failures
```

### 4. Migration Failure (Production with Force Start)

```bash
# Set environment variable
export FORCE_START_ON_MIGRATION_FAILURE=true

# Container continues despite migration failure
⚠️ Migration failed with exit code 1
⚠️ FORCE_START_ON_MIGRATION_FAILURE is set, continuing with deployment
⚠️ Please check migration logs and run migrations manually if needed
🚀 Starting application on port 8080...
```

## Troubleshooting

### Check Migration Status

```bash
# Inside the container
docker exec -it myapp sh
npm run migration:show

# From outside
docker exec myapp npm run migration:show
```

### Run Migrations Manually

```bash
# Inside the container
docker exec -it myapp sh
npm run migration:run:verbose

# Skip migrations on next restart
docker run -e SKIP_MIGRATIONS=true myapp:prod
```

### View Migration Logs

```bash
# View all container logs
docker logs myapp

# Follow logs
docker logs -f myapp

# Filter for migration logs
docker logs myapp 2>&1 | grep -i migration
```

## Best Practices

1. **Always Test Migrations Locally**: Run migrations in development before deploying
2. **Use Migration Checks in CI/CD**: Add `npm run check:migrations` to your CI pipeline
3. **Monitor Deployment Logs**: Watch for migration errors during deployment
4. **Have a Rollback Plan**: Keep migration revert scripts ready
5. **Database Backups**: Always backup before deploying migrations

## CI/CD Integration

### GitHub Actions Example

```yaml
- name: Build and Deploy
  run: |
    docker build -f Dockerfile.prod -t myapp:${{ github.sha }} .
    docker run --rm \
      --env-file .env.production \
      myapp:${{ github.sha }} \
      npm run check:migrations
```

### Health Checks

The Docker setup includes health checks that verify:
- Redis connectivity
- Application HTTP endpoint
- Database connectivity (through app health endpoint)

## Security Considerations

1. **Never Skip Migrations in Production**: Unless absolutely necessary
2. **Use Read-Only Database Users**: For the application after migrations
3. **Encrypt Database Connections**: Always use SSL in production
4. **Audit Migration Scripts**: Review all migrations before deployment
5. **Limit Migration Permissions**: Use a separate DB user for migrations with appropriate permissions