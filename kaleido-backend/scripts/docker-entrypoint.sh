#!/bin/sh

# Docker Entrypoint Script
# This script is the entrypoint for the Docker container
# It starts Redis, runs migrations, and then the application

set -e

echo "🚀 Starting container services..."

# Start Redis
echo "🔄 Starting Redis..."
sh /app/scripts/start-redis-docker.sh

# Check Redis status (but don't fail if check fails)
echo "🔄 Checking Redis status..."
sh /app/scripts/check-redis-docker.sh || echo "❌ Redis check failed but continuing with application startup"

# Set Node.js memory options
export NODE_OPTIONS="--max-old-space-size=8192"
echo "🔧 Set Node.js memory limit to 8GB"

# Run database migrations
echo "🔄 Running database migrations..."
if [ "$SKIP_MIGRATIONS" != "true" ]; then
  echo "📋 Starting migration process..."

  # Check which environment we're in
  if [ "$NODE_ENV" = "production" ]; then
    echo "🏭 Running production migrations..."
    # Use the production migration script
    node scripts/run-migrations-prod.js
    MIGRATION_EXIT_CODE=$?
  else
    echo "🔧 Running development migrations with detailed output..."
    # Use the migration wrapper for better visibility
    sh scripts/migration-wrapper.sh
    MIGRATION_EXIT_CODE=$?
  fi

  if [ $MIGRATION_EXIT_CODE -eq 0 ]; then
    echo "✅ Migrations completed successfully"
  else
    echo "⚠️ Migration failed with exit code $MIGRATION_EXIT_CODE"
    
    # In production, we might want to continue despite migration failure
    if [ "$NODE_ENV" = "production" ] && [ "$FORCE_START_ON_MIGRATION_FAILURE" = "true" ]; then
      echo "⚠️ FORCE_START_ON_MIGRATION_FAILURE is set, continuing with deployment"
      echo "⚠️ Please check migration logs and run migrations manually if needed"
    else
      echo "❌ Stopping deployment due to migration failure"
      echo "💡 Set FORCE_START_ON_MIGRATION_FAILURE=true to continue despite failures"
      exit $MIGRATION_EXIT_CODE
    fi
  fi
else
  echo "⚠️  Migrations skipped (SKIP_MIGRATIONS=true)"
fi

# Wait a moment to ensure everything is ready
echo "⏳ Waiting for services to stabilize..."
sleep 2

# Start the application
echo "🚀 Starting application on port ${PORT:-8080}..."
node dist/main