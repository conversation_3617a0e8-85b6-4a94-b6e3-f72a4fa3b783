#!/bin/bash

# Simple wrapper to add feedback to the standard migration:run command

echo "🚀 Running database migrations..."

# Run the standard migration command
NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:run -d src/config/migration.config.ts

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    # Check if there were actually migrations to run
    if grep -q "migrations are new migrations must be executed" <<< "$(NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:show -d src/config/migration.config.ts 2>&1)"; then
        echo "✨ Migrations completed successfully!"
    else
        echo "✅ All migrations are already up to date!"
    fi
else
    echo "❌ Migration failed! Check the error output above."
fi

exit $EXIT_CODE