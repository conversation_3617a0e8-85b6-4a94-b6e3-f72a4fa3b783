#!/bin/sh

# Migration Status Check Script
# Used to verify migrations are up to date before starting the application

set -e

echo "🔍 Checking migration status..."

# Set Node.js options
export NODE_OPTIONS="--max-old-space-size=8192"

# Run migration show command and capture output
MIGRATION_OUTPUT=$(npx typeorm-ts-node-commonjs migration:show -d src/config/migration.config.ts 2>&1)
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
  echo "❌ Failed to check migration status"
  echo "$MIGRATION_OUTPUT"
  exit 1
fi

# Check if there are pending migrations
if echo "$MIGRATION_OUTPUT" | grep -q "^\[ \]"; then
  echo "⚠️  Found pending migrations:"
  echo "$MIGRATION_OUTPUT" | grep "^\[ \]" | sed 's/^\[ \] /  - /'
  exit 1
else
  echo "✅ All migrations are up to date"
  # Show executed migrations count
  EXECUTED_COUNT=$(echo "$MIGRATION_OUTPUT" | grep -c "^\[X\]" || true)
  echo "📊 Total executed migrations: $EXECUTED_COUNT"
  exit 0
fi