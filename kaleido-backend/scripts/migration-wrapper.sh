#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Running database migrations...${NC}\n"

# Run the migration command and capture output
OUTPUT_FILE=$(mktemp)
ERROR_FILE=$(mktemp)

# First, check for pending migrations
echo -e "${CYAN}ℹ Checking for pending migrations...${NC}"
PENDING_CHECK_RAW=$(NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:show -d src/config/migration.config.ts 2>&1)

# Strip ANSI codes for easier parsing
PENDING_CHECK=$(echo "$PENDING_CHECK_RAW" | sed 's/\x1b\[[0-9;]*m//g')

# Count pending migrations
PENDING_COUNT=$(echo "$PENDING_CHECK" | grep -c "^\[ \]" || true)
EXECUTED_COUNT=$(echo "$PENDING_CHECK" | grep -c "^\[X\]" || true)

if [ $PENDING_COUNT -eq 0 ]; then
    echo -e "${GREEN}✅ All migrations are up to date!${NC}"
    echo -e "${CYAN}ℹ Total executed migrations: $EXECUTED_COUNT${NC}"
    
    # Show last few executed migrations
    if [ $EXECUTED_COUNT -gt 0 ]; then
        echo -e "\n${CYAN}Recent migrations:${NC}"
        echo "$PENDING_CHECK" | grep "^\[X\]" | tail -5 | while read -r line; do
            # Extract the migration name (format: [X] number MigrationName)
            migration_name=$(echo "$line" | sed 's/^\[X\] [0-9]* //')
            echo -e "  ${GREEN}✓${NC} $migration_name"
        done
    fi
    
    echo -e "\n${GREEN}No migrations to run.${NC}"
    exit 0
fi

echo -e "${YELLOW}⚠ Found $PENDING_COUNT pending migration(s)${NC}\n"

# Show pending migrations
echo -e "${CYAN}Pending migrations:${NC}"
echo "$PENDING_CHECK" | grep "^\[ \]" | while read -r line; do
    migration_name=$(echo "$line" | sed 's/^\[ \] //')
    echo -e "  ${YELLOW}○${NC} $migration_name"
done
echo ""

# Run migration with real-time output
NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:run -d src/config/migration.config.ts 2>&1 | tee "$OUTPUT_FILE" | while IFS= read -r line; do
    if [[ "$line" == *"query:"* ]]; then
        # Show queries in gray
        echo -e "\033[90m$line\033[0m"
    elif [[ "$line" == *"Migration"*"has been executed successfully"* ]]; then
        # Show success in green
        echo -e "${GREEN}✓ $line${NC}"
    elif [[ "$line" == *"error:"* ]] || [[ "$line" == *"Error"* ]]; then
        # Show errors in red
        echo -e "${RED}✗ $line${NC}"
    elif [[ "$line" == *"migrations are new migrations must be executed"* ]]; then
        # Show pending migrations in yellow
        echo -e "${YELLOW}⚠ $line${NC}"
    elif [[ "$line" == *"migrations are already loaded"* ]]; then
        # Show status info in cyan
        echo -e "${CYAN}ℹ $line${NC}"
    elif [[ "$line" == *"Migration"* ]]; then
        # Highlight migration names
        echo -e "${CYAN}$line${NC}"
    else
        # Show other output normally
        echo "$line"
    fi
done

# Check exit status
EXIT_CODE=${PIPESTATUS[0]}

if [ $EXIT_CODE -eq 0 ]; then
    echo -e "\n${GREEN}✨ Migrations completed successfully!${NC}"
    
    # Show final status
    echo -e "\n${CYAN}ℹ Checking final migration status...${NC}"
    FINAL_CHECK_RAW=$(NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:show -d src/config/migration.config.ts 2>&1)
    FINAL_CHECK=$(echo "$FINAL_CHECK_RAW" | sed 's/\x1b\[[0-9;]*m//g')
    FINAL_EXECUTED=$(echo "$FINAL_CHECK" | grep -c "^\[X\]" || true)
    FINAL_PENDING=$(echo "$FINAL_CHECK" | grep -c "^\[ \]" || true)
    
    echo -e "${GREEN}✓ Total executed migrations: $FINAL_EXECUTED${NC}"
    if [ $FINAL_PENDING -gt 0 ]; then
        echo -e "${YELLOW}⚠ Remaining pending migrations: $FINAL_PENDING${NC}"
    else
        echo -e "${GREEN}✓ No pending migrations remaining${NC}"
    fi
else
    echo -e "\n${RED}❌ Migration failed!${NC}"
    echo -e "${YELLOW}Please check the error output above for details.${NC}"
    
    # Check for common issues
    if grep -q "does not exist" "$OUTPUT_FILE"; then
        echo -e "\n${YELLOW}💡 Hint: Check if all referenced tables and columns exist${NC}"
    fi
    
    if grep -q "enum" "$OUTPUT_FILE"; then
        echo -e "\n${YELLOW}💡 Hint: For enum type changes, you may need to:${NC}"
        echo "   1. Drop the default value"
        echo "   2. Alter the column type" 
        echo "   3. Re-add the default value"
    fi
    
    if grep -q "ECONNREFUSED" "$OUTPUT_FILE"; then
        echo -e "\n${YELLOW}💡 Hint: Cannot connect to database. Check:${NC}"
        echo "   • Is PostgreSQL running?"
        echo "   • Are your DB credentials correct in .env?"
        echo "   • Is the database accessible?"
    fi
fi

# Cleanup
rm -f "$OUTPUT_FILE" "$ERROR_FILE"

exit $EXIT_CODE