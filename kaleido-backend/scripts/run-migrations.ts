#!/usr/bin/env ts-node
import { DataSource } from 'typeorm';

// Register TypeScript paths
try {
  require('tsconfig-paths/register');
} catch (error) {
  console.warn('Warning: Could not register TypeScript paths:', error.message);
}

// Load environment variables
import { config } from 'dotenv';
config();

// Import the migration config
import AppDataSource from '../src/config/migration.config';

// Simple color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  gray: '\x1b[90m',
};

const log = {
  info: (msg: string) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg: string) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  error: (msg: string) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  warning: (msg: string) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  step: (msg: string) => console.log(`${colors.gray}→${colors.reset} ${msg}`),
  bold: (msg: string) => `${colors.bright}${msg}${colors.reset}`,
};

// Override console.log to capture TypeORM output
const originalLog = console.log;
let capturedQueries: string[] = [];
let isCapturing = false;

console.log = (...args: any[]) => {
  if (isCapturing && args[0] && typeof args[0] === 'string' && args[0].includes('query:')) {
    capturedQueries.push(args.join(' '));
  }
  originalLog.apply(console, args);
};

async function runMigrations() {
  console.log(`\n${log.bold('🚀 Starting migration process...')}\n`);

  try {
    // Check database connection
    log.info('Checking database connection...');
    log.step(`Host: ${process.env.DB_HOST}`);
    log.step(`Database: ${process.env.DB_NAME}`);
    log.step(`User: ${process.env.DB_USERNAME}`);
    
    // Initialize the data source with logging
    log.info('Initializing data source...');
    AppDataSource.setOptions({ 
      logging: ['query', 'error', 'warn', 'info', 'migration'],
      logger: 'advanced-console'
    });
    
    await AppDataSource.initialize();
    log.success('Data source initialized successfully');

    // Check pending migrations
    log.info('Checking for pending migrations...');
    
    const executedMigrations = await AppDataSource.query(
      `SELECT * FROM "migrations" ORDER BY "id" DESC`
    );
    
    console.log(`\n${log.bold('Executed migrations:')}`);
    if (executedMigrations.length === 0) {
      console.log(`  ${colors.gray}(none)${colors.reset}`);
    } else {
      executedMigrations.slice(0, 5).forEach((m: any) => {
        console.log(`  ${colors.green}✓${colors.reset} ${m.name}`);
      });
      if (executedMigrations.length > 5) {
        console.log(`  ${colors.gray}... and ${executedMigrations.length - 5} more${colors.reset}`);
      }
    }

    // Check pending migrations using TypeORM's method
    const pendingMigrations = await AppDataSource.showMigrations();
    
    if (!pendingMigrations) {
      log.success('\nAll migrations are up to date!');
      await AppDataSource.destroy();
      return;
    }

    // Run migrations with detailed output
    console.log(`\n${log.bold('Running migrations...')}\n`);
    
    try {
      isCapturing = true;
      capturedQueries = [];
      
      await AppDataSource.runMigrations({
        transaction: 'each'
      });
      
      isCapturing = false;
      
      console.log(`\n${colors.green}${colors.bright}✨ All migrations completed successfully!${colors.reset}`);
      
      // Show what was executed
      if (capturedQueries.length > 0) {
        console.log(`\n${log.bold('Executed queries:')}`);
        capturedQueries.forEach(query => {
          console.log(`  ${colors.gray}${query.substring(0, 100)}...${colors.reset}`);
        });
      }
      
    } catch (error: any) {
      isCapturing = false;
      
      log.error('Migration failed!');
      
      // Display detailed error information
      console.log(`\n${colors.red}${colors.bright}Error Details:${colors.reset}`);
      console.log(colors.red + '─'.repeat(60) + colors.reset);
      
      if (error.message) {
        console.log(`\n${colors.yellow}Error Message:${colors.reset}`);
        console.log(colors.red + error.message + colors.reset);
      }
      
      if (error.query) {
        console.log(`\n${colors.yellow}Failed Query:${colors.reset}`);
        console.log(colors.gray + error.query + colors.reset);
      }
      
      if (error.parameters && error.parameters.length > 0) {
        console.log(`\n${colors.yellow}Parameters:${colors.reset}`);
        console.log(colors.gray + JSON.stringify(error.parameters) + colors.reset);
      }
      
      if (error.driverError) {
        console.log(`\n${colors.yellow}Database Error:${colors.reset}`);
        console.log(colors.red + error.driverError.message + colors.reset);
        
        if (error.driverError.detail) {
          console.log(`\n${colors.yellow}Details:${colors.reset}`);
          console.log(colors.gray + error.driverError.detail + colors.reset);
        }
        
        if (error.driverError.hint) {
          console.log(`\n${colors.yellow}Hint:${colors.reset}`);
          console.log(colors.gray + error.driverError.hint + colors.reset);
        }
        
        if (error.driverError.code) {
          console.log(`\n${colors.yellow}Error Code:${colors.reset} ${error.driverError.code}`);
        }
      }
      
      console.log(colors.red + '─'.repeat(60) + colors.reset);
      
      // Show suggestions
      console.log(`\n${colors.yellow}Suggestions:${colors.reset}`);
      if (error.message && error.message.includes('enum')) {
        console.log('  • Check if enum values match between migration and existing data');
        console.log('  • Consider dropping default values before altering enum types');
      }
      if (error.message && error.message.includes('does not exist')) {
        console.log('  • Verify that all referenced tables and columns exist');
        console.log('  • Check if migrations are running in the correct order');
      }
      if (error.code === '23505') {
        console.log('  • Duplicate key violation - check for existing data conflicts');
      }
      
      await AppDataSource.destroy();
      process.exit(1);
    }

    // Show final migration status
    const finalMigrations = await AppDataSource.query(
      `SELECT * FROM "migrations" ORDER BY "id" DESC LIMIT 5`
    );
    
    console.log(`\n${log.bold('Current migration status:')}`);
    finalMigrations.forEach((m: any) => {
      const timestamp = new Date(parseInt(m.timestamp)).toLocaleString();
      console.log(`  ${colors.green}✓${colors.reset} ${m.name}`);
      console.log(`    ${colors.gray}Executed: ${timestamp}${colors.reset}`);
    });

    await AppDataSource.destroy();
    
  } catch (error: any) {
    log.error('Migration process failed!');
    
    console.log(`\n${colors.red}${colors.bright}Error Details:${colors.reset}`);
    console.log(colors.red + '─'.repeat(60) + colors.reset);
    
    if (error.code === 'ECONNREFUSED') {
      log.error('Could not connect to database');
      console.log(`\n${colors.yellow}Connection Details:${colors.reset}`);
      console.log(`  Host: ${process.env.DB_HOST || '(not set)'}`);
      console.log(`  Port: ${process.env.DB_PORT || '5432'}`);
      console.log(`  Database: ${process.env.DB_NAME || '(not set)'}`);
      console.log(`  User: ${process.env.DB_USERNAME || '(not set)'}`);
      console.log(`  SSL: ${process.env.DB_SSL || 'false'}`);
      console.log(`\n${colors.yellow}Please check:${colors.reset}`);
      console.log('  • Database server is running');
      console.log('  • Connection settings in .env file');
      console.log('  • Network connectivity to database');
    } else if (error.code === '28P01') {
      log.error('Authentication failed');
      console.log(`\n${colors.yellow}Please check:${colors.reset}`);
      console.log('  • Database username and password');
      console.log('  • User permissions in PostgreSQL');
    } else if (error.code === '3D000') {
      log.error('Database does not exist');
      console.log(`\n${colors.yellow}Database: ${process.env.DB_NAME}${colors.reset}`);
      console.log('Please create the database first');
    } else {
      console.log(colors.red + (error.message || error) + colors.reset);
      
      if (error.stack && process.env.NODE_ENV !== 'production') {
        console.log(`\n${colors.yellow}Stack trace:${colors.reset}`);
        console.log(colors.gray + error.stack + colors.reset);
      }
    }
    
    console.log(colors.red + '─'.repeat(60) + colors.reset);
    
    process.exit(1);
  }
}

// Run the migrations
runMigrations().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});