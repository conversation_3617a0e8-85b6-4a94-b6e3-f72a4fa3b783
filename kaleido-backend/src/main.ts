import './polyfills';
// import './instrument.js';
import './instrument';

import { join } from 'path';

import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import fastifyStatic from '@fastify/static';
import fastifyMultipart from '@fastify/multipart';
import fastifyCors from '@fastify/cors';
import fastifyHelmet from '@fastify/helmet';
import fastifyCompress from '@fastify/compress';

import { AppModule } from './app.module';
import { setupUploads } from './config/setup';
import { GlobalExceptionFilter } from './shared/filters/global-exception.filter';
import { Logger } from './shared/logger';
import { LoggingInterceptor } from './shared/logging.interceptor';
import { RedisConfigService } from './shared/services/redis-config.service';

// Suppress AWS SDK v2 warning
process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = 'true';

const logger = new Logger('Bootstrap');

async function bootstrap() {
  // Create a minimal app to get the RedisConfigService
  const minimalAdapter = new FastifyAdapter({ logger: false });
  const app = await NestFactory.create(AppModule, minimalAdapter, {
    logger: ['error', 'warn'],
    bufferLogs: true,
  });

  // Get the RedisConfigService from the app
  const redisConfigService = app.get(RedisConfigService);

  // Check Redis connectivity using the service
  try {
    logger.debug('Checking Redis connectivity...');
    const isConnected = await redisConfigService.checkRedisConnection();

    if (!isConnected) {
      logger.warn(
        'Redis connection check failed, but continuing startup. Background jobs may not work properly.',
      );
    } else {
      logger.debug('Redis connection check passed. Redis is available.');
    }
  } catch (error) {
    // Log the error but don't fail startup
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`Redis connection check error: ${errorMessage}`);
    logger.warn(
      'Continuing startup despite Redis connection issues. Background jobs may not work properly.',
    );
  }

  // Always log that we're continuing regardless of Redis status
  logger.debug('Application will start regardless of Redis connectivity status');

  // Close the minimal app
  await app.close();

  setupUploads();
  try {
    // Create main app with debug logging enabled using Fastify
    const fastifyAdapter = new FastifyAdapter({
      logger: false,
      trustProxy: true,
    });
    
    const app = await NestFactory.create<NestFastifyApplication>(
      AppModule,
      fastifyAdapter,
      {
        logger: ['error', 'warn', 'log', 'debug', 'verbose'],
        bufferLogs: true,
      },
    );

    // Register Fastify plugins
    await app.register(fastifyCompress);
    await app.register(fastifyHelmet, {
      contentSecurityPolicy: false, // Disable for Swagger UI
    });
    
    // Configure static file serving
    await app.register(fastifyStatic, {
      root: join(__dirname, '..', 'public'),
      prefix: '/static/',
      decorateReply: false,
    });

    // Register multipart for file uploads
    await app.register(fastifyMultipart, {
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    });

    // Add early request logging hook
    app.getHttpAdapter().getInstance().addHook('onRequest', async (request, reply) => {
      logger.debug(`Incoming ${request.method} request to ${request.url}`);
    });

    // Configure main app
    app.setGlobalPrefix('api', {
      exclude: ['/', '/health', '/startup', '/liveness', '/readiness'],
    });
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    app.useGlobalInterceptors(new LoggingInterceptor());
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Configure CORS
    const isDev = process.env.NODE_ENV === 'development';
    logger.debug(`Configuring CORS for ${isDev ? 'development' : 'production'} environment`);

    await app.register(fastifyCors, {
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) {
          callback(null, true);
          return;
        }

        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:8080',
          'https://localhost:8080',
          'http://localhost:3001',
          'https://kaleidotalent.com',
          'https://www.kaleidotalent.com',
          'https://api.kaleidotalent.com',
          'https://app.kaleidotalent.com',
          'https://docker.kaleidotalent.com',
          'https://staging.kaleidotalent.com',
        ];

        const isAllowed =
          allowedOrigins.includes(origin) || /^https:\/\/.*\.kaleidotalent\.com$/.test(origin);

        logger.debug(`CORS origin check: ${origin} - ${isAllowed ? 'allowed' : 'denied'}`);

        if (isAllowed) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'), false);
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin',
        'Access-Control-Allow-Headers',
        'Access-Control-Request-Method',
        'Access-Control-Request-Headers',
        'Access-Control-Allow-Credentials',
        'Access-Control-Allow-Origin',
        'X-CSRF-Token',
        'Accept-Version',
        'Content-Length',
        'Content-MD5',
        'Date',
        'X-Api-Version',
        'X-Onboarding-Context',
        'X-Onboarding-Role',
        'X-Temp-User-ID',
      ],
      preflightContinue: false,
      optionsSuccessStatus: 204,
      exposedHeaders: ['Authorization', 'Access-Control-Allow-Origin'],
      maxAge: 7200, // 2 hours
    });

    // Swagger setup for main app
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Your API')
      .setDescription('Your API description')
      .setVersion('1.0')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api/docs', app, document);

    // Start application
    const PORT = process.env.PORT || 8080;
    await app.listen(PORT, '0.0.0.0');

    // Get application details
    const appUrl = await app.getUrl();

    // Clear console and show application banner
    console.clear();

    logger.startupBox(
      [
        '🚀 Application Successfully Started',
        '',
        '📡 API Details',
        `   • Server     : ${appUrl}/api`,
        `   • Swagger    : ${appUrl}/api/docs`,
        `   • Health     : ${appUrl}/api/health`,
        `   • Status     : ${appUrl}/api/status-dashboard`,
        `   • Email Test : ${appUrl}/static/email-test.html`,
        `   • Environment: ${process.env.NODE_ENV || 'development'}`,
        `   • Log Levels : error, warn, log, debug, verbose`,
      ].filter(Boolean),
    );

    // Keep the process running
    process.on('SIGINT', async () => {
      await app.close();
      process.exit(0);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Promise Rejection', {
        reason,
        promise,
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack,
      });
      // Give logger time to write before exiting
      setTimeout(() => process.exit(1), 1000);
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      await logger.error('Failed to start application', {
        error: error.message,
        stack: error.stack,
      });
    } else {
      await logger.error('Failed to start application', {
        error: 'An unknown error occurred',
      });
    }
    process.exit(1);
  }
}

// Execute bootstrap
bootstrap().catch(async (error: unknown) => {
  if (error instanceof Error) {
    logger.error('Unhandled bootstrap error', {
      error: error.message,
      stack: error.stack,
    });
  } else {
    await logger.error('Unhandled bootstrap error', { error });
  }
  process.exit(1);
});
