import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

interface CacheEntry {
  data: any;
  timestamp: number;
  hits: number;
}

/**
 * Simple in-memory cache for AI operations
 * TODO: Replace with Redis for production use
 */
@Injectable()
export class AICacheService {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly defaultTTL = 3600000; // 1 hour
  private readonly maxCacheSize = 1000;

  /**
   * Get cached data
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > this.defaultTTL) {
      this.cache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.data;
  }

  /**
   * Set cached data
   */
  set(key: string, data: any): void {
    // Implement LRU eviction when cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = Array.from(this.cache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp,
      )[0][0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hits: 0,
    });
  }

  /**
   * Generate cache key from input
   */
  generateKey(operation: string, input: any): string {
    const hash = crypto
      .createHash('sha256')
      .update(JSON.stringify({ operation, input }))
      .digest('hex');
    return `ai:${operation}:${hash.substring(0, 16)}`;
  }

  /**
   * Get cache statistics
   */
  getStats(): any {
    const entries = Array.from(this.cache.entries());
    const totalHits = entries.reduce((sum, [_, entry]) => sum + entry.hits, 0);

    return {
      size: this.cache.size,
      totalHits,
      averageHits: this.cache.size > 0 ? totalHits / this.cache.size : 0,
      oldestEntry:
        entries.length > 0 ? new Date(Math.min(...entries.map((e) => e[1].timestamp))) : null,
    };
  }

  /**
   * Clear cache
   */
  clear(): void {
    this.cache.clear();
  }
}

/**
 * Decorator for caching AI operation results
 * Usage: @AICache('operation-name')
 */
export function AICache(operationName: string) {
  const cacheService = new AICacheService(); // TODO: Inject this properly

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Generate cache key
      const cacheKey = cacheService.generateKey(operationName, args);

      // Check cache
      const cachedResult = cacheService.get(cacheKey);
      if (cachedResult !== null) {
        console.log(`🎯 Cache hit for ${operationName}`);
        return cachedResult;
      }

      // Call original method
      const result = await originalMethod.apply(this, args);

      // Cache result
      cacheService.set(cacheKey, result);

      return result;
    };

    return descriptor;
  };
}
