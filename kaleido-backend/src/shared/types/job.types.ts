export interface CultureFitQuestions {
  id: string;
  question: string;
  duration: number;
}

export enum JobStatus {
  DRAFT = 'DRAFT',
  NEW = 'NEW',
  APPLIED = 'APPLIED',
  MATCHED = 'MATCHED',
  OPEN = 'OPEN',
  CONTACTED = 'CONTACTED',
  INTERESTED = 'INTERESTED',
  NOT_INTERESTED = 'NOT_INTERESTED',
  INTERVIEWING = 'INTERVIEWING',
  HIRED = 'HIRED',
  REJECTED = 'REJECTED',
}

export enum TypeOfHiring {
  EMPLOYMENT = 'EMPLOYMENT',
  PROJECT = 'PROJECT',
}

export enum TypeOfJob {
  PERMANENT = 'PERMANENT',
  CONTRACT = 'CONTRACT',
  PART_TIME = 'PART_TIME',
}

export interface JobTLDR {
  summary: string;
  salary?: string;
  location?: string[];
  experience?: string;
  jobType?: string;
  typeOfHiring?: TypeOfHiring;
  typeOfJob?: TypeOfJob;
}

export interface MatchDetails {
  overallScore: number;
  skillsMatch: {
    score: number;
    matchedSkills: string[];
  };
  jobTitleMatch: {
    score: number;
    relevance: string; // e.g., 'high', 'medium', 'low'
  };
  locationMatch: {
    score: number;
    matchType: string; // e.g., 'exact', 'city', 'region', 'country', 'remote'
  };
  experienceMatch: {
    score: number;
    relevance: string; // e.g., 'high', 'medium', 'low'
  };
  educationMatch?: {
    score: number;
    relevance: string; // e.g., 'high', 'medium', 'low'
  };
}

export interface JobMetrics {
  views: number;
  applications: number;
  clickThroughRate?: number;
  lastViewed?: Date;
  viewSources?: Record<string, number>; // Track views by source (e.g., 'linkedin', 'direct', 'email')
  applicationSources?: Record<string, number>; // Track applications by source
  matchScores?: Record<string, number>; // Store candidate match scores by candidateId
  matchDetails?: Record<string, MatchDetails>; // Store detailed match information by candidateId
}
