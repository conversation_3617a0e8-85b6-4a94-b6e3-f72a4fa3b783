import Redis, { RedisOptions } from 'ioredis';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Centralized service for Redis configuration
 * Simple configuration that directly uses environment variables
 */
@Injectable()
export class RedisConfigService {
  private readonly logger = new Logger(RedisConfigService.name);
  private readonly nodeEnv: string;

  constructor(private readonly configService: ConfigService) {
    this.nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    this.logger.log(`Redis configuration initialized for ${this.nodeEnv} environment`);

    // Log basic configuration info
    if (this.isDevelopment()) {
      this.logger.log(
        `Development mode: Using Redis at ${this.configService.get<string>(
          'REDIS_HOST',
          'localhost',
        )}`,
      );
    } else {
      const redisUrl = this.configService.get<string>('REDIS_URL');
      if (redisUrl) {
        const maskedUrl = redisUrl.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
        this.logger.log(`Production mode: Using Redis URL: ${maskedUrl}`);
      } else {
        this.logger.warn('No REDIS_URL found in production environment');
      }
    }
  }

  /**
   * Check if we're in development environment
   */
  private isDevelopment(): boolean {
    return this.nodeEnv === 'development' || this.nodeEnv === 'local';
  }

  /**
   * Decide whether we should attach a password to the Redis connection.
   * We only want to forward the password in non-development environments
   * to avoid noisy ACL warnings coming from local/CI Redis instances that
   * have no authentication configured.
   */
  private shouldIncludePassword(password?: string | null): boolean {
    // Never add a password in development or test – local Redis usually has none.
    if (this.isDevelopment() || this.nodeEnv === 'test') {
      return false;
    }

    if (!password || password.trim() === '') return false;

    const lower = password.trim().toLowerCase();
    // Ignore common placeholders so they are not treated as real passwords.
    return !['null', 'undefined', 'none'].includes(lower);
  }

  /**
   * Create a Redis client based on configuration
   * Private helper method to avoid code duplication
   */
  private createRedisClientInternal(options?: { forceUrl?: string; forBull?: boolean }): Redis {
    const redisHost = this.configService.get<string>('REDIS_HOST', 'localhost');
    const redisPort = parseInt(this.configService.get<string>('REDIS_PORT', '6379'));
    const redisPassword = this.configService.get<string>('REDIS_PASSWORD');
    const includePassword = this.shouldIncludePassword(redisPassword);

    // Log connection details
    this.logger.log(`Creating Redis client for ${options?.forBull ? 'Bull' : 'general use'}`);
    this.logger.log(
      `Redis connection details: ${redisHost}:${redisPort} with${
        redisPassword ? '' : 'out'
      } password`,
    );

    // If this is for Bull, use the Bull-specific configuration
    if (options?.forBull) {
      const bullConfig = this.getBullSpecificRedisConfig();

      // Convert the Bull config to Redis options
      const redisOptions: RedisOptions = {};

      // Handle individual parameters
      redisOptions.host = bullConfig.host;
      redisOptions.port = bullConfig.port;
      if (bullConfig.password) redisOptions.password = bullConfig.password;

      // Add minimal connection options
      redisOptions.connectTimeout = 30000;
      redisOptions.retryStrategy = (times: number) => {
        if (times > 10) {
          this.logger.warn(`Redis connection failed after ${times} attempts, stopping retries`);
          return null; // Stop retrying after 10 attempts
        }
        const delay = Math.min(times * 50, 2000);
        this.logger.debug(`Redis connection retry attempt ${times} with delay ${delay}ms`);
        return delay;
      };

      return new Redis(redisOptions);
    }

    // For non-Bull clients, use the standard options
    // Common Redis options for better reliability
    const commonOptions: RedisOptions = {
      maxRetriesPerRequest: 5, // Limit retries to prevent excessive logging
      enableReadyCheck: true,
      retryStrategy: (times: number) => {
        if (times > 10) {
          this.logger.warn(`Redis connection failed after ${times} attempts, stopping retries`);
          return null; // Stop retrying after 10 attempts
        }
        const delay = Math.min(times * 50, 2000); // Exponential backoff with max 2 seconds
        this.logger.debug(`Redis connection retry attempt ${times} with delay ${delay}ms`);
        return delay;
      },
      reconnectOnError: (err: Error) => {
        const targetError =
          err.message.includes('READONLY') ||
          err.message.includes('ETIMEDOUT') ||
          err.message.includes('ECONNRESET') ||
          err.message.includes('ENOTFOUND');
        if (targetError) {
          this.logger.warn(`Redis reconnecting due to error: ${err.message}`);
          return true; // Reconnect for these errors
        }
        return false;
      },
      connectTimeout: 10000, // 10 seconds
      commandTimeout: 5000, // 5 seconds
      keepAlive: 10000, // 10 seconds
      autoResubscribe: true,
      autoResendUnfulfilledCommands: true,
    };

    // Use Redis configuration from environment
    this.logger.debug(`Using Redis at ${redisHost}:${redisPort}`);
    const redisOptions: RedisOptions = {
      ...commonOptions,
      host: redisHost,
      port: redisPort,
    };

    if (includePassword) {
      redisOptions.password = redisPassword as string;
    } else {
      // Ensure we don't send an empty password
      delete redisOptions.password;
      this.logger.debug('Omitting Redis password for non-production environment');
    }

    return new Redis(redisOptions);
  }

  /**
   * Check Redis connection
   * This method attempts to connect to Redis and verify connectivity
   * Returns true if connection is successful, false otherwise
   * Enhanced for DigitalOcean App Platform compatibility
   */
  async checkRedisConnection(): Promise<boolean> {
    // Skip if we're in test mode
    if (this.nodeEnv === 'test') {
      this.logger.debug('Skipping Redis pre-check - Test mode');
      return true;
    }

    let client: Redis | null = null;
    const maxRetries = 3;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const redisHost = this.configService.get<string>('REDIS_HOST', 'localhost');
        const redisPort = parseInt(this.configService.get<string>('REDIS_PORT', '6379'));
        const redisPassword = this.configService.get<string>('REDIS_PASSWORD');

        this.logger.log(
          `Checking Redis connectivity to ${redisHost}:${redisPort} with${
            redisPassword ? '' : 'out'
          } password (attempt ${attempt}/${maxRetries})...`,
        );

        // Using individual connection parameters for local Redis
        const redisOptions: RedisOptions = {
          host: redisHost,
          port: redisPort,
          connectTimeout: 10000, // 10 seconds
          retryStrategy: (times: number) => {
            // For DigitalOcean App Platform, use a more aggressive retry strategy
            const delay = Math.min(times * 100, 2000);
            this.logger.debug(`Redis connection retry attempt ${times} with delay ${delay}ms`);
            return delay;
          },
          // Disable ready check for faster connection in DO App Platform
          enableReadyCheck: false,
          // Increase max retries for DO App Platform
          maxRetriesPerRequest: 3,
        };

        if (this.shouldIncludePassword(redisPassword)) {
          redisOptions.password = redisPassword as string;
        }

        client = new Redis(redisOptions);

        // Setup event handlers
        client.on('error', (err: Error) => {
          this.logger.error(
            `⚠️ REDIS CONNECTION ERROR: ${err.message}. Background jobs will not work!`,
            {
              error: err.message,
              stack: err.stack,
            },
          );
        });

        // Try to ping Redis with a timeout
        const pingPromise = client.ping();
        const timeoutPromise = new Promise<string>((_, reject) => {
          setTimeout(() => reject(new Error('Redis ping timed out after 5 seconds')), 5000);
        });

        const pong = await Promise.race([pingPromise, timeoutPromise]);

        if (pong !== 'PONG') {
          throw new Error(`Redis ping returned unexpected response: ${pong}`);
        }

        this.logger.log('✅ Redis connectivity check passed - Redis is running and accessible');

        // Clean up the client connection
        if (client) {
          try {
            await client.quit();
          } catch (quitError) {
            this.logger.warn(
              `Error while closing Redis client: ${
                quitError instanceof Error ? quitError.message : 'Unknown error'
              }`,
            );
          }
        }

        return true;
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : undefined;

        this.logger.warn(
          `⚠️ REDIS CONNECTION ATTEMPT ${attempt}/${maxRetries} FAILED: ${errorMessage}`,
          {
            error: errorMessage,
            stack: errorStack,
            host: this.configService.get<string>('REDIS_HOST', 'localhost'),
            port: this.configService.get<string>('REDIS_PORT', '6379'),
          },
        );

        // Clean up the client connection
        if (client) {
          try {
            await client.quit();
          } catch (quitError) {
            this.logger.warn(
              `Error while closing Redis client: ${
                quitError instanceof Error ? quitError.message : 'Unknown error'
              }`,
            );
          }
          client = null;
        }

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const delayMs = attempt * 1000; // Increase delay with each attempt
          this.logger.log(`Waiting ${delayMs}ms before retry ${attempt + 1}/${maxRetries}...`);
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        } else {
          // Last attempt failed
          this.logger.error(
            `⚠️ REDIS CONNECTION FAILED AFTER ${maxRetries} ATTEMPTS! Background workers will not function properly.`,
            {
              host: this.configService.get<string>('REDIS_HOST', 'localhost'),
              port: this.configService.get<string>('REDIS_PORT', '6379'),
            },
          );
          return false;
        }
      }
    }

    // This should never be reached due to the return statements above
    return false;
  }

  /**
   * Get Redis connection options for Bull
   */
  getBullRedisConfig(): { redis: any } {
    return {
      redis: this.getBullSpecificRedisConfig(),
    };
  }

  /**
   * Get Bull-specific Redis configuration
   * This method provides a configuration that's compatible with Bull's requirements
   * Enhanced for DigitalOcean App Platform compatibility
   */
  getBullSpecificRedisConfig(): Record<string, any> {
    // Skip if we're in test mode
    if (this.nodeEnv === 'test') {
      this.logger.debug('Using test mode Redis configuration for Bull');
      return {
        host: 'localhost',
        port: 6379,
      };
    }

    // Use local Redis configuration
    const host = this.configService.get<string>('REDIS_HOST', 'localhost');
    const port = parseInt(this.configService.get<string>('REDIS_PORT', '6379'));
    const password = this.configService.get<string>('REDIS_PASSWORD');

    this.logger.log(
      `Configuring Bull Redis connection to ${host}:${port} with${password ? '' : 'out'} password`,
    );

    // Enhanced configuration for DigitalOcean App Platform
    const config: Record<string, any> = {
      host: host,
      port: port,
      // Bull-specific settings optimized for DO App Platform
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      // Connection settings for better reliability in DO App Platform
      connectTimeout: 30000, // 30 seconds
      commandTimeout: 60000, // 60 seconds
      // Retry strategy for better reliability
      retryStrategy: (times: number) => {
        if (times > 10) {
          this.logger.warn(`Redis connection failed after ${times} attempts, stopping retries`);
          return null; // Stop retrying after 10 attempts
        }
        const delay = Math.min(times * 100, 3000); // Exponential backoff with max 3 seconds
        this.logger.debug(`Redis connection retry attempt ${times} with delay ${delay}ms`);
        return delay;
      },
    };

    if (this.shouldIncludePassword(password)) {
      config.password = password as string;
    }

    return config;
  }

  /**
   * Get direct Redis configuration for general Redis usage (not for Bull)
   */
  getDirectRedisConfig(): Record<string, any> {
    // Common Redis options for better reliability
    const commonOptions: Record<string, any> = {
      maxRetriesPerRequest: null, // Disable max retries per request
      enableReadyCheck: true,
      connectTimeout: 30000, // 30 seconds
      commandTimeout: 10000, // 10 seconds
      keepAlive: 10000, // 10 seconds
      autoResendUnfulfilledCommands: true,
      enableOfflineQueue: true,
    };

    // Skip if we're in test mode
    if (this.nodeEnv === 'test') {
      this.logger.debug('Using test mode Redis configuration');
      return {
        host: 'localhost',
        port: 6379,
        ...commonOptions,
      };
    }

    // Use local Redis configuration
    this.logger.debug('Using local Redis instance for direct Redis configuration');

    const config: Record<string, any> = {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: parseInt(this.configService.get<string>('REDIS_PORT', '6379')),
      ...commonOptions,
    };

    const password = this.configService.get<string>('REDIS_PASSWORD');
    if (this.shouldIncludePassword(password)) {
      config.password = password as string;
    }

    return config;
  }

  /**
   * Get Redis options for IoRedis client
   */
  getRedisOptions(): RedisOptions {
    return this.getDirectRedisConfig() as RedisOptions;
  }

  /**
   * Create a new Redis client instance
   * This method creates a new Redis client based on the current configuration
   * @param options Optional configuration options
   * @param options.forBull Whether this client is for Bull (avoids problematic options)
   */
  createRedisClient(options?: { forBull?: boolean }): Redis {
    return this.createRedisClientInternal({ forBull: options?.forBull });
  }

  /**
   * Create a Redis client specifically configured for Bull
   * This avoids using options that cause issues with Bull's internal clients
   */
  createBullRedisClient(): Redis {
    return this.createRedisClientInternal({ forBull: true });
  }

  /**
   * Get Redis URL for IoRedis client
   */
  getRedisUrl(): string | null {
    // Construct URL from individual parameters
    const host = this.configService.get<string>('REDIS_HOST', 'localhost');
    const port = this.configService.get<string>('REDIS_PORT', '6379');
    const password = this.configService.get<string>('REDIS_PASSWORD');

    // Only include password if it's valid
    if (password && password.trim() !== '') {
      // Check if the password is "null", "undefined", or "none" (common placeholder values)
      const lowerPassword = password.trim().toLowerCase();
      if (lowerPassword !== 'null' && lowerPassword !== 'undefined' && lowerPassword !== 'none') {
        return `redis://:${password}@${host}:${port}`;
      }
    }

    return `redis://${host}:${port}`;
  }
}
