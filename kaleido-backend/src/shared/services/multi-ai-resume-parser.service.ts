import { CreateCandidateDto } from '@/modules/candidate/dto/create-candidate.dto';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GroqService } from './groq.service';
import { OpenAIService } from './openai-base.service';

export enum AIProvider {
  GROQ = 'groq',
  OPENAI_GPT4O_MINI = 'openai-gpt4o-mini',
  OPENAI_GPT4 = 'openai-gpt4',
}

export interface AIProviderConfig {
  provider: AIProvider;
  enabled: boolean;
  priority: number; // Lower number = higher priority
  maxConcurrency: number;
  estimatedSpeed: string;
}

/**
 * Multi-AI Resume Parser Service
 * Intelligently routes resume processing to the fastest available AI provider
 * with automatic fallback to ensure reliability
 */
@Injectable()
export class MultiAIResumeParserService {
  private readonly logger = new Logger(MultiAIResumeParserService.name);

  private readonly providerConfigs: AIProviderConfig[] = [
    {
      provider: AIProvider.GROQ,
      enabled: true,
      priority: 1, // Highest priority - fastest
      maxConcurrency: 100, // Increased from 50 to 100 for better throughput
      estimatedSpeed: '500+ tokens/sec',
    },
    {
      provider: AIProvider.OPENAI_GPT4O_MINI,
      enabled: true,
      priority: 2, // Second choice - good balance
      maxConcurrency: 50, // Increased from 30 to 50 for better throughput
      estimatedSpeed: '100+ tokens/sec',
    },
    {
      provider: AIProvider.OPENAI_GPT4,
      enabled: true,
      priority: 3, // Fallback - most reliable
      maxConcurrency: 20, // Increased from 10 to 20 for better throughput
      estimatedSpeed: '50+ tokens/sec',
    },
  ];

  constructor(
    private readonly groqService: GroqService,
    private readonly openaiService: OpenAIService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Parse a single resume using the best available AI provider
   */
  async parseResumeData(
    file: {
      mimetype: string;
      buffer: Buffer;
      originalname: string;
    },
    jobId: string,
  ): Promise<CreateCandidateDto> {
    const startTime = Date.now();

    // Extract text content first
    const { extractFileContents } = await import('@/utils/file-extractor.util');
    const fileWithRequiredProps = {
      ...file,
      fieldname: 'file',
      encoding: '7bit',
      size: file.buffer.length,
      stream: null as any,
      destination: '',
      filename: file.originalname,
      path: '',
    };
    const { contents, duplicateFiles } = await extractFileContents([fileWithRequiredProps], []);

    if (duplicateFiles.length > 0) {
      throw new Error('Duplicate resume detected.');
    }

    if (contents.length === 0) {
      throw new Error('No content extracted from the resume.');
    }

    const resumeContent = contents[0].content;

    // Get available providers in priority order
    const availableProviders = this.getAvailableProviders();

    if (availableProviders.length === 0) {
      throw new Error('No AI providers available for resume processing');
    }

    let lastError: Error | null = null;

    // Try each provider in priority order
    for (const config of availableProviders) {
      try {
        this.logger.log(
          `🤖 Attempting resume parsing with ${config.provider} (${config.estimatedSpeed})`,
        );

        const extractedData = await this.extractWithProvider(config.provider, resumeContent);
        const processingTime = (Date.now() - startTime) / 1000;

        this.logger.log(
          `✅ Successfully parsed with ${config.provider} in ${processingTime.toFixed(2)}s`,
        );

        // Convert to CreateCandidateDto format
        return this.convertToCreateCandidateDto(extractedData, jobId);
      } catch (error: any) {
        lastError = error;
        this.logger.warn(`❌ ${config.provider} failed: ${error.message}`);

        // Continue to next provider
        continue;
      }
    }

    // All providers failed
    const totalTime = (Date.now() - startTime) / 1000;
    this.logger.error(`💥 All AI providers failed after ${totalTime.toFixed(2)}s`);
    throw new Error(`Resume parsing failed with all providers. Last error: ${lastError?.message}`);
  }

  /**
   * Parse multiple resumes using intelligent load balancing across multiple AI providers
   * Distributes workload to maximize throughput by using all available providers simultaneously
   */
  async parseMultipleResumesWithLoadBalancing(
    files: Array<{
      mimetype: string;
      buffer: Buffer;
      originalname: string;
    }>,
    jobId: string,
  ): Promise<{
    successful: CreateCandidateDto[];
    failed: { filename: string; error: string }[];
    providerStats: Record<string, { processed: number; failed: number; avgTime: number }>;
  }> {
    const startTime = Date.now();
    this.logger.log(`🚀 Starting load-balanced processing of ${files.length} resumes`);

    const results = {
      successful: [] as CreateCandidateDto[],
      failed: [] as { filename: string; error: string }[],
      providerStats: {} as Record<string, { processed: number; failed: number; avgTime: number }>,
    };

    // Get all available providers
    const availableProviders = this.getAvailableProviders();

    if (availableProviders.length === 0) {
      throw new Error('No AI providers available for processing');
    }

    this.logger.log(
      `📊 Available providers: ${availableProviders.map((p) => `${p.provider} (${p.maxConcurrency} concurrent)`).join(', ')}`,
    );

    // Distribute files across providers based on their capacity and speed
    const providerWorkloads = this.distributeWorkloadAcrossProviders(files, availableProviders);

    // Process all providers simultaneously
    const providerPromises = Object.entries(providerWorkloads).map(
      async ([provider, assignedFiles]) => {
        const providerConfig = availableProviders.find((p) => p.provider === provider)!;
        const providerStartTime = Date.now();

        this.logger.log(
          `🤖 ${provider}: Processing ${assignedFiles.length} files (max concurrency: ${providerConfig.maxConcurrency})`,
        );

        const providerResults = {
          successful: [] as CreateCandidateDto[],
          failed: [] as { filename: string; error: string }[],
          processingTimes: [] as number[],
        };

        // Process files for this provider in batches based on its concurrency limit
        const batches = this.chunkArray(assignedFiles, providerConfig.maxConcurrency);

        for (const batch of batches) {
          const batchPromises = batch.map(async (fileData: any) => {
            const fileStartTime = Date.now();
            try {
              // Extract content from file if not already extracted
              let content = fileData.content;
              if (!content && fileData.file) {
                const { extractFileContents } = await import('@/utils/file-extractor.util');
                const { contents } = await extractFileContents([fileData.file], []);
                if (contents.length === 0) {
                  throw new Error('No content extracted from the resume');
                }
                content = contents[0].content;
              }

              const extractedData = await this.extractWithProvider(
                providerConfig.provider,
                content,
              );
              const result = this.convertToCreateCandidateDto(extractedData, jobId);
              const fileProcessingTime = Date.now() - fileStartTime;

              return {
                success: true,
                data: result,
                filename: fileData.originalname,
                processingTime: fileProcessingTime,
              };
            } catch (error: any) {
              const fileProcessingTime = Date.now() - fileStartTime;
              return {
                success: false,
                error: error.message || 'Unknown error',
                filename: fileData.originalname,
                processingTime: fileProcessingTime,
              };
            }
          });

          const batchResults = await Promise.allSettled(batchPromises);

          batchResults.forEach((result) => {
            if (result.status === 'fulfilled') {
              providerResults.processingTimes.push(result.value.processingTime);
              if (result.value.success && result.value.data) {
                providerResults.successful.push(result.value.data);
              } else {
                providerResults.failed.push({
                  filename: result.value.filename,
                  error: result.value.error,
                });
              }
            } else {
              providerResults.failed.push({
                filename: 'unknown',
                error: result.reason?.message || 'Promise rejected',
              });
            }
          });
        }

        const providerTotalTime = (Date.now() - providerStartTime) / 1000;
        const avgProcessingTime =
          providerResults.processingTimes.length > 0
            ? providerResults.processingTimes.reduce((a, b) => a + b, 0) /
              providerResults.processingTimes.length
            : 0;

        this.logger.log(
          `✅ ${provider}: Completed ${providerResults.successful.length} files in ${providerTotalTime.toFixed(2)}s (avg: ${(avgProcessingTime / 1000).toFixed(2)}s/file)`,
        );

        return {
          provider,
          results: providerResults,
          avgTime: avgProcessingTime,
        };
      },
    );

    // Wait for all providers to complete
    const allProviderResults = await Promise.allSettled(providerPromises);

    // Aggregate results from all providers
    allProviderResults.forEach((result) => {
      if (result.status === 'fulfilled') {
        const { provider, results: providerResults, avgTime } = result.value;

        results.successful.push(...providerResults.successful);
        results.failed.push(...providerResults.failed);
        results.providerStats[provider] = {
          processed: providerResults.successful.length,
          failed: providerResults.failed.length,
          avgTime,
        };
      } else {
        this.logger.error(`Provider processing failed:`, result.reason);
      }
    });

    const totalProcessingTime = (Date.now() - startTime) / 1000;
    const throughput = files.length / totalProcessingTime;

    this.logger.log(`\n🎯 LOAD-BALANCED PROCESSING COMPLETE`);
    this.logger.log(`⏱️  Total Time: ${totalProcessingTime.toFixed(2)}s`);
    this.logger.log(`🚀 Throughput: ${throughput.toFixed(2)} files/second`);
    this.logger.log(
      `✅ Success: ${results.successful.length}, ❌ Failed: ${results.failed.length}`,
    );

    // Log provider performance breakdown
    this.logger.log(`\n📊 PROVIDER PERFORMANCE:`);
    Object.entries(results.providerStats).forEach(([provider, stats]) => {
      const providerThroughput = stats.processed / (stats.avgTime / 1000);
      this.logger.log(
        `  ${provider}: ${stats.processed} files, ${(stats.avgTime / 1000).toFixed(2)}s avg, ${providerThroughput.toFixed(2)} files/sec`,
      );
    });

    return results;
  }

  /**
   * Legacy method for backward compatibility - now uses load balancing
   */
  async parseMultipleResumes(
    files: Array<{
      mimetype: string;
      buffer: Buffer;
      originalname: string;
    }>,
    jobId: string,
  ): Promise<{
    successful: CreateCandidateDto[];
    failed: { filename: string; error: string }[];
  }> {
    const result = await this.parseMultipleResumesWithLoadBalancing(files, jobId);
    return {
      successful: result.successful,
      failed: result.failed,
    };
  }

  /**
   * Extract resume data using a specific AI provider
   */
  private async extractWithProvider(provider: AIProvider, resumeContent: string): Promise<any> {
    switch (provider) {
      case AIProvider.GROQ:
        return await this.groqService.extractResumeData(resumeContent);

      case AIProvider.OPENAI_GPT4O_MINI:
        // Use OpenAI with GPT-4o-mini model
        return await this.extractWithOpenAI(resumeContent, 'gpt-4o-mini');

      case AIProvider.OPENAI_GPT4:
        // Use OpenAI with GPT-4 model
        return await this.extractWithOpenAI(resumeContent, 'gpt-4');

      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }

  /**
   * Extract using OpenAI with specified model
   */
  private async extractWithOpenAI(resumeContent: string, model: string): Promise<any> {
    const OpenAI = (await import('openai')).default;
    const openai = new OpenAI({
      apiKey: this.configService.get('OPENAI_API_KEY'),
    });

    const systemPrompt = `You are an expert resume parser. Extract key information from resumes and return ONLY a valid JSON object with the following structure:

{
  "fullName": "string",
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "location": "string",
  "jobTitle": "string",
  "currentCompany": "string",
  "yearsOfExperience": number,
  "summary": "string",
  "skills": ["string"],
  "experience": [
    {
      "title": "string",
      "company": "string",
      "duration": "string",
      "startDate": "string",
      "endDate": "string",
      "location": "string"
    }
  ],
  "education": [
    {
      "degree": "string",
      "institution": "string",
      "year": "string",
      "location": "string"
    }
  ],
  "linkedinUrl": "string",
  "githubUrl": "string"
}

Rules:
- Return ONLY valid JSON, no markdown or explanations
- Use null for missing values
- Extract email addresses carefully
- Calculate yearsOfExperience from work history if not explicitly stated
- Keep skills as an array of strings
- Preserve original formatting for names and companies`;

    const response = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: `Extract information from this resume:\n\n${resumeContent}`,
        },
      ],
      max_tokens: 2000,
      temperature: 0,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    try {
      return JSON.parse(content);
    } catch (parseError) {
      this.logger.error('Failed to parse OpenAI response as JSON:', content);
      throw new Error(`Invalid JSON response from OpenAI: ${parseError}`);
    }
  }

  /**
   * Get available providers sorted by priority
   */
  private getAvailableProviders(): AIProviderConfig[] {
    return this.providerConfigs
      .filter((config) => {
        if (!config.enabled) return false;

        switch (config.provider) {
          case AIProvider.GROQ:
            return this.groqService.isAvailable();
          case AIProvider.OPENAI_GPT4O_MINI:
          case AIProvider.OPENAI_GPT4:
            return !!this.configService.get('OPENAI_API_KEY');
          default:
            return false;
        }
      })
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get the best provider for batch processing based on file count
   */
  private getBestProviderForBatch(fileCount: number): AIProviderConfig | null {
    const available = this.getAvailableProviders();

    // For large batches, prefer providers with higher concurrency
    if (fileCount > 20) {
      return available.find((p) => p.maxConcurrency >= 30) || available[0] || null;
    }

    // For smaller batches, use the fastest provider
    return available[0] || null;
  }

  /**
   * Convert extracted data to CreateCandidateDto format
   */
  private convertToCreateCandidateDto(extractedData: any, jobId: string): CreateCandidateDto {
    return {
      jobId,
      fullName:
        extractedData.fullName ||
        `${extractedData.firstName || ''} ${extractedData.lastName || ''}`.trim(),
      firstName: extractedData.firstName || '',
      lastName: extractedData.lastName || '',
      email: extractedData.email || '',
      phone: extractedData.phone || '',
      location: extractedData.location || '',
      jobTitle: extractedData.jobTitle || '',
      currentCompany: extractedData.currentCompany || '',
      summary: extractedData.summary || '',
      skills: extractedData.skills || [],
      experience: extractedData.experience || [],
      linkedinUrl: extractedData.linkedinUrl || '',
      githubUrl: extractedData.githubUrl || '',
      profileUrl: extractedData.profileUrl || '',
      source: 'resume_upload',
    };
  }

  /**
   * Distribute workload across multiple AI providers based on their capacity and speed
   * Faster providers get more files, ensuring optimal throughput
   */
  private distributeWorkloadAcrossProviders(
    files: Array<{
      mimetype: string;
      buffer: Buffer;
      originalname: string;
    }>,
    providers: AIProviderConfig[],
  ): Record<string, Array<{ originalname: string; content: string }>> {
    const workloads: Record<string, Array<{ originalname: string; content: string }>> = {};

    // Initialize workloads for each provider
    providers.forEach((provider) => {
      workloads[provider.provider] = [];
    });

    // Calculate total capacity (weighted by speed and concurrency)
    const providerWeights = providers.map((provider) => {
      // Weight based on priority (lower priority = higher weight) and max concurrency
      const priorityWeight = 1 / provider.priority; // Groq (priority 1) gets weight 1, OpenAI (priority 2) gets 0.5
      const concurrencyWeight = provider.maxConcurrency / 50; // Normalize to max 50
      return {
        provider: provider.provider,
        weight: priorityWeight * concurrencyWeight,
        maxConcurrency: provider.maxConcurrency,
      };
    });

    const totalWeight = providerWeights.reduce((sum, p) => sum + p.weight, 0);

    // Extract content from all files first (this is fast and can be done sequentially)
    const filesWithContent = files.map((file) => {
      // For now, we'll extract content in the processing phase
      // This is a placeholder - content will be extracted per file during processing
      return {
        originalname: file.originalname,
        file: file,
      };
    });

    // Distribute files based on weighted capacity
    let currentProviderIndex = 0;
    let filesAssignedToCurrentProvider = 0;

    filesWithContent.forEach((fileData, index) => {
      const currentProvider = providerWeights[currentProviderIndex];
      const expectedFilesForProvider = Math.ceil(
        (files.length * currentProvider.weight) / totalWeight,
      );

      // Add file to current provider (content will be extracted during processing)
      workloads[currentProvider.provider].push({
        originalname: fileData.originalname,
        content: '', // Placeholder - will be extracted during processing
        file: fileData.file, // Keep reference to original file
      } as any);

      filesAssignedToCurrentProvider++;

      // Move to next provider if current one has enough files or we've reached its max capacity
      if (
        filesAssignedToCurrentProvider >= expectedFilesForProvider ||
        filesAssignedToCurrentProvider >= currentProvider.maxConcurrency
      ) {
        currentProviderIndex = (currentProviderIndex + 1) % providerWeights.length;
        filesAssignedToCurrentProvider = 0;
      }
    });

    // Log distribution
    this.logger.log(`\n📊 WORKLOAD DISTRIBUTION:`);
    Object.entries(workloads).forEach(([provider, assignedFiles]) => {
      const providerConfig = providers.find((p) => p.provider === provider)!;
      this.logger.log(
        `  ${provider}: ${assignedFiles.length} files (${providerConfig.estimatedSpeed})`,
      );
    });

    return workloads;
  }

  /**
   * Utility function to chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get status of all AI providers
   */
  getProvidersStatus(): Array<{
    provider: AIProvider;
    available: boolean;
    priority: number;
    maxConcurrency: number;
    estimatedSpeed: string;
  }> {
    return this.providerConfigs.map((config) => ({
      provider: config.provider,
      available: this.getAvailableProviders().some((p) => p.provider === config.provider),
      priority: config.priority,
      maxConcurrency: config.maxConcurrency,
      estimatedSpeed: config.estimatedSpeed,
    }));
  }
}
