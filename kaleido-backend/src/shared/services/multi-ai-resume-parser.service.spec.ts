import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { GroqService } from './groq.service';
import {
  AIProvider,
  AIProviderConfig,
  MultiAIResumeParserService,
} from './multi-ai-resume-parser.service';
import { OpenAIService } from './openai-base.service';

describe('MultiAIResumeParserService - Provider Switching Logic', () => {
  let service: MultiAIResumeParserService;
  let groqService: jest.Mocked<GroqService>;
  let openaiService: jest.Mocked<OpenAIService>;
  let configService: jest.Mocked<ConfigService>;

  const mockFiles = Array.from({ length: 10 }, (_, i) => ({
    mimetype: 'application/pdf',
    buffer: Buffer.from(`mock resume content ${i}`),
    originalname: `resume_${i}.pdf`,
  }));

  beforeEach(async () => {
    const mockGroqService = {
      isAvailable: jest.fn(),
      extractResumeData: jest.fn(),
      getStatus: jest.fn(),
    };

    const mockOpenAIService = {
      // Mock OpenAI service methods
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MultiAIResumeParserService,
        { provide: GroqService, useValue: mockGroqService },
        { provide: OpenAIService, useValue: mockOpenAIService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<MultiAIResumeParserService>(MultiAIResumeParserService);
    groqService = module.get(GroqService);
    openaiService = module.get(OpenAIService);
    configService = module.get(ConfigService);
  });

  describe('getAvailableProviders', () => {
    it('should return all available providers sorted by priority', () => {
      // Mock Groq as available
      groqService.isAvailable.mockReturnValue(true);
      configService.get.mockReturnValue('mock-api-key');

      const availableProviders = service['getAvailableProviders']();

      expect(availableProviders).toHaveLength(3);
      expect(availableProviders[0].provider).toBe(AIProvider.GROQ);
      expect(availableProviders[0].priority).toBe(1);
      expect(availableProviders[1].provider).toBe(AIProvider.OPENAI_GPT4O_MINI);
      expect(availableProviders[1].priority).toBe(2);
      expect(availableProviders[2].provider).toBe(AIProvider.OPENAI_GPT4);
      expect(availableProviders[2].priority).toBe(3);
    });

    it('should exclude Groq when not available', () => {
      groqService.isAvailable.mockReturnValue(false);
      configService.get.mockReturnValue('mock-api-key');

      const availableProviders = service['getAvailableProviders']();

      expect(availableProviders).toHaveLength(2);
      expect(availableProviders.every((p) => p.provider !== AIProvider.GROQ)).toBe(true);
    });

    it('should exclude OpenAI providers when API key not available', () => {
      groqService.isAvailable.mockReturnValue(true);
      configService.get.mockReturnValue(null);

      const availableProviders = service['getAvailableProviders']();

      expect(availableProviders).toHaveLength(1);
      expect(availableProviders[0].provider).toBe(AIProvider.GROQ);
    });
  });

  describe('distributeWorkloadAcrossProviders', () => {
    const mockProviders: AIProviderConfig[] = [
      {
        provider: AIProvider.GROQ,
        enabled: true,
        priority: 1,
        maxConcurrency: 50,
        estimatedSpeed: '500+ tokens/sec',
      },
      {
        provider: AIProvider.OPENAI_GPT4O_MINI,
        enabled: true,
        priority: 2,
        maxConcurrency: 30,
        estimatedSpeed: '100+ tokens/sec',
      },
      {
        provider: AIProvider.OPENAI_GPT4,
        enabled: true,
        priority: 3,
        maxConcurrency: 10,
        estimatedSpeed: '50+ tokens/sec',
      },
    ];

    it('should distribute workload with Groq getting the most files', () => {
      const workloads = service['distributeWorkloadAcrossProviders'](mockFiles, mockProviders);

      expect(workloads[AIProvider.GROQ].length).toBeGreaterThan(
        workloads[AIProvider.OPENAI_GPT4O_MINI].length,
      );
      expect(workloads[AIProvider.OPENAI_GPT4O_MINI].length).toBeGreaterThan(
        workloads[AIProvider.OPENAI_GPT4].length,
      );

      // Total files should equal input
      const totalDistributed = Object.values(workloads).reduce(
        (sum, files) => sum + files.length,
        0,
      );
      expect(totalDistributed).toBe(mockFiles.length);
    });

    it('should respect provider max concurrency limits', () => {
      const largeFileSet = Array.from({ length: 100 }, (_, i) => ({
        mimetype: 'application/pdf',
        buffer: Buffer.from(`content ${i}`),
        originalname: `file_${i}.pdf`,
      }));

      const workloads = service['distributeWorkloadAcrossProviders'](largeFileSet, mockProviders);

      expect(workloads[AIProvider.GROQ].length).toBeLessThanOrEqual(100);
      expect(workloads[AIProvider.OPENAI_GPT4O_MINI].length).toBeLessThanOrEqual(50);
      expect(workloads[AIProvider.OPENAI_GPT4].length).toBeLessThanOrEqual(20);
    });

    it('should handle single provider scenario', () => {
      const singleProvider = [mockProviders[0]]; // Only Groq
      const workloads = service['distributeWorkloadAcrossProviders'](mockFiles, singleProvider);

      expect(Object.keys(workloads)).toHaveLength(1);
      expect(workloads[AIProvider.GROQ].length).toBe(mockFiles.length);
    });

    it('should distribute evenly when providers have equal weights', () => {
      const equalProviders: AIProviderConfig[] = [
        { ...mockProviders[0], priority: 1, maxConcurrency: 25 },
        { ...mockProviders[1], priority: 1, maxConcurrency: 25 },
      ];

      const workloads = service['distributeWorkloadAcrossProviders'](mockFiles, equalProviders);

      const groqFiles = workloads[AIProvider.GROQ].length;
      const openaiFiles = workloads[AIProvider.OPENAI_GPT4O_MINI].length;

      // Should be roughly equal (within 1 file difference)
      expect(Math.abs(groqFiles - openaiFiles)).toBeLessThanOrEqual(1);
    });
  });

  describe('getBestProviderForBatch', () => {
    beforeEach(() => {
      // Mock getAvailableProviders to return all providers
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([
        {
          provider: AIProvider.GROQ,
          priority: 1,
          maxConcurrency: 50,
          estimatedSpeed: '500+ tokens/sec',
        },
        {
          provider: AIProvider.OPENAI_GPT4O_MINI,
          priority: 2,
          maxConcurrency: 30,
          estimatedSpeed: '100+ tokens/sec',
        },
        {
          provider: AIProvider.OPENAI_GPT4,
          priority: 3,
          maxConcurrency: 10,
          estimatedSpeed: '50+ tokens/sec',
        },
      ]);
    });

    it('should prefer high-concurrency providers for large batches', () => {
      const bestProvider = service['getBestProviderForBatch'](50);

      expect(bestProvider?.provider).toBe(AIProvider.GROQ);
      expect(bestProvider?.maxConcurrency).toBe(50);
    });

    it('should return fastest provider for small batches', () => {
      const bestProvider = service['getBestProviderForBatch'](5);

      expect(bestProvider?.provider).toBe(AIProvider.GROQ);
      expect(bestProvider?.priority).toBe(1);
    });

    it('should return null when no providers available', () => {
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([]);

      const bestProvider = service['getBestProviderForBatch'](10);

      expect(bestProvider).toBeNull();
    });

    it('should fallback to available provider when preferred is not available', () => {
      // Mock only OpenAI providers available
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([
        {
          provider: AIProvider.OPENAI_GPT4O_MINI,
          priority: 2,
          maxConcurrency: 30,
          estimatedSpeed: '100+ tokens/sec',
        },
      ]);

      const bestProvider = service['getBestProviderForBatch'](25);

      expect(bestProvider?.provider).toBe(AIProvider.OPENAI_GPT4O_MINI);
    });
  });

  describe('parseResumeData - Provider Fallback', () => {
    const mockFile = {
      mimetype: 'application/pdf',
      buffer: Buffer.from('mock content'),
      originalname: 'test.pdf',
    };

    beforeEach(() => {
      // Mock file extraction
      jest.doMock('@/utils/file-extractor.util', () => ({
        extractFileContents: jest.fn().mockResolvedValue({
          contents: [{ fileName: 'test.pdf', content: 'extracted content' }],
          duplicateFiles: [],
        }),
      }));
    });

    it('should try providers in priority order and succeed with first available', async () => {
      groqService.isAvailable.mockReturnValue(true);
      groqService.extractResumeData.mockResolvedValue({
        fullName: 'John Doe',
        email: '<EMAIL>',
        skills: ['JavaScript', 'TypeScript'],
      });
      configService.get.mockReturnValue('mock-api-key');

      const result = await service.parseResumeData(mockFile, 'job-123');

      expect(groqService.extractResumeData).toHaveBeenCalledWith('extracted content');
      expect(result.fullName).toBe('John Doe');
      expect(result.jobId).toBe('job-123');
    });

    it('should fallback to next provider when first fails', async () => {
      groqService.isAvailable.mockReturnValue(true);
      groqService.extractResumeData.mockRejectedValue(new Error('Groq API error'));
      configService.get.mockReturnValue('mock-api-key');

      // Mock successful OpenAI extraction
      jest.spyOn(service as any, 'extractWithOpenAI').mockResolvedValue({
        fullName: 'Jane Doe',
        email: '<EMAIL>',
        skills: ['Python', 'React'],
      });

      const result = await service.parseResumeData(mockFile, 'job-123');

      expect(groqService.extractResumeData).toHaveBeenCalled();
      expect(service['extractWithOpenAI']).toHaveBeenCalledWith('extracted content', 'gpt-4o-mini');
      expect(result.fullName).toBe('Jane Doe');
    });

    it('should throw error when all providers fail', async () => {
      groqService.isAvailable.mockReturnValue(true);
      groqService.extractResumeData.mockRejectedValue(new Error('Groq failed'));
      configService.get.mockReturnValue('mock-api-key');

      jest.spyOn(service as any, 'extractWithOpenAI').mockRejectedValue(new Error('OpenAI failed'));

      await expect(service.parseResumeData(mockFile, 'job-123')).rejects.toThrow(
        'Resume parsing failed with all providers',
      );
    });
  });

  describe('getProvidersStatus', () => {
    it('should return correct status for all providers', () => {
      groqService.isAvailable.mockReturnValue(true);
      configService.get.mockReturnValue('mock-api-key');

      const status = service.getProvidersStatus();

      expect(status).toHaveLength(3);
      expect(status[0]).toEqual({
        provider: AIProvider.GROQ,
        available: true,
        priority: 1,
        maxConcurrency: 100,
        estimatedSpeed: '500+ tokens/sec',
      });
    });

    it('should mark providers as unavailable when not configured', () => {
      groqService.isAvailable.mockReturnValue(false);
      configService.get.mockReturnValue(null);

      const status = service.getProvidersStatus();

      expect(status.every((p) => !p.available)).toBe(true);
    });
  });

  describe('parseMultipleResumesWithLoadBalancing', () => {
    const mockProviders: AIProviderConfig[] = [
      {
        provider: AIProvider.GROQ,
        enabled: true,
        priority: 1,
        maxConcurrency: 50,
        estimatedSpeed: '500+ tokens/sec',
      },
      {
        provider: AIProvider.OPENAI_GPT4O_MINI,
        enabled: true,
        priority: 2,
        maxConcurrency: 30,
        estimatedSpeed: '100+ tokens/sec',
      },
    ];

    beforeEach(() => {
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue(mockProviders);

      // Mock file extraction
      jest.doMock('@/utils/file-extractor.util', () => ({
        extractFileContents: jest.fn().mockResolvedValue({
          contents: [{ fileName: 'test.pdf', content: 'extracted content' }],
          duplicateFiles: [],
        }),
      }));
    });

    it('should distribute workload and process files concurrently', async () => {
      // Mock successful extraction for both providers
      groqService.extractResumeData.mockResolvedValue({
        fullName: 'Groq User',
        email: '<EMAIL>',
      });

      jest.spyOn(service as any, 'extractWithOpenAI').mockResolvedValue({
        fullName: 'OpenAI User',
        email: '<EMAIL>',
      });

      const result = await service.parseMultipleResumesWithLoadBalancing(mockFiles, 'job-123');

      expect(result.successful).toHaveLength(mockFiles.length);
      expect(result.failed).toHaveLength(0);
      expect(result.providerStats).toHaveProperty(AIProvider.GROQ);
      expect(result.providerStats).toHaveProperty(AIProvider.OPENAI_GPT4O_MINI);
    });

    it('should handle provider failures gracefully', async () => {
      // Mock Groq failure and OpenAI success
      groqService.extractResumeData.mockRejectedValue(new Error('Groq service down'));
      jest.spyOn(service as any, 'extractWithOpenAI').mockResolvedValue({
        fullName: 'OpenAI User',
        email: '<EMAIL>',
      });

      const result = await service.parseMultipleResumesWithLoadBalancing(mockFiles, 'job-123');

      // Should have some failures from Groq and some successes from OpenAI
      expect(result.successful.length + result.failed.length).toBe(mockFiles.length);
      expect(result.failed.length).toBeGreaterThan(0);
      expect(result.providerStats[AIProvider.GROQ].failed).toBeGreaterThan(0);
    });

    it('should provide accurate performance statistics', async () => {
      groqService.extractResumeData.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () =>
                resolve({
                  fullName: 'Test User',
                  email: '<EMAIL>',
                }),
              100,
            ),
          ),
      );

      const result = await service.parseMultipleResumesWithLoadBalancing(
        mockFiles.slice(0, 4),
        'job-123',
      );

      expect(result.providerStats[AIProvider.GROQ]).toHaveProperty('processed');
      expect(result.providerStats[AIProvider.GROQ]).toHaveProperty('avgTime');
      expect(result.providerStats[AIProvider.GROQ].avgTime).toBeGreaterThan(0);
    });

    it('should throw error when no providers are available', async () => {
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([]);

      await expect(
        service.parseMultipleResumesWithLoadBalancing(mockFiles, 'job-123'),
      ).rejects.toThrow('No AI providers available for processing');
    });
  });

  describe('Load Balancing Edge Cases', () => {
    it('should handle empty file array', async () => {
      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([
        {
          provider: AIProvider.GROQ,
          enabled: true,
          priority: 1,
          maxConcurrency: 50,
          estimatedSpeed: '500+ tokens/sec',
        },
      ]);

      const result = await service.parseMultipleResumesWithLoadBalancing([], 'job-123');

      expect(result.successful).toHaveLength(0);
      expect(result.failed).toHaveLength(0);
      expect(Object.keys(result.providerStats)).toHaveLength(1);
    });

    it('should handle single file with multiple providers', async () => {
      const singleFile = [mockFiles[0]];

      jest.spyOn(service as any, 'getAvailableProviders').mockReturnValue([
        {
          provider: AIProvider.GROQ,
          enabled: true,
          priority: 1,
          maxConcurrency: 50,
          estimatedSpeed: '500+ tokens/sec',
        },
        {
          provider: AIProvider.OPENAI_GPT4O_MINI,
          enabled: true,
          priority: 2,
          maxConcurrency: 30,
          estimatedSpeed: '100+ tokens/sec',
        },
      ]);

      groqService.extractResumeData.mockResolvedValue({
        fullName: 'Single User',
        email: '<EMAIL>',
      });

      const result = await service.parseMultipleResumesWithLoadBalancing(singleFile, 'job-123');

      expect(result.successful).toHaveLength(1);
      // Should only use one provider (likely Groq due to priority)
      const usedProviders = Object.keys(result.providerStats).filter(
        (provider) => result.providerStats[provider].processed > 0,
      );
      expect(usedProviders).toHaveLength(1);
    });

    it('should respect concurrency limits during distribution', () => {
      const manyFiles = Array.from({ length: 200 }, (_, i) => ({
        mimetype: 'application/pdf',
        buffer: Buffer.from(`content ${i}`),
        originalname: `file_${i}.pdf`,
      }));

      const providers: AIProviderConfig[] = [
        {
          provider: AIProvider.GROQ,
          enabled: true,
          priority: 1,
          maxConcurrency: 10, // Low limit
          estimatedSpeed: '500+ tokens/sec',
        },
        {
          provider: AIProvider.OPENAI_GPT4O_MINI,
          enabled: true,
          priority: 2,
          maxConcurrency: 5, // Even lower limit
          estimatedSpeed: '100+ tokens/sec',
        },
      ];

      const workloads = service['distributeWorkloadAcrossProviders'](manyFiles, providers);

      expect(workloads[AIProvider.GROQ].length).toBeLessThanOrEqual(150);
      expect(workloads[AIProvider.OPENAI_GPT4O_MINI].length).toBeLessThanOrEqual(70);

      // Should still distribute all files
      const totalDistributed = Object.values(workloads).reduce(
        (sum, files) => sum + files.length,
        0,
      );
      expect(totalDistributed).toBe(manyFiles.length);
    });
  });
});
