import { Injectable, Logger } from '@nestjs/common';

export interface AIOperationMetrics {
  operation: string;
  provider: string;
  model?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  error?: string;
  inputTokens?: number;
  outputTokens?: number;
  estimatedCost?: number;
  metadata?: Record<string, any>;
}

export interface ProviderStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  totalCost: number;
  successRate: number;
}

@Injectable()
export class AIPerformanceMonitorService {
  private readonly logger = new Logger(AIPerformanceMonitorService.name);
  private metrics: AIOperationMetrics[] = [];
  private readonly maxMetricsSize = 10000; // Keep last 10k operations

  /**
   * Start tracking an AI operation
   */
  startOperation(operation: string, provider: string, model?: string): AIOperationMetrics {
    const metric: AIOperationMetrics = {
      operation,
      provider,
      model,
      startTime: Date.now(),
      success: false,
    };

    return metric;
  }

  /**
   * Complete tracking an AI operation
   */
  completeOperation(
    metric: AIOperationMetrics,
    success: boolean,
    error?: string,
    tokenUsage?: { input: number; output: number },
  ) {
    metric.endTime = Date.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    metric.error = error;

    if (tokenUsage) {
      metric.inputTokens = tokenUsage.input;
      metric.outputTokens = tokenUsage.output;
      metric.estimatedCost = this.calculateCost(metric.provider, metric.model, tokenUsage);
    }

    // Log performance data
    this.logger.log(
      `🎯 ${metric.operation} | ${metric.provider}${metric.model ? `/${metric.model}` : ''} | ` +
        `${metric.duration}ms | ${success ? '✅' : '❌'} ${error ? `| ${error}` : ''}`,
    );

    // Store metrics (with size limit)
    this.metrics.push(metric);
    if (this.metrics.length > this.maxMetricsSize) {
      this.metrics = this.metrics.slice(-this.maxMetricsSize);
    }
  }

  /**
   * Calculate estimated cost based on token usage
   */
  private calculateCost(
    provider: string,
    model?: string,
    tokenUsage?: { input: number; output: number },
  ): number {
    if (!tokenUsage) return 0;

    // Cost per 1M tokens
    const costs: Record<string, { input: number; output: number }> = {
      'groq/llama-3.3-70b-versatile': { input: 0.59, output: 0.79 }, // Groq pricing
      'openai/gpt-4o-mini': { input: 0.15, output: 0.6 },
      'openai/gpt-4o': { input: 5.0, output: 15.0 },
      'openai/gpt-4': { input: 30.0, output: 60.0 },
      'openai/gpt-3.5-turbo': { input: 0.5, output: 1.5 },
    };

    const key = `${provider}/${model}`.toLowerCase();
    const cost = costs[key] || costs['openai/gpt-4o-mini']; // Default to GPT-4O-MINI pricing

    return (
      (tokenUsage.input * cost.input) / 1_000_000 + (tokenUsage.output * cost.output) / 1_000_000
    );
  }

  /**
   * Get performance statistics by provider
   */
  getProviderStats(hours: number = 24): Record<string, ProviderStats> {
    const cutoffTime = Date.now() - hours * 60 * 60 * 1000;
    const recentMetrics = this.metrics.filter((m) => m.startTime > cutoffTime);

    const stats: Record<string, ProviderStats> = {};

    recentMetrics.forEach((metric) => {
      const key = metric.provider;

      if (!stats[key]) {
        stats[key] = {
          totalRequests: 0,
          successfulRequests: 0,
          failedRequests: 0,
          averageResponseTime: 0,
          totalCost: 0,
          successRate: 0,
        };
      }

      stats[key].totalRequests++;
      if (metric.success) {
        stats[key].successfulRequests++;
      } else {
        stats[key].failedRequests++;
      }

      stats[key].totalCost += metric.estimatedCost || 0;
    });

    // Calculate averages and success rates
    Object.keys(stats).forEach((provider) => {
      const providerMetrics = recentMetrics.filter((m) => m.provider === provider && m.duration);
      const totalDuration = providerMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);

      stats[provider].averageResponseTime =
        providerMetrics.length > 0 ? totalDuration / providerMetrics.length : 0;

      stats[provider].successRate =
        stats[provider].totalRequests > 0
          ? (stats[provider].successfulRequests / stats[provider].totalRequests) * 100
          : 0;
    });

    return stats;
  }

  /**
   * Get operation performance statistics
   */
  getOperationStats(hours: number = 24): Record<string, any> {
    const cutoffTime = Date.now() - hours * 60 * 60 * 1000;
    const recentMetrics = this.metrics.filter((m) => m.startTime > cutoffTime);

    const operationStats: Record<string, any> = {};

    recentMetrics.forEach((metric) => {
      const key = metric.operation;

      if (!operationStats[key]) {
        operationStats[key] = {
          count: 0,
          averageTime: 0,
          successRate: 0,
          totalCost: 0,
          providers: {},
        };
      }

      operationStats[key].count++;
      operationStats[key].totalCost += metric.estimatedCost || 0;

      // Track by provider
      const provider = metric.provider;
      if (!operationStats[key].providers[provider]) {
        operationStats[key].providers[provider] = {
          count: 0,
          successCount: 0,
        };
      }

      operationStats[key].providers[provider].count++;
      if (metric.success) {
        operationStats[key].providers[provider].successCount++;
      }
    });

    // Calculate averages
    Object.keys(operationStats).forEach((operation) => {
      const opMetrics = recentMetrics.filter((m) => m.operation === operation && m.duration);
      const totalDuration = opMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
      const successCount = opMetrics.filter((m) => m.success).length;

      operationStats[operation].averageTime =
        opMetrics.length > 0 ? totalDuration / opMetrics.length : 0;

      operationStats[operation].successRate =
        operationStats[operation].count > 0
          ? (successCount / operationStats[operation].count) * 100
          : 0;
    });

    return operationStats;
  }

  /**
   * Get hourly performance trends
   */
  getHourlyTrends(hours: number = 24): any[] {
    const cutoffTime = Date.now() - hours * 60 * 60 * 1000;
    const recentMetrics = this.metrics.filter((m) => m.startTime > cutoffTime);

    const hourlyData: Record<string, any> = {};

    recentMetrics.forEach((metric) => {
      const hour = new Date(metric.startTime).toISOString().slice(0, 13); // YYYY-MM-DDTHH

      if (!hourlyData[hour]) {
        hourlyData[hour] = {
          hour,
          requests: 0,
          avgResponseTime: 0,
          successRate: 0,
          cost: 0,
        };
      }

      hourlyData[hour].requests++;
      hourlyData[hour].cost += metric.estimatedCost || 0;
    });

    // Calculate averages for each hour
    Object.keys(hourlyData).forEach((hour) => {
      const hourMetrics = recentMetrics.filter(
        (m) => new Date(m.startTime).toISOString().slice(0, 13) === hour,
      );

      const successCount = hourMetrics.filter((m) => m.success).length;
      const totalDuration = hourMetrics
        .filter((m) => m.duration)
        .reduce((sum, m) => sum + (m.duration || 0), 0);

      hourlyData[hour].successRate =
        hourlyData[hour].requests > 0 ? (successCount / hourlyData[hour].requests) * 100 : 0;

      hourlyData[hour].avgResponseTime = successCount > 0 ? totalDuration / successCount : 0;
    });

    return Object.values(hourlyData).sort((a, b) => a.hour.localeCompare(b.hour));
  }

  /**
   * Log current statistics summary
   */
  logSummary() {
    const providerStats = this.getProviderStats(1); // Last hour
    const operationStats = this.getOperationStats(1);

    this.logger.log('📊 AI Performance Summary (Last Hour)');
    this.logger.log('=====================================');

    // Provider summary
    Object.entries(providerStats).forEach(([provider, stats]) => {
      this.logger.log(
        `${provider}: ${stats.totalRequests} requests | ` +
          `${stats.successRate.toFixed(1)}% success | ` +
          `${stats.averageResponseTime.toFixed(0)}ms avg | ` +
          `$${stats.totalCost.toFixed(4)} cost`,
      );
    });

    this.logger.log('');

    // Operation summary (top 5)
    const topOperations = Object.entries(operationStats)
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 5);

    this.logger.log('Top Operations:');
    topOperations.forEach(([operation, stats]) => {
      this.logger.log(
        `${operation}: ${stats.count} calls | ` +
          `${stats.averageTime.toFixed(0)}ms avg | ` +
          `${stats.successRate.toFixed(1)}% success | ` +
          `$${stats.totalCost.toFixed(4)} cost`,
      );
    });
  }
}
