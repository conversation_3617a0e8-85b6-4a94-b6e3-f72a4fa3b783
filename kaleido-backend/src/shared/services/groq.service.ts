import Groq from 'groq-sdk';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import {
  cleanVideoScript,
  COMPANY_SUMMARY_SYSTEM_PROMPT,
  getCompanySummaryUserPrompt,
  getSocialMediaUserPrompt,
  getSkillsResponsibilitiesUserPrompt,
  getVideoScriptSystemPrompt,
  getVideoScriptUserPrompt,
  JOB_SUMMARY_SYSTEM_PROMPT,
  SKILLS_RESPONSIBILITIES_SYSTEM_PROMPT,
  SOCIAL_MEDIA_SYSTEM_PROMPT,
  VIDEO_SCRIPT_LENGTH_SPECS,
} from '../constants/ai-prompts';
import { GenerateSkillsAndResponsibilitiesResponse } from '../types/openai.types';

/**
 * Groq AI Service for ultra-fast resume processing
 * Groq offers 500+ tokens/second vs OpenAI's ~50 tokens/second
 * Perfect for high-volume resume extraction tasks
 */
@Injectable()
export class GroqService {
  private readonly logger = new Logger(GroqService.name);
  private readonly groq: Groq;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('GROQ_API_KEY');
    if (!apiKey) {
      this.logger.warn('GROQ_API_KEY not found. Groq service will not be available.');
    }

    this.groq = new Groq({
      apiKey: apiKey || '',
    });
  }

  /**
   * Extract resume data using Groq's ultra-fast inference
   * Uses Llama 3.1 70B model for high-quality extraction at 500+ tokens/sec
   */
  async extractResumeData(resumeContent: string, retryCount: number = 0): Promise<any> {
    if (!this.groq.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const maxRetries = 2;
    const startTime = Date.now();

    try {
      const systemPrompt = `You are an expert resume parser. Extract key information from resumes and return ONLY a valid JSON object with the following structure:

{
  "fullName": "string",
  "firstName": "string", 
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "location": "string",
  "jobTitle": "string",
  "currentCompany": "string",
  "yearsOfExperience": number,
  "summary": "string",
  "skills": ["string"],
  "experience": [
    {
      "title": "string",
      "company": "string", 
      "duration": "string",
      "startDate": "string",
      "endDate": "string",
      "location": "string"
    }
  ],
  "education": [
    {
      "degree": "string",
      "institution": "string",
      "year": "string",
      "location": "string"
    }
  ],
  "linkedinUrl": "string",
  "githubUrl": "string"
}

Rules:
- Return ONLY valid JSON, no markdown code blocks, no backticks, no explanations
- DO NOT wrap the JSON in markdown code blocks or any other formatting
- Output must be parseable by JSON.parse() directly
- Use null for missing values
- Extract email addresses carefully
- Calculate yearsOfExperience from work history if not explicitly stated
- Keep skills as an array of strings
- Preserve original formatting for names and companies`;

      const userPrompt = `Extract information from this resume:\n\n${resumeContent}`;

      const response = await this.groq.chat.completions.create({
        model: 'llama-3.3-70b-versatile', // Groq's latest high-quality model
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: 2000,
        temperature: 0, // Deterministic output for consistent extraction
        top_p: 1,
        stream: false,
      });

      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(`⚡ Groq processing completed in ${processingTime.toFixed(2)}s`);

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from Groq');
      }

      // Parse and validate JSON response
      let parsedData;
      try {
        // Clean the response - remove markdown code blocks if present
        let cleanContent = content.trim();

        // Remove markdown code blocks (```json or ``` at start/end)
        if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```(?:json)?\s*\n?/, '');
          cleanContent = cleanContent.replace(/\n?```\s*$/, '');
        }

        parsedData = JSON.parse(cleanContent);
      } catch (parseError) {
        this.logger.error('Failed to parse Groq response as JSON:', content);
        // Provide more descriptive error for markdown code block issue
        if (content.includes('```')) {
          throw new Error(
            `Groq returned JSON wrapped in markdown code blocks. Response cleaning failed: ${parseError}`,
          );
        }
        throw new Error(`Invalid JSON response from Groq: ${parseError}`);
      }

      // Validate required fields
      if (!parsedData.fullName && !parsedData.firstName && !parsedData.lastName) {
        throw new Error('Could not extract candidate name from resume');
      }

      return parsedData;
    } catch (error: any) {
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.error(`Groq processing failed after ${processingTime.toFixed(2)}s:`, error);

      // Retry logic
      if (retryCount < maxRetries) {
        this.logger.warn(
          `Retrying Groq extraction (attempt ${retryCount + 2}/${maxRetries + 1})...`,
        );
        // Add a small delay before retry
        await new Promise((resolve) => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.extractResumeData(resumeContent, retryCount + 1);
      }

      throw error;
    }
  }

  /**
   * Process multiple resumes concurrently using Groq's high throughput
   * Optimized to handle 100+ concurrent requests efficiently
   */
  async extractMultipleResumes(resumeContents: string[]): Promise<{
    successful: any[];
    failed: { index: number; error: string }[];
  }> {
    const startTime = Date.now();
    this.logger.log(`🚀 Processing ${resumeContents.length} resumes with Groq concurrently`);

    const results = {
      successful: [] as any[],
      failed: [] as { index: number; error: string }[],
    };

    // Process in batches of 100 for optimal throughput
    const batchSize = 100; // Increased from implicit unlimited to controlled batches
    const batches: string[][] = [];

    for (let i = 0; i < resumeContents.length; i += batchSize) {
      batches.push(resumeContents.slice(i, i + batchSize));
    }

    // Process each batch concurrently
    for (const batch of batches) {
      const promises = batch.map(async (content, localIndex) => {
        const index = batches.indexOf(batch) * batchSize + localIndex;
        try {
          const extractedData = await this.extractResumeData(content);
          return { success: true, data: extractedData, index };
        } catch (error: any) {
          return {
            success: false,
            error: error.message || 'Unknown error',
            index,
          };
        }
      });

      const responses = await Promise.allSettled(promises);

      responses.forEach((response) => {
        if (response.status === 'fulfilled') {
          if (response.value.success) {
            results.successful.push(response.value.data);
          } else {
            results.failed.push({
              index: response.value.index,
              error: response.value.error,
            });
          }
        } else {
          results.failed.push({
            index: -1,
            error: response.reason?.message || 'Promise rejected',
          });
        }
      });
    }

    const processingTime = (Date.now() - startTime) / 1000;
    const throughput = resumeContents.length / processingTime;

    this.logger.log(`⚡ Groq batch processing completed in ${processingTime.toFixed(2)}s`);
    this.logger.log(`📊 Throughput: ${throughput.toFixed(2)} resumes/second`);
    this.logger.log(
      `✅ Success: ${results.successful.length}, ❌ Failed: ${results.failed.length}`,
    );

    return results;
  }

  /**
   * Check if Groq service is available and configured
   */
  isAvailable(): boolean {
    return !!this.groq.apiKey;
  }

  /**
   * Get service status and performance metrics
   */
  getStatus(): {
    available: boolean;
    provider: string;
    model: string;
    estimatedSpeed: string;
  } {
    return {
      available: this.isAvailable(),
      provider: 'Groq',
      model: 'llama-3.3-70b-versatile',
      estimatedSpeed: '500+ tokens/second',
    };
  }

  /**
   * Extract job information from raw job descriptions using Groq
   * Replaces OpenAI's job extraction for cost efficiency
   */
  async extractJobInformation(jobDescription: string): Promise<any> {
    if (!this.groq.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const startTime = Date.now();

    try {
      const systemPrompt = `You are an expert job description parser. Extract key information from job descriptions and return ONLY a valid JSON object with the following structure:

{
  "title": "string",
  "company": "string",
  "department": "string",
  "location": "string",
  "jobType": "string", // Full-time, Part-time, Contract, etc.
  "experienceLevel": "string", // Entry, Mid, Senior, Executive
  "requiredExperience": number, // years
  "salary": {
    "min": number,
    "max": number,
    "currency": "string"
  },
  "skills": ["string"],
  "responsibilities": ["string"],
  "requirements": ["string"],
  "benefits": ["string"],
  "educationLevel": "string",
  "industry": "string",
  "remote": boolean,
  "summary": "string" // 2-3 sentence summary
}

Rules:
- Return ONLY valid JSON, no markdown code blocks, no backticks, no explanations
- DO NOT wrap the JSON in markdown code blocks or any other formatting
- Output must be parseable by JSON.parse() directly
- Use null for missing values
- Extract salary as numbers only (no commas or symbols)
- Infer experience level from context if not explicit
- Keep arrays concise but comprehensive`;

      const userPrompt = `Extract job information from this description:\n\n${jobDescription}`;

      const response = await this.groq.chat.completions.create({
        model: 'llama-3.3-70b-versatile',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: 1500,
        temperature: 0,
        top_p: 1,
        stream: false,
      });

      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(`⚡ Groq job extraction completed in ${processingTime.toFixed(2)}s`);

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from Groq');
      }

      // Clean the response - remove markdown code blocks if present
      let cleanContent = content.trim();
      if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```(?:json)?\s*\n?/, '');
        cleanContent = cleanContent.replace(/\n?```\s*$/, '');
      }

      const parsedData = JSON.parse(cleanContent);
      return parsedData;
    } catch (error: any) {
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.error(`Groq job extraction failed after ${processingTime.toFixed(2)}s:`, error);
      throw error;
    }
  }

  /**
   * Generate job-related content using Groq
   * Handles skills, responsibilities, summaries, and social media content
   * Uses shared prompts to ensure consistency with OpenAI service
   */
  async generateContent(
    type: 'skills' | 'responsibilities' | 'summary' | 'companySummary' | 'socialMedia',
    context: any,
  ): Promise<any> {
    if (!this.groq.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const startTime = Date.now();

    try {
      let systemPrompt = '';
      let userPrompt = '';
      let maxTokens = 500;
      let temperature = 0.7; // Default to match OpenAI

      switch (type) {
        case 'skills':
        case 'responsibilities':
          // For skills and responsibilities, we generate both in one call
          systemPrompt = SKILLS_RESPONSIBILITIES_SYSTEM_PROMPT;
          userPrompt = getSkillsResponsibilitiesUserPrompt(
            context.jobType || context.title,
            context.industry || 'technology',
            context.experienceLevel || context.experience,
          );
          maxTokens = 2000;
          break;

        case 'summary':
          systemPrompt = JOB_SUMMARY_SYSTEM_PROMPT;
          userPrompt = context; // For summary, context is the data string
          maxTokens = 150;
          break;

        case 'companySummary':
          systemPrompt = COMPANY_SUMMARY_SYSTEM_PROMPT;
          userPrompt = getCompanySummaryUserPrompt(context);
          maxTokens = 150;
          break;

        case 'socialMedia':
          systemPrompt = SOCIAL_MEDIA_SYSTEM_PROMPT;
          // Build job context for social media
          const jobContext = `
            Job Title: ${context.jobType || 'N/A'}
            Company: ${context.companyName || 'N/A'}
            Location: ${context.location?.join(', ') || 'N/A'}
            Experience Level: ${context.experienceLevel || 'N/A'}
            Salary Range: ${context.salaryRange || 'N/A'}
            Department: ${context.department || 'N/A'}
            Company Description: ${context.companyDescription || 'N/A'}
            Job Description: ${context.finalDraft || 'N/A'}
            Key Skills: ${context.skills?.join(', ') || 'N/A'}
            Benefits: ${context.benefits?.join(', ') || 'N/A'}
          `.trim();
          userPrompt = getSocialMediaUserPrompt(jobContext);
          maxTokens = 100;
          temperature = 0.8; // Match OpenAI's temperature for social media
          break;
      }

      const response = await this.groq.chat.completions.create({
        model: 'llama-3.3-70b-versatile',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: maxTokens,
        temperature: temperature,
        top_p: 1,
        stream: false,
      });

      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(
        `⚡ Groq content generation (${type}) completed in ${processingTime.toFixed(2)}s`,
      );

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from Groq');
      }

      // Handle skills and responsibilities parsing
      if (type === 'skills' || type === 'responsibilities') {
        // Parse the structured response
        const [skillsRaw, responsibilitiesRaw] = content.split('\n\nResponsibilities:');

        const extractItems = (raw: string) => {
          return raw
            .split('\n')
            .filter((line) => line.trim().startsWith('•'))
            .map((line) => line.replace('•', '').trim())
            .slice(0, 10);
        };

        const skills = extractItems(skillsRaw.replace('Skills:', '').trim());
        const responsibilities = extractItems(responsibilitiesRaw.trim());

        // Return based on what was requested
        if (type === 'skills') {
          return { skills, responsibilities: [] };
        } else {
          return responsibilities;
        }
      }

      // For social media, ensure it's within character limit
      if (type === 'socialMedia') {
        const trimmedContent = content.trim();
        if (trimmedContent.length > 280) {
          return trimmedContent.substring(0, 277) + '...';
        }
        return trimmedContent;
      }

      // Return text directly for summaries
      return content.trim();
    } catch (error: any) {
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.error(
        `Groq content generation (${type}) failed after ${processingTime.toFixed(2)}s:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Generate summary using shared prompts - matches OpenAI format
   */
  async generateSummary(data: string): Promise<string | null> {
    try {
      return await this.generateContent('summary', data);
    } catch (error) {
      this.logger.error('Error generating summary:', error);
      return null;
    }
  }

  /**
   * Generate company summary using shared prompts - matches OpenAI format
   */
  async generateCompanySummary(data: string): Promise<string | null> {
    try {
      return await this.generateContent('companySummary', data);
    } catch (error) {
      this.logger.error('Error generating company summary:', error);
      return null;
    }
  }

  /**
   * Generate social media description using shared prompts - matches OpenAI format
   */
  async generateSocialMediaDescription(job: any): Promise<string | null> {
    try {
      return await this.generateContent('socialMedia', job);
    } catch (error) {
      this.logger.error('Error generating social media description:', error);
      return null;
    }
  }

  /**
   * Generate skills and responsibilities using shared prompts - matches OpenAI format
   */
  async generateSkillsAndResponsibilities(
    industry: string,
    jobType: string,
    experience?: string,
  ): Promise<GenerateSkillsAndResponsibilitiesResponse> {
    try {
      const systemPrompt = SKILLS_RESPONSIBILITIES_SYSTEM_PROMPT;
      const userPrompt = getSkillsResponsibilitiesUserPrompt(jobType, industry, experience);

      const response = await this.groq.chat.completions.create({
        model: 'llama-3.3-70b-versatile',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: 2000,
        temperature: 0.7,
        top_p: 1,
        stream: false,
      });

      const content = response.choices[0]?.message?.content?.trim();
      if (!content) {
        throw new Error('No content returned from Groq');
      }

      // Parse the response in the same format as OpenAI
      const [skillsRaw, responsibilitiesRaw] = content.split('\n\nResponsibilities:');

      const extractItems = (raw: string) => {
        return raw
          .split('\n')
          .filter((line) => line.trim().startsWith('•'))
          .map((line) => line.replace('•', '').trim())
          .slice(0, 10);
      };

      const skills = extractItems(skillsRaw.replace('Skills:', '').trim());
      const responsibilities = extractItems(responsibilitiesRaw.trim());

      return {
        skills,
        responsibilities,
      };
    } catch (error) {
      this.logger.error('Error generating skills and responsibilities:', error);
      throw new Error('Failed to generate skills and responsibilities');
    }
  }

  /**
   * Generate video script for job descriptions using Groq
   * Matches OpenAI format exactly using shared prompts
   */
  async generateVideoScript(
    job: any,
    tone: string,
    language: string = 'en',
    scriptLength: string = 'medium',
  ): Promise<string> {
    if (!this.groq.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const startTime = Date.now();

    try {
      // Get the appropriate length spec
      const lengthSpec =
        VIDEO_SCRIPT_LENGTH_SPECS[scriptLength as keyof typeof VIDEO_SCRIPT_LENGTH_SPECS] ||
        VIDEO_SCRIPT_LENGTH_SPECS.medium;

      // Get system prompt using shared function
      const systemPrompt = getVideoScriptSystemPrompt({
        tone,
        language,
        scriptLength: scriptLength as 'short' | 'medium' | 'long',
      });

      // Get user prompt using shared function
      const userPrompt = getVideoScriptUserPrompt(job);

      const response = await this.groq.chat.completions.create({
        model: 'llama-3.3-70b-versatile',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: lengthSpec.maxTokens,
        temperature: 0.7, // Match OpenAI's temperature
        top_p: 1,
        stream: false,
      });

      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.log(`⚡ Groq video script generation completed in ${processingTime.toFixed(2)}s`);

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from Groq');
      }

      // Clean the script using shared function to match OpenAI format
      return cleanVideoScript(content);
    } catch (error: any) {
      const processingTime = (Date.now() - startTime) / 1000;
      this.logger.error(
        `Groq video script generation failed after ${processingTime.toFixed(2)}s:`,
        error,
      );
      throw error;
    }
  }
}
