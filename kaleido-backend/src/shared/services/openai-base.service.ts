import Bottleneck from 'bottleneck';
import OpenAI from 'openai';

import { Injectable } from '@nestjs/common';

// Rate limiting constants for OpenAI API
const MAX_CONCURRENT = 1666;
const MIN_TIME = 6;
const RESERVOIR = 20000;

// OpenAI model definitions with pricing information
export enum OpenAIModel {
  // Latest o-series models (April 2025)
  O3 = 'o3', // Most powerful reasoning model for complex tasks
  O3_MINI = 'o3-mini', // Cost-efficient reasoning model for STEM tasks
  O4_MINI = 'o4-mini', // Smaller, faster reasoning model optimized for math and coding

  // GPT-4.5 models
  GPT_4_5 = 'gpt-4.5', // Latest frontier model with improved reasoning

  // GPT-4o models
  GPT_4O = 'gpt-4o', // 128K context, optimized for speed and quality
  GPT_4O_MINI = 'gpt-4o-mini', // 128K context, fastest and most cost-efficient
  GPT_4O_2024_11_20 = 'gpt-4o-2024-11-20', // Latest stable GPT-4o snapshot

  // Large context window models
  GPT_4_TURBO = 'gpt-4-turbo', // 128K context, quality/performance balance
  GPT_4_TURBO_PREVIEW = 'gpt-4-turbo-preview', // Latest preview with 128K context

  // Cost-efficient models
  GPT_3_5_TURBO = 'gpt-3.5-turbo', // 16K context, very cost-efficient

  // Legacy models (for backward compatibility)
  GPT_4_1106_PREVIEW = 'gpt-4-1106-preview', // Legacy 128K context model

  // Deprecated models (kept for backward compatibility)
  GPT_4_1 = 'gpt-4.1', // Deprecated, use GPT_4O instead
  GPT_4_1_MINI = 'gpt-4.1-mini', // Deprecated, use GPT_4O_MINI instead
  GPT_4_1_PREVIEW = 'gpt-4.1-preview', // Deprecated, use GPT_4O instead
}

// Model pricing information (per 1M tokens)
export const MODEL_PRICING = {
  // Latest o-series models
  [OpenAIModel.O3]: { input: 15, output: 75 }, // $15 per 1M input tokens, $75 per 1M output tokens
  [OpenAIModel.O3_MINI]: { input: 0.2, output: 0.8 }, // $0.20 per 1M input tokens, $0.80 per 1M output tokens
  [OpenAIModel.O4_MINI]: { input: 0.2, output: 0.8 }, // $0.20 per 1M input tokens, $0.80 per 1M output tokens

  // GPT-4.5 models
  [OpenAIModel.GPT_4_5]: { input: 10, output: 30 }, // $10 per 1M input tokens, $30 per 1M output tokens

  // GPT-4o models
  [OpenAIModel.GPT_4O]: { input: 5, output: 15 }, // $5 per 1M input tokens, $15 per 1M output tokens
  [OpenAIModel.GPT_4O_MINI]: { input: 0.15, output: 0.6 }, // $0.15 per 1M input tokens, $0.60 per 1M output tokens
  [OpenAIModel.GPT_4O_2024_11_20]: { input: 5, output: 15 }, // Same as GPT_4O

  // Large context window models
  [OpenAIModel.GPT_4_TURBO]: { input: 10, output: 30 }, // $10 per 1M input tokens, $30 per 1M output tokens
  [OpenAIModel.GPT_4_TURBO_PREVIEW]: { input: 10, output: 30 }, // Same as GPT_4_TURBO

  // Cost-efficient models
  [OpenAIModel.GPT_3_5_TURBO]: { input: 0.5, output: 1.5 }, // $0.50 per 1M input tokens, $1.50 per 1M output tokens

  // Legacy models
  [OpenAIModel.GPT_4_1106_PREVIEW]: { input: 10, output: 30 }, // Legacy pricing

  // Deprecated models (kept for backward compatibility)
  [OpenAIModel.GPT_4_1]: { input: 10, output: 30 }, // Deprecated
  [OpenAIModel.GPT_4_1_MINI]: { input: 0.15, output: 0.6 }, // Deprecated
  [OpenAIModel.GPT_4_1_PREVIEW]: { input: 10, output: 30 }, // Deprecated
};

// Batch processing configuration
export interface BatchProcessingConfig {
  batchSize: number; // Number of items to process in a single batch
  concurrentBatches: number; // Number of batches to process concurrently
  model: OpenAIModel; // Model to use for processing
  temperature?: number; // Temperature for generation (default: 0.2)
  maxTokens?: number; // Maximum tokens to generate (default: model-specific)
}

@Injectable()
export class OpenAIService {
  public readonly openai: OpenAI;
  public readonly limiter: Bottleneck;

  // Default batch processing configuration - optimized for high throughput
  private defaultBatchConfig: BatchProcessingConfig = {
    batchSize: 100, // Increased from 50 to 100 for maximum throughput
    concurrentBatches: 20, // Increased from 10 to 20 for higher concurrency
    model: OpenAIModel.GPT_4O_MINI, // Using fastest model
    temperature: 0, // Set to 0 for consistent extraction results
    maxTokens: 2000,
  };

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.limiter = new Bottleneck({
      maxConcurrent: MAX_CONCURRENT,
      minTime: MIN_TIME,
      reservoir: RESERVOIR,
      reservoirRefreshInterval: 60000,
      reservoirRefreshAmount: RESERVOIR,
    });
  }

  /**
   * Process items in batches using the OpenAI API
   * @param items Array of items to process
   * @param processFn Function to process each item
   * @param config Batch processing configuration
   * @returns Processed items
   */
  protected async processBatch<T, R>(
    items: T[],
    processFn: (item: T, model: OpenAIModel) => Promise<R>,
    config?: Partial<BatchProcessingConfig>,
  ): Promise<R[]> {
    const batchConfig = { ...this.defaultBatchConfig, ...config };
    const { batchSize, concurrentBatches, model } = batchConfig;

    // Split items into batches
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    // Process batches concurrently with rate limiting
    const results: R[] = [];
    for (let i = 0; i < batches.length; i += concurrentBatches) {
      const batchPromises = batches.slice(i, i + concurrentBatches).map(async (batch) => {
        // Process each item in the batch
        const batchResults = await Promise.all(
          batch.map((item) => this.limiter.schedule(() => processFn(item, model))),
        );
        return batchResults;
      });

      // Wait for the current set of batches to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.flat());
    }

    return results;
  }

  /**
   * Select the most appropriate model based on the task and context size
   * @param contextSize Estimated context size in tokens
   * @param priority Priority of the task (quality vs cost)
   * @returns The most appropriate OpenAI model
   */
  protected selectModel(
    contextSize: number = 0,
    priority: 'quality' | 'balanced' | 'cost' | 'speed' = 'balanced',
  ): OpenAIModel {
    // For very small contexts, use the most cost-efficient model
    if (contextSize < 4000) {
      switch (priority) {
        case 'quality':
          return OpenAIModel.GPT_4O;
        case 'balanced':
          return OpenAIModel.GPT_4O_MINI;
        case 'cost':
          return OpenAIModel.GPT_3_5_TURBO;
        case 'speed':
          return OpenAIModel.GPT_4O_MINI; // Fastest model for small contexts
      }
    }

    // For medium contexts (4K-16K)
    if (contextSize < 16000) {
      switch (priority) {
        case 'quality':
          return OpenAIModel.O3;
        case 'balanced':
          return OpenAIModel.GPT_4O;
        case 'cost':
          return OpenAIModel.GPT_4O_MINI;
        case 'speed':
          return OpenAIModel.GPT_4O_MINI; // Fastest model for medium contexts
      }
    }

    // For large contexts (16K-128K)
    switch (priority) {
      case 'quality':
        return OpenAIModel.O3;
      case 'balanced':
        return OpenAIModel.GPT_4O;
      case 'cost':
        return OpenAIModel.GPT_4O_MINI;
      case 'speed':
        return OpenAIModel.GPT_4O_MINI; // Fastest model for large contexts
    }
  }

  /**
   * Select the most appropriate model for data extraction tasks
   * @param contextSize Estimated context size in tokens
   * @param needsSpeed Whether speed is a critical factor
   * @returns The most appropriate OpenAI model for data extraction
   */
  protected selectDataExtractionModel(
    contextSize: number = 0,
    needsSpeed: boolean = false,
  ): OpenAIModel {
    // For data extraction tasks that need speed (like resume parsing)
    if (needsSpeed) {
      return OpenAIModel.GPT_4O_MINI; // Fastest model with good extraction capabilities
    }

    // For data extraction tasks that need accuracy over speed
    if (contextSize < 8000) {
      return OpenAIModel.GPT_4O_MINI; // Good balance for smaller documents
    } else {
      return OpenAIModel.GPT_4O; // Better for larger, more complex documents
    }
  }

  /**
   * Estimate token count for a string
   * Very rough estimation: ~4 chars per token for English text
   */
  protected estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }

  protected normalizeScore(score: number): number {
    if (typeof score !== 'number') return 0;
    return Math.max(0, Math.min(100, score));
  }
}
