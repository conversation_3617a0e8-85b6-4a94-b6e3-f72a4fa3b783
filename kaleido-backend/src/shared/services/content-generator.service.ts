import OpenAI from 'openai';

import { Job } from '@/modules/entities';
import { Injectable } from '@nestjs/common';

import { GenerateSkillsAndResponsibilitiesResponse } from '../types/openai.types';
import { OpenAIModel, OpenAIService } from './openai-base.service';

@Injectable()
export class ContentGeneratorService extends OpenAIService {
  async generateSkillsAndResponsibilities(
    industry: string,
    jobType: string,
    experience?: string,
  ): Promise<GenerateSkillsAndResponsibilitiesResponse> {
    try {
      // Use GPT-4O-MINI for faster skills & responsibilities generation (optimized for speed)
      const model = OpenAIModel.GPT_4O_MINI;

      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content:
            'You are a helpful assistant that generates relevant skills and job responsibilities for specific industries and job types.',
        },
        {
          role: 'user',
          content: `Generate a list of exactly 10 relevant skills and 10 job responsibilities for a ${jobType} position in the ${industry} industry${experience ? ` at the ${experience} level` : ''}.

    ${experience ? `Consider the experience level (${experience}) when determining the complexity and seniority of skills and responsibilities. For entry-level positions, focus on foundational skills and learning-oriented responsibilities. For senior positions, emphasize leadership, strategic thinking, and advanced technical skills.` : ''}

    Format the response as follows:
    Skills:
    • Skill 1
    • Skill 2
    ...
    • Skill 10

    Responsibilities:
    • Responsibility 1
    • Responsibility 2
    ...
    • Responsibility 10

    Ensure each skill and responsibility is on a new line, preceded by a bullet point (•). Do not include any numbering or additional text.`,
        },
      ];

      const response = await this.openai.chat.completions.create({
        model,
        messages,
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content?.trim();
      if (!content) {
        throw new Error('No content returned from OpenAI');
      }

      const [skillsRaw, responsibilitiesRaw] = content.split('\n\nResponsibilities:');

      const extractItems = (raw: string) => {
        return raw
          .split('\n')
          .filter((line) => line.trim().startsWith('•'))
          .map((line) => line.replace('•', '').trim())
          .slice(0, 10);
      };

      const skills = extractItems(skillsRaw.replace('Skills:', '').trim());
      const responsibilities = extractItems(responsibilitiesRaw.trim());

      return {
        skills,
        responsibilities,
      };
    } catch (error) {
      console.error('Error generating skills and responsibilities:', error);
      throw new Error('Failed to generate skills and responsibilities');
    }
  }

  async generateSummary(data: string): Promise<string | null> {
    try {
      // Use GPT-4O-MINI for faster job summary generation (optimized for speed)
      const model = OpenAIModel.GPT_4O_MINI;

      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content:
              'You are a professional job description summarizer. Create a concise, engaging summary that captures the key aspects of the role in 2-3 sentences.',
          },
          {
            role: 'user',
            content: data,
          },
        ],
        temperature: 0.7,
        max_tokens: 150,
      });

      return response.choices[0].message.content || null;
    } catch (error) {
      console.error('Error generating summary:', error);
      return null;
    }
  }

  async generateSocialMediaDescription(job: any): Promise<string | null> {
    try {
      // Use GPT-4O-MINI for faster social media description generation (optimized for speed)
      const model = OpenAIModel.GPT_4O_MINI;

      // Build context from job data
      const jobContext = `
        Job Title: ${job.jobType || 'N/A'}
        Company: ${job.companyName || 'N/A'}
        Location: ${job.location?.join(', ') || 'N/A'}
        Experience Level: ${job.experienceLevel || 'N/A'}
        Salary Range: ${job.salaryRange || 'N/A'}
        Department: ${job.department || 'N/A'}
        Company Description: ${job.companyDescription || 'N/A'}
        Job Description: ${job.finalDraft || 'N/A'}
        Key Skills: ${job.skills?.join(', ') || 'N/A'}
        Benefits: ${job.benefits?.join(', ') || 'N/A'}
      `.trim();

      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a social media marketing expert specializing in job postings. Create engaging, concise social media descriptions that attract top talent.

REQUIREMENTS:
- Maximum 280 characters (Twitter/X limit)
- Include relevant emojis (2-3 max)
- Use action words and compelling language
- Highlight key benefits or unique selling points
- Include a call-to-action
- Make it shareable and engaging
- Avoid corporate jargon
- Focus on what makes this opportunity exciting

STYLE:
- Professional but approachable
- Energetic and positive
- Clear and direct
- Use hashtags sparingly (1-2 relevant ones)`,
          },
          {
            role: 'user',
            content: `Create a social media description for this job posting:\n\n${jobContext}`,
          },
        ],
        temperature: 0.8,
        max_tokens: 100,
      });

      const description = response.choices[0].message.content?.trim() || null;

      // Ensure it's within character limit
      if (description && description.length > 280) {
        return description.substring(0, 277) + '...';
      }

      return description;
    } catch (error) {
      console.error('Error generating social media description:', error);
      return null;
    }
  }

  async generateCompanySummary(data: string): Promise<string | null> {
    try {
      // Use GPT-4O-MINI for faster company summary generation (optimized for speed)
      const model = OpenAIModel.GPT_4O_MINI;

      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content:
              'You are a helpful assistant that summarizes company information. Provide concise, factual summaries without personal opinions or exaggeration.',
          },
          {
            role: 'user',
            content: `Summarize the following company information in a concise paragraph, focusing on key facts about the company's business, products, or services:\n\n${data}`,
          },
        ],
        temperature: 0.7,
      });

      if (
        response.choices &&
        response.choices[0] &&
        response.choices[0].message &&
        response.choices[0].message.content
      ) {
        return response.choices[0].message.content.trim() || null;
      }
      return null;
    } catch (error) {
      console.error('Error generating company summary:', error);
      return null;
    }
  }

  async generateVideoScript(
    job: Job,
    tone: string,
    language: string = 'en',
    scriptLength: string = 'medium',
  ): Promise<string> {
    try {
      // For video scripts, we can use a more cost-efficient model
      const model = OpenAIModel.GPT_4O_MINI;

      // Normalize language code by extracting the language part before the hyphen
      // e.g., 'de-DE' becomes 'de', 'en-US' becomes 'en'
      const normalizedLanguage = language.split('-')[0];

      const languageInstructions = {
        en: 'Write the script in English.',
        ar: 'Write the script in Saudi Arabic. Use formal Modern Standard Arabic with Gulf dialect influences where appropriate.',
        fr: 'Write the script in French. Use formal French suitable for professional communication.',
        de: 'Write the script in German. Use formal German suitable for professional communication.',
        es: 'Write the script in Spanish. Use formal Spanish suitable for professional communication.',
      };

      // Define script length specifications
      const lengthSpecs = {
        short: {
          duration: '10-15 seconds',
          wordCount: '160 characters (approximately 25-30 words)',
          structure: 'Company name, role title, key highlight, and quick call to action',
          maxTokens: 100,
        },
        medium: {
          duration: '30-45 seconds',
          wordCount: '60-90 words',
          structure:
            'Brief company intro, role overview, 1-2 key responsibilities, and call to action',
          maxTokens: 400,
        },
        long: {
          duration: '45-60 seconds',
          wordCount: '90-120 words',
          structure:
            'Company introduction, role overview, key responsibilities, requirements, benefits, and call to action',
          maxTokens: 600,
        },
      };

      const currentLengthSpec =
        lengthSpecs[scriptLength as keyof typeof lengthSpecs] || lengthSpecs.medium;

      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content: `You are an AI video script writer specializing in job descriptions.
                ${languageInstructions[normalizedLanguage as keyof typeof languageInstructions] || languageInstructions['en']}
                Create a natural, engaging script for a video job description.

                Write in a ${tone.toLowerCase()} tone that would appeal to potential candidates.

                SCRIPT LENGTH REQUIREMENTS:
                - Target duration: ${currentLengthSpec.duration}
                - Target word count: ${currentLengthSpec.wordCount}
                - Structure: ${currentLengthSpec.structure}

                Guidelines:
                - Write naturally as if having a conversation
                - Use concise, clear sentences
                - Avoid complex jargon
                - Create natural paragraph breaks for pausing
                - Do not include any stage directions or expressions
                - Focus on the most compelling aspects of the role
                - End with a clear call to action
                - STRICTLY adhere to the word count limit: ${currentLengthSpec.wordCount}

                Important: Write the script as plain text without any [expressions], [emotions], or stage directions. The script must be exactly within the specified word count range.`,
          },
          {
            role: 'user',
            content: JSON.stringify({
              companyName: job.companyName,
              jobType: job.jobType,
              companyDescription: job.companyDescription,
              keyResponsibilities:
                (job.jobResponsibilities && job.jobResponsibilities.slice(0, 4)) || [],
              keySkills: (job.skills && job.skills.slice(0, 4)) || [],
              benefits: job.benefits,
              location: job.location,
              experience: job.experience,
              salaryRange: job.salaryRange,
              cultureFit: job.cultureFitDescription,
              careerGrowth: job.careerGrowth,
            }),
          },
        ],
        temperature: 0.7,
        max_tokens: currentLengthSpec.maxTokens,
      });

      // Clean up any accidentally included expressions
      let script = response.choices[0]?.message?.content || '';

      // Remove any bracketed expressions that might slip through
      script = script
        .replace(/\[.*?\]/g, '')
        .replace(/\(.*?\)/g, '')
        .trim();

      return script;
    } catch (error) {
      console.error('Error generating video script:', error);
      throw new Error('Failed to generate video script');
    }
  }

  /**
   * Process multiple job descriptions in batches to generate summaries
   * @param jobs Array of job descriptions to process
   * @returns Array of job summaries
   */
  async batchGenerateSummaries(
    jobs: { id: string; description: string }[],
  ): Promise<{ id: string; summary: string | null }[]> {
    return this.processBatch(
      jobs,
      async (job, model) => {
        try {
          const response = await this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content:
                  'You are a professional job description summarizer. Create a concise, engaging summary that captures the key aspects of the role in 2-3 sentences.',
              },
              {
                role: 'user',
                content: job.description,
              },
            ],
            temperature: 0.7,
            max_tokens: 150,
          });

          return {
            id: job.id,
            summary: response.choices[0].message.content || null,
          };
        } catch (error) {
          console.error(`Error generating summary for job ${job.id}:`, error);
          return {
            id: job.id,
            summary: null,
          };
        }
      },
      {
        batchSize: 10,
        concurrentBatches: 2,
        model: OpenAIModel.GPT_4O_MINI,
      },
    );
  }
}
