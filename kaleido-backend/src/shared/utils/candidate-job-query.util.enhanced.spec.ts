import { SelectQueryBuilder } from 'typeorm';
import {
  getCandidateJobWhereClause,
  applyCandidateJobFilter,
  countCandidatesForJob,
  getCandidateJobWhereParams,
  andWhereCandidateJob,
} from './candidate-job-query.util';

describe('Candidate-Job Query Utilities Enhanced Tests', () => {
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<any>>;
  let mockRepository: any;

  beforeEach(() => {
    mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      setParameter: jest.fn().mockReturnThis(),
      getCount: jest.fn(),
      getMany: jest.fn(),
      getRawMany: jest.fn(),
    } as any;

    mockRepository = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    };
  });

  describe('getCandidateJobWhereClause', () => {
    it('should generate correct WHERE clause with default alias', () => {
      const clause = getCandidateJobWhereClause();
      expect(clause).toBe('(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))');
    });

    it('should generate correct WHERE clause with custom alias', () => {
      const clause = getCandidateJobWhereClause('c');
      expect(clause).toBe('(c.jobId = :jobId OR :jobId = ANY(c."appliedJobs"))');
    });

    it('should handle different alias formats', () => {
      expect(getCandidateJobWhereClause('candidate_alias')).toBe(
        '(candidate_alias.jobId = :jobId OR :jobId = ANY(candidate_alias."appliedJobs"))',
      );
      expect(getCandidateJobWhereClause('c1')).toBe(
        '(c1.jobId = :jobId OR :jobId = ANY(c1."appliedJobs"))',
      );
    });
  });

  describe('applyCandidateJobFilter', () => {
    it('should apply filter with default alias', () => {
      const jobId = 'job-123';
      const result = applyCandidateJobFilter(mockQueryBuilder, jobId);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId },
      );
      expect(result).toBe(mockQueryBuilder);
    });

    it('should apply filter with custom alias', () => {
      const jobId = 'job-456';
      const result = applyCandidateJobFilter(mockQueryBuilder, jobId, 'c');

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        '(c.jobId = :jobId OR :jobId = ANY(c."appliedJobs"))',
        { jobId },
      );
      expect(result).toBe(mockQueryBuilder);
    });

    it('should handle special job IDs', () => {
      // UUID format
      const uuidJobId = '550e8400-e29b-41d4-a716-************';
      applyCandidateJobFilter(mockQueryBuilder, uuidJobId);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(expect.any(String), { jobId: uuidJobId });

      // Job ID with special characters
      const specialJobId = 'job_2023-01-01';
      applyCandidateJobFilter(mockQueryBuilder, specialJobId);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(expect.any(String), {
        jobId: specialJobId,
      });
    });
  });

  describe('countCandidatesForJob', () => {
    it('should count candidates for a specific job', async () => {
      const jobId = 'job-789';
      mockQueryBuilder.getCount.mockResolvedValue(42);

      const count = await countCandidatesForJob(mockRepository, jobId);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('candidate');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId },
      );
      expect(mockQueryBuilder.getCount).toHaveBeenCalled();
      expect(count).toBe(42);
    });

    it('should handle zero count', async () => {
      mockQueryBuilder.getCount.mockResolvedValue(0);

      const count = await countCandidatesForJob(mockRepository, 'job-no-candidates');
      expect(count).toBe(0);
    });

    it('should handle database errors', async () => {
      mockQueryBuilder.getCount.mockRejectedValue(new Error('Database error'));

      await expect(countCandidatesForJob(mockRepository, 'job-error')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getCandidateJobWhereParams', () => {
    it('should return parameters object', () => {
      const jobId = 'job-params-test';
      const params = getCandidateJobWhereParams(jobId);

      expect(params).toEqual({ jobId });
    });

    it('should handle different job ID types', () => {
      expect(getCandidateJobWhereParams('123')).toEqual({ jobId: '123' });
      expect(getCandidateJobWhereParams('')).toEqual({ jobId: '' });
      expect(getCandidateJobWhereParams('job-with-special-chars!@#')).toEqual({
        jobId: 'job-with-special-chars!@#',
      });
    });
  });

  describe('andWhereCandidateJob', () => {
    it('should add AND WHERE clause with default alias', () => {
      const jobId = 'job-and-where';
      const result = andWhereCandidateJob(mockQueryBuilder, jobId);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId },
      );
      expect(result).toBe(mockQueryBuilder);
    });

    it('should add AND WHERE clause with custom alias', () => {
      const jobId = 'job-custom-alias';
      const result = andWhereCandidateJob(mockQueryBuilder, jobId, 'custom');

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(custom.jobId = :jobId OR :jobId = ANY(custom."appliedJobs"))',
        { jobId },
      );
      expect(result).toBe(mockQueryBuilder);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complex query building', () => {
      const jobId = 'job-complex';

      // Simulate a complex query with multiple conditions
      mockQueryBuilder.where.mockImplementation(function (this: any) {
        return this;
      });
      mockQueryBuilder.andWhere.mockImplementation(function (this: any) {
        return this;
      });

      // Initial condition
      mockQueryBuilder.where('candidate.status = :status', { status: 'active' });

      // Add job filter
      andWhereCandidateJob(mockQueryBuilder, jobId);

      // Add more conditions
      mockQueryBuilder.andWhere('candidate.tier IN (:...tiers)', { tiers: ['TOP', 'SECOND'] });

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('candidate.status = :status', {
        status: 'active',
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('candidate.tier IN (:...tiers)', {
        tiers: ['TOP', 'SECOND'],
      });
    });

    it('should work with joins and relations', () => {
      const jobId = 'job-with-joins';

      // Mock a more complex query builder with joins
      const mockJoinQueryBuilder = {
        ...mockQueryBuilder,
        leftJoin: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
      };

      mockJoinQueryBuilder
        .leftJoinAndSelect('candidate.evaluations', 'eval')
        .leftJoin('candidate.job', 'job')
        .where(getCandidateJobWhereClause(), { jobId })
        .andWhere('eval.matchScore >= :minScore', { minScore: 70 });

      expect(mockJoinQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'candidate.evaluations',
        'eval',
      );
      expect(mockJoinQueryBuilder.where).toHaveBeenCalledWith(getCandidateJobWhereClause(), {
        jobId,
      });
    });

    it('should handle subqueries correctly', () => {
      const mainJobId = 'main-job';
      const relatedJobIds = ['job-1', 'job-2', 'job-3'];

      // Create a mock subquery builder
      const mockSubQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getQuery: jest.fn().mockReturnValue('SELECT sub.id FROM ...'),
      };

      // Override createQueryBuilder to return different builders based on alias
      const originalCreateQueryBuilder = mockRepository.createQueryBuilder;
      mockRepository.createQueryBuilder = jest.fn((alias?: string) => {
        if (alias === 'sub') {
          return mockSubQueryBuilder;
        }
        return mockQueryBuilder;
      });

      // Simulate a scenario where we need candidates from multiple jobs
      const subquery = mockRepository
        .createQueryBuilder('sub')
        .select('sub.id')
        .where('sub.jobId IN (:...jobIds) OR sub."appliedJobs" && :jobArray', {
          jobIds: relatedJobIds,
          jobArray: relatedJobIds,
        });

      const mainQuery = mockRepository
        .createQueryBuilder('candidate')
        .where(`candidate.id IN (${subquery.getQuery()})`)
        .andWhere(getCandidateJobWhereClause(), { jobId: mainJobId });

      expect(mockSubQueryBuilder.select).toHaveBeenCalledWith('sub.id');
      expect(mockSubQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.andWhere).toHaveBeenCalled();

      // Restore original
      mockRepository.createQueryBuilder = originalCreateQueryBuilder;
    });
  });

  describe('Edge cases', () => {
    it('should handle empty job ID gracefully', () => {
      const result = applyCandidateJobFilter(mockQueryBuilder, '');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(expect.any(String), { jobId: '' });
    });

    it('should handle very long job IDs', () => {
      const longJobId = 'job-' + 'x'.repeat(1000);
      const result = applyCandidateJobFilter(mockQueryBuilder, longJobId);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(expect.any(String), { jobId: longJobId });
    });

    it('should handle job IDs with SQL-like syntax', () => {
      const sqlLikeJobId = "job'; DROP TABLE candidates; --";
      const result = applyCandidateJobFilter(mockQueryBuilder, sqlLikeJobId);

      // The parameterized query should safely handle this
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(expect.any(String), {
        jobId: sqlLikeJobId,
      });
    });
  });

  describe('Performance considerations', () => {
    it('should generate efficient SQL for PostgreSQL arrays', () => {
      // The generated SQL uses ANY() which is optimized for PostgreSQL arrays
      const clause = getCandidateJobWhereClause();
      expect(clause).toContain('ANY(');
      expect(clause).toContain('"appliedJobs"'); // Quoted for PostgreSQL
    });

    it('should use parameterized queries for security', () => {
      const jobId = 'secure-job-id';
      applyCandidateJobFilter(mockQueryBuilder, jobId);

      // Verify that jobId is passed as a parameter, not concatenated
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        expect.stringContaining(':jobId'),
        expect.objectContaining({ jobId }),
      );
    });
  });
});
