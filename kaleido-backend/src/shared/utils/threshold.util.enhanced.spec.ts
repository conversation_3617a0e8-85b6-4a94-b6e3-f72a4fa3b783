import {
  normalizeThreshold,
  normalizeJobThresholds,
  validateThresholdOrder,
  determineCandidateTier,
  thresholdToDecimal,
  formatThreshold,
  DEFAULT_THRESHOLDS,
} from './threshold.util';

describe('Threshold Utilities Enhanced Tests', () => {
  describe('normalizeThreshold', () => {
    it('should handle percentage values correctly', () => {
      expect(normalizeThreshold(80, 70)).toBe(80);
      expect(normalizeThreshold(25, 50)).toBe(25);
      expect(normalizeThreshold(100, 50)).toBe(100);
    });

    it('should convert decimal values to percentages', () => {
      expect(normalizeThreshold(0.8, 70)).toBe(80);
      expect(normalizeThreshold(0.25, 50)).toBe(25);
      expect(normalizeThreshold(0.01, 50)).toBe(1);
      expect(normalizeThreshold(1, 50)).toBe(100); // Edge case: 1 as decimal
    });

    it('should handle string values', () => {
      expect(normalizeThreshold('80', 70)).toBe(80);
      expect(normalizeThreshold('0.75', 50)).toBe(75);
      expect(normalizeThreshold('25.00', 60)).toBe(25);
      expect(normalizeThreshold('0.333', 50)).toBe(33); // Rounding
    });

    it('should use fallback for invalid values', () => {
      expect(normalizeThreshold(null, 70)).toBe(70);
      expect(normalizeThreshold(undefined, 60)).toBe(60);
      expect(normalizeThreshold('invalid', 50)).toBe(50);
      expect(normalizeThreshold(NaN, 80)).toBe(80);
      expect(normalizeThreshold('', 75)).toBe(0); // Empty string converts to 0
    });

    it('should handle edge cases', () => {
      expect(normalizeThreshold(0, 50)).toBe(0); // Valid zero
      expect(normalizeThreshold('0', 50)).toBe(0);
      expect(normalizeThreshold(1.01, 50)).toBe(1); // Just over 1, treat as percentage
      expect(normalizeThreshold(0.999, 50)).toBe(100); // Just under 1, treat as decimal
    });

    it('should round decimal conversions properly', () => {
      expect(normalizeThreshold(0.754, 50)).toBe(75); // Rounds down
      expect(normalizeThreshold(0.755, 50)).toBe(76); // Rounds up
      expect(normalizeThreshold(0.3333, 50)).toBe(33);
      expect(normalizeThreshold(0.6666, 50)).toBe(67);
    });
  });

  describe('normalizeJobThresholds', () => {
    it('should normalize both thresholds with defaults', () => {
      const result = normalizeJobThresholds({});
      expect(result).toEqual({
        topThreshold: DEFAULT_THRESHOLDS.TOP_TIER,
        secondThreshold: DEFAULT_THRESHOLDS.SECOND_TIER,
      });
    });

    it('should handle mixed formats', () => {
      const result = normalizeJobThresholds({
        topCandidateThreshold: '0.85',
        secondTierCandidateThreshold: 65,
      });
      expect(result).toEqual({
        topThreshold: 85,
        secondThreshold: 65,
      });
    });

    it('should handle null values', () => {
      const result = normalizeJobThresholds({
        topCandidateThreshold: null,
        secondTierCandidateThreshold: null,
      });
      expect(result).toEqual({
        topThreshold: DEFAULT_THRESHOLDS.TOP_TIER,
        secondThreshold: DEFAULT_THRESHOLDS.SECOND_TIER,
      });
    });

    it('should normalize job entity formats', () => {
      // Simulating data from database that might be stored as strings
      const result = normalizeJobThresholds({
        topCandidateThreshold: '25.00', // Stored as percentage string
        secondTierCandidateThreshold: '0.60', // Stored as decimal string
      });
      expect(result).toEqual({
        topThreshold: 25,
        secondThreshold: 60,
      });
    });
  });

  describe('validateThresholdOrder', () => {
    it('should validate correct threshold order', () => {
      expect(validateThresholdOrder(80, 60)).toBe(true);
      expect(validateThresholdOrder(90, 70)).toBe(true);
      expect(validateThresholdOrder(100, 100)).toBe(true); // Equal is valid
    });

    it('should invalidate incorrect threshold order', () => {
      expect(validateThresholdOrder(60, 80)).toBe(false);
      expect(validateThresholdOrder(50, 70)).toBe(false);
      expect(validateThresholdOrder(0, 10)).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(validateThresholdOrder(0, 0)).toBe(true);
      expect(validateThresholdOrder(100, 0)).toBe(true);
      expect(validateThresholdOrder(-10, -20)).toBe(true); // Technically valid order
    });
  });

  describe('determineCandidateTier', () => {
    const topThreshold = 80;
    const secondThreshold = 60;

    describe('with evaluation', () => {
      it('should categorize TOP tier correctly', () => {
        expect(determineCandidateTier(80, topThreshold, secondThreshold)).toBe('TOP');
        expect(determineCandidateTier(85, topThreshold, secondThreshold)).toBe('TOP');
        expect(determineCandidateTier(100, topThreshold, secondThreshold)).toBe('TOP');
      });

      it('should categorize SECOND tier correctly', () => {
        expect(determineCandidateTier(60, topThreshold, secondThreshold)).toBe('SECOND');
        expect(determineCandidateTier(70, topThreshold, secondThreshold)).toBe('SECOND');
        expect(determineCandidateTier(79, topThreshold, secondThreshold)).toBe('SECOND');
      });

      it('should categorize OTHER tier correctly', () => {
        expect(determineCandidateTier(59, topThreshold, secondThreshold)).toBe('OTHER');
        expect(determineCandidateTier(30, topThreshold, secondThreshold)).toBe('OTHER');
        expect(determineCandidateTier(0, topThreshold, secondThreshold)).toBe('OTHER');
      });
    });

    describe('without evaluation', () => {
      it('should return UNRANKED when hasEvaluation is false', () => {
        expect(determineCandidateTier(0, topThreshold, secondThreshold, false)).toBe('UNRANKED');
        expect(determineCandidateTier(50, topThreshold, secondThreshold, false)).toBe('UNRANKED');
        expect(determineCandidateTier(100, topThreshold, secondThreshold, false)).toBe('UNRANKED');
      });
    });

    describe('edge cases', () => {
      it('should handle boundary values precisely', () => {
        expect(determineCandidateTier(79.99, topThreshold, secondThreshold)).toBe('SECOND');
        expect(determineCandidateTier(80.0, topThreshold, secondThreshold)).toBe('TOP');
        expect(determineCandidateTier(59.99, topThreshold, secondThreshold)).toBe('OTHER');
        expect(determineCandidateTier(60.0, topThreshold, secondThreshold)).toBe('SECOND');
      });

      it('should handle unusual threshold configurations', () => {
        // When thresholds are reversed (invalid but should handle gracefully)
        expect(determineCandidateTier(70, 60, 80)).toBe('TOP'); // >= 60

        // When thresholds are equal
        expect(determineCandidateTier(70, 70, 70)).toBe('TOP');
        expect(determineCandidateTier(69, 70, 70)).toBe('OTHER');

        // When thresholds are 0
        expect(determineCandidateTier(0, 0, 0)).toBe('TOP'); // 0 >= 0
      });
    });
  });

  describe('thresholdToDecimal', () => {
    it('should convert percentages to decimals', () => {
      expect(thresholdToDecimal(80)).toBe(0.8);
      expect(thresholdToDecimal(25)).toBe(0.25);
      expect(thresholdToDecimal(100)).toBe(1);
      expect(thresholdToDecimal(0)).toBe(0);
    });

    it('should handle precision', () => {
      expect(thresholdToDecimal(33)).toBe(0.33);
      expect(thresholdToDecimal(66)).toBe(0.66);
      expect(thresholdToDecimal(75)).toBe(0.75);
    });

    it('should handle values outside normal range', () => {
      expect(thresholdToDecimal(150)).toBe(1.5);
      expect(thresholdToDecimal(-50)).toBe(-0.5);
    });
  });

  describe('formatThreshold', () => {
    describe('percentage format', () => {
      it('should format as percentage by default', () => {
        expect(formatThreshold(80)).toBe('80%');
        expect(formatThreshold(25)).toBe('25%');
        expect(formatThreshold(100)).toBe('100%');
        expect(formatThreshold(0)).toBe('0%');
      });

      it('should handle decimal percentages', () => {
        expect(formatThreshold(75.5)).toBe('75.5%');
        expect(formatThreshold(33.33)).toBe('33.33%');
      });
    });

    describe('decimal format', () => {
      it('should format as decimal when specified', () => {
        expect(formatThreshold(80, 'decimal')).toBe('0.80');
        expect(formatThreshold(25, 'decimal')).toBe('0.25');
        expect(formatThreshold(100, 'decimal')).toBe('1.00');
        expect(formatThreshold(0, 'decimal')).toBe('0.00');
      });

      it('should always show two decimal places', () => {
        expect(formatThreshold(33, 'decimal')).toBe('0.33');
        expect(formatThreshold(50, 'decimal')).toBe('0.50');
        expect(formatThreshold(66.66, 'decimal')).toBe('0.67');
      });
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complete threshold processing pipeline', () => {
      // Simulate data from database
      const jobData = {
        topCandidateThreshold: '0.75', // Stored as decimal string
        secondTierCandidateThreshold: 60, // Stored as percentage number
      };

      // Normalize thresholds
      const normalized = normalizeJobThresholds(jobData);
      expect(normalized).toEqual({
        topThreshold: 75,
        secondThreshold: 60,
      });

      // Validate order
      expect(validateThresholdOrder(normalized.topThreshold, normalized.secondThreshold)).toBe(
        true,
      );

      // Determine candidate tiers
      const candidates = [
        { score: 80, hasEval: true },
        { score: 70, hasEval: true },
        { score: 50, hasEval: true },
        { score: 0, hasEval: false },
      ];

      const tiers = candidates.map((c) =>
        determineCandidateTier(
          c.score,
          normalized.topThreshold,
          normalized.secondThreshold,
          c.hasEval,
        ),
      );

      expect(tiers).toEqual(['TOP', 'SECOND', 'OTHER', 'UNRANKED']);

      // Format for display
      const formatted = formatThreshold(normalized.topThreshold);
      expect(formatted).toBe('75%');

      // Convert back to decimal for API response
      const decimal = thresholdToDecimal(normalized.topThreshold);
      expect(decimal).toBe(0.75);
    });

    it('should handle legacy data migration scenario', () => {
      // Old format: thresholds stored as decimals
      const legacyJob = {
        topCandidateThreshold: 0.8,
        secondTierCandidateThreshold: 0.6,
      };

      const normalized = normalizeJobThresholds(legacyJob);
      expect(normalized).toEqual({
        topThreshold: 80,
        secondThreshold: 60,
      });

      // New format: thresholds stored as percentages
      const modernJob = {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      };

      const normalizedModern = normalizeJobThresholds(modernJob);
      expect(normalizedModern).toEqual(normalized); // Should be identical
    });
  });
});
