import { CandidateStatus } from '@/shared/types/candidate.types';
import {
  BadRequestException,
  Body,
  Controller,
  forwardRef,
  Inject,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';

import { Auth0Guard } from '../../auth/auth.guard';
import { CandidateService } from '../candidate/candidate.service';
import { JobService } from '../job/job.service';
import { EmailService } from './email.service';

@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('email')
export class EmailController {
  constructor(
    private readonly emailService: EmailService,
    @Inject(forwardRef(() => CandidateService))
    private readonly candidateService: CandidateService,
    @Inject(forwardRef(() => JobService))
    private readonly jobService: JobService,
  ) {}

  @Post('invite-video-intro')
  async testShortlistEmail(
    @Body()
    data: {
      candidateEmail: string;
      candidateName: string;
      jobTitle: string;
      jobId: string;
      candidateId: string;
      companyName?: string;
    },
  ) {
    const job = await this.jobService.findByJobId(data.jobId);
    if (!job.cultureFitQuestions?.length) {
      throw new BadRequestException(
        'No cultural fit questions for this JD. Please add that before sending email to candidates.',
      );
    }

    // If jobTitle is not provided, create a descriptive one
    let jobTitle = data.jobTitle;
    if (!jobTitle) {
      jobTitle =
        job.department && job.jobType
          ? `${job.department} ${job.jobType}`
          : job.jobType || 'Job Position';
    }

    return this.emailService.sendCandidateVideoIntroEmail(
      data.candidateEmail,
      data.candidateName,
      jobTitle,
      data.jobId,
      data.candidateId,
      data.companyName,
    );
  }

  @Post('test-cultural-fit-completed')
  async testCulturalFitCompletedEmail(
    @Body()
    data: {
      candidateId: string;
      jobId: string;
      candidateEmail?: string;
      candidateName?: string;
      jobTitle?: string;
      companyName?: string;
      dashboardUrl?: string;
    },
  ) {
    // If we only received IDs, fetch the necessary information
    if (
      data.candidateId &&
      data.jobId &&
      (!data.candidateEmail || !data.candidateName || !data.jobTitle)
    ) {
      const candidate = await this.candidateService.findOne(data.candidateId);
      const job = await this.jobService.findOne(data.jobId);

      if (!candidate || !job) {
        throw new Error('Candidate or Job not found');
      }

      // Create a more descriptive job title using department and job type
      const jobTitle =
        job.department && job.jobType
          ? `${job.department} ${job.jobType}`
          : job.jobType || 'Job Position';

      return this.emailService.sendCulturalFitCompletedEmail(
        candidate.email!,
        candidate.fullName,
        jobTitle,
        job.companyName,
      );
    }

    // Otherwise, use the provided data
    return this.emailService.sendCulturalFitCompletedEmail(
      data.candidateEmail!,
      data.candidateName!,
      data.jobTitle!,
      data.companyName,
      data.dashboardUrl,
    );
  }

  @Post('bulk-shortlist')
  async sendBulkShortlistEmails(
    @Body()
    data: {
      jobId: string;
      candidateIds: string[];
      tier?: 'TOP' | 'SECOND' | 'OTHER';
      shortlistedOnly?: boolean;
    },
  ) {
    const job = await this.jobService.findOne(data.jobId);
    const candidates = await Promise.all(
      data.candidateIds.map((id) => this.candidateService.findOne(id)),
    );

    // Filter candidates based on shortlisted status if requested
    let targetCandidates = candidates;
    if (data.shortlistedOnly) {
      targetCandidates = candidates.filter((c) => c.status === 'SHORTLISTED');
    }

    // Create a more descriptive job title using department and job type
    const jobTitle =
      job.department && job.jobType
        ? `${job.department} ${job.jobType}`
        : job.jobType || 'Job Position';

    const results = await Promise.allSettled(
      targetCandidates.map((candidate) =>
        this.emailService.sendCandidateVideoIntroEmail(
          candidate.email!,
          candidate.fullName,
          jobTitle,
          job.id,
          candidate.id,
          job.companyName,
        ),
      ),
    );

    return {
      success: results.filter((r) => r.status === 'fulfilled').length,
      failed: results.filter((r) => r.status === 'rejected').length,
      total: results.length,
      tier: data.tier,
      shortlistedOnly: data.shortlistedOnly,
    };
  }

  @Post('status-update')
  async sendStatusUpdateEmail(
    @Body()
    data: {
      to: string;
      candidateName: string;
      jobTitle: string;
      companyName?: string;
      message?: string;
      statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status';
      candidateStatus?: CandidateStatus;
      interviewDate?: string;
      meetingLink?: string;
      startDate?: string;
      onboardingLink?: string;
      expirationDate?: string;
      responseLink?: string;
    },
  ) {
    // Validate required fields
    if (!data.to || !data.candidateName || !data.jobTitle || !data.statusType) {
      throw new BadRequestException(
        'Missing required fields: to, candidateName, jobTitle, and statusType are required',
      );
    }

    // Map candidate status to email status type if provided
    let statusType = data.statusType;
    if (data.candidateStatus) {
      statusType = this.mapCandidateStatusToEmailType(data.candidateStatus, statusType);
    }

    // Format additional data
    const additionalData: Record<string, string | undefined> = {};

    // Add specific data based on status type
    if (statusType === 'interview') {
      additionalData.interviewDate = data.interviewDate;
      additionalData.meetingLink = data.meetingLink;
    } else if (statusType === 'hired') {
      additionalData.startDate = data.startDate;
      additionalData.onboardingLink = data.onboardingLink;
    } else if (statusType === 'offer' || statusType === 'offerAccepted') {
      additionalData.expirationDate = data.expirationDate;
      additionalData.responseLink = data.responseLink;
    }

    // Send the email
    const result = await this.emailService.sendStatusUpdateEmail(
      data.to,
      data.candidateName,
      data.jobTitle,
      statusType as any,
      data.message,
      data.companyName,
      additionalData as any,
    );

    return {
      success: true,
      message: 'Status update email sent successfully',
      result,
    };
  }

  /**
   * Maps a candidate status to an email status type
   */
  private mapCandidateStatusToEmailType(
    candidateStatus: CandidateStatus,
    defaultType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status',
  ): 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status' {
    switch (candidateStatus) {
      case CandidateStatus.INTERVIEWING:
        return 'interview';
      case CandidateStatus.HIRED:
        return 'hired';
      case CandidateStatus.OFFER_EXTENDED:
        return 'offer';
      case CandidateStatus.OFFER_ACCEPTED:
        return 'offerAccepted';
      case CandidateStatus.SHORTLISTED:
      case CandidateStatus.MATCHED:
      case CandidateStatus.CONTACTED:
      case CandidateStatus.INTERESTED:
      case CandidateStatus.CULTURAL_FIT_ANSWERED:
      case CandidateStatus.OFFER_PENDING_APPROVAL:
      case CandidateStatus.OFFER_APPROVED:
      case CandidateStatus.HIRE_PENDING_APPROVAL:
      case CandidateStatus.HIRE_APPROVED:
      default:
        return defaultType;
    }
  }
}
