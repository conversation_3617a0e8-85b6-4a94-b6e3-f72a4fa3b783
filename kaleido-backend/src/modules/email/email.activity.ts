import { Repository } from 'typeorm';
import { Logger } from '@nestjs/common';
import { ActivityType } from '@/shared/types/activity.types';
import { randomUUID } from 'crypto';

export interface ActivityLogOptions {
  candidateId?: string;
  candidateEmail: string;
  emailType: string;
  jobId?: string;
  jobTitle?: string;
  companyName?: string;
  subject: string;
  clientId?: string;
  additionalMetadata?: Record<string, any>;
}

/**
 * Activity logger for email-related activities
 */
export class EmailActivityLogger {
  constructor(
    private readonly repository: Repository<any>,
    private readonly logger: Logger,
  ) {}

  /**
   * Log email activity to candidate's activity history
   */
  async logEmailActivity(options: ActivityLogOptions): Promise<void> {
    const {
      candidateId,
      candidateEmail,
      emailType,
      jobId,
      jobTitle,
      companyName,
      subject,
      clientId,
      additionalMetadata,
    } = options;

    if (!candidateId) return;

    try {
      const candidate = await this.repository.findOne({ where: { id: candidateId } });
      if (candidate) {
        // Initialize activityHistory if it doesn't exist
        if (!candidate.activityHistory) {
          candidate.activityHistory = [];
        }

        // Add the email activity
        candidate.activityHistory.push({
          id: randomUUID(),
          type: ActivityType.EMAIL_SENT,
          timestamp: new Date(),
          description: `${emailType} email sent to ${candidateEmail}`,
          metadata: {
            emailType,
            jobId,
            jobTitle,
            companyName,
            recipientEmail: candidateEmail,
            subject,
            ...additionalMetadata,
          },
          performedBy: clientId,
          relatedEntityId: jobId,
          relatedEntityType: jobId ? 'job' : undefined,
        });

        await this.repository.save(candidate);
      }
    } catch (error) {
      this.logger.error(`Failed to log ${emailType} email activity:`, error);
      // Don't fail the email send if activity logging fails
    }
  }

  /**
   * Create activity logger for a specific repository
   */
  static create(repository: Repository<any>, logger: Logger): EmailActivityLogger {
    return new EmailActivityLogger(repository, logger);
  }
}
