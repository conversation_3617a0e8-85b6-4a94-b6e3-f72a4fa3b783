import * as React from 'react';

import { Button, Heading, Section, Text } from '@react-email/components';

import { EmailFooter, EmailLayout, EmailLogo } from './components/EmailLayout';

interface TeamInvitationEmailProps {
  recipientEmail: string;
  inviterName: string;
  companyName: string;
  role: string;
  invitationUrl: string;
  expiresAt: Date;
  message?: string;
}

export const TeamInvitationEmail: React.FC<TeamInvitationEmailProps> = ({
  recipientEmail,
  inviterName,
  companyName,
  role,
  invitationUrl,
  expiresAt,
  message,
}) => {
  const preview = `${inviterName} has invited you to join ${companyName} on Kaleido Talent`;
  
  // Icon components for better visual design
  const TeamIcon = () => (
    <div style={iconWrapper}>
      <span style={teamIcon}>👥</span>
    </div>
  );

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <EmailLayout preview={preview}>
      {/* Company Logo */}
      <EmailLogo />

      {/* Header with gradient background */}
      <Section style={headerSection}>
        <div style={gradientHeader}>
          <TeamIcon />
          <Heading style={heroHeading}>You're invited to join {companyName}!</Heading>
          <Text style={heroSubheading}>Collaborate with your team on Kaleido Talent</Text>
        </div>
      </Section>

      {/* Main content */}
      <Section style={mainSection}>
        {/* Invitation card */}
        <div style={invitationCard}>
          <Text style={greetingText}>
            Hi there! 👋
          </Text>
          <Text style={mainText}>
            <strong>{inviterName}</strong> has invited you to join <strong>{companyName}</strong> as a <strong style={roleHighlight}>{role}</strong> on Kaleido Talent.
          </Text>
          
          {message && (
            <div style={messageBox}>
              <Text style={messageLabel}>Personal message from {inviterName}:</Text>
              <Text style={messageText}>"{message}"</Text>
            </div>
          )}
        </div>

        {/* CTA Section */}
        <div style={ctaSection}>
          <div style={buttonContainer}>
            <Button style={primaryButton} href={invitationUrl}>
              ✨ Accept Invitation
            </Button>
          </div>
          
          <Text style={helperText}>
            By accepting this invitation, you'll be able to collaborate with your team on Kaleido Talent's platform.
          </Text>
        </div>

        {/* Expiration notice */}
        <div style={expirationNotice}>
          <Text style={expirationText}>
            ⏰ This invitation expires on <strong>{formatDate(expiresAt)}</strong>
          </Text>
        </div>

        {/* Info Section */}
        <div style={infoSection}>
          <Text style={infoTitle}>🎯 What happens next?</Text>
          <Text style={infoText}>
            • Click the button above to accept the invitation
            <br />
            • Log in or create your Kaleido Talent account
            <br />
            • Start collaborating with your team immediately
            <br />
            • Access all team features and permissions based on your role
          </Text>
        </div>

        {/* Security notice */}
        <div style={securityNotice}>
          <Text style={securityText}>
            🔒 <strong>Security Note:</strong> This invitation was sent to {recipientEmail}. If you didn't expect this invitation, you can safely ignore this email.
          </Text>
        </div>
      </Section>

      {/* Footer */}
      <EmailFooter />
    </EmailLayout>
  );
};

// Modern Email Styles with gradients and better UX

const iconWrapper = {
  display: 'inline-block',
  margin: '0 8px',
};

const teamIcon = {
  fontSize: '32px',
  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
};

// Header styles
const headerSection = {
  margin: '0 0 40px 0',
  padding: '0 16px',
};

const gradientHeader = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5cf6 100%)',
  borderRadius: '20px',
  padding: '48px 32px',
  textAlign: 'center' as const,
  color: '#ffffff',
  margin: '0',
  boxShadow: '0 20px 40px -12px rgba(139, 92, 246, 0.25)',
  position: 'relative' as const,
};

const heroHeading = {
  color: '#ffffff',
  fontSize: '32px',
  fontWeight: '700',
  lineHeight: '40px',
  margin: '16px 0 8px 0',
  textAlign: 'center' as const,
};

const heroSubheading = {
  color: '#f3f4f6',
  fontSize: '18px',
  lineHeight: '26px',
  margin: '0',
  opacity: '0.9',
};

// Main content styles
const mainSection = {
  padding: '0 20px',
  margin: '0 0 40px 0',
};

const invitationCard = {
  backgroundColor: '#ffffff',
  border: '1px solid #e5e7eb',
  borderRadius: '16px',
  padding: '32px',
  margin: '0 0 32px 0',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
};

const greetingText = {
  color: '#1f2937',
  fontSize: '20px',
  fontWeight: '600',
  lineHeight: '28px',
  margin: '0 0 16px 0',
};

const mainText = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '0 0 20px 0',
};

const roleHighlight = {
  color: '#8b5cf6',
  fontWeight: '600',
};

const messageBox = {
  backgroundColor: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '12px',
  padding: '20px',
  margin: '20px 0 0 0',
};

const messageLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: '500',
  margin: '0 0 8px 0',
};

const messageText = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0',
  fontStyle: 'italic',
};

// CTA styles
const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const primaryButton = {
  background: 'linear-gradient(135deg, #8b5cf6, #06b6d4)',
  borderRadius: '12px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '16px 48px',
  margin: '0',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  border: 'none',
  transition: 'all 0.2s ease',
};

const helperText = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '16px 0 0 0',
};

// Expiration notice
const expirationNotice = {
  backgroundColor: '#fef3c7',
  border: '1px solid #fbbf24',
  borderRadius: '12px',
  padding: '16px',
  margin: '24px 0',
  textAlign: 'center' as const,
};

const expirationText = {
  color: '#92400e',
  fontSize: '14px',
  margin: '0',
};

// Info section
const infoSection = {
  backgroundColor: '#fef7ff',
  border: '2px solid #c084fc',
  borderRadius: '12px',
  padding: '24px',
  margin: '32px 0',
  textAlign: 'left' as const,
};

const infoTitle = {
  color: '#7c3aed',
  fontSize: '16px',
  fontWeight: '600',
  margin: '0 0 12px 0',
};

const infoText = {
  color: '#7c3aed',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '0',
};

// Security notice
const securityNotice = {
  backgroundColor: '#f3f4f6',
  borderRadius: '8px',
  padding: '16px',
  margin: '24px 0 0 0',
};

const securityText = {
  color: '#6b7280',
  fontSize: '13px',
  lineHeight: '20px',
  margin: '0',
  textAlign: 'center' as const,
};

export default TeamInvitationEmail;