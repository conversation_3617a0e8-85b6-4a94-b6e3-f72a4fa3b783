import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from './company.entity';
import { CompanyMemberRole, CompanyMemberPermissions } from './company-member.entity';

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

@Entity('company_invitations')
@Index('idx_company_invitations_token', ['invitationToken'], { unique: true })
@Index('idx_company_invitations_email', ['email'])
@Index('idx_company_invitations_status', ['status'])
@Index('idx_company_invitations_company', ['companyId'])
export class CompanyInvitation {
  @ApiProperty({ example: 'uuid' })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({ type: () => Company })
  @ManyToOne(() => Company, { eager: false })
  @JoinColumn({ name: 'companyId' })
  company!: Company;

  @ApiProperty({ example: 'uuid' })
  @Column()
  companyId!: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Column()
  email!: string;

  @ApiProperty({ example: 'auth0|123456' })
  @Column()
  invitedByClientId!: string;

  @ApiProperty({ example: 'John Smith', description: 'Name of the person who sent the invitation' })
  @Column({ nullable: true })
  invitedByName?: string;

  @ApiProperty({ enum: CompanyMemberRole, example: CompanyMemberRole.MEMBER })
  @Column({
    type: 'enum',
    enum: CompanyMemberRole,
    default: CompanyMemberRole.MEMBER,
  })
  role!: CompanyMemberRole;

  @ApiProperty({
    example: {
      canManageJobs: true,
      canViewCandidates: true,
      canManageCandidates: false,
    },
    description: 'Custom permissions for this invitation, if different from role defaults',
  })
  @Column('jsonb', { nullable: true })
  permissions?: CompanyMemberPermissions;

  @ApiProperty({ example: 'inv_1234567890abcdef', description: 'Unique invitation token' })
  @Column({ unique: true })
  invitationToken!: string;

  @ApiProperty({ enum: InvitationStatus, example: InvitationStatus.PENDING })
  @Column({
    type: 'enum',
    enum: InvitationStatus,
    default: InvitationStatus.PENDING,
  })
  status!: InvitationStatus;

  @ApiProperty({
    example: 'Please join our team to help manage job postings',
    description: 'Optional message from inviter',
  })
  @Column({ type: 'text', nullable: true })
  message?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-08T00:00:00Z', description: 'When the invitation expires' })
  @Column({ type: 'timestamp' })
  expiresAt!: Date;

  @ApiProperty({ example: '2024-01-02T00:00:00Z', description: 'When the invitation was accepted' })
  @Column({ type: 'timestamp', nullable: true })
  acceptedAt?: Date;

  @ApiProperty({
    example: 'auth0|789012',
    description: 'ClientId of user who accepted the invitation',
  })
  @Column({ nullable: true })
  acceptedByClientId?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  @UpdateDateColumn()
  updatedAt!: Date;

  // Helper method to check if invitation is expired
  isExpired(): boolean {
    return new Date() > new Date(this.expiresAt);
  }

  // Helper method to check if invitation can be accepted
  canBeAccepted(): boolean {
    return this.status === InvitationStatus.PENDING && !this.isExpired();
  }

  // Helper method to generate a secure invitation token
  static generateToken(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 15);
    const randomStr2 = Math.random().toString(36).substring(2, 15);
    return `inv_${timestamp}${randomStr}${randomStr2}`;
  }

  // Helper method to calculate default expiration (7 days from now)
  static getDefaultExpiration(): Date {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date;
  }
}
