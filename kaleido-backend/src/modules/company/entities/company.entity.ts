import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { CreditPurchase } from '@/entities/CreditPurchase.entity';
import { SubscriptionCreditsType } from '@/shared/enums/subscription-limit-type.enum';
import { Job } from '@modules/job/entities/job.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@shared/entities/base.entity';

import { SubscriptionPlan } from '../../../shared/enums/subscription-plan.enum';
import { CompanyMember } from './company-member.entity';
import { CompanyInvitation } from './company-invitation.entity';

@Entity('companies')
@Index('idx_companies_subscription_plan', ['subscriptionPlan'])
@Index('idx_companies_created_at', ['createdAt'])
@Index('idx_companies_industry', ['industry'])
export class Company extends BaseEntity {
  @ApiProperty({ example: 'uuid' })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({ example: 'uuid' })
  @Column({ nullable: true, unique: true })
  @Index('IDX_COMPANY_CLIENT_ID', { unique: true })
  clientId!: string;

  @ApiProperty({ example: 'Acme Inc.' })
  @Column({ nullable: true })
  companyName!: string;

  @ApiProperty({ example: 'http://www.acme.com' })
  @Column({ nullable: true })
  companyWebsite!: string;

  @ApiProperty({ example: 'Human Resources' })
  @Column({ nullable: true })
  department!: string;

  @ApiProperty({ example: 'Technology' })
  @Column({ nullable: true })
  industry!: string;

  @ApiProperty({ example: '11-50 employees' })
  @Column({ nullable: true })
  size!: string;

  @ApiProperty({ example: 'London, UK' })
  @Column({ nullable: true })
  location!: string;

  @ApiProperty({ example: 'John Doe' })
  @Column({ nullable: true })
  contactName!: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Column({ nullable: true })
  contactEmail!: string;

  @ApiProperty({ example: '+44 1234 567890' })
  @Column({ nullable: true })
  phoneNumber!: string;

  @ApiProperty({ example: 'https://acme.com/logo.png' })
  @Column({ nullable: true })
  logo!: string;

  @ApiProperty({
    example: 'Acme Inc. is a leading provider of innovative solutions for businesses of all sizes.',
  })
  @Column({ nullable: true, type: 'text' })
  description!: string;

  @ApiProperty({
    example: 'preferences',
  })
  @Column('jsonb', { nullable: true, default: '{}' })
  preferences: any; // eslint-disable-line

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Store relationship IDs directly rather than using entity relations
  // until the related entities are properly implemented
  @ApiProperty({ example: 'uuid' })
  @Column({ nullable: true })
  userId!: string;

  // Profile styling fields
  @ApiProperty({ example: 'default' })
  @Column({ nullable: true, default: 'default' })
  layoutPreference!: string;

  @ApiProperty({ example: '#6366f1' })
  @Column({ nullable: true, default: '#6366f1' })
  primaryColor!: string;

  @ApiProperty({ example: '#4f46e5' })
  @Column({ nullable: true, default: '#4f46e5' })
  secondaryColor!: string;

  @ApiProperty({ example: '#4338ca' })
  @Column({ nullable: true, default: '#4338ca' })
  accentColor!: string;

  @ApiProperty({ example: 'https://example.com/hero.jpg' })
  @Column({ nullable: true })
  heroImage!: string;

  @ApiProperty({
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  })
  @Column('jsonb', { nullable: true, default: '[]' })
  featuredImages!: string[];

  @ApiProperty({ example: '.company-profile { font-family: "Roboto", sans-serif; }' })
  @Column({ nullable: true, type: 'text' })
  customCss!: string;

  @ApiProperty({
    example: { provider: 'Greenhouse', apiKey: 'xxx', subdomain: 'company' },
    description: 'ATS integration configuration',
  })
  @Column('jsonb', { nullable: true, default: '{}' })
  atsConfig!: Record<string, any>;

  @ApiProperty({
    example: {
      linkedin: {
        accessToken: 'encrypted_token',
        refreshToken: 'encrypted_refresh_token',
        expiresAt: '2024-12-31T23:59:59Z',
        profileInfo: { id: 'linkedin_id', name: 'Company Name' },
        isConnected: true,
      },
      facebook: {
        accessToken: 'encrypted_token',
        refreshToken: 'encrypted_refresh_token',
        expiresAt: '2024-12-31T23:59:59Z',
        profileInfo: { id: 'facebook_page_id', name: 'Company Page' },
        isConnected: true,
      },
      instagram: {
        accessToken: 'encrypted_token',
        refreshToken: 'encrypted_refresh_token',
        expiresAt: '2024-12-31T23:59:59Z',
        profileInfo: { id: 'instagram_business_id', name: 'Company Instagram' },
        isConnected: false,
      },
    },
    description: 'Social media platform connectors for job posting',
  })
  @Column('jsonb', { nullable: true, default: '{}' })
  socialMediaConnectors!: Record<string, any>;

  @Column({ default: false })
  isPublished!: boolean;

  @ApiProperty({
    example: 0,
    description: 'Number of jobs created by this company',
  })
  @Column({ default: 0 })
  jobCount!: number;

  @ApiProperty({
    example: 0,
    description: 'Number of candidates uploaded by this company',
  })
  @Column({ default: 0 })
  candidateUploadCount!: number;

  @ApiProperty({
    example: ['Innovation', 'Teamwork', 'Customer Focus'],
    description: 'Company values',
  })
  @Column('jsonb', { nullable: true, default: '[]' })
  companyValues!: string[];

  @ApiProperty({
    example: true,
    description: 'When enabled, emails are only sent to the current user and whitelisted addresses',
  })
  @Column({ default: true })
  isDemoMode!: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the company registration has been approved',
  })
  @Column({ default: false, nullable: true })
  isApproved?: boolean;

  @ApiProperty({
    example: 'Company information is incomplete',
    description: 'Reason for declining the company registration',
  })
  @Column({ nullable: true })
  declineReason?: string;

  @OneToMany(() => Job, (job) => job.company)
  jobs?: Promise<Job[]>;

  @OneToMany(() => CreditPurchase, (creditPurchase) => creditPurchase.company)
  creditPurchases?: CreditPurchase[];

  @OneToMany(() => CompanyMember, (member) => member.company)
  members?: CompanyMember[];

  @OneToMany(() => CompanyInvitation, (invitation) => invitation.company)
  invitations?: CompanyInvitation[];

  // Subscription fields
  @ApiProperty({ enum: SubscriptionPlan, default: SubscriptionPlan.FREE })
  @Column({
    type: 'enum',
    enum: SubscriptionPlan,
    default: SubscriptionPlan.FREE,
    nullable: true,
  })
  subscriptionPlan?: SubscriptionPlan;

  // Credit-based subscription data
  @Column({
    type: 'jsonb',
    nullable: true,
    default: {
      totalCredits: 5,
      usedCredits: 0,
      remainingCredits: 5,
      monthlyAllocation: 5,
      lastResetDate: new Date(),
      videoJdMaxDuration: 30,
      atsIntegration: 'basic',
      databaseRetentionMonths: 3,
    },
  })
  subscriptionCredits?: SubscriptionCreditsType;

  @ApiProperty({ example: new Date() })
  @Column({ type: 'timestamp', nullable: true })
  subscriptionStartDate!: Date;

  @ApiProperty({ example: new Date() })
  @Column({ type: 'timestamp', nullable: true })
  subscriptionEndDate!: Date;

  @ApiProperty({ example: false })
  @Column({ default: false })
  subscriptionAutoRenew!: boolean;

  @ApiProperty({ example: 'credit_card' })
  @Column({ nullable: true })
  subscriptionPaymentMethod!: string;

  @ApiProperty({ example: 'payment_id_123' })
  @Column({ nullable: true })
  subscriptionPaymentId!: string;

  @ApiProperty({ example: 'cus_123456789' })
  @Column({ nullable: true })
  stripeCustomerId!: string;

  @ApiProperty({ example: 'sub_123456789' })
  @Column({ nullable: true })
  stripeSubscriptionId!: string;

  // Team management fields
  @ApiProperty({
    example: ['company.com', 'subsidiary.com'],
    description: 'Email domains allowed for auto-join',
  })
  @Column('jsonb', { nullable: true, default: '[]' })
  allowedEmailDomains!: string[];

  @ApiProperty({
    example: false,
    description: 'Whether users with matching email domains can auto-join',
  })
  @Column({ default: false })
  autoJoinEnabled!: boolean;

  @ApiProperty({
    example: 'member',
    description: 'Default role for auto-joined members',
  })
  @Column({ nullable: true, default: 'member' })
  defaultAutoJoinRole!: string;
}
