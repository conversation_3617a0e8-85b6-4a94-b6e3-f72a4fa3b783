import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsObject,
  IsArray,
  IsBoolean,
} from 'class-validator';
import {
  CompanyMemberRole,
  CompanyMemberPermissions,
  CompanyMemberStatus,
} from '../entities/company-member.entity';

export class InviteTeamMemberDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email!: string;

  @ApiProperty({ enum: CompanyMemberRole, example: CompanyMemberRole.MEMBER })
  @IsEnum(CompanyMemberRole)
  role!: CompanyMemberRole;

  @ApiProperty({
    required: false,
    example: {
      canManageJobs: true,
      canViewCandidates: true,
      canManageCandidates: false,
    },
  })
  @IsOptional()
  @IsObject()
  permissions?: CompanyMemberPermissions;

  @ApiProperty({
    required: false,
    example: 'Please join our team to help manage job postings',
  })
  @IsOptional()
  @IsString()
  message?: string;
}

export class AcceptInvitationDto {
  @ApiProperty({ example: 'inv_1234567890abcdef' })
  @IsString()
  token!: string;
}

export class UpdateTeamMemberDto {
  @ApiProperty({ enum: CompanyMemberRole, required: false })
  @IsOptional()
  @IsEnum(CompanyMemberRole)
  role?: CompanyMemberRole;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  permissions?: CompanyMemberPermissions;

  @ApiProperty({ enum: CompanyMemberStatus, required: false })
  @IsOptional()
  @IsEnum(CompanyMemberStatus)
  status?: CompanyMemberStatus;
}

export class UpdateCompanyTeamSettingsDto {
  @ApiProperty({
    example: ['company.com', 'subsidiary.com'],
    description: 'Email domains allowed for auto-join',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedEmailDomains?: string[];

  @ApiProperty({
    example: false,
    description: 'Whether users with matching email domains can auto-join',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  autoJoinEnabled?: boolean;

  @ApiProperty({
    example: 'member',
    description: 'Default role for auto-joined members',
    required: false,
  })
  @IsOptional()
  @IsEnum(CompanyMemberRole)
  defaultAutoJoinRole?: CompanyMemberRole;
}

export class CompanyMemberResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  companyId!: string;

  @ApiProperty()
  clientId!: string;

  @ApiProperty()
  email!: string;

  @ApiProperty({ enum: CompanyMemberRole })
  role!: CompanyMemberRole;

  @ApiProperty()
  permissions!: CompanyMemberPermissions;

  @ApiProperty({ enum: CompanyMemberStatus })
  status!: CompanyMemberStatus;

  @ApiProperty({ required: false })
  invitedBy?: string;

  @ApiProperty()
  invitedAt!: Date;

  @ApiProperty({ required: false })
  joinedAt?: Date;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class CompanyInvitationResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  companyId!: string;

  @ApiProperty()
  email!: string;

  @ApiProperty()
  invitedByClientId!: string;

  @ApiProperty({ required: false })
  invitedByName?: string;

  @ApiProperty({ enum: CompanyMemberRole })
  role!: CompanyMemberRole;

  @ApiProperty({ required: false })
  permissions?: CompanyMemberPermissions;

  @ApiProperty()
  status!: string;

  @ApiProperty({ required: false })
  message?: string;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  expiresAt!: Date;
}
