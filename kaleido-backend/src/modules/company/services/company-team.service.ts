import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from '../entities/company.entity';
import {
  CompanyMember,
  CompanyMemberRole,
  CompanyMemberStatus,
} from '../entities/company-member.entity';
import { CompanyInvitation, InvitationStatus } from '../entities/company-invitation.entity';
import { EmailService } from '@modules/email/email.service';
import { RolesService } from '@modules/roles/roles.service';
import { UserRole } from '@/common/enums/role.enum';
import { Auth0ManagementService } from '@/auth/auth0-management.service';
import { UpdateCompanyTeamSettingsDto } from '../dto/team-management.dto';

@Injectable()
export class CompanyTeamService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(CompanyMember)
    private companyMemberRepository: Repository<CompanyMember>,
    @InjectRepository(CompanyInvitation)
    private invitationRepository: Repository<CompanyInvitation>,
    private emailService: EmailService,
    private rolesService: RolesService,
    private auth0ManagementService: Auth0ManagementService,
  ) {}

  /**
   * Get all members of a company
   */
  async getCompanyMembers(companyId: string): Promise<CompanyMember[]> {
    return this.companyMemberRepository.find({
      where: { companyId },
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Get a specific member by clientId and companyId
   */
  async getMemberByClientId(companyId: string, clientId: string): Promise<CompanyMember | null> {
    return this.companyMemberRepository.findOne({
      where: { companyId, clientId },
    });
  }

  /**
   * Check if a user has permission to perform an action
   */
  async hasPermission(
    companyId: string,
    clientId: string,
    permission: keyof CompanyMember['permissions'],
  ): Promise<boolean> {
    const member = await this.getMemberByClientId(companyId, clientId);
    if (!member || member.status !== CompanyMemberStatus.ACTIVE) {
      return false;
    }

    // Owners have all permissions
    if (member.role === CompanyMemberRole.OWNER) {
      return true;
    }

    // Check specific permission
    return member.permissions?.[permission] === true;
  }

  /**
   * Invite a new team member
   */
  async inviteTeamMember(
    companyId: string,
    inviterClientId: string,
    inviteeEmail: string,
    role: CompanyMemberRole,
    permissions?: CompanyMember['permissions'],
    message?: string,
  ): Promise<CompanyInvitation> {
    // Check if inviter has permission
    const canInvite = await this.hasPermission(companyId, inviterClientId, 'canManageTeam');
    if (!canInvite) {
      throw new ForbiddenException('You do not have permission to invite team members');
    }

    // Get company and inviter details
    const company = await this.companyRepository.findOne({ where: { id: companyId } });
    if (!company) {
      throw new NotFoundException('Company not found');
    }

    const inviter = await this.getMemberByClientId(companyId, inviterClientId);
    if (!inviter) {
      throw new NotFoundException('Inviter not found');
    }
    
    // Get inviter's name from Auth0
    let inviterName = inviter.email;
    try {
      const inviterProfile = await this.auth0ManagementService.getUserProfile(inviterClientId);
      if (inviterProfile && inviterProfile.data) {
        inviterName = inviterProfile.data.name || inviterProfile.data.email || inviter.email;
      }
    } catch (error) {
      // Fall back to email if we can't get the name
      console.error('Failed to get inviter profile from Auth0:', error);
    }

    // Check if email domain is allowed (if configured)
    if (company.allowedEmailDomains && company.allowedEmailDomains.length > 0) {
      const emailDomain = inviteeEmail.split('@')[1];
      if (!company.allowedEmailDomains.includes(emailDomain)) {
        throw new BadRequestException(
          `Email domain @${emailDomain} is not allowed for this company. Allowed domains: ${company.allowedEmailDomains.join(', ')}`,
        );
      }
    }

    // Check if user is already a member
    const existingMember = await this.companyMemberRepository.findOne({
      where: { companyId, email: inviteeEmail },
    });
    if (existingMember) {
      throw new BadRequestException('User is already a member of this company');
    }

    // Check for pending invitation
    const pendingInvitation = await this.invitationRepository.findOne({
      where: {
        companyId,
        email: inviteeEmail,
        status: InvitationStatus.PENDING,
      },
    });
    if (pendingInvitation && !pendingInvitation.isExpired()) {
      throw new BadRequestException('An invitation has already been sent to this email');
    }

    // Create invitation
    const invitation = this.invitationRepository.create({
      companyId,
      email: inviteeEmail,
      invitedByClientId: inviterClientId,
      invitedByName: inviterName,
      role,
      permissions: permissions || CompanyMember.getDefaultPermissions(role),
      invitationToken: CompanyInvitation.generateToken(),
      expiresAt: CompanyInvitation.getDefaultExpiration(),
      message,
    });

    await this.invitationRepository.save(invitation);

    // Send invitation email
    const frontendUrl =
      process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const invitationUrl = `${frontendUrl}/accept-invitation?token=${invitation.invitationToken}`;

    await this.emailService.sendTeamInvitationEmail(
      inviteeEmail,
      inviterName,
      company.companyName || 'the company',
      role,
      invitationUrl,
      invitation.expiresAt,
      message,
    );

    return invitation;
  }

  /**
   * Accept an invitation
   */
  async acceptInvitation(token: string, clientId: string, email: string): Promise<CompanyMember> {
    const invitation = await this.invitationRepository.findOne({
      where: { invitationToken: token },
      relations: ['company'],
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (!invitation.canBeAccepted()) {
      throw new BadRequestException('Invitation is no longer valid');
    }

    // Verify email matches
    if (invitation.email !== email) {
      throw new BadRequestException('Invitation was sent to a different email address');
    }

    // Create company member
    const member = this.companyMemberRepository.create({
      companyId: invitation.companyId,
      clientId,
      email,
      role: invitation.role,
      permissions: invitation.permissions,
      status: CompanyMemberStatus.ACTIVE,
      invitedBy: invitation.invitedByClientId,
      joinedAt: new Date(),
    });

    await this.companyMemberRepository.save(member);

    // Update invitation status
    invitation.status = InvitationStatus.ACCEPTED;
    invitation.acceptedAt = new Date();
    invitation.acceptedByClientId = clientId;
    await this.invitationRepository.save(invitation);

    // Update user role if needed
    const existingRole = await this.rolesService.findByClientId(clientId);
    if (!existingRole) {
      await this.rolesService.create({ clientId, role: UserRole.EMPLOYER });
    } else if (existingRole.role !== UserRole.EMPLOYER && existingRole.role !== UserRole.ADMIN) {
      await this.rolesService.updateRole(clientId, UserRole.EMPLOYER);
    }

    // Update Auth0 metadata
    await this.auth0ManagementService.updateUserProfile(clientId, {
      app_metadata: {
        companies: [invitation.companyId],
      },
    });

    return member;
  }

  /**
   * Auto-join company based on email domain
   */
  async autoJoinCompany(clientId: string, email: string): Promise<CompanyMember | null> {
    const emailDomain = email.split('@')[1];
    if (!emailDomain) {
      return null;
    }

    // Find companies that allow this email domain
    const companies = await this.companyRepository
      .createQueryBuilder('company')
      .where('company.autoJoinEnabled = true')
      .andWhere(`company.allowedEmailDomains @> :domain`, { domain: JSON.stringify([emailDomain]) })
      .getMany();

    if (companies.length === 0) {
      return null;
    }

    // For now, join the first matching company
    // In the future, you might want to handle multiple matches differently
    const company = companies[0];

    // Check if already a member
    const existingMember = await this.getMemberByClientId(company.id, clientId);
    if (existingMember) {
      return existingMember;
    }

    // Create member with default role
    const role = (company.defaultAutoJoinRole as CompanyMemberRole) || CompanyMemberRole.MEMBER;
    const member = this.companyMemberRepository.create({
      companyId: company.id,
      clientId,
      email,
      role,
      permissions: CompanyMember.getDefaultPermissions(role),
      status: CompanyMemberStatus.ACTIVE,
      joinedAt: new Date(),
    });

    await this.companyMemberRepository.save(member);

    // Update user role
    const existingRole = await this.rolesService.findByClientId(clientId);
    if (!existingRole) {
      await this.rolesService.create({ clientId, role: UserRole.EMPLOYER });
    } else if (existingRole.role !== UserRole.EMPLOYER && existingRole.role !== UserRole.ADMIN) {
      await this.rolesService.updateRole(clientId, UserRole.EMPLOYER);
    }

    // Update Auth0 metadata
    await this.auth0ManagementService.updateUserProfile(clientId, {
      app_metadata: {
        companies: [company.id],
      },
    });

    return member;
  }

  /**
   * Update member role and permissions
   */
  async updateMember(
    companyId: string,
    requesterClientId: string,
    targetClientId: string,
    updates: {
      role?: CompanyMemberRole;
      permissions?: CompanyMember['permissions'];
      status?: CompanyMemberStatus;
    },
  ): Promise<CompanyMember> {
    // Check if requester has permission
    const canManage = await this.hasPermission(companyId, requesterClientId, 'canManageTeam');
    if (!canManage) {
      throw new ForbiddenException('You do not have permission to manage team members');
    }

    const member = await this.getMemberByClientId(companyId, targetClientId);
    if (!member) {
      throw new NotFoundException('Member not found');
    }

    // Prevent changing owner role
    if (member.role === CompanyMemberRole.OWNER && updates.role !== CompanyMemberRole.OWNER) {
      throw new BadRequestException('Cannot change owner role');
    }

    // Update member
    if (updates.role !== undefined) {
      member.role = updates.role;
      // Apply default permissions for new role if not explicitly provided
      if (!updates.permissions) {
        member.permissions = CompanyMember.getDefaultPermissions(updates.role);
      }
    }

    if (updates.permissions !== undefined) {
      member.permissions = updates.permissions;
    }

    if (updates.status !== undefined) {
      member.status = updates.status;
    }

    return this.companyMemberRepository.save(member);
  }

  /**
   * Remove a team member
   */
  async removeMember(
    companyId: string,
    requesterClientId: string,
    targetClientId: string,
  ): Promise<void> {
    // Check if requester has permission
    const canManage = await this.hasPermission(companyId, requesterClientId, 'canManageTeam');
    if (!canManage) {
      throw new ForbiddenException('You do not have permission to remove team members');
    }

    const member = await this.getMemberByClientId(companyId, targetClientId);
    if (!member) {
      throw new NotFoundException('Member not found');
    }

    // Prevent removing owner
    if (member.role === CompanyMemberRole.OWNER) {
      throw new BadRequestException('Cannot remove company owner');
    }

    await this.companyMemberRepository.remove(member);
  }

  /**
   * Get all companies a user is a member of
   */
  async getUserCompanies(clientId: string): Promise<CompanyMember[]> {
    return this.companyMemberRepository.find({
      where: { clientId, status: CompanyMemberStatus.ACTIVE },
      relations: ['company'],
    });
  }

  /**
   * Cancel an invitation
   */
  async cancelInvitation(
    companyId: string,
    requesterClientId: string,
    invitationId: string,
  ): Promise<void> {
    // Check if requester has permission
    const canManage = await this.hasPermission(companyId, requesterClientId, 'canManageTeam');
    if (!canManage) {
      throw new ForbiddenException('You do not have permission to cancel invitations');
    }

    const invitation = await this.invitationRepository.findOne({
      where: { id: invitationId, companyId },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new BadRequestException('Only pending invitations can be cancelled');
    }

    invitation.status = InvitationStatus.CANCELLED;
    await this.invitationRepository.save(invitation);
  }

  /**
   * Get all pending invitations for a company
   */
  async getCompanyInvitations(companyId: string): Promise<CompanyInvitation[]> {
    return this.invitationRepository.find({
      where: { companyId, status: InvitationStatus.PENDING },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Resend an invitation email
   */
  async resendInvitation(companyId: string, invitationId: string): Promise<void> {
    const invitation = await this.invitationRepository.findOne({
      where: { id: invitationId, companyId },
      relations: ['company'],
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new BadRequestException('Only pending invitations can be resent');
    }

    if (invitation.isExpired()) {
      // Create a new invitation with fresh expiration
      invitation.invitationToken = CompanyInvitation.generateToken();
      invitation.expiresAt = CompanyInvitation.getDefaultExpiration();
      await this.invitationRepository.save(invitation);
    }

    // Get the inviter details for the email
    const inviter = await this.getMemberByClientId(companyId, invitation.invitedByClientId);
    const company =
      invitation.company || (await this.companyRepository.findOne({ where: { id: companyId } }));

    if (!company) {
      throw new NotFoundException('Company not found');
    }
    
    // Get inviter's name from Auth0 or use the stored name
    let inviterName = invitation.invitedByName;
    if (inviter && invitation.invitedByClientId) {
      try {
        const inviterProfile = await this.auth0ManagementService.getUserProfile(invitation.invitedByClientId);
        if (inviterProfile && inviterProfile.data) {
          inviterName = inviterProfile.data.name || inviterProfile.data.email || invitation.invitedByName;
        }
      } catch (error) {
        // Fall back to stored name if we can't get the profile
        console.error('Failed to get inviter profile from Auth0:', error);
      }
    }

    // Resend the invitation email
    const frontendUrl =
      process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const invitationUrl = `${frontendUrl}/accept-invitation?token=${invitation.invitationToken}`;

    await this.emailService.sendTeamInvitationEmail(
      invitation.email,
      inviterName || 'A team member',
      company.companyName || 'the company',
      invitation.role,
      invitationUrl,
      invitation.expiresAt,
      invitation.message,
    );
  }

  async updateTeamSettings(
    companyId: string,
    settingsDto: UpdateCompanyTeamSettingsDto,
  ): Promise<Company> {
    const company = await this.companyRepository.findOne({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Update team settings
    if (settingsDto.allowedEmailDomains !== undefined) {
      company.allowedEmailDomains = settingsDto.allowedEmailDomains;
    }
    if (settingsDto.autoJoinEnabled !== undefined) {
      company.autoJoinEnabled = settingsDto.autoJoinEnabled;
    }
    if (settingsDto.defaultAutoJoinRole !== undefined) {
      company.defaultAutoJoinRole = settingsDto.defaultAutoJoinRole;
    }

    return this.companyRepository.save(company);
  }
}
