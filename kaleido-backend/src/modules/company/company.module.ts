import { EmailModule } from '@modules/email/email.module';
import { Candidate } from '@modules/entities';
import { Job } from '@modules/job/entities/job.entity';
import { SubscriptionModule } from '@modules/subscription/subscription.module';
import { FacebookService } from '@modules/vendors/facebook/facebook.service';
import { InstagramService } from '@modules/vendors/instagram/instagram.service';
import { LinkedInService } from '@modules/vendors/linkedin/linkedin.service';
import { VideoJD } from '@modules/video-jd/entities/video-jd.entity';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DigitalOceanSpacesService } from '@shared/services/digital-ocean-spaces.service';
import { Auth0ManagementService } from '@/auth/auth0-management.service';
import { RolesModule } from '@modules/roles/roles.module';

import { CompanyController } from './company.controller';
import { CompanyService } from './company.service';
import { SocialMediaConnectorController } from './controllers/social-media-connector.controller';
import { CompanyTeamController } from './controllers/company-team.controller';
import { Company } from './entities/company.entity';
import { CompanyMember } from './entities/company-member.entity';
import { CompanyInvitation } from './entities/company-invitation.entity';
import { EnhancedJobPublishingService } from './services/enhanced-job-publishing.service';
import { SocialMediaConnectorService } from './services/social-media-connector.service';
import { CompanyTeamService } from './services/company-team.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, CompanyMember, CompanyInvitation, Job, VideoJD, Candidate]),
    forwardRef(() => SubscriptionModule),
    EmailModule,
    RolesModule,
  ],
  controllers: [CompanyController, SocialMediaConnectorController, CompanyTeamController],
  providers: [
    CompanyService,
    CompanyTeamService,
    DigitalOceanSpacesService,
    SocialMediaConnectorService,
    EnhancedJobPublishingService,
    LinkedInService,
    FacebookService,
    InstagramService,
    Auth0ManagementService,
  ],
  exports: [
    CompanyService,
    CompanyTeamService,
    SocialMediaConnectorService,
    EnhancedJobPublishingService,
  ],
})
export class CompanyModule {}
