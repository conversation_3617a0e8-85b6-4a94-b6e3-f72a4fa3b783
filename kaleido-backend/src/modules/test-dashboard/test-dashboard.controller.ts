import { Body, Controller, Get, HttpException, HttpStatus, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { TestDashboardService } from './test-dashboard.service';

@ApiTags('test-dashboard')
@Controller('test-dashboard')
export class TestDashboardController {
  constructor(private readonly testDashboardService: TestDashboardService) {}

  @Get('data')
  @ApiOperation({ summary: 'Get lightweight test summary data from both projects' })
  @ApiQuery({
    name: 'detailed',
    required: false,
    type: Bo<PERSON>an,
    description: 'Whether to include detailed test information (slower)',
  })
  @ApiResponse({
    status: 200,
    description: 'Test data successfully retrieved',
    schema: {
      type: 'object',
      properties: {
        collectedAt: { type: 'string', format: 'date-time' },
        projects: {
          type: 'object',
          properties: {
            frontend: { type: 'object' },
            backend: { type: 'object' },
          },
        },
        summary: {
          type: 'object',
          properties: {
            totalProjects: { type: 'number' },
            totalTests: { type: 'number' },
            totalSuites: { type: 'number' },
            totalFiles: { type: 'number' },
            passed: { type: 'number' },
            failed: { type: 'number' },
            skipped: { type: 'number' },
            e2eTests: { type: 'number' },
            overallCoverage: { type: 'object' },
            frontendHealth: { type: 'string' },
            backendHealth: { type: 'string' },
          },
        },
        testDistribution: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Error collecting test data',
  })
  async getTestData(@Query('detailed') detailed: boolean = false) {
    try {
      if (detailed) {
        return await this.testDashboardService.collectDetailedTestData();
      } else {
        return await this.testDashboardService.collectAllTestData();
      }
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to collect test data',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('frontend')
  @ApiOperation({ summary: 'Get frontend test data only' })
  @ApiResponse({
    status: 200,
    description: 'Frontend test data successfully retrieved',
  })
  async getFrontendTestData() {
    try {
      return await this.testDashboardService.collectFrontendTestData();
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to collect frontend test data',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('backend')
  @ApiOperation({ summary: 'Get backend test data only' })
  @ApiResponse({
    status: 200,
    description: 'Backend test data successfully retrieved',
  })
  async getBackendTestData() {
    try {
      return await this.testDashboardService.collectBackendTestData();
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to collect backend test data',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('run-tests')
  @ApiOperation({ summary: 'Run tests for a specific project or test type' })
  @ApiQuery({
    name: 'project',
    required: false,
    enum: ['frontend', 'backend', 'all'],
    description: 'Which project to run tests for',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['unit', 'integration', 'e2e', 'all'],
    description: 'Type of tests to run',
  })
  @ApiResponse({
    status: 200,
    description: 'Tests executed successfully',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalTests: { type: 'number' },
            totalSuites: { type: 'number' },
            totalFiles: { type: 'number' },
            passed: { type: 'number' },
            failed: { type: 'number' },
            skipped: { type: 'number' },
            e2eTests: { type: 'number' },
            success: { type: 'boolean' },
          },
        },
        coverage: {
          type: 'object',
          properties: {
            lines: { type: 'object' },
            statements: { type: 'object' },
            functions: { type: 'object' },
            branches: { type: 'object' },
          },
        },
      },
    },
  })
  async runTests(
    @Query('project') project: 'frontend' | 'backend' | 'all' = 'all',
    @Query('type') type: 'unit' | 'integration' | 'e2e' | 'all' = 'all',
  ) {
    try {
      // Run tests and get raw results
      const rawTestResults = await this.testDashboardService.runTests(project, type);

      // Format results for frontend consumption - only return clean summary format
      const cleanResults = this.testDashboardService.formatTestResultsForFrontend(rawTestResults);

      // Update test data with actual results (for internal tracking)
      await this.testDashboardService.updateTestDataWithResults(rawTestResults);

      // Return only the clean formatted results - no raw Jest output
      return cleanResults;
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to run tests',
          error: error instanceof Error ? error.message : 'Unknown error',
          project,
          type,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('coverage')
  @ApiOperation({ summary: 'Get test coverage information' })
  @ApiResponse({
    status: 200,
    description: 'Coverage data successfully retrieved',
  })
  async getCoverageData() {
    try {
      return await this.testDashboardService.getCoverageData();
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to get coverage data',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('test-health')
  @ApiOperation({ summary: 'Get test environment health status' })
  @ApiResponse({
    status: 200,
    description: 'Test environment health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        frontend: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            dependencies: { type: 'object' },
            testFrameworks: { type: 'array' },
          },
        },
        backend: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            dependencies: { type: 'object' },
            testFrameworks: { type: 'array' },
          },
        },
      },
    },
  })
  async getTestHealth() {
    try {
      return await this.testDashboardService.getTestEnvironmentHealth();
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to get test environment health',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('update')
  @ApiOperation({ summary: 'Update test data from Jest reporter (internal use)' })
  @ApiResponse({
    status: 200,
    description: 'Test data updated successfully',
  })
  async updateTestData(@Body() testData: any) {
    try {
      await this.testDashboardService.updateTestDataWithResults(testData);
      return { message: 'Test data updated successfully', timestamp: new Date().toISOString() };
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to update test data',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
