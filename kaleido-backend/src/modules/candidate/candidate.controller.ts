import { Public } from '@/auth/public.decorator';
import { GetUser } from '@/shared/decorators/get-user.decorator';
import { CandidateStatus } from '@/shared/types';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { Auth0Guard } from '../../auth/auth.guard';
import { User } from '../../shared/decorators/get-user.decorator';
import { CreateCandidateDto } from './dto/create-candidate.dto';
import { ScoutCandidatesDto } from './dto/scout-candidates.dto';
import { UpdateCandidateDto } from './dto/update-candidate.dto';
// Import new services
import { CandidateUploadService } from './services/candidate-upload.service';
import { CandidateStatusService } from './services/candidate-status.service';
import { CandidateSearchService } from './services/candidate-search.service';
import { CandidateScoutingService } from './services/candidate-scouting.service';
import { CandidateCacheService } from './services/candidate-cache.service';
import { CandidateCrudService } from './services/candidate-crud.service';
import { CandidateProfileService } from './services/candidate-profile.service';
import { CandidateEnhancementService } from './services/candidate-enhancement.service';
import { EmployerDashboardService } from './services/employer-dashboard.service';
// Core services still needed
import { CandidateService } from './candidate.service';
import { QueueService } from '@/shared/services/queue.service';
import { OpenaiService } from '@shared/services/openai.service';
import { ProcessedApplication } from '@/shared/types/openai.types';

type UploadedFile = Express.Multer.File;

@ApiTags('candidates')
@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('candidates')
export class CandidateController {
  private readonly logger = new Logger('CandidateController');

  constructor(
    // New services
    private readonly candidateUploadService: CandidateUploadService,
    private readonly candidateStatusService: CandidateStatusService,
    private readonly candidateSearchService: CandidateSearchService,
    private readonly candidateScoutingService: CandidateScoutingService,
    private readonly candidateCacheService: CandidateCacheService,
    private readonly candidateCrudService: CandidateCrudService,
    private readonly candidateProfileService: CandidateProfileService,
    private readonly candidateEnhancementService: CandidateEnhancementService,
    private readonly employerDashboardService: EmployerDashboardService,
    // Still needed for some methods
    private readonly candidateService: CandidateService,
    private readonly queueService: QueueService,
    private readonly openaiService: OpenaiService,
  ) {}

  @Post('upload')
  @ApiOperation({
    summary: 'Upload one or more candidate resumes',
    description:
      'Upload one or more resumes for processing. Supports PDF, DOCX, TXT, ZIP, and email files up to 10MB each. Returns a single candidate object for single file uploads or a batch result for multiple files.',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 100 }]))
  @Public()
  async uploadResumes(
    @UploadedFiles() files: { files: UploadedFile[] },
    @Body('jobId') jobId: string,
    @GetUser() user: User,
  ) {
    return this.candidateUploadService.uploadResumes(files, jobId, user);
  }

  @Post('upload-single')
  @ApiOperation({
    summary: 'Upload single candidate resume (deprecated)',
    description:
      'DEPRECATED: Please use /upload endpoint instead. This endpoint will be removed in a future version.',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingleResume(
    @UploadedFile()
    file: Express.Multer.File,
    @Body('jobId') jobId: string,
    @GetUser() user: User,
  ) {
    return this.candidateUploadService.uploadSingleResume(file, jobId, user);
  }

  @Post(':id/status')
  @ApiOperation({ summary: 'Update candidate status (Legacy - use PATCH instead)' })
  async updateStatus(
    @Param('id') id: string,
    @Body()
    updateData: {
      status: CandidateStatus;
      notes?: string;
      jobId?: string;
      dateSensitiveInfo?: {
        date?: string;
        meetingLink?: string;
        startDate?: string;
        onboardingLink?: string;
      };
    },
  ) {
    return this.candidateStatusService.updateStatus(
      id,
      updateData.status,
      updateData.notes,
      updateData,
    );
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update candidate status' })
  async patchStatus(
    @Param('id') id: string,
    @Body()
    updateData: {
      status: CandidateStatus;
      notes?: string;
      jobId?: string;
      dateSensitiveInfo?: {
        date?: string;
        meetingLink?: string;
        startDate?: string;
        onboardingLink?: string;
      };
    },
    @GetUser() user: User,
  ) {
    return this.candidateStatusService.updateStatus(id, updateData.status, updateData.notes, {
      ...updateData,
      userId: user.userId,
    });
  }

  @Post(':id/video-response')
  @ApiOperation({ summary: 'Submit video response' })
  async submitVideoResponse(
    @Param('id') id: string,
    @Body()
    videoData: {
      jobId: string;
      videoResponses: any[];
      isLastQuestion: boolean;
    },
  ) {
    // TODO: Implement submitVideoResponse method in CandidateStatusService
    throw new Error('submitVideoResponse method not implemented');
  }

  @Get('job/:jobId')
  @ApiOperation({ summary: 'Get candidates by job' })
  async findByJob(@Param('jobId') jobId: string, @GetUser() user: User) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateSearchService.findByJob(jobId, clientId);
  }

  @Get('search/:id')
  @ApiOperation({ summary: 'Search candidate by ID' })
  async searchById(@Param('id') id: string, @GetUser() user: User) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateSearchService.searchById(id, clientId);
  }

  // Standard CRUD operations
  @Post()
  create(@Body() createCandidateDto: CreateCandidateDto) {
    return this.candidateCrudService.create(createCandidateDto);
  }

  @Get('paginated')
  @ApiOperation({ summary: 'Get paginated candidates with filtering' })
  async findPaginated(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('jobId') jobId?: string,
    @Query('status') status?: string,
    @Query('tier') tier?: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('typeOfJob') typeOfJob?: string,
    @Query('department') department?: string,
    @Query('experienceLevel') experienceLevel?: string,
    @Query('experience') experience?: string,
    @Query('salaryRangeMin') salaryRangeMin?: number,
    @Query('salaryRangeMax') salaryRangeMax?: number,
    @Query('currency') currency?: string,
    @Query('salaryRange') salaryRange?: string,
    @Query('skills') skills?: string,
    @Query('typeOfHiring') typeOfHiring?: string,
    @Query('culturalFit') culturalFit?: string,
    @Query('companyValues') companyValues?: string,
    @Query('location') location?: string,
    @Query('isGraduateRole') isGraduateRole?: boolean,
    @Query('clientId') providedClientId?: string,
    @GetUser() user?: User,
  ) {
    // Always use the authenticated user's clientId for security
    // Only allow the provided clientId if the user is an admin
    let clientId = providedClientId;

    if (user) {
      // If user is authenticated, enforce their clientId unless they're an admin
      if (!user.roles?.includes('admin')) {
        clientId = user.companyId || user.userId;
      }
    }

    return this.candidateSearchService.findPaginated({
      page: page ? parseInt(page.toString(), 10) : undefined,
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
      jobId,
      status,
      tier,
      searchTerm,
      typeOfJob,
      department,
      experienceLevel,
      experience,
      salaryRangeMin: salaryRangeMin ? parseFloat(salaryRangeMin.toString()) : undefined,
      salaryRangeMax: salaryRangeMax ? parseFloat(salaryRangeMax.toString()) : undefined,
      currency,
      salaryRange,
      skills: skills ? skills.split(',').map((s) => s.trim()) : undefined,
      typeOfHiring,
      culturalFit: culturalFit ? culturalFit.split(',').map((c) => c.trim()) : undefined,
      companyValues: companyValues ? companyValues.split(',').map((v) => v.trim()) : undefined,
      location: location ? location.split(',').map((l) => l.trim()) : undefined,
      isGraduateRole,
      clientId,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all candidates with optional filtering' })
  async findAll(
    @GetUser() user: User,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('jobType') jobType?: string,
    @Query('excludeJobId') excludeJobId?: string,
  ) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateSearchService.findAll(clientId, {
      page: Number(page),
      limit: Number(limit),
      jobType,
      excludeJobId,
    });
  }

  @Get('matching')
  @ApiOperation({ summary: 'Get matching candidates based on evaluation and activity history' })
  async findMatching(
    @GetUser() user: User,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('jobType') jobType?: string,
    @Query('jobId') jobId?: string,
  ) {
    if (!jobId) {
      throw new BadRequestException('Job ID is required');
    }

    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateSearchService.findMatching(clientId, {
      page: Number(page),
      limit: Number(limit),
      jobType,
      jobId,
    });
  }

  @Get(':id/status-history')
  @ApiOperation({ summary: 'Get candidate status history' })
  async getStatusHistory(@Param('id') id: string) {
    // TODO: Implement getStatusHistory method in CandidateStatusService
    throw new Error('getStatusHistory method not implemented');
  }

  @Post('download-profile')
  @ApiOperation({ summary: 'Download candidate profile as PDF' })
  @Public()
  async downloadCandidateProfile(
    @Body() body: { candidateId: string; jobId: string; format?: string },
  ) {
    return this.candidateProfileService.downloadCandidateProfile(body);
  }

  @Get(':id/profile')
  @ApiOperation({ summary: 'Get comprehensive candidate profile with evaluation and job data' })
  @Public()
  async getCandidateProfile(
    @Param('id') candidateId: string,
    @Query('jobId') jobId: string,
    @GetUser() user?: User,
  ) {
    return this.candidateProfileService.getCandidateProfile(candidateId, jobId, user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get candidate by ID' })
  async findOne(@Param('id') id: string, @GetUser() user: User) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateCrudService.findOne(id, clientId);
  }

  @Get(':id/minimal')
  @ApiOperation({ summary: 'Get candidate with minimal fields for list views' })
  @ApiResponse({ status: 200, description: 'Candidate retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Candidate not found' })
  async findOneMinimal(@Param('id') id: string, @GetUser() user: User) {
    const clientId = user.companyId || user.userId;
    return this.candidateCrudService.findOneMinimal(id, clientId);
  }

  @Get(':id/detailed')
  @ApiOperation({ summary: 'Get candidate with detailed fields for profile views' })
  @ApiResponse({ status: 200, description: 'Candidate retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Candidate not found' })
  async findOneDetailed(
    @Param('id') id: string,
    @Query('jobId') jobId: string,
    @GetUser() user: User,
  ) {
    const clientId = user.companyId || user.userId;
    return this.candidateCrudService.findOneDetailed(id, clientId, jobId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCandidateDto: UpdateCandidateDto,
    @GetUser() user: User,
  ) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateCrudService.update(id, updateCandidateDto, clientId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @GetUser() user: User) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateCrudService.remove(id, clientId);
  }

  // Scout route moved to LinkedInScoutController to avoid duplicate route conflict

  @Get('scout-status/:jobId')
  @ApiOperation({ summary: 'Get scouting job status' })
  async getScoutStatus(@Param('jobId') jobId: string) {
    return this.candidateScoutingService.getScoutStatus(jobId);
  }

  @Post('cancel-scout/:jobId')
  @ApiOperation({ summary: 'Cancel a scouting job' })
  async cancelScout(@Param('jobId') jobId: string) {
    return this.candidateScoutingService.cancelScout(jobId);
  }

  @Post('enhanced-search')
  @ApiOperation({
    summary: 'Enhanced candidate search with intelligent caching',
    description:
      'Search for candidates using the enhanced Apollo service with intelligent caching, pagination-based randomization, and company-specific optimization. This endpoint provides better duplicate prevention and cost optimization.',
  })
  async enhancedCandidateSearch(
    @Body()
    searchDto: {
      jobType: string;
      skills: string[];
      location?: string | string[];
      limit?: number;
      jobId?: string;
      companyId?: string;
      forceApollo?: boolean;
      maxCacheAge?: number;
    },
    @GetUser() user: User,
  ) {
    return this.candidateScoutingService.enhancedCandidateSearch(searchDto, user);
  }

  @Get('bulk-cache-analytics')
  @ApiOperation({
    summary: 'Get bulk cache analytics and optimization insights',
    description:
      'Retrieve comprehensive analytics about bulk cache usage, credit savings, and optimization recommendations for the company.',
  })
  async getBulkCacheAnalytics(@GetUser() user: User) {
    return this.candidateCacheService.getBulkCacheAnalytics(user);
  }

  @Post('cleanup-cache')
  @ApiOperation({
    summary: 'Clean up old cached candidates',
    description:
      'Remove old cached candidates that are older than the specified number of days to free up database space.',
  })
  async cleanupCache(@Body() cleanupDto: { maxAgeInDays?: number }, @GetUser() user: User) {
    return this.candidateCacheService.cleanupCache(cleanupDto.maxAgeInDays, user);
  }

  @Post('enrich-single')
  @ApiOperation({
    summary: 'Enrich a single candidate with contact information',
    description:
      'Enrich a single candidate profile using Apollo API to get email, phone number, and other contact details.',
  })
  async enrichSingleCandidate(
    @Body()
    enrichDto: {
      profileId?: string;
      firstName?: string;
      lastName?: string;
      email?: string;
      linkedinUrl?: string;
      companyName?: string;
      jobTitle?: string;
    },
    @GetUser() user: User,
  ) {
    return this.candidateEnhancementService.enrichSingleCandidate(enrichDto, user);
  }

  @Post('enrich-bulk')
  @ApiOperation({
    summary: 'Enrich multiple candidates with contact information',
    description:
      'Enrich multiple candidate profiles using Apollo Bulk API to get email, phone numbers, and other contact details efficiently.',
  })
  async enrichBulkCandidates(
    @Body()
    enrichDto: {
      candidates: Array<{
        profileId?: string;
        firstName?: string;
        lastName?: string;
        email?: string;
        linkedinUrl?: string;
        companyName?: string;
        jobTitle?: string;
      }>;
      batchSize?: number;
    },
    @GetUser() user: User,
  ) {
    return this.candidateEnhancementService.enrichBulkCandidates(enrichDto, user);
  }

  @Post('test-enrichment')
  @ApiOperation({
    summary: 'Test Apollo enrichment with sample data',
    description:
      'Test endpoint to verify Apollo enrichment is working correctly with known sample data.',
  })
  async testEnrichment(@GetUser() user: User) {
    return this.candidateEnhancementService.testEnrichment(user);
  }

  @Post('relaxed-search')
  @ApiOperation({
    summary: 'Candidate search with precise location but no skills/keywords',
    description:
      'Search for candidates using job title and precise location matching. Skills/keywords are removed for broader results. Location filtering remains precise and critical.',
  })
  async relaxedCandidateSearch(
    @Body()
    searchDto: {
      jobType: string;
      location?: string | string[];
      limit?: number;
      jobId?: string;
      companyId?: string;
    },
    @GetUser() user: User,
  ) {
    return this.candidateScoutingService.relaxedCandidateSearch(searchDto, user);
  }

  @Delete('resume/:jobId/:filename')
  @ApiOperation({ summary: 'Remove candidate from job' })
  async deleteResume(
    @Param('jobId') jobId: string,
    @Param('filename') filename: string,
    @GetUser() user: User,
  ) {
    return this.candidateProfileService.deleteResume(jobId, filename, user);
  }

  @Delete('job/:jobId/candidate/:candidateId')
  @ApiOperation({ summary: 'Remove candidate from job by candidate ID' })
  async removeCandidateFromJob(
    @Param('jobId') jobId: string,
    @Param('candidateId') candidateId: string,
    @GetUser() user: User,
  ) {
    return this.candidateProfileService.removeCandidateFromJob(jobId, candidateId, user);
  }

  @Post('process-resumes')
  @ApiOperation({ summary: 'Process uploaded resumes' })
  async processResumes(@Body() body: { jobId: string }): Promise<{
    success: boolean;
    data?: ProcessedApplication[];
    error?: string;
    updatedCandidates?: any;
  }> {
    try {
      const job = await this.candidateService.findJobById(body.jobId);

      const processedApplications = await this.openaiService.processApplications({ job });

      // Update candidates with processed data
      const updatedCandidates =
        await this.candidateService.updateCandidatesWithProcessedData(processedApplications);

      return {
        success: true,
        data: processedApplications,
        updatedCandidates,
      };
    } catch (error) {
      console.error('Error processing resumes:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process resumes',
      };
    }
  }

  @Post('direct-create')
  @ApiOperation({ summary: 'Directly create one or more candidates' })
  async directCreate(
    @Body() updateCandidateDtos: UpdateCandidateDto | UpdateCandidateDto[],
    @GetUser() user: User,
  ) {
    return this.candidateCrudService.directCreate(updateCandidateDtos, user.userId);
  }

  @Get('employer/dashboard')
  async getEmployerDashboardData(
    @Query('jobId') jobId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('tier') tier?: string,
    @Query('typeOfJob') typeOfJob?: string,
    @Query('department') department?: string,
    @Query('experienceLevel') experienceLevel?: string,
    @Query('skills') skills?: string,
    @Query('location') location?: string,
    @GetUser() user?: User,
  ): Promise<any> {
    // Verify user has employer role or is admin
    if (!user?.roles?.includes('employer') && !user?.roles?.includes('admin')) {
      throw new BadRequestException('You do not have permission to access this resource');
    }

    const clientId = user?.userId || user?.sub || '';

    const queryParams = {
      jobId,
      page,
      limit,
      status,
      searchTerm,
      tier,
      typeOfJob,
      department,
      experienceLevel,
      skills,
      location,
    };

    return this.employerDashboardService.getEmployerDashboardData(clientId, queryParams);
  }

  @Get('employer/talent-hub')
  async getTalentHubData(
    @Query('jobId') jobId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('tier') tier?: string,
    @Query('typeOfJob') typeOfJob?: string,
    @Query('department') department?: string,
    @Query('experienceLevel') experienceLevel?: string,
    @Query('skills') skills?: string,
    @Query('location') location?: string,
    @GetUser() user?: User,
  ): Promise<any> {
    // Verify user has employer role or is admin
    if (!user?.roles?.includes('employer') && !user?.roles?.includes('admin')) {
      throw new BadRequestException('You do not have permission to access this resource');
    }

    const clientId = user?.userId || user?.sub || '';

    const queryParams = {
      jobId,
      page,
      limit,
      status,
      searchTerm,
      tier,
      typeOfJob,
      department,
      experienceLevel,
      skills,
      location,
    };

    return this.employerDashboardService.getTalentHubData(clientId, queryParams);
  }

  @Get('upload-status/:jobId')
  @ApiOperation({ summary: 'Get upload job status' })
  async getUploadStatus(@Param('jobId') jobId: string) {
    const status = await this.queueService.getJobStatus(jobId);

    // Add additional information for partial successes
    if (status.status === 'completed' && status.result) {
      const result = status.result;

      if (result.candidates && result.failedUploads && result.failedUploads.length > 0) {
        status.partialSuccess = true;
        status.successCount = result.candidates.length;
        status.failureCount = result.failedUploads.length;
        status.failedFiles = result.failedUploads;

        // Add a more helpful message
        status.message = `Processed ${
          result.candidates.length + result.failedUploads.length
        } files: ${result.candidates.length} succeeded, ${result.failedUploads.length} failed.`;

        // Provide error summary by type
        const errorTypes: Record<string, number> = {};
        result.failedUploads.forEach((fail: { error: string }) => {
          errorTypes[fail.error] = (errorTypes[fail.error] || 0) + 1;
        });

        status.errorSummary = Object.entries(errorTypes).map(([error, count]) => ({
          error,
          count,
        }));
      }
    }

    return status;
  }

  @Post('cancel-upload/:jobId')
  @ApiOperation({ summary: 'Cancel an in-progress upload job' })
  async cancelUpload(@Param('jobId') jobId: string) {
    try {
      await this.queueService.cancelJob(jobId);
      return {
        success: true,
        status: 'cancelled',
        message: 'Upload job has been cancelled',
      };
    } catch (error) {
      this.logger.error(`Error cancelling upload job ${jobId}:`, error);
      throw new BadRequestException('Failed to cancel upload job. Please try again later.');
    }
  }

  @Get('queue-test')
  @ApiOperation({ summary: 'Test the queue system' })
  @Public()
  async testQueue() {
    try {
      // Create a test job
      const job = await this.queueService.addToUploadQueue({
        files: [],
        jobId: 'test-job',
        userId: 'test-user',
        clientId: 'test-user',
      });

      // Try to get the job status immediately
      const status = await this.queueService.getJobStatus(job.jobId);

      return {
        success: true,
        job,
        status,
        message: 'Queue test completed. Check the logs for details.',
      };
    } catch (error: any) {
      console.error('Queue test failed:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
        message: 'Queue test failed. Check the logs for details.',
      };
    }
  }

  @Get(':id/job-applications')
  @ApiOperation({
    summary: 'Get candidate job applications with status information',
    description:
      'Returns candidate job applications grouped by job with their current status and history',
  })
  async getCandidateJobApplications(@Param('id') id: string, @GetUser() user: User) {
    return this.candidateProfileService.getCandidateJobApplications(id, user);
  }

  @Post(':id/enhance')
  @ApiOperation({
    summary: 'Enhance a single candidate with full details',
    description:
      'Enhance a candidate profile using Apollo API to get complete contact information, experience details, and other missing data. Returns the full updated candidate data for UI refresh.',
  })
  @ApiResponse({
    status: 200,
    description: 'Candidate successfully enhanced',
    type: () =>
      import('./dto/candidate-enhancement.dto').then((m) => m.CandidateEnhancementResponseDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Candidate not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Enhancement failed or candidate cannot be enhanced',
  })
  async enhanceCandidate(@Param('id') id: string, @GetUser() user: User) {
    this.logger.log(`Enhancing candidate ${id}`, {
      candidateId: id,
      userId: user?.userId || 'anonymous',
      companyId: user?.companyId,
      timestamp: new Date().toISOString(),
    });

    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateService.enhanceCandidate(id, clientId);
  }

  @Get(':id/enhancement-status')
  @ApiOperation({
    summary: 'Check if a candidate needs enhancement',
    description:
      'Check what data is missing from a candidate profile and whether it can be enhanced using Apollo API.',
  })
  @ApiResponse({
    status: 200,
    description: 'Enhancement status retrieved successfully',
    type: () =>
      import('./dto/candidate-enhancement.dto').then((m) => m.CandidateEnhancementStatusDto),
  })
  @ApiResponse({
    status: 404,
    description: 'Candidate not found',
  })
  async getCandidateEnhancementStatus(@Param('id') id: string, @GetUser() user: User) {
    // Use companyId as primary clientId if available, otherwise fall back to userId
    const clientId = user.companyId || user.userId;
    return this.candidateService.getCandidateEnhancementStatus(id, clientId);
  }
}
