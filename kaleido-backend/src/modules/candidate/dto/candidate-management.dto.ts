import { <PERSON><PERSON><PERSON>y, IsBoolean, <PERSON><PERSON><PERSON>al, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class UpdateCandidateManagementDto {
  @ApiProperty({ description: 'Mark candidate as favorite', required: false })
  @IsOptional()
  @IsBoolean()
  isFavorited?: boolean;

  @ApiProperty({ description: 'Mark candidate as shortlisted', required: false })
  @IsOptional()
  @IsBoolean()
  isShortlisted?: boolean;
}

export class AddScrapebookNoteDto {
  @ApiProperty({ description: 'Note content' })
  @IsString()
  content!: string;

  @ApiProperty({ description: 'Note tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Whether the note is private', required: false })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

export class UpdateScrapebookNoteDto {
  @ApiProperty({ description: 'Note ID' })
  @IsUUID()
  id!: string;

  @ApiProperty({ description: 'Updated note content', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ description: 'Updated note tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Whether the note is private', required: false })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

export class DeleteScrapebookNoteDto {
  @ApiProperty({ description: 'Note ID' })
  @IsUUID()
  id!: string;
}

export class CandidateManagementFilterDto {
  @ApiProperty({ description: 'Filter by favorite status', required: false })
  @IsOptional()
  @IsBoolean()
  isFavorited?: boolean;

  @ApiProperty({ description: 'Filter by shortlisted status', required: false })
  @IsOptional()
  @IsBoolean()
  isShortlisted?: boolean;

  @ApiProperty({ description: 'Search in notes content', required: false })
  @IsOptional()
  @IsString()
  notesSearch?: string;

  @ApiProperty({ description: 'Filter by note tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export interface ScrapebookNote {
  id: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  tags?: string[];
  isPrivate?: boolean;
}

export class AddStickyNoteDto {
  @ApiProperty({ description: 'Page ID where the note is placed' })
  @IsString()
  pageId!: string;

  @ApiProperty({ description: 'Note content' })
  @IsString()
  content!: string;

  @ApiProperty({ description: 'Note color' })
  @IsString()
  color!: string;

  @ApiProperty({ 
    description: 'Note position',
    type: 'object',
    properties: {
      x: { type: 'number' },
      y: { type: 'number' }
    }
  })
  position!: { x: number; y: number };
}

export class UpdateStickyNoteDto {
  @ApiProperty({ description: 'Page ID where the note is placed' })
  @IsString()
  pageId!: string;

  @ApiProperty({ description: 'Note ID' })
  @IsUUID()
  noteId!: string;

  @ApiProperty({ description: 'Updated note content', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ description: 'Updated note color', required: false })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({ description: 'Updated note position', required: false })
  @IsOptional()
  position?: { x: number; y: number };
}

export class DeleteStickyNoteDto {
  @ApiProperty({ description: 'Page ID where the note is placed' })
  @IsString()
  pageId!: string;

  @ApiProperty({ description: 'Note ID' })
  @IsUUID()
  noteId!: string;
}

export interface StickyNote {
  id: string;
  content: string;
  color: string;
  position: { x: number; y: number };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}
