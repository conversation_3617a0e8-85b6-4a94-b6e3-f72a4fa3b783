# Candidate Comparison Module

This module implements a comprehensive candidate comparison system that allows recruiters to compare 2-3 candidates side-by-side using AI-powered analysis.

## Features

- Compare 2-3 candidates simultaneously
- 6 preset comparison types:
  - Quick Overview
  - Skills & Technical Fit
  - Leadership & Management
  - Cultural & Team Fit
  - Cost-Benefit Analysis
  - Risk Assessment
- Custom comparison criteria and prompts
- Scenario-based comparisons
- AI-generated insights and recommendations
- Visual data preparation for charts and graphs

## Usage

### Import Module

Add the `CandidateComparisonModule` to your app module:

```typescript
import { CandidateComparisonModule } from './modules/comparison';

@Module({
  imports: [
    // ... other modules
    CandidateComparisonModule,
  ],
})
export class AppModule {}
```

### API Endpoints

1. **Create Comparison**
   - `POST /api/comparisons`
   - Compare 2-3 candidates with preset or custom criteria

2. **Create Scenario Comparison**
   - `POST /api/comparisons/scenario`
   - Compare candidates based on a specific scenario

3. **Get Comparison Results**
   - `GET /api/comparisons/:id`
   - Retrieve comparison results once processing is complete

4. **Get Comparison Status**
   - `GET /api/comparisons/:id/status`
   - Check processing status and progress

5. **List Job Comparisons**
   - `GET /api/comparisons/jobs/:jobId`
   - Get all comparisons for a specific job

6. **Generate Report**
   - `GET /api/comparisons/:id/report`
   - Generate a formatted comparison report

7. **Delete Comparison**
   - `DELETE /api/comparisons/:id`
   - Remove a comparison

8. **Get Configuration Options**
   - `GET /api/comparisons/config/options`
   - Retrieve available comparison types and configurations

### Database Migration

Run the migration to create the necessary database table:

```bash
npm run typeorm migration:run
```

### Example Usage

```typescript
// Create a quick overview comparison
const comparison = await fetch('/api/comparisons', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    jobId: 'job-uuid',
    candidateIds: ['candidate-1', 'candidate-2'],
    comparisonType: 'quick_overview'
  })
});

// Check status
const status = await fetch(`/api/comparisons/${comparison.id}/status`);

// Get results when completed
const results = await fetch(`/api/comparisons/${comparison.id}`);
```

## Configuration

The module includes preset comparison types in `config/comparison-options.config.ts`. You can modify these presets or add new ones as needed.

## Dependencies

- TypeORM for database operations
- OpenAI service for AI-powered analysis
- NestJS framework

## Notes

- Comparisons are processed asynchronously
- Results are typically available within 30 seconds to 2 minutes
- The system stores comparison results for future reference
- Visualization data is prepared for frontend charting libraries