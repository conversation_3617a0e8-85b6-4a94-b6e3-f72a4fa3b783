import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { ChatCompletionMessageParam } from 'openai/resources/chat';

interface CompletionOptions {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  response_format?: { type: 'text' | 'json_object' };
}

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);
  private openai: OpenAI;
  private readonly defaultModel = 'gpt-4o-mini'; // Cost-efficient model for comparisons

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    
    if (!apiKey) {
      this.logger.warn('OpenAI API key not configured. AI comparisons will not work.');
    }

    this.openai = new OpenAI({
      apiKey: apiKey || 'dummy-key', // Use dummy key to prevent initialization errors
    });
  }

  /**
   * Generate a completion using OpenAI's chat API
   */
  async generateCompletion(
    prompt: string,
    options: CompletionOptions = {}
  ): Promise<string> {
    try {
      const messages: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: 'You are an expert recruiter and hiring manager assistant. Provide objective, data-driven analysis of candidates.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ];

      const completion = await this.openai.chat.completions.create({
        model: options.model || this.defaultModel,
        messages,
        temperature: options.temperature ?? 0.7,
        max_tokens: options.max_tokens ?? 2000,
        response_format: options.response_format || { type: 'json_object' },
      });

      const content = completion.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      return content;
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      
      // Fallback to mock response for development/testing
      if (process.env.NODE_ENV === 'development' || !this.configService.get('OPENAI_API_KEY')) {
        return this.generateMockCompletion(prompt);
      }
      
      throw error;
    }
  }

  /**
   * Generate a mock completion for development/testing
   */
  private generateMockCompletion(prompt: string): string {
    this.logger.warn('Using mock completion - configure OpenAI API key for real results');
    
    // Extract candidate count from prompt
    const candidateCountMatch = prompt.match(/comparing (\d+) candidates/i);
    const candidateCount = candidateCountMatch ? parseInt(candidateCountMatch[1]) : 2;
    
    // Generate mock candidate IDs
    const candidateIds = Array.from({ length: candidateCount }, (_, i) => `candidate-${i + 1}`);
    
    const mockResponse = {
      executiveSummary: "This is a mock comparison result. Configure OpenAI API key for real analysis.",
      candidateAnalysis: candidateIds.reduce((acc, id, index) => {
        acc[id] = {
          overallRank: index + 1,
          strengths: ["Mock strength 1", "Mock strength 2"],
          weaknesses: ["Mock weakness 1"],
          uniqueAdvantages: ["Mock advantage"],
          riskFactors: ["Mock risk"],
          scores: {
            skills: 80 - (index * 10),
            experience: 75 - (index * 10),
            leadership: 70 - (index * 10),
            culturalFit: 85 - (index * 10),
            availability: 90
          },
          keyDifferentiator: "Mock differentiator"
        };
        return acc;
      }, {} as any),
      headToHeadComparisons: [{
        candidate1: candidateIds[0],
        candidate2: candidateIds[1],
        keyDifference: "Mock difference",
        recommendation: "Mock recommendation"
      }],
      recommendations: {
        topChoice: {
          candidateId: candidateIds[0],
          reasoning: "Mock reasoning for top choice"
        },
        alternativeScenarios: [{
          scenario: "If immediate availability is critical",
          recommendedCandidate: candidateIds[1],
          reasoning: "Mock alternative reasoning"
        }],
        hiringStrategy: "Mock hiring strategy"
      },
      criticalConsiderations: ["Mock consideration 1", "Mock consideration 2"]
    };
    
    return JSON.stringify(mockResponse, null, 2);
  }
}