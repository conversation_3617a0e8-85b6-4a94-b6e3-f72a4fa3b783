import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Job } from '@modules/job/entities/job.entity';
import { getComparisonOption, interpolatePrompt } from '../config/comparison-options.config';

export interface CandidateData {
  id: string;
  fullName: string;
  jobTitle: string;
  experience: Array<{
    title: string;
    company: string;
    duration?: string;
    startDate: string;
    endDate: string | null;
  }>;
  skills: string[];
  evaluation: {
    matchScore: number;
    criterionMatchedOn: string[];
    detailedScoreAnalysis: any;
  };
  yearsOfExperience: number | null;
  currentCompany: string;
  status: string;
  tier: string;
}

export class ComparisonPromptGenerator {
  /**
   * Generates the optimal prompt for comparing candidates based on criteria and context
   */
  static generateComparisonPrompt(
    candidates: Candidate[],
    job: Job,
    comparisonType: string,
    userContext?: string,
    customCriteria?: string[]
  ): string {
    const candidateCount = candidates.length;
    const jobTitle = job.jobType;
    
    // Transform candidates to CandidateData format
    const candidateData = candidates.map((candidate) => ({
      id: candidate.id,
      fullName: candidate.fullName,
      jobTitle: candidate.getDisplayTitle(),
      currentCompany: candidate.currentCompany || 'Not specified',
      yearsOfExperience: candidate.getYearsOfExperience(),
      status: candidate.status,
      tier: candidate.tier || 'OTHER',
      skills: candidate.skills || [],
      experience: candidate.experience || [],
      evaluation: candidate.getEvaluationForJob(job.id) || {
        matchScore: 0,
        criterionMatchedOn: [],
        detailedScoreAnalysis: {},
      },
    }));

    // Base context about the comparison
    const baseContext = `You are comparing ${candidateCount} candidates for the ${jobTitle} position. 
Here is the detailed information for each candidate:

${candidateData.map((candidate, index) => `
Candidate ${index + 1}: ${candidate.fullName}
- Current Role: ${candidate.jobTitle} at ${candidate.currentCompany}
- Match Score: ${candidate.evaluation.matchScore}%
- Status: ${candidate.status} (${candidate.tier} tier)
- Years of Experience: ${candidate.yearsOfExperience || 'Not specified'}
- Key Matched Criteria: ${candidate.evaluation.criterionMatchedOn.join(', ')}

Recent Experience:
${candidate.experience.slice(0, 3).map(exp => 
  `  • ${exp.title} at ${exp.company} (${exp.startDate} - ${exp.endDate || 'Present'})`
).join('\n')}

Top Skills:
${candidate.skills.slice(0, 10).join(', ')}

Evaluation Summary:
- Strengths: ${JSON.stringify((candidate.evaluation.detailedScoreAnalysis as any)?.areasOfStrength || [])}
- Training Needs: ${JSON.stringify((candidate.evaluation.detailedScoreAnalysis as any)?.trainingNeeds || [])}
- Missing Requirements: ${JSON.stringify((candidate.evaluation.detailedScoreAnalysis as any)?.missingCriticalRequirements || [])}
`).join('\n---\n')}`;

    // Specific instructions based on comparison type
    const typeSpecificInstructions = this.getTypeSpecificInstructions(comparisonType, candidateCount, jobTitle);
    
    // Custom criteria instructions
    const criteriaInstructions = customCriteria && customCriteria.length > 0
      ? `\nPay special attention to these comparison criteria: ${customCriteria.join(', ')}`
      : '';
    
    // User context integration
    const contextInstructions = userContext
      ? `\nAdditional context from the hiring manager: "${userContext}"`
      : '';
    
    // Output format instructions
    const outputInstructions = `
Please provide your analysis in the following JSON format:
{
  "executiveSummary": "2-3 sentence overview of the comparison",
  "candidateAnalysis": {
    "[candidateId]": {
      "overallRank": 1-${candidateCount},
      "strengths": ["strength1", "strength2", ...],
      "weaknesses": ["weakness1", "weakness2", ...],
      "uniqueAdvantages": ["advantage1", "advantage2", ...],
      "riskFactors": ["risk1", "risk2", ...],
      "scores": {
        "skills": 0-100,
        "experience": 0-100,
        "leadership": 0-100,
        "culturalFit": 0-100,
        "availability": 0-100
      },
      "keyDifferentiator": "What makes this candidate stand out"
    }
  },
  "headToHeadComparisons": [
    {
      "candidate1": "name",
      "candidate2": "name", 
      "keyDifference": "Main distinguishing factor",
      "recommendation": "Which is better for what scenario"
    }
  ],
  "recommendations": {
    "topChoice": {
      "candidateId": "id",
      "reasoning": "Detailed explanation"
    },
    "alternativeScenarios": [
      {
        "scenario": "If immediate availability is critical",
        "recommendedCandidate": "candidateId",
        "reasoning": "Why this candidate for this scenario"
      }
    ],
    "hiringStrategy": "Overall recommendation for the hiring decision"
  },
  "criticalConsiderations": ["Important factors to consider in final decision"]
}`;

    // Combine all parts
    return `${baseContext}

${typeSpecificInstructions}
${criteriaInstructions}
${contextInstructions}

${outputInstructions}

Ensure your analysis is objective, data-driven, and provides actionable insights for the hiring decision.`;
  }

  /**
   * Get specific instructions based on comparison type
   */
  private static getTypeSpecificInstructions(comparisonType: string, candidateCount: number, jobTitle: string): string {
    const option = getComparisonOption(comparisonType);
    
    if (option) {
      return interpolatePrompt(option.prompt, { count: candidateCount, jobTitle });
    }
    
    // Default detailed comparison
    return `Provide a comprehensive analysis covering all aspects: skills match, experience relevance, leadership capability, cultural fit, compensation considerations, and risk factors. This should be the most thorough comparison possible.`;
  }

  /**
   * Generate a prompt for scenario-based comparison
   */
  static generateScenarioPrompt(
    candidates: Candidate[],
    job: Job,
    scenario: string
  ): string {
    return `Given the following specific scenario: "${scenario}"

${this.generateComparisonPrompt(candidates, job, 'detailed')}

Additionally, specifically address how each candidate would handle the given scenario based on their experience and demonstrated capabilities.`;
  }

  /**
   * Generate visualization data from comparison results
   */
  static generateVisualizationData(comparisonResults: any): any {
    // Transform comparison results into chart-ready format
    const candidateAnalysis = comparisonResults.candidateAnalysis || comparisonResults.detailedComparison;
    
    const radarChartData = {
      labels: ['Skills', 'Experience', 'Leadership', 'Cultural Fit', 'Availability'],
      datasets: Object.entries(candidateAnalysis).map(([id, analysis]: [string, any]) => ({
        label: id, // This should be candidate name in real implementation
        data: [
          analysis.scores?.skills || 0,
          analysis.scores?.experience || 0,
          analysis.scores?.leadership || 0,
          analysis.scores?.culturalFit || 0,
          analysis.scores?.availability || 0
        ]
      }))
    };

    // Bar chart for overall scores
    const barChartData = {
      labels: Object.keys(candidateAnalysis),
      datasets: [{
        label: 'Overall Score',
        data: Object.values(candidateAnalysis).map((analysis: any) => {
          const scores = analysis.scores || {};
          return Object.values(scores).reduce((sum: number, score: any) => sum + score, 0) / Object.keys(scores).length;
        })
      }]
    };

    // Comparison matrix
    const comparisonMatrix = {
      headers: ['Candidate', 'Match Score', 'Key Strengths', 'Main Concerns', 'Unique Value'],
      rows: Object.entries(candidateAnalysis).map(([id, analysis]: [string, any]) => [
        id,
        analysis.matchScore || 'N/A',
        analysis.strengths?.slice(0, 2).join(', ') || 'N/A',
        analysis.weaknesses?.slice(0, 2).join(', ') || 'N/A',
        analysis.keyDifferentiator || 'N/A'
      ])
    };

    return {
      radarChartData,
      barChartData,
      comparisonMatrix
    };
  }

  /**
   * Transform raw AI response into structured comparison results
   */
  static parseAIResponse(aiResponse: string): any {
    try {
      // Remove any markdown code block markers if present
      const cleanedResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '');
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      throw new Error('Failed to parse comparison results from AI');
    }
  }
}