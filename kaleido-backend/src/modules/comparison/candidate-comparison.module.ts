import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CandidateComparison } from './entities/candidate-comparison.entity';
import { CandidateComparisonService } from './services/candidate-comparison.service';
import { CandidateComparisonController } from './controllers/candidate-comparison.controller';
import { ComparisonPromptGenerator } from './services/comparison-prompt-generator';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Job } from '@modules/job/entities/job.entity';
import { OpenAIModule } from './openai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CandidateComparison, Candidate, Job]),
    OpenAIModule
  ],
  providers: [
    CandidateComparisonService,
    ComparisonPromptGenerator
  ],
  controllers: [CandidateComparisonController],
  exports: [CandidateComparisonService]
})
export class CandidateComparisonModule {}