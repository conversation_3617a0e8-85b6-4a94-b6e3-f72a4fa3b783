import { Repository } from 'typeorm';

import { MultiAIContentService } from '@/shared/services/multi-ai-content.service';
import { OpenaiService } from '@/shared/services/openai.service';
import { ContentGeneratorService } from '@/shared/services/content-generator.service';
import { QueueService } from '@/shared/services/queue.service';
import { VideoJDStatus, VideoJDType } from '@/shared/types/video.types';
import { NotificationService } from '@modules/notification/notification.service';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SynthesiaService } from '@shared/services/synthesia.service';
import { Queue } from 'bull';
import { DigitalOceanSpacesService } from '../../shared/services/digital-ocean-spaces.service';

import { VideoProcessingService } from '../../shared/services/video-processing.service';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../../shared/utils/error-handler.util';
import { Job } from '../entities';
import { VideoConversionJobData } from '../queue/processors/video-conversion.processor';
import { CreateVideoJDDto } from './dto/create-video-jd.dto';
import { CreateVideoRecordingDto } from './dto/create-video-recording.dto';
import { UpdateVideoJDDto } from './dto/update-video-jd.dto';
import { UploadVideoRecordingDto } from './dto/upload-video-recording.dto';
import { VideoJD } from './entities/video-jd.entity';
import { SynthesiaCreatePayload } from './interfaces/synthesia.interface';

export interface VideoJDScriptSettings {
  job: Job;
  settings: Partial<VideoJD>;
}

interface JobsResponse {
  data: Job[];
  metadata: {
    total: number;
    filtered: number;
    preferences?: {
      jobTypes: string[];
      departments: string[];
      locations: string[];
      remotePreference?: string;
    };
  };
}

@Injectable()
export class VideoJDService {
  private readonly logger = new Logger(VideoJDService.name);

  constructor(
    @InjectRepository(VideoJD)
    private readonly videoJDRepository: Repository<VideoJD>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly synthesiaService: SynthesiaService,
    private readonly notificationService: NotificationService,
    private readonly openAiService: OpenaiService,
    private readonly multiAIContentService: MultiAIContentService,
    private readonly contentGeneratorService: ContentGeneratorService,
    private readonly videoProcessingService: VideoProcessingService,
    private readonly digitalOceanSpacesService: DigitalOceanSpacesService,
    private readonly queueService: QueueService,
    @InjectQueue('video-conversion')
    private readonly videoConversionQueue: Queue<VideoConversionJobData>,
  ) {}

  async saveVideoJD(
    jobId: string,
    settings: Partial<VideoJD>,
    generatedScript: string,
    clientId: string,
  ): Promise<VideoJD> {
    try {
      const videoJD = this.videoJDRepository.create({
        jobId,
        clientId,
        ...settings,
        generatedScript,
        status: settings.status ?? VideoJDStatus.PENDING,
      } as VideoJD);

      const savedVideoJD = await this.videoJDRepository.save(videoJD);

      await this.notificationService.createVideoJdGeneratingNotification(
        clientId,
        jobId,
        settings.jobType || 'position',
      );

      return savedVideoJD;
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'saveVideoJD');
    }
  }

  async generateScript({ settings }: VideoJDScriptSettings) {
    const job = await this.jobRepository.findOne({
      where: { id: settings.jobId },
    });

    if (!job) {
      ErrorHandler.handleNotFound('Job', settings?.jobId ?? '');
    }

    try {
      // Use OpenAI directly for video script generation (as requested)
      const script = await this.contentGeneratorService.generateVideoScript(
        job,
        settings.tone ?? 'professional',
        settings.languageCode ?? 'en-US',
        settings.scriptLength ?? 'medium',
      );
      settings.generatedScript = script;

      return await this.saveVideoJD(job.id, settings, script, job.clientId!);
    } catch (error) {
      ErrorHandler.handleScriptGenerationError(error, 'generateScript');
    }
  }

  async generateVideo(
    id: string,
    clientId: string,
    synthesiaPayload: SynthesiaCreatePayload,
  ): Promise<VideoJD> {
    try {
      const videoJD = await this.findOne(id);
      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', id);
      }

      const synthesiaResponse = await this.synthesiaService.createVideo(synthesiaPayload);

      videoJD.synthesiaVideoId = synthesiaResponse.id;
      videoJD.status = VideoJDStatus.GENERATING;
      videoJD.clientId = clientId;

      // Save the videoJD first
      const savedVideoJD = await this.videoJDRepository.save(videoJD);

      // Create a queue monitoring job for this Video JD
      try {
        await this.queueService.addVideoJDMonitoringTask({
          videoJDId: id,
          synthesiaVideoId: synthesiaResponse.id,
          jobId: videoJD.jobId,
          userId: clientId,
        });

        this.logger.log(
          `Queue monitoring started for Video JD ${id} with Synthesia ID ${synthesiaResponse.id}`,
        );
      } catch (queueError) {
        this.logger.error(`Failed to create queue monitoring job for Video JD ${id}:`, queueError);
        // Don't fail the entire operation if queue creation fails
        // The old polling system can still work as a fallback
      }

      return savedVideoJD;
    } catch (error) {
      ErrorHandler.handleVideoGenerationError(error, 'generateVideo');
    }
  }

  async getStatus(synthesiaVideoId: string): Promise<VideoJD> {
    try {
      const videoJD = await this.videoJDRepository.findOne({
        where: { synthesiaVideoId },
      });

      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', synthesiaVideoId);
      }

      if (videoJD.status === VideoJDStatus.GENERATING && videoJD.synthesiaVideoId) {
        const synthesiaStatus = await this.synthesiaService.getVideoStatus(
          videoJD.synthesiaVideoId,
        );

        if (synthesiaStatus.download) {
          videoJD.status = VideoJDStatus.COMPLETED;
          videoJD.videoUrl = synthesiaStatus.download || '';
          await this.videoJDRepository.save(videoJD);
        } else if (synthesiaStatus.status === 'failed') {
          videoJD.status = VideoJDStatus.FAILED;
          await this.videoJDRepository.save(videoJD);
        }
      }

      return videoJD;
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'getStatus');
    }
  }

  async handleCallback(videoId: string, status: string, url: string): Promise<VideoJD> {
    const videoJD = await this.videoJDRepository.findOne({
      where: { synthesiaVideoId: videoId },
      relations: ['job'],
    });

    if (!videoJD) {
      throw new NotFoundException('VideoJD not found');
    }

    if (status === 'complete') {
      videoJD.status = VideoJDStatus.COMPLETED;
      videoJD.videoUrl = url;

      const job = await videoJD.job;
      if (job?.clientId) {
        await this.notificationService.updateVideoJdToReadyNotification(videoJD.jobId);
      }
    } else if (status === 'failed') {
      videoJD.status = VideoJDStatus.FAILED;
    }

    return await this.videoJDRepository.save(videoJD);
  }

  async findOneWithJob(id: string): Promise<VideoJD | null> {
    return await this.videoJDRepository.findOne({
      where: { id },
      relations: ['job'],
    });
  }

  async create(createVideoJDDto: CreateVideoJDDto): Promise<VideoJD> {
    try {
      const videoJD = this.videoJDRepository.create(createVideoJDDto);
      return await this.videoJDRepository.save(videoJD);
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'create VideoJD');
    }
  }

  async findAll(): Promise<VideoJD[]> {
    const videoJDs = await this.videoJDRepository.find();
    return videoJDs.filter((videoJD) => videoJD.status !== null);
  }

  async findOne(id: string): Promise<VideoJD> {
    try {
      const videoJD = await this.videoJDRepository.findOne({ where: { id } });
      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', id);
      }
      return videoJD;
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'findOne VideoJD');
    }
  }

  async update(id: string, updateVideoJDDto: UpdateVideoJDDto): Promise<VideoJD> {
    const videoJD = await this.findOne(id);
    Object.assign(videoJD, updateVideoJDDto);
    return await this.videoJDRepository.save(videoJD);
  }

  async delete(id: string): Promise<void> {
    try {
      const videoJD = await this.videoJDRepository.find({ where: { id } });
      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', id);
      }
      await this.videoJDRepository.delete(id);
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'delete VideoJD');
    }
  }

  async findByJob(jobId: string): Promise<VideoJD[]> {
    const videoJDs = await this.videoJDRepository.find({
      where: { jobId },
      relations: ['job'],
    });
    return videoJDs.filter((videoJD) => videoJD.status === VideoJDStatus.COMPLETED);
  }

  async findAllByJobId(jobId: string): Promise<VideoJD[]> {
    // Optimized query - no relations needed since jobType and clientId are now on VideoJD
    const videoJDs = await this.videoJDRepository.find({
      where: { jobId },
      order: {
        updatedAt: 'DESC',
      },
    });
    return videoJDs;
  }

  async findAllByClientId(clientId: string): Promise<VideoJD[]> {
    const videoJDs = await this.videoJDRepository.find({
      where: { clientId },
      relations: ['job'],
      order: {
        updatedAt: 'DESC',
      },
    });
    return videoJDs.filter((videoJD) => videoJD.status === VideoJDStatus.COMPLETED);
  }

  async getJobsWithVideos(clientId: string): Promise<JobsResponse> {
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.videoJDs', 'videoJD')
      .where('job.clientId = :clientId', { clientId });

    queryBuilder.andWhere('job.jobType IS NOT NULL AND job.jobType != :emptyString', {
      emptyString: '',
    });

    queryBuilder.orderBy('job.updatedAt', 'DESC');

    const jobs = await queryBuilder.getMany();

    // Update status for all videoJDs that are generating
    const statusUpdatePromises = jobs.flatMap((job) => {
      if (!job?.videoJDs) {
        return;
      }

      return job.videoJDs
        .filter(
          (videoJD) => videoJD.status === VideoJDStatus.GENERATING && videoJD.synthesiaVideoId,
        )
        .map(async (videoJD) => {
          const updatedVideoJD = await this.getStatus(videoJD.synthesiaVideoId);
          Object.assign(videoJD, updatedVideoJD);
        });
    });

    // Wait for all status updates to complete
    await Promise.all(statusUpdatePromises);

    // Sort videoJDs within each job to show completed ones first
    jobs.forEach((job) => {
      if (job.videoJDs) {
        job.videoJDs.sort((a, b) => {
          if (a.status === VideoJDStatus.COMPLETED && b.status !== VideoJDStatus.COMPLETED)
            return -1;
          if (b.status === VideoJDStatus.COMPLETED && a.status !== VideoJDStatus.COMPLETED)
            return 1;
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        });
      }
    });

    return {
      data: jobs,
      metadata: {
        total: jobs.length,
        filtered: jobs.length,
      },
    };
  }

  async patch(id: string, patchData: Partial<VideoJD>): Promise<VideoJD> {
    try {
      const videoJD = await this.findOne(id);
      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', id);
      }

      // Only allow updating specific fields
      const allowedFields: (keyof VideoJD)[] = [
        'avatarId',
        'voiceId',
        'generatedScript',
        'voiceSpeed',
        'voicePitch',
        'tone',
        'scriptLength',
        'languageCode',
        'gender',
      ];

      // Filter out any fields that aren't in the allowed list
      const filteredPatchData = Object.keys(patchData).reduce((acc: any, key) => {
        if (allowedFields.includes(key as keyof VideoJD)) {
          acc[key] = patchData[key as keyof VideoJD];
        }
        return acc;
      }, {} as Partial<VideoJD>);

      Object.assign(videoJD, filteredPatchData);
      return await this.videoJDRepository.save(videoJD);
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'patch VideoJD');
    }
  }

  async getDownloadUrl(
    id: string,
    downloadRequest: { aspectRatio: string; format: string },
  ): Promise<{ downloadUrl: string }> {
    try {
      const videoJD = await this.findOne(id);
      if (!videoJD) {
        ErrorHandler.handleNotFound('VideoJD', id);
      }

      // Check if video is ready for download
      // For live recordings: just need videoUrl (status can be RECORDED or COMPLETED)
      // For AI-generated videos: status must be COMPLETED
      const isLiveRecording = videoJD.type === VideoJDType.LIVE_RECORDING;
      const isVideoReady = isLiveRecording
        ? !!videoJD.videoUrl
        : videoJD.status === VideoJDStatus.COMPLETED && videoJD.videoUrl;

      if (!isVideoReady) {
        this.logger.error(
          `Video not ready for download. Type: ${videoJD.type}, Status: ${videoJD.status}, VideoUrl: ${!!videoJD.videoUrl}, VideoJD ID: ${id}`,
        );
        throw new Error(
          `Video must be ready before downloading. Current status: ${videoJD.status}`,
        );
      }

      // For now, return the original video URL
      // In the future, this could be enhanced to:
      // 1. Generate different aspect ratios using video processing services
      // 2. Store multiple versions of the video
      // 3. Use a CDN to serve optimized versions

      // Log the download request for analytics

      return {
        downloadUrl: videoJD.videoUrl,
      };
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'getDownloadUrl VideoJD');
    }
  }

  async downloadForPlatform(
    id: string,
    downloadDto: any,
    userId: string,
  ): Promise<{
    downloadUrl?: string;
    filename: string;
    platformSpecs: any;
    jobId?: string;
    processing?: boolean;
  }> {
    try {
      this.logger.log(`[VIDEO_DOWNLOAD] Looking for VideoJD with ID: ${id}`);

      const videoJD = await this.findOne(id);
      if (!videoJD) {
        this.logger.error(`[VIDEO_DOWNLOAD] VideoJD with ID ${id} not found`);
        ErrorHandler.handleNotFound('VideoJD', id);
      }

      this.logger.log(
        `[VIDEO_DOWNLOAD] Found VideoJD: ${videoJD.id}, Type: ${videoJD.type}, Status: ${videoJD.status}, VideoUrl: ${!!videoJD.videoUrl}, SynthesiaId: ${!!videoJD.synthesiaVideoId}`,
      );

      // Check if video URL is provided in the request (for live recordings)
      const providedVideoUrl = downloadDto.videoUrl;
      const videoUrlToUse = providedVideoUrl || videoJD.videoUrl;

      this.logger.log(
        `[VIDEO_DOWNLOAD] Video URL sources - Provided: ${!!providedVideoUrl}, Database: ${!!videoJD.videoUrl}, Using: ${!!videoUrlToUse}`,
      );

      // Check if video is ready for download based on source
      // For videos in Digital Ocean Spaces (live recordings): use provided URL or database URL
      // For Synthesia videos: status must be COMPLETED and use database URL
      const isDigitalOceanVideo = videoUrlToUse && videoUrlToUse.includes('digitaloceanspaces.com');
      const isSynthesiaVideo = !!videoJD.synthesiaVideoId;

      let isVideoReady = false;

      if (providedVideoUrl && isDigitalOceanVideo) {
        // Live recording with provided video URL - always ready
        isVideoReady = true;
        this.logger.log(
          `[VIDEO_DOWNLOAD] Using provided video URL for live recording: ${providedVideoUrl}`,
        );
      } else if (isDigitalOceanVideo) {
        // Digital Ocean videos are ready as soon as they have a URL
        isVideoReady = !!videoJD.videoUrl;
      } else if (isSynthesiaVideo) {
        // Synthesia videos need to be completed
        isVideoReady = videoJD.status === VideoJDStatus.COMPLETED && !!videoJD.videoUrl;
      } else {
        // Fallback: check if we have a video URL
        isVideoReady = !!videoUrlToUse;
      }

      if (!isVideoReady) {
        this.logger.error(
          `Video not ready for download. Type: ${videoJD.type}, Status: ${videoJD.status}, VideoUrl: ${!!videoJD.videoUrl}, ProvidedUrl: ${!!providedVideoUrl}, SynthesiaId: ${!!videoJD.synthesiaVideoId}, VideoJD ID: ${id}`,
        );
        throw new Error(
          `Video must be ready before downloading. Current status: ${videoJD.status}`,
        );
      }

      // Import the platform specifications
      const { PLATFORM_SPECIFICATIONS } = await import('./dto/download-video.dto');

      // Type check the platform before indexing
      if (!downloadDto.platform || typeof downloadDto.platform !== 'string') {
        throw new Error('Platform is required and must be a string');
      }

      const platformSpecs =
        PLATFORM_SPECIFICATIONS[downloadDto.platform as keyof typeof PLATFORM_SPECIFICATIONS];

      if (!platformSpecs) {
        throw new Error(`Unsupported platform: ${downloadDto.platform}`);
      }

      // Generate filename
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = downloadDto.filename
        ? `${downloadDto.filename}-${downloadDto.platform}-${timestamp}.${platformSpecs.format}`
        : `video-jd-${downloadDto.platform}-${timestamp}.${platformSpecs.format}`;

      // Log the download request for analytics
      this.logger.log(`Platform download requested for VideoJD ${id}:`, {
        platform: downloadDto.platform,
        quality: downloadDto.quality,
        userId,
        platformSpecs,
        originalUrl: videoUrlToUse,
        providedUrl: providedVideoUrl,
        databaseUrl: videoJD.videoUrl,
      });

      // Check if the original video is already in the correct format
      const isWebmToMp4Conversion =
        videoUrlToUse.includes('.webm') && platformSpecs.format === 'mp4';

      if (isWebmToMp4Conversion) {
        // Check if FFmpeg is available before attempting conversion
        try {
          // Try to queue video conversion job
          const conversionJob = await this.videoConversionQueue.add('convert-video', {
            videoJDId: id,
            videoUrl: videoUrlToUse,
            platform: downloadDto.platform,
            quality: downloadDto.quality || 'high',
            filename,
            platformSpecs,
            userId,
          });

          this.logger.log(`Video conversion job queued with ID: ${conversionJob.id}`);

          return {
            filename,
            platformSpecs,
            jobId: conversionJob.id.toString(),
            processing: true,
          };
        } catch (error) {
          this.logger.warn(`Video conversion failed, returning original video: ${error.message}`);
          // Fallback to original video if conversion fails
          return {
            downloadUrl: videoUrlToUse,
            filename: filename.replace('.mp4', '.webm'), // Keep original format
            platformSpecs,
            processing: false,
          };
        }
      } else {
        // Return video URL if no conversion needed
        return {
          downloadUrl: videoUrlToUse,
          filename,
          platformSpecs,
          processing: false,
        };
      }
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'downloadForPlatform VideoJD');
    }
  }

  /**
   * Simplified video processing that updates VideoJD directly
   * This method processes video and updates the VideoJD record with the processed video URL
   */
  async processVideoAndUpdateJD(
    videoJDId: string,
    platformSpecs: any,
    options: any,
    uploadService: any,
    onProgress?: (progress: number) => void,
  ): Promise<{ downloadUrl: string; filename: string }> {
    try {
      this.logger.log(`[VIDEO_PROCESS] Starting processing for VideoJD: ${videoJDId}`);

      // Get the VideoJD record
      const videoJD = await this.findOne(videoJDId);
      if (!videoJD) {
        throw new NotFoundException(`VideoJD with ID ${videoJDId} not found`);
      }

      if (!videoJD.videoUrl) {
        throw new Error('No video URL found in VideoJD record');
      }

      onProgress?.(5);

      // Process and upload the video
      const result = await this.videoProcessingService.processAndUploadVideo(
        videoJD.videoUrl,
        platformSpecs,
        options,
        uploadService,
        `processed-videos/${options.platform}`,
        onProgress,
      );

      // Update VideoJD with processed video information
      if (result.uploadedUrl) {
        // Store the processed video metadata in recordingSettings
        const updatedSettings = {
          ...(videoJD.recordingSettings || {}),
          lastProcessedVideo: {
            platform: options.platform,
            url: result.uploadedUrl,
            filename: result.filename,
            processedAt: new Date().toISOString(),
            specs: platformSpecs,
          },
        };

        await this.videoJDRepository.update(videoJDId, {
          recordingSettings: updatedSettings,
        });

        this.logger.log(`[VIDEO_PROCESS] VideoJD ${videoJDId} updated with processed video URL`);
      }

      return {
        downloadUrl: result.uploadedUrl || videoJD.videoUrl,
        filename: result.filename,
      };
    } catch (error) {
      this.logger.error(`[VIDEO_PROCESS] Failed to process video for VideoJD ${videoJDId}:`, error);
      throw error;
    }
  }

  async getVideoConversionStatus(jobId: string): Promise<{
    status: string;
    progress: number;
    result?: any;
    error?: string;
    message?: string;
  }> {
    try {
      const job = await this.videoConversionQueue.getJob(jobId);

      if (!job) {
        return {
          status: 'not_found',
          progress: 0,
          error: 'Conversion job not found',
          message: 'Video conversion job no longer exists on server',
        };
      }

      const state = await job.getState();
      const progress = job.progress();

      // Map Bull state to our status format
      let status = 'queued';
      switch (state) {
        case 'active':
          status = 'active';
          break;
        case 'completed':
          status = 'completed';
          break;
        case 'failed':
          status = 'failed';
          break;
        case 'delayed':
        case 'waiting':
          status = 'queued';
          break;
        default:
          status = state;
      }

      let result = null;
      let error: string | undefined = undefined;
      let message: string | undefined = undefined;

      if (status === 'completed') {
        result = job.returnvalue;
        message = 'Video conversion completed successfully';
      } else if (status === 'failed') {
        error = job.failedReason || 'Video conversion failed';
        message = error;
      } else if (status === 'active') {
        message = `Converting video... ${Math.round(progress || 0)}% complete`;
      } else if (status === 'queued') {
        message = 'Video conversion queued, waiting to start...';
      }

      return {
        status,
        progress: typeof progress === 'number' ? progress : 0,
        result,
        error,
        message,
      };
    } catch (error) {
      this.logger.error(`Failed to get conversion job status: ${error.message}`);
      return {
        status: 'error',
        progress: 0,
        error: error.message,
        message: 'Failed to get video conversion status',
      };
    }
  }

  // New methods for video recording functionality
  async createVideoRecording(
    createVideoRecordingDto: CreateVideoRecordingDto,
    clientId: string,
    avatarId?: string,
  ): Promise<VideoJD> {
    try {
      this.logger.log(
        `[PERF] Starting FIXED createVideoRecording for jobId: ${createVideoRecordingDto.jobId}`,
      );
      const startTime = Date.now();

      // FIXED: Use relations: [] to disable eager loading that was causing 44+ second delays
      this.logger.log(`[PERF] Fetching job from database (without eager loading)...`);
      const job = await this.jobRepository.findOne({
        where: { id: createVideoRecordingDto.jobId },
        relations: [], // Disable eager loading of candidates, videoResponses, etc.
      });
      this.logger.log(`[PERF] Job fetch completed in ${Date.now() - startTime}ms`);

      if (!job) {
        throw new NotFoundException(`Job with ID ${createVideoRecordingDto.jobId} not found`);
      }

      this.logger.log(`[PERF] Creating videoJD entity...`);
      const videoJD = this.videoJDRepository.create({
        ...createVideoRecordingDto,
        clientId, // Set the authenticated user's ID
        avatarId: avatarId || clientId, // Use Auth0 sub as avatarId, fallback to clientId
        status: VideoJDStatus.SCRIPT_GENERATED,
        type: VideoJDType.LIVE_RECORDING,
      });
      this.logger.log(`[PERF] VideoJD entity created in ${Date.now() - startTime}ms`);

      this.logger.log(`[PERF] Saving videoJD to database...`);
      const savedVideoJD = await this.videoJDRepository.save(videoJD);
      this.logger.log(`[PERF] VideoJD saved in ${Date.now() - startTime}ms`);

      // Create notification asynchronously to avoid blocking the response
      this.logger.log(`[PERF] Starting async notification creation...`);
      setImmediate(() => {
        this.notificationService
          .createVideoJdGeneratingNotification(
            clientId,
            createVideoRecordingDto.jobId,
            job.jobType || 'position',
          )
          .catch((error) => {
            this.logger.error('Failed to create video JD notification:', error);
          });
      });

      this.logger.log(`[PERF] FIXED createVideoRecording completed in ${Date.now() - startTime}ms`);
      return savedVideoJD;
    } catch (error) {
      this.logger.error(`[PERF] createVideoRecording failed: ${error.message}`);
      ErrorHandler.handleDatabaseError(error, 'createVideoRecording');
    }
  }

  async startRecording(videoJDId: string): Promise<VideoJD> {
    try {
      const videoJD = await this.videoJDRepository.findOne({
        where: { id: videoJDId },
      });

      if (!videoJD) {
        throw new NotFoundException(`VideoJD with ID ${videoJDId} not found`);
      }

      videoJD.status = VideoJDStatus.RECORDING;
      return await this.videoJDRepository.save(videoJD);
    } catch (error) {
      ErrorHandler.handleDatabaseError(error, 'startRecording');
    }
  }

  async uploadRecordedVideo(uploadDto: UploadVideoRecordingDto): Promise<VideoJD> {
    try {
      this.logger.log(`[VIDEO_UPLOAD] Looking for VideoJD with ID: ${uploadDto.videoJDId}`);

      const videoJD = await this.videoJDRepository.findOne({
        where: { id: uploadDto.videoJDId },
      });

      if (!videoJD) {
        this.logger.error(`[VIDEO_UPLOAD] VideoJD with ID ${uploadDto.videoJDId} not found`);
        throw new NotFoundException(`VideoJD with ID ${uploadDto.videoJDId} not found`);
      }

      this.logger.log(
        `[VIDEO_UPLOAD] Found VideoJD: ${videoJD.id}, Type: ${videoJD.type}, Status: ${videoJD.status}`,
      );

      videoJD.videoUrl = uploadDto.videoUrl;
      videoJD.status = VideoJDStatus.COMPLETED;

      if (uploadDto.actualDuration) {
        videoJD.recordingDuration = uploadDto.actualDuration;
      }

      const savedVideoJD = await this.videoJDRepository.save(videoJD);

      this.logger.log(
        `[VIDEO_UPLOAD] VideoJD updated successfully: ${savedVideoJD.id}, Status: ${savedVideoJD.status}, VideoUrl: ${!!savedVideoJD.videoUrl}`,
      );

      return savedVideoJD;
    } catch (error) {
      this.logger.error(`[VIDEO_UPLOAD] Error in uploadRecordedVideo: ${error.message}`);
      ErrorHandler.handleDatabaseError(error, 'uploadRecordedVideo');
    }
  }

  async completeRecordingWithUpload(
    videoJDId: string,
    file: Express.Multer.File,
    metadata: {
      actualDuration?: number;
      fileSize?: number;
      format?: string;
    },
    user?: any,
  ): Promise<VideoJD> {
    try {
      this.logger.log(
        `[COMPLETE_RECORDING] Starting upload and completion for VideoJD: ${videoJDId}`,
      );

      // Upload the file to Digital Ocean Spaces
      const path = 'candidate-videos';
      const videoUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      this.logger.log(`[COMPLETE_RECORDING] File uploaded to: ${videoUrl}`);

      // Update the VideoJD record with the video URL and completion status
      const uploadDto: UploadVideoRecordingDto = {
        videoJDId,
        videoUrl,
        actualDuration: metadata.actualDuration,
        fileSize: metadata.fileSize || file.size,
        format: metadata.format || file.mimetype,
      };

      const updatedVideoJD = await this.uploadRecordedVideo(uploadDto);

      // Create completion notification if user is provided
      if (user) {
        await this.notificationService.createNotification({
          clientId: user.userId,
          type: 'VIDEO_JD_READY' as any,
          title: 'Video Recording Completed',
          message: 'Your video recording has been successfully uploaded and processed.',
          jobId: updatedVideoJD.jobId,
        });
      }

      this.logger.log(
        `[COMPLETE_RECORDING] Recording completed successfully for VideoJD: ${updatedVideoJD.id}`,
      );

      return updatedVideoJD;
    } catch (error) {
      this.logger.error(`[COMPLETE_RECORDING] Error completing recording: ${error.message}`);
      ErrorHandler.handleDatabaseError(error, 'completeRecordingWithUpload');
    }
  }
}
