import { Repository } from 'typeorm';

import { User } from '@/shared/decorators/get-user.decorator';
import { OpenaiService } from '@/shared/services/openai.service';
import { ApplicationStatus } from '@/shared/types';
import { JobStatus as JobStatusType } from '@/shared/types/job.types';
import { CandidateEvaluation } from '@modules/candidate/entities/candidate-evaluation.entity';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { VideoResponse } from '@modules/video-response/entities/video-response.entity';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CandidateStatus } from '@shared/types/candidate.types';
import {
  countCandidatesForJob,
  getCandidateJobWhereClause,
} from '@shared/utils/candidate-job-query.util';
import { determineCandidateTier, normalizeJobThresholds } from '@shared/utils/threshold.util';

import { CompanyService } from '../company/company.service';
import { EnhancedJobPublishingService } from '../company/services/enhanced-job-publishing.service';
import { SocialMediaPlatform } from '../company/types/social-media-connector.types';
import { LinkedInService } from '../vendors/linkedin/linkedin.service';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { Job } from './entities/job.entity';
import { JobAiHelpers } from './job.ai.helpers';
import { JobCandidatesHelpers } from './job.candidates.helpers';
import { JobCrudUtils } from './job.crud.utils';
import { JobMatchRankHelper } from './job.matchrank.helper';
import { JobsResponse, MatchRankResponse, PaginationOptions, PublicJobFilters } from './jobs.types';

@Injectable()
export class JobService {
  private readonly logger = new Logger(JobService.name);

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(CandidateEvaluation)
    private readonly candidateEvaluationRepository: Repository<CandidateEvaluation>,
    @InjectRepository(VideoResponse)
    private readonly videoResponseRepository: Repository<VideoResponse>,
    private readonly companyService: CompanyService,
    private readonly openAiService: OpenaiService,
    private readonly linkedInService: LinkedInService,
    private readonly jobMatchRankHelper: JobMatchRankHelper,
    private readonly jobAiHelpers: JobAiHelpers,
    private readonly jobCrudUtils: JobCrudUtils,
    private readonly jobCandidatesHelpers: JobCandidatesHelpers,
    private readonly enhancedJobPublishingService: EnhancedJobPublishingService,
  ) {}

  async create(createJobDto: CreateJobDto, authUser: User): Promise<Job> {
    // Check if id exists and is not empty
    if (createJobDto.id && createJobDto.id.trim() !== '') {
      const job = await this.jobRepository.findOne({
        where: { clientId: createJobDto.clientId || authUser.userId, id: createJobDto.id },
      });
      if (!job) {
        throw new Error('Job not found');
      }

      const { cultureFitQuestions, ...jobData } = createJobDto;
      const updateData = {
        ...jobData,
        cultureFitQuestions: cultureFitQuestions?.map((question) =>
          typeof question === 'string' ? ({ question } as any) : question,
        ),
      };

      await this.jobRepository.update(job.id, updateData);
      return job;
    }

    // Find the company associated with the user
    const company = await this.companyService.findByClientId(authUser.userId);
    if (company) {
      // Set company-related fields
      createJobDto.companyName = company.companyName;
      createJobDto.companyDescription = company.description || '';

      // Ensure clientId is set to the user's ID
      createJobDto.clientId = authUser.userId;
    } else {
      this.logger.warn(
        `No company found for user ${authUser.userId}, creating job without company association`,
      );
    }

    const { cultureFitQuestions, ...jobData } = createJobDto;

    // Clean up empty string fields that should be null/undefined
    const cleanedJobData = Object.entries(jobData).reduce((acc, [key, value]) => {
      // Skip empty strings for UUID fields
      if ((key === 'id' || key === 'clientId') && value === '') {
        return acc;
      }
      acc[key] = value;
      return acc;
    }, {} as any);

    const createJob = this.jobRepository.create({
      ...cleanedJobData,
      clientId: authUser.userId, // Always use the authenticated user's ID
      cultureFitQuestions: cultureFitQuestions?.map((question) =>
        typeof question === 'string' ? ({ question } as any) : question,
      ),
    } as Job) as unknown as Job;

    // Generate a slug for the job
    createJob.slug = this.generateSlug(createJob.jobType);

    // Save the job first to get an ID and return quickly
    const savedJob = await this.jobRepository.save(createJob);

    // Schedule AI generation tasks asynchronously (don't wait for them)
    this.scheduleInitialAIGeneration(savedJob);

    return savedJob;
  }

  // Helper method to generate a slug
  private generateSlug(title: string): string {
    // Base slug from title
    let slug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with a single one
      .trim();

    // Add a timestamp to ensure uniqueness
    slug = `${slug}-${Date.now().toString().slice(-8)}`;

    return slug;
  }

  /**
   * Checks if critical job criteria have changed that would require re-evaluation of candidates
   * @param oldJob Previous state of the job
   * @param updateData Update data being applied
   * @returns boolean indicating if critical criteria have changed
   */
  private hasCriticalCriteriaChanged(oldJob: Job, updateData: UpdateJobDto): boolean {
    // Only check criteria that would affect the actual matching algorithm
    // and are available in the UpdateJobDto
    return (
      (updateData.skills !== undefined &&
        JSON.stringify(updateData.skills) !== JSON.stringify(oldJob.skills)) ||
      (updateData.requirements !== undefined &&
        JSON.stringify(updateData.requirements) !== JSON.stringify(oldJob.requirements)) ||
      (updateData.finalDraft !== undefined && updateData.finalDraft !== oldJob.finalDraft) ||
      (updateData.jobType !== undefined && updateData.jobType !== oldJob.jobType) ||
      (updateData.department !== undefined && updateData.department !== oldJob.department)
      // Note: jobResponsibilities, softSkills, and experienceLevel are not available in UpdateJobDto
      // Threshold changes don't affect match scores, only tier assignment
    );
  }

  async findAll(): Promise<JobsResponse> {
    return this.jobCrudUtils.findAll();
  }

  async findByClientId(
    userId: string,
    relations?: string[],
    requirePublished: boolean = false,
  ): Promise<JobsResponse> {
    return this.jobCrudUtils.findByClientId(userId, relations, requirePublished);
  }

  async findOne(id: string, relations?: string[], forceFresh: boolean = false): Promise<Job> {
    return this.jobCrudUtils.findOne(id, relations, forceFresh);
  }

  async findBySlug(slug: string): Promise<Job> {
    return this.jobCrudUtils.findBySlug(slug);
  }

  async findByJobId(
    id: string,
    relations?: string[],
    requirePublished: boolean = false,
  ): Promise<Job> {
    return this.jobCrudUtils.findByJobId(id, relations, requirePublished);
  }

  async update(id: string, updateJobDto: UpdateJobDto): Promise<Job> {
    // First verify the job exists with minimal relations
    const existingJob = await this.jobRepository.findOne({
      where: { id },
      relations: ['candidateEvaluations'], // Only load what we need for tier updates
    });

    if (!existingJob) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Store threshold values before update for comparison
    const thresholdsBeforeUpdate = {
      topCandidateThreshold: existingJob.topCandidateThreshold,
      secondTierCandidateThreshold: existingJob.secondTierCandidateThreshold,
    };

    // Check if thresholds are actually being changed
    const thresholdsChanged =
      (updateJobDto.topCandidateThreshold !== undefined &&
        updateJobDto.topCandidateThreshold !== existingJob.topCandidateThreshold) ||
      (updateJobDto.secondTierCandidateThreshold !== undefined &&
        updateJobDto.secondTierCandidateThreshold !== existingJob.secondTierCandidateThreshold);

    // Check if critical criteria have changed that would require re-evaluation
    // We need to compare the existing job with the potential updates
    const criticalCriteriaChanged = this.hasCriticalCriteriaChanged(existingJob, updateJobDto);

    // Prepare update data
    const processedUpdateData = { ...updateJobDto };

    // Handle cultureFitQuestions transformation if present
    if (processedUpdateData.cultureFitQuestions) {
      processedUpdateData.cultureFitQuestions = processedUpdateData.cultureFitQuestions.map(
        (question) => (typeof question === 'string' ? ({ question } as any) : question),
      );
    }

    // If job title (jobType) changed or slug doesn't exist, generate a new slug
    if (
      updateJobDto.jobType &&
      (updateJobDto.jobType !== existingJob.jobType || !existingJob.slug)
    ) {
      processedUpdateData.slug = this.generateSlug(updateJobDto.jobType);
    }

    // Apply the updates to the existing job
    Object.assign(existingJob, processedUpdateData);

    // Perform the database update
    await this.jobRepository.save(existingJob);

    // Schedule AI generation tasks asynchronously (don't wait for them)
    this.scheduleAIGenerationTasks(existingJob, criticalCriteriaChanged, updateJobDto);

    // Handle candidate updates if needed
    await this.handleCandidateUpdates(
      id,
      thresholdsBeforeUpdate,
      criticalCriteriaChanged,
      thresholdsChanged,
    );

    // Return the updated job without refetching (for performance)
    return existingJob;
  }

  /**
   * Update only MatchRank criteria (thresholds/requirements) without any side effects
   * No AI generation, no candidate modifications, minimal database operations
   */
  async updateMatchRankCriteriaOnly(
    id: string,
    updateData: {
      topCandidateThreshold?: number;
      secondTierCandidateThreshold?: number;
      requirements?: string[];
    },
  ): Promise<Job> {
    // Verify the job exists (minimal query)
    const existingJob = await this.jobRepository.findOne({
      where: { id },
      // No relations needed for this operation
    });

    if (!existingJob) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Only update the specific fields that are allowed for MatchRank
    const allowedUpdates: Partial<Job> = {};

    if (updateData.topCandidateThreshold !== undefined) {
      allowedUpdates.topCandidateThreshold = updateData.topCandidateThreshold;
    }

    if (updateData.secondTierCandidateThreshold !== undefined) {
      allowedUpdates.secondTierCandidateThreshold = updateData.secondTierCandidateThreshold;
    }

    if (updateData.requirements !== undefined) {
      allowedUpdates.requirements = updateData.requirements;
    }

    // Perform minimal database update
    if (Object.keys(allowedUpdates).length > 0) {
      await this.jobRepository.update(id, allowedUpdates);

      // Apply updates to the existing job object for return
      Object.assign(existingJob, allowedUpdates);

      this.logger.log(
        `MatchRank criteria updated for job ${id}: ${Object.keys(allowedUpdates).join(', ')}`,
      );
    }

    // Return the updated job immediately (no refetch, no AI calls, no candidate updates)
    return existingJob;
  }

  /**
   * Update only culture fit questions and description without any side effects
   * No AI generation, no candidate modifications, minimal database operations
   */
  async updateCultureFitOnly(
    id: string,
    updateData: {
      cultureFitQuestions?: any[];
      cultureFitDescription?: string;
    },
  ): Promise<Job> {
    // Verify the job exists (minimal query)
    const existingJob = await this.jobRepository.findOne({
      where: { id },
      // No relations needed for this operation
    });

    if (!existingJob) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Only allow culture fit fields to be updated
    const allowedUpdates: Partial<Job> = {};

    if (updateData.cultureFitQuestions !== undefined) {
      allowedUpdates.cultureFitQuestions = updateData.cultureFitQuestions;
    }

    if (updateData.cultureFitDescription !== undefined) {
      allowedUpdates.cultureFitDescription = updateData.cultureFitDescription;
    }

    // Perform minimal database update
    if (Object.keys(allowedUpdates).length > 0) {
      await this.jobRepository.update(id, allowedUpdates);

      // Apply updates to the existing job object for return
      Object.assign(existingJob, allowedUpdates);

      this.logger.log(
        `Culture fit updated for job ${id}: ${Object.keys(allowedUpdates).join(', ')}`,
      );
    }

    // Return the updated job immediately (no refetch, no AI calls, no candidate updates)
    return existingJob;
  }

  /**
   * Schedule initial AI generation tasks for newly created jobs
   */
  private scheduleInitialAIGeneration(job: Job): void {
    // Run AI tasks asynchronously without waiting
    setImmediate(async () => {
      try {
        let needsUpdate = false;
        const updates: Partial<Job> = {};

        // Only generate TLDR if we have meaningful content
        const hasContent =
          job.finalDraft ||
          job.companyDescription ||
          (job.jobResponsibilities && job.jobResponsibilities.length > 0);

        if (hasContent) {
          const tldr = await this.openAiService.generateJobTLDR(job);
          if (tldr) {
            updates.tldr = tldr;
            needsUpdate = true;
          }

          // Generate social media description
          const socialMediaDescription =
            await this.openAiService.generateSocialMediaDescription(job);
          if (socialMediaDescription) {
            updates.socialMediaDescription = socialMediaDescription;
            needsUpdate = true;
          }
        }

        // Update the job with AI-generated content if any was created
        if (needsUpdate) {
          await this.jobRepository.update(job.id, updates);
          this.logger.log(`Initial AI content generated for job ${job.id}`);
        }
      } catch (error) {
        this.logger.error(`Failed to generate initial AI content for job ${job.id}:`, error);
      }
    });
  }

  /**
   * Schedule AI generation tasks to run asynchronously without blocking the response
   */
  private scheduleAIGenerationTasks(
    job: Job,
    criticalCriteriaChanged: boolean,
    updateJobDto: UpdateJobDto,
  ): void {
    // Run AI tasks asynchronously without waiting
    setImmediate(async () => {
      try {
        let needsUpdate = false;
        const updates: Partial<Job> = {};

        // Always regenerate TLDR (but don't block the response)
        const tldr = await this.openAiService.generateJobTLDR(job);
        if (tldr) {
          updates.tldr = tldr;
          needsUpdate = true;
        }

        // Regenerate social media description if key fields changed
        if (
          criticalCriteriaChanged ||
          updateJobDto.companyDescription !== undefined ||
          updateJobDto.salaryRange !== undefined
        ) {
          const socialMediaDescription =
            await this.openAiService.generateSocialMediaDescription(job);
          if (socialMediaDescription) {
            updates.socialMediaDescription = socialMediaDescription;
            needsUpdate = true;
          }
        }

        // Update the job with AI-generated content if any was created
        if (needsUpdate) {
          await this.jobRepository.update(job.id, updates);
          this.logger.log(`AI content updated for job ${job.id}`);
        }
      } catch (error) {
        this.logger.error(`Failed to generate AI content for job ${job.id}:`, error);
      }
    });
  }

  /**
   * Handle candidate updates efficiently with batch operations
   */
  private async handleCandidateUpdates(
    jobId: string,
    thresholdsBeforeUpdate: {
      topCandidateThreshold?: number;
      secondTierCandidateThreshold?: number;
    },
    criticalCriteriaChanged: boolean,
    thresholdsChanged: boolean,
  ): Promise<void> {
    // Get the updated job data
    const updatedJob = await this.jobRepository.findOne({ where: { id: jobId } });
    if (!updatedJob) {
      return;
    }

    // Debug logging to understand what's triggering updates
    this.logger.debug(`Checking candidate updates for job ${jobId}:
      - Critical criteria changed: ${criticalCriteriaChanged}
      - Thresholds changed: ${thresholdsChanged}
      - Old thresholds: top=${thresholdsBeforeUpdate.topCandidateThreshold}, second=${thresholdsBeforeUpdate.secondTierCandidateThreshold}
      - New thresholds: top=${updatedJob.topCandidateThreshold}, second=${updatedJob.secondTierCandidateThreshold}
    `);

    // If critical criteria changed, update all matched candidates to MATCH_MODIFIED status
    if (criticalCriteriaChanged) {
      this.logger.log(
        `Critical job criteria changed for job ${jobId}, updating candidate statuses to MATCH_MODIFIED`,
      );

      // Use batch updates for better performance
      await Promise.all([
        this.candidateRepository.update(
          { jobId, status: CandidateStatus.MATCHED },
          { status: CandidateStatus.MATCH_MODIFIED },
        ),
        this.candidateEvaluationRepository.update(
          { jobId, status: CandidateStatus.MATCHED },
          { status: CandidateStatus.MATCH_MODIFIED },
        ),
      ]);

      this.logger.log(`Updated candidates to MATCH_MODIFIED status for job ${jobId}`);
    }
    // If only thresholds changed, update candidate tiers efficiently
    else if (thresholdsChanged) {
      // Only update if we have valid threshold values
      if (
        updatedJob.topCandidateThreshold != null &&
        updatedJob.secondTierCandidateThreshold != null
      ) {
        await this.updateCandidateTiersEfficiently(jobId, updatedJob);
      } else {
        this.logger.warn(
          `Skipping tier update for job ${jobId}: Invalid threshold values (top=${updatedJob.topCandidateThreshold}, second=${updatedJob.secondTierCandidateThreshold})`,
        );
      }
    }
  }

  /**
   * Update candidate tiers efficiently using batch SQL operations
   */
  private async updateCandidateTiersEfficiently(jobId: string, updatedJob: Job): Promise<void> {
    this.logger.log(`Threshold values changed for job ${jobId}, updating candidate tiers`);

    const topTierThreshold = updatedJob.topCandidateThreshold;
    const secondTierThreshold = updatedJob.secondTierCandidateThreshold;

    // Use raw SQL for efficient batch updates
    const queryRunner = this.jobRepository.manager.connection.createQueryRunner();

    try {
      await queryRunner.startTransaction();

      // Update candidate evaluations tiers based on match scores
      await queryRunner.query(
        `
        UPDATE candidate_evaluations
        SET tier = CASE
          WHEN "matchScore" >= $1 THEN 'TOP'::candidate_tier_enum
          WHEN "matchScore" >= $2 THEN 'SECOND'::candidate_tier_enum
          ELSE 'OTHER'::candidate_tier_enum
        END
        WHERE "jobId" = $3 AND "matchScore" IS NOT NULL
      `,
        [topTierThreshold, secondTierThreshold, jobId],
      );

      // Update candidates tiers to match their evaluations
      // Note: candidates table uses candidates_tier_enum (plural) while candidate_evaluations uses candidate_tier_enum (singular)
      await queryRunner.query(
        `
        UPDATE candidates
        SET tier = ce.tier::text::candidates_tier_enum
        FROM candidate_evaluations ce
        WHERE candidates.id = ce."candidateId"
        AND ce."jobId" = $1
        AND ce."matchScore" IS NOT NULL
      `,
        [jobId],
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Updated candidate tiers based on new thresholds for job ${jobId}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update candidate tiers for job ${jobId}:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: string): Promise<void> {
    const job = await this.jobRepository.findOne({
      where: { id },
      relations: [
        'candidates',
        'videoResponses',
        'videoJDs',
        'notifications',
        'candidates.videoResponses',
      ],
    });

    if (!job) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Start a transaction to ensure all related entities are deleted
    await this.jobRepository.manager.transaction(async (transactionalEntityManager) => {
      // Delete related entities first
      if (job.candidates) {
        for (const candidate of job.candidates) {
          if (candidate.videoResponses) {
            await transactionalEntityManager.remove(candidate.videoResponses);
          }
          await transactionalEntityManager.remove(candidate);
        }
      }

      if (job.videoJDs) {
        await transactionalEntityManager.remove(job.videoJDs);
      }

      if (job.videoResponses) {
        await transactionalEntityManager.remove(job.videoResponses);
      }

      if (job.notifications) {
        await transactionalEntityManager.remove(job.notifications);
      }

      // Finally delete the job
      await transactionalEntityManager.remove(job);
    });
  }

  /**
   * Updates the job application status to match the candidate status
   * This ensures that the job application status is always in sync with the candidate status
   */
  async updateJobApplicationStatus(
    jobId: string,
    jobSeekerId: string,
    candidateStatus: CandidateStatus,
  ): Promise<void> {
    if (!jobSeekerId || !jobId) {
      return; // Skip if we don't have both IDs
    }

    try {
      // Find the job application
      const application = await this.jobApplicationRepository.findOne({
        where: { jobId, jobSeekerId },
      });

      if (!application) {
        return;
      }

      // Map candidate status to application status
      let applicationStatus: ApplicationStatus;

      switch (candidateStatus) {
        case CandidateStatus.HIRED:
          applicationStatus = ApplicationStatus.HIRED;
          break;
        case CandidateStatus.REJECTED:
          applicationStatus = ApplicationStatus.REJECTED;
          break;
        case CandidateStatus.WITHDRAWN:
          applicationStatus = ApplicationStatus.WITHDRAWN;
          break;
        case CandidateStatus.SHORTLISTED:
          applicationStatus = ApplicationStatus.SHORTLISTED;
          break;
        case CandidateStatus.INTERVIEWING:
        case CandidateStatus.OFFER_PENDING_APPROVAL:
        case CandidateStatus.OFFER_APPROVED:
        case CandidateStatus.OFFER_EXTENDED:
        case CandidateStatus.OFFER_ACCEPTED:
        case CandidateStatus.HIRE_PENDING_APPROVAL:
        case CandidateStatus.HIRE_APPROVED:
          applicationStatus = ApplicationStatus.REVIEWING;
          break;
        default:
          applicationStatus = ApplicationStatus.APPLIED;
      }

      // Update the application status
      await this.jobApplicationRepository.update(application.id, {
        status: applicationStatus,
      });
    } catch (error) {
      console.error('Error updating job application status:', error);
    }
  }

  async findJobWithCandidates(jobId: string): Promise<Job> {
    return this.jobCrudUtils.findJobWithCandidates(jobId);
  }

  async findCandidates(jobId: string) {
    // First get the job with candidates and their video responses
    return this.jobCandidatesHelpers.findCandidates(jobId);
  }

  async getCandidatesCount(jobId: string): Promise<number> {
    // Use utility function for consistent candidate-job queries
    return countCandidatesForJob(this.candidateRepository, jobId);
  }

  async getUnevaluatedCandidateCount(jobId: string): Promise<number> {
    // Use a lightweight query to count unevaluated candidates with utility function
    const count = await this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoin(
        'candidate_evaluations',
        'ce',
        'ce.candidateId = candidate.id AND ce.jobId = :jobId',
        { jobId },
      )
      .where(getCandidateJobWhereClause(), { jobId })
      .andWhere('ce.id IS NULL')
      .getCount();

    return count;
  }

  async updateCandidateRanks(jobId: string, candidateRanks: Array<{ id: string; rank: number }>) {
    return this.jobCandidatesHelpers.updateCandidateRanks(jobId, candidateRanks);
  }

  async toggleJobPublication(jobId: string, platforms: string[]) {
    const job = await this.findOne(jobId);
    const results = [];

    // Check if the job already has a clientId set
    if (!job.clientId) {
      this.logger.warn(
        `Job ${jobId} has no clientId set. This may cause foreign key constraint errors.`,
      );
    }

    // Separate social media platforms from jobboard
    const socialPlatforms: SocialMediaPlatform[] = [];
    const otherPlatforms: string[] = [];

    for (const platform of platforms) {
      if (Object.values(SocialMediaPlatform).includes(platform as SocialMediaPlatform)) {
        socialPlatforms.push(platform as SocialMediaPlatform);
      } else {
        otherPlatforms.push(platform);
      }
    }

    // Handle social media platforms using enhanced publishing service
    if (socialPlatforms.length > 0) {
      try {
        const socialResults = await this.enhancedJobPublishingService.publishJobToPlatforms(
          jobId,
          socialPlatforms,
        );
        results.push(...socialResults);
      } catch (error: any) {
        this.logger.error('Enhanced publishing failed:', error);
        // Add failed results for each social platform
        socialPlatforms.forEach((platform) => {
          results.push({
            platform,
            status: 'failed',
            error: error.message,
            timestamp: new Date(),
          });
        });
      }
    }

    // Handle other platforms (like jobboard)
    for (const platform of otherPlatforms) {
      try {
        if (platform === 'linkedin') {
          // For LinkedIn, we need to implement a proper publish method
          // Since we don't have direct support for publishing posts yet,
          // we'll return a status indicating manual action is needed
          results.push({
            platform,
            status: 'warning',
            message: 'LinkedIn publishing must be done manually at this time',
          });
        } else if (platform === 'jobboard') {
          // Toggle the publication status for our job board
          const wasPublished = job.isPublished;
          job.isPublished = !wasPublished;

          if (job.isPublished) {
            // Publishing: Set status to OPEN to make it visible
            job.status = JobStatusType.OPEN;
          } else {
            // Unpublishing: Only change status if it's currently OPEN (don't affect other statuses)
            if (job.status === JobStatusType.OPEN) {
              job.status = JobStatusType.NEW;
            }
          }

          try {
            await this.jobRepository.save(job);
          } catch (saveError: any) {
            this.logger.error(
              `Error saving job during publication toggle: ${saveError.message}`,
              saveError.stack,
            );
            throw new Error(`Failed to toggle job publication: ${saveError.message}`);
          }

          results.push({
            platform: 'jobboard',
            status: 'success',
            message: job.isPublished
              ? 'Published to our job board'
              : 'Unpublished from our job board',
            isPublished: job.isPublished,
          });
        }
      } catch (error: any) {
        results.push({
          platform,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return results;
  }

  async publishJob(jobId: string, platforms: string[]) {
    const job = await this.findOne(jobId);
    const results = [];

    // Check if the job already has a clientId set
    if (!job.clientId) {
      this.logger.warn(
        `Job ${jobId} has no clientId set. This may cause foreign key constraint errors.`,
      );
    }

    // Separate social media platforms from jobboard
    const socialPlatforms: SocialMediaPlatform[] = [];
    const otherPlatforms: string[] = [];

    for (const platform of platforms) {
      if (Object.values(SocialMediaPlatform).includes(platform as SocialMediaPlatform)) {
        socialPlatforms.push(platform as SocialMediaPlatform);
      } else {
        otherPlatforms.push(platform);
      }
    }

    // Handle social media platforms using enhanced publishing service
    if (socialPlatforms.length > 0) {
      try {
        const socialResults = await this.enhancedJobPublishingService.publishJobToPlatforms(
          jobId,
          socialPlatforms,
        );
        results.push(...socialResults);
      } catch (error: any) {
        this.logger.error('Enhanced publishing failed:', error);
        // Add failed results for each social platform
        socialPlatforms.forEach((platform) => {
          results.push({
            platform,
            status: 'failed',
            error: error.message,
            timestamp: new Date(),
          });
        });
      }
    }

    // Handle other platforms (like jobboard) with existing logic
    for (const platform of otherPlatforms) {
      try {
        if (platform === 'linkedin') {
          // Fallback to old LinkedIn service if not using connector
          const result = await this.linkedInService.createPost(job);
          const { status: resultStatus, ...restOfResult } = result;
          results.push({
            platform,
            status: 'success',
            linkedinStatus: resultStatus,
            ...restOfResult,
          });
        } else if (platform === 'jobboard') {
          // Check if the job is already published to avoid unnecessary saves
          if (!job.isPublished || job.status !== JobStatusType.OPEN) {
            // Update the job to set isPublished to true for our job board
            job.isPublished = true;
            job.status = JobStatusType.OPEN; // Set status to OPEN to make it visible

            try {
              await this.jobRepository.save(job);
            } catch (saveError: any) {
              this.logger.error(
                `Error saving job during publish: ${saveError.message}`,
                saveError.stack,
              );
              throw new Error(`Failed to publish job: ${saveError.message}`);
            }
          }

          results.push({
            platform: 'jobboard',
            status: 'success',
            message: 'Published to our job board',
          });
        }
      } catch (error: any) {
        results.push({
          platform,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return results;
  }

  async generateResponsibilities(jobId: string) {
    return this.jobAiHelpers.generateResponsibilities(jobId);
  }

  async generateSummaryForCompany({ url, clientId }: { url: string; clientId: string }) {
    return this.jobAiHelpers.generateSummaryForCompany({ url, clientId });
  }

  async getJobsForJobSeeker(userId: string, all?: boolean, relations?: string[]): Promise<any> {
    return this.jobCandidatesHelpers.getJobsForJobSeeker(userId, all, relations);
  }

  async getJobsForGraduates(clientId: string, relations?: string[]): Promise<JobsResponse> {
    return this.jobCandidatesHelpers.getJobsForGraduates(clientId, relations);
  }

  async getJobSeekerApplications(clientId: string) {
    return this.jobCandidatesHelpers.getJobSeekerApplications(clientId);
  }

  async getJobApplicationStatusForJobSeeker(jobId: string, clientId: string) {
    return this.jobCandidatesHelpers.getJobApplicationStatusForJobSeeker(jobId, clientId);
  }

  async getAllMatchedCandidates(
    clientId: string,
    pagination: { page?: number; limit?: number } = {},
  ) {
    return this.jobMatchRankHelper.getAllMatchedCandidates(clientId, pagination);
  }

  async getMatchedCandidates(jobId: string, pagination: { page?: number; limit?: number } = {}) {
    return this.jobMatchRankHelper.getMatchedCandidates(jobId, pagination);
  }

  async addCandidatesToJob(jobId: string, candidateIds: string[], userId: string): Promise<Job> {
    return this.jobCandidatesHelpers.addCandidatesToJob(jobId, candidateIds, userId);
  }

  async getMatchRanks(jobId: string, options: PaginationOptions): Promise<MatchRankResponse> {
    return this.jobMatchRankHelper.getMatchRanks(jobId, options);
  }

  async getMatchRankedJobs(userId: string, options: { page: number; limit: number }) {
    return this.jobMatchRankHelper.getMatchRankedJobs(userId, options);
  }

  async getJobsByStatus(
    clientId: string,
    options: { page: number; limit: number; status?: string; includeStats?: boolean },
  ) {
    return this.jobMatchRankHelper.getJobsByStatus(clientId, options);
  }

  async generateWithTone(jobId: string, tone: string): Promise<string> {
    return this.jobAiHelpers.generateWithTone(jobId, tone);
  }

  async startMatchRankProcess(jobId: string, clientId: string): Promise<any> {
    return this.jobMatchRankHelper.startMatchRankProcess(jobId, clientId);
  }

  async getMatchRankStatus(jobId: string): Promise<any> {
    return this.jobMatchRankHelper.getMatchRankStatus(jobId);
  }

  async getMatchRankResults(jobId: string, options: { page: number; limit: number }): Promise<any> {
    return this.jobMatchRankHelper.getMatchRankResults(jobId, options);
  }

  async cancelMatchRankJob(jobId: string): Promise<any> {
    return this.jobMatchRankHelper.cancelMatchRankJob(jobId);
  }

  async getJobsByCompanyId(companyId: string, relations?: string[]): Promise<JobsResponse> {
    return this.jobCrudUtils.getJobsByCompanyId(companyId, relations);
  }

  async getPublicJobs(
    companyId: string,
    paginationOptions: PaginationOptions,
    filters?: PublicJobFilters,
    search?: string,
  ): Promise<JobsResponse> {
    return this.jobCrudUtils.getPublicJobs(companyId, paginationOptions, filters, search);
  }

  static processJobCandidatesStatic(job: Job): Job {
    return JobCrudUtils.processJobCandidatesStatic(job);
  }

  async processJobCandidates(job: Job): Promise<Job> {
    return JobService.processJobCandidatesStatic(job);
  }

  async getJobCandidatesMinimal(
    jobId: string,
    options: {
      page: number;
      limit: number;
      tier?: string;
      status?: string;
    },
  ) {
    const startTime = Date.now();
    this.logger.log(`Starting getJobCandidatesMinimal for job ${jobId} with options:`, options);

    try {
      const { page, limit, tier, status } = options;

      // Get job basic info without heavy relations
      this.logger.log(`Fetching job ${jobId}...`);
      const job = await this.findOne(jobId, []);
      if (!job) {
        throw new Error('Job not found');
      }
      this.logger.log(`Job ${jobId} found in ${Date.now() - startTime}ms`);

      // Build query for candidates with filters - only essential fields for list view
      this.logger.log(`Building candidate query for job ${jobId}...`);
      const queryBuilder = this.candidateRepository
        .createQueryBuilder('candidate')
        .leftJoinAndSelect('candidate.evaluations', 'evaluation', 'evaluation.jobId = :evalJobId')
        .select([
          'candidate.id',
          'candidate.fullName',
          'candidate.jobTitle',
          'candidate.location',
          'candidate.email',
          'candidate.currentCompany',
          'candidate.yearsOfExperience',
          'candidate.status',
          'candidate.tier',
          'candidate.contacted',
          'candidate.isShortlisted',
          'candidate.source',
          'candidate.createdAt',
          'evaluation.id',
          'evaluation.matchScore',
          'evaluation.evaluation',
        ])
        .where('candidate.jobId = :jobId')
        .setParameter('jobId', jobId)
        .setParameter('evalJobId', jobId)
        .orderBy('evaluation.matchScore', 'DESC');

      // Apply tier filter if provided
      if (tier) {
        queryBuilder.andWhere('candidate.tier = :tier', { tier });
      }

      // Apply status filter if provided
      if (status) {
        queryBuilder.andWhere('candidate.status = :status', { status });
      }

      // Get total count for pagination
      this.logger.log(`Getting candidate count for job ${jobId}...`);
      const countStartTime = Date.now();
      const totalItems = await Promise.race([
        queryBuilder.getCount(),
        new Promise<number>((_, reject) =>
          setTimeout(() => reject(new Error('Count query timeout')), 10000),
        ),
      ]);
      const totalPages = Math.ceil(totalItems / limit);
      this.logger.log(
        `Found ${totalItems} candidates for job ${jobId} in ${Date.now() - countStartTime}ms`,
      );

      // Apply pagination
      this.logger.log(`Fetching paginated candidates for job ${jobId}...`);
      const queryStartTime = Date.now();
      const candidates = await Promise.race([
        queryBuilder
          .skip((page - 1) * limit)
          .take(limit)
          .getMany(),
        new Promise<any[]>((_, reject) =>
          setTimeout(() => reject(new Error('Candidates query timeout')), 15000),
        ),
      ]);

      this.logger.log(
        `Fetched ${candidates.length} candidates for job ${jobId} in ${Date.now() - queryStartTime}ms`,
      );

      // Group candidates by tier for easier frontend consumption
      this.logger.log(`Grouping candidates for job ${jobId}...`);

      // Calculate tier thresholds using utility function
      const { topThreshold, secondThreshold } = normalizeJobThresholds(job);

      const groupedCandidates = {
        topTier: [] as any[],
        secondTier: [] as any[],
        others: [] as any[],
        unranked: [] as any[],
        shortlisted: [] as any[],
      };

      // Process each candidate and determine tier dynamically
      candidates.forEach((candidate) => {
        // Get match score from evaluation
        const matchScore = candidate.evaluations?.[0]?.matchScore || 0;
        const hasEvaluation = !!candidate.evaluations?.[0];

        // Determine tier using utility function
        const tier = determineCandidateTier(
          matchScore,
          topThreshold,
          secondThreshold,
          hasEvaluation,
        );

        // Add to appropriate group
        if (candidate.status === 'SHORTLISTED') {
          groupedCandidates.shortlisted.push(candidate);
        } else {
          switch (tier) {
            case 'TOP':
              groupedCandidates.topTier.push(candidate);
              break;
            case 'SECOND':
              groupedCandidates.secondTier.push(candidate);
              break;
            case 'OTHER':
              groupedCandidates.others.push(candidate);
              break;
            case 'UNRANKED':
              groupedCandidates.unranked.push(candidate);
              break;
          }
        }
      });

      // If no candidates found, return early with empty structure
      if (candidates.length === 0) {
        this.logger.log(`No candidates found for job ${jobId}`);

        // Calculate normalized thresholds for consistent response
        const { topThreshold, secondThreshold } = normalizeJobThresholds(job);

        const result = {
          job: {
            id: job.id,
            jobType: job.jobType,
            department: job.department,
            companyName: job.companyName,
            topCandidateThreshold: topThreshold,
            secondTierCandidateThreshold: secondThreshold,
            status: job.status,
          },
          candidates: {
            topTier: [],
            secondTier: [],
            others: [],
            unranked: [],
            shortlisted: [],
          },
          pagination: {
            currentPage: page,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: limit,
          },
          stats: {
            totalCandidates: 0,
            topTierCount: 0,
            secondTierCount: 0,
            othersCount: 0,
            unrankedCount: 0,
            shortlistedCount: 0,
          },
        };

        const duration = Date.now() - startTime;
        this.logger.log(
          `getJobCandidatesMinimal completed for job ${jobId} in ${duration}ms (no candidates)`,
        );
        return result;
      }

      // Calculate stats
      const stats = {
        totalCandidates: totalItems,
        topTierCount: groupedCandidates.topTier.length,
        secondTierCount: groupedCandidates.secondTier.length,
        othersCount: groupedCandidates.others.length,
        unrankedCount: groupedCandidates.unranked.length,
        shortlistedCount: groupedCandidates.shortlisted.length,
      };

      // Transform candidates to minimal format
      this.logger.log(`Transforming candidates for job ${jobId}...`);
      const transformCandidate = (candidate: any) => ({
        id: candidate.id,
        fullName: candidate.fullName,
        jobTitle: candidate.jobTitle,
        location: candidate.location,
        skills: candidate.skills || [],
        status: candidate.status,
        contacted: candidate.contacted || false,
        tier: candidate.tier,
        evaluation: candidate.evaluation
          ? {
              matchScore: candidate.evaluation.matchScore,
              lastEvaluatedAt: candidate.evaluation.lastEvaluatedAt,
              criterionMatchedOn: candidate.evaluation.criterionMatchedOn || [],
            }
          : null,
        currentCompany: candidate.currentCompany,
        yearsOfExperience: candidate.yearsOfExperience,
      });

      const result = {
        job: {
          id: job.id,
          jobType: job.jobType,
          department: job.department,
          companyName: job.companyName,
          topCandidateThreshold: topThreshold,
          secondTierCandidateThreshold: secondThreshold,
          status: job.status,
        },
        candidates: {
          topTier: groupedCandidates.topTier.map(transformCandidate),
          secondTier: groupedCandidates.secondTier.map(transformCandidate),
          others: groupedCandidates.others.map(transformCandidate),
          unranked: groupedCandidates.unranked.map(transformCandidate),
          shortlisted: groupedCandidates.shortlisted.map(transformCandidate),
        },
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
        },
        stats,
      };

      const duration = Date.now() - startTime;
      this.logger.log(`getJobCandidatesMinimal completed for job ${jobId} in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(
        `getJobCandidatesMinimal failed for job ${jobId} after ${duration}ms:`,
        error.message,
      );
      throw error;
    }
  }

  async getCultureFitDetails(jobId: string) {
    const startTime = Date.now();
    this.logger.log(`Fetching culture fit details for job ${jobId}`);

    try {
      // Fetch job with minimal fields
      const job = await this.jobRepository.findOne({
        where: { id: jobId },
        select: [
          'id',
          'jobType',
          'companyName',
          'department',
          'cultureFitQuestions',
          'cultureFitDescription',
          'topCandidateThreshold',
          'secondTierCandidateThreshold',
        ],
      });

      if (!job) {
        throw new NotFoundException(`Job with ID ${jobId} not found`);
      }

      // Fetch candidates with their video responses
      // Only get candidates that belong to this specific job
      const candidates = await this.candidateRepository
        .createQueryBuilder('candidate')
        .leftJoinAndSelect(
          'candidate.videoResponses',
          'videoResponse',
          'videoResponse.candidateId = candidate.id AND videoResponse.jobId = :jobId AND (videoResponse.type = :type OR videoResponse.type IS NULL)',
          {
            jobId,
            type: 'video_interview',
          },
        )
        .where('candidate.jobId = :jobId', { jobId })
        .select([
          'candidate.id',
          'candidate.fullName',
          'candidate.jobTitle',
          'candidate.email',
          'candidate.evaluation',
          'candidate.activityHistory',
          'videoResponse.id',
          'videoResponse.questionId',
          'videoResponse.question',
          'videoResponse.videoUrl',
          'videoResponse.duration',
          'videoResponse.recordedAt',
          'videoResponse.status',
          'videoResponse.isExpired',
        ])
        .orderBy(`candidate.evaluation->>'matchScore'`, 'DESC')
        .getMany();

      // Transform the response to include only necessary data
      const transformedCandidates = candidates.map((candidate) => {
        // Check if video intro email has been sent
        let videoIntroEmailSent = false;
        if (candidate.activityHistory && candidate.activityHistory.length > 0) {
          videoIntroEmailSent = candidate.activityHistory.some((activity: any) => {
            // Check for video intro email activity
            const isEmailSent = activity.type === 'EMAIL_SENT';
            const emailType = activity.metadata?.emailType;
            const isVideoIntroEmail = emailType === 'video_intro';
            const isCorrectJob = activity.metadata?.jobId === jobId;

            return isEmailSent && isVideoIntroEmail && isCorrectJob;
          });
        }

        return {
          id: candidate.id,
          fullName: candidate.fullName,
          position: candidate.jobTitle, // Using 'position' as requested
          email: candidate.email,
          matchScore: candidate.evaluation?.matchScore || 0,
          videoResponses: candidate.videoResponses || [],
          status: {
            videoIntroEmailSent,
          },
        };
      });

      // Sort candidates: those with video responses first, then by match score
      transformedCandidates.sort((a, b) => {
        // First priority: candidates with video responses
        const aHasVideos = a.videoResponses.length > 0;
        const bHasVideos = b.videoResponses.length > 0;

        if (aHasVideos && !bHasVideos) return -1;
        if (!aHasVideos && bHasVideos) return 1;

        // Second priority: by match score (higher scores first)
        return b.matchScore - a.matchScore;
      });

      const result = {
        job: {
          id: job.id,
          jobType: job.jobType,
          companyName: job.companyName,
          department: job.department,
          cultureFitQuestions: job.cultureFitQuestions || [],
          cultureFitDescription: job.cultureFitDescription || '',
          topCandidateThreshold: job.topCandidateThreshold || 70,
          secondTierCandidateThreshold: job.secondTierCandidateThreshold || 50,
        },
        candidates: transformedCandidates,
        totalCandidates: transformedCandidates.length,
      };

      const duration = Date.now() - startTime;
      this.logger.log(`Culture fit details fetched for job ${jobId} in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(
        `Failed to fetch culture fit details for job ${jobId} after ${duration}ms:`,
        error.message,
      );
      throw error;
    }
  }

  async debugVideoResponses(jobId: string) {
    // Get all video responses for this job
    const allVideoResponses = await this.videoResponseRepository.find({
      where: { jobId },
      select: ['id', 'candidateId', 'jobId', 'type', 'videoUrl', 'status', 'clientId', 'createdAt'],
    });

    // Get candidates for this job
    const candidates = await this.candidateRepository.find({
      where: { jobId },
      select: ['id', 'fullName', 'hasCompletedVideoInterview'],
    });

    // Get video responses by raw query to debug
    const rawVideoResponses = await this.videoResponseRepository
      .createQueryBuilder('vr')
      .where('vr.jobId = :jobId', { jobId })
      .getMany();

    return {
      jobId,
      candidatesCount: candidates.length,
      candidates: candidates.map((c) => ({
        id: c.id,
        fullName: c.fullName,
        hasCompletedVideoInterview: c.hasCompletedVideoInterview,
      })),
      videoResponsesCount: allVideoResponses.length,
      videoResponses: allVideoResponses,
      rawVideoResponses: rawVideoResponses.map((vr: any) => ({
        id: vr.id,
        candidateId: vr.candidateId,
        jobId: vr.jobId,
        type: vr.type,
        hasUrl: !!vr.videoUrl,
        status: vr.status,
        clientId: vr.clientId,
      })),
    };
  }
}
