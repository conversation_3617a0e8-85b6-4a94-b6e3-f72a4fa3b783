import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Optional, IsString, <PERSON><PERSON>ength } from 'class-validator';

import { CultureFitQuestions, JobStatus, TypeOfHiring, TypeOfJob } from '@/shared/types/job.types';

export class CreateJobDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsOptional()
  companyName?: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsString()
  @IsOptional()
  companyDescription?: string;

  @IsString()
  jobType!: string;

  @IsString()
  department!: string;

  @IsString()
  @IsOptional()
  experience?: string;

  @IsString()
  @IsOptional()
  hiringManagerDescription?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  salaryRange?: string;

  @IsString()
  @IsOptional()
  finalDraft?: string;

  @IsString()
  @IsOptional()
  @MaxLength(280, { message: 'Social media description must be 280 characters or less' })
  socialMediaDescription?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requirements?: string[];

  @IsString()
  @IsOptional()
  paymentPeriod?: string;

  @IsEnum(TypeOfHiring)
  @IsOptional()
  typeOfHiring?: TypeOfHiring;

  @IsEnum(TypeOfJob)
  @IsOptional()
  typeOfJob?: TypeOfJob;

  @IsEnum(JobStatus)
  @IsOptional()
  status?: JobStatus;

  @IsString()
  @IsOptional()
  clientId?: string;

  @IsArray()
  @IsOptional()
  cultureFitQuestions?: string[] | CultureFitQuestions[];

  @IsString()
  @IsOptional()
  cultureFitDescription?: string;
}
