import { OpenA<PERSON> } from 'openai';
import { Repository } from 'typeorm';

import { MultiAIContentService } from '@/shared/services/multi-ai-content.service';
import { OpenaiService } from '@/shared/services/openai.service';
import { ContentGeneratorService } from '@/shared/services/content-generator.service';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CompanyService } from '../company/company.service';
import { Job } from './entities/job.entity';
import { JobCrudUtils } from './job.crud.utils';
import { generateWithToneHelper } from './job.helpers';

@Injectable()
export class JobAiHelpers {
  private openai: OpenAI;

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly companyService: CompanyService,
    private readonly openAiService: OpenaiService,
    private readonly multiAIContentService: MultiAIContentService,
    private readonly contentGeneratorService: ContentGeneratorService,
    private readonly jobCrudUtils: JobCrudUtils,
  ) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async generateResponsibilities(jobId: string) {
    const job = await this.jobCrudUtils.findOne(jobId);

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'Generate relevant job responsibilities based on industry and skills.',
        },
        {
          role: 'user',
          content: `Generate a list of 10 relevant job responsibilities for a ${
            job.jobType
          } position in the ${
            job.department
          } industry with the following skills: ${job.skills?.join(', ')}.`,
        },
      ],
      temperature: 0.7,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('Failed to generate responsibilities');
    }

    const responsibilities = content.trim().split('\n');

    job.jobResponsibilities = responsibilities;
    await this.jobRepository.save(job);

    return responsibilities;
  }

  async generateSummaryForCompany({ url, clientId }: { url: string; clientId: string }) {
    try {
      // Use OpenAI directly for company summary generation (as requested)
      const summary = await this.contentGeneratorService.generateCompanySummary(url);
      await this.companyService.updateByClientId(clientId, { description: summary ?? '' });
      return {
        success: true,
        summary,
      };
    } catch (error) {
      throw new Error('Failed to generate summary');
    }
  }

  async generateWithTone(jobId: string, tone: string): Promise<string> {
    return generateWithToneHelper(this.jobRepository, this.openai, jobId, tone);
  }

  async generateSocialMediaDescription(jobId: string): Promise<string | null> {
    const job = await this.jobRepository.findOne({ where: { id: jobId } });
    if (!job) {
      throw new Error('Job not found');
    }

    // Use multi-AI service with Groq primary, OpenAI fallback
    const socialMediaDescription =
      await this.multiAIContentService.generateSocialMediaDescription(job);

    if (socialMediaDescription) {
      job.socialMediaDescription = socialMediaDescription;
      await this.jobRepository.save(job);
    }

    return socialMediaDescription;
  }
}
