import {
  Injectable,
  Logger,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from '../entities/job.entity';
import { MultiAIContentService } from '@/shared/services/multi-ai-content.service';
import { OpenaiService } from '@/shared/services/openai.service';
import { ContentGeneratorService } from '@/shared/services/content-generator.service';
import { GenerateSkillsAndResponsibilitiesResponse } from '@/shared/types/openai.types';
import { JobService } from '../job.service';

@Injectable()
export class JobAIOperationsService {
  private readonly logger = new Logger(JobAIOperationsService.name);

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly multiAIContentService: MultiAIContentService,
    private readonly openaiService: OpenaiService,
    private readonly contentGeneratorService: ContentGeneratorService,
    private readonly jobService: JobService,
  ) {}

  /**
   * Extract structured information from job description text
   */
  async extractJobDescription(jobDescriptionText: string, userId: string) {
    if (!jobDescriptionText || !jobDescriptionText.trim()) {
      throw new BadRequestException('Job description text is required');
    }

    try {
      this.logger.log(`Extracting job information for user ${userId}`);

      // Use OpenAI directly for job information extraction (as requested)
      const extractedData = await this.openaiService.extractJobInformation(jobDescriptionText);

      // Validate and clean the extracted data to ensure it matches our enums
      const validatedData = this.validateExtractedJobData(extractedData);

      this.logger.log(`Successfully extracted job information for user ${userId}`);
      return validatedData;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to extract job information: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException('Failed to extract job information from description');
    }
  }

  /**
   * Generate job responsibilities using AI
   */
  async generateResponsibilities(data: {
    department: string;
    jobType: string;
    experience?: string;
  }): Promise<GenerateSkillsAndResponsibilitiesResponse> {
    try {
      // Use OpenAI directly for skills & responsibilities generation (as requested)
      const response = await this.contentGeneratorService.generateSkillsAndResponsibilities(
        data.department,
        data.jobType,
        data.experience,
      );
      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to generate responsibilities: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException('Failed to generate responsibilities');
    }
  }

  /**
   * Generate company summary using AI
   */
  async generateCompanySummary(data: { url: string; clientId: string }) {
    try {
      return await this.jobService.generateSummaryForCompany(data);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to generate summary: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException('Failed to generate summary');
    }
  }

  /**
   * Validate extracted job data
   */
  private validateExtractedJobData(data: any): any {
    const validDepartments = [
      'Engineering',
      'Product',
      'Design',
      'Marketing',
      'Sales',
      'Customer Support',
      'Finance',
      'Human Resources',
      'Operations',
      'Legal',
      'Research & Development',
      'Information Technology',
      'Data Science',
      'Business Development',
      'Administration',
      'Quality Assurance',
      'Project Management',
      'Supply Chain',
      'Public Relations',
      'Strategy',
    ];

    const validJobTypes = [
      'Software Engineer',
      'Data Scientist',
      'Product Manager',
      'UX Designer',
      'Marketing Specialist',
      'Sales Representative',
      'Customer Support Representative',
      'Financial Analyst',
      'Human Resources Manager',
      'Project Manager',
      'Business Analyst',
      'Graphic Designer',
      'Content Writer',
      'Social Media Manager',
      'Systems Administrator',
      'Network Engineer',
      'Database Administrator',
      'DevOps Engineer',
      'Quality Assurance Tester',
      'Front-end Developer',
      'Back-end Developer',
      'Full-stack Developer',
      'Mobile App Developer',
      'AI/Machine Learning Engineer',
      'Cloud Architect',
      'Cybersecurity Specialist',
      'Data Analyst',
      'Business Intelligence Analyst',
      'Operations Manager',
      'Supply Chain Manager',
      'Accountant',
      'Legal Counsel',
    ];

    const validExperienceLevels = ['graduate', 'junior', 'mid', 'senior', 'lead'];
    const validTypeOfHiring = ['EMPLOYMENT', 'PROJECT'];
    const validTypeOfJob = ['PERMANENT', 'CONTRACT', 'PART_TIME'];
    const validCurrencies = ['AED', 'USD', 'EUR', 'GBP', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR'];

    // Helper functions
    const validateString = (value: any): string | null => {
      if (typeof value === 'string' && value.trim().length > 0) {
        return value.trim();
      }
      return null;
    };

    const validateArray = (value: any): string[] | null => {
      if (Array.isArray(value) && value.length > 0) {
        const cleanedArray = value
          .filter((item) => typeof item === 'string' && item.trim().length > 0)
          .map((item) => item.trim());
        return cleanedArray.length > 0 ? cleanedArray : null;
      }
      return null;
    };

    const validateEnum = (value: any, validValues: string[]): string | null => {
      if (typeof value === 'string' && value.trim().length > 0) {
        const trimmedValue = value.trim();
        // Exact match first
        if (validValues.includes(trimmedValue)) {
          return trimmedValue;
        }
        // Case-insensitive match
        const lowerValue = trimmedValue.toLowerCase();
        const match = validValues.find((valid) => valid.toLowerCase() === lowerValue);
        if (match) {
          return match;
        }
      }
      return null;
    };

    const validateSalaryRange = (value: any): string | null => {
      if (typeof value === 'string' && value.trim().length > 0) {
        const trimmed = value.trim();
        if (
          /\d/.test(trimmed) &&
          (trimmed.includes('-') || trimmed.includes('to') || /\d+[kK]?/.test(trimmed))
        ) {
          return trimmed;
        }
      }
      return null;
    };

    return {
      title: validateString(data.title),
      companyName: validateString(data.companyName),
      companyDescription: validateString(data.companyDescription),
      experience: validateString(data.experience),
      location: validateString(data.location),
      hiringManagerDescription: validateString(data.hiringManagerDescription),
      socialMediaDescription: validateString(data.socialMediaDescription),
      department: validateEnum(data.department, validDepartments),
      jobType: validateEnum(data.jobType, validJobTypes),
      experienceLevel: validateEnum(data.experienceLevel, validExperienceLevels),
      typeOfHiring: validateEnum(data.typeOfHiring, validTypeOfHiring),
      typeOfJob: validateEnum(data.typeOfJob, validTypeOfJob),
      currency: validateEnum(data.currency, validCurrencies),
      skills: validateArray(data.skills),
      jobResponsibilities: validateArray(data.jobResponsibilities),
      requirements: validateArray(data.requirements),
      benefits: validateArray(data.benefits),
      salaryRange: validateSalaryRange(data.salaryRange),
    };
  }
}
