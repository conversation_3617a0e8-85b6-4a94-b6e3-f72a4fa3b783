import { Injectable } from '@nestjs/common';
import { Job } from '../entities/job.entity';

@Injectable()
export class JobTransformationService {
  /**
   * Transform job data for criteria endpoint response
   */
  transformJobCriteriaResponse(
    job: Job,
    totalCandidates: number,
    recentCandidates: any[],
    matchRankCost: any,
  ) {
    const jobData = {
      id: job.id,
      jobType: job.jobType,
      jobTitle: job.jobType, // For JobForm compatibility - use jobType as jobTitle
      department: job.department,
      companyName: job.companyName,
      companyDescription: job.companyDescription,
      requirements: job.requirements,
      skills: job.skills,
      softSkills: job.softSkills,
      topCandidateThreshold: job.topCandidateThreshold,
      secondTierCandidateThreshold: job.secondTierCandidateThreshold,
      cultureFitQuestions: job.cultureFitQuestions,
      cultureFitDescription: job.cultureFitDescription,
      experienceLevel: job.experienceLevel,
      experience: job.experience,
      location: job.location,
      typeOfJob: job.typeOfJob,
      typeOfHiring: job.typeOfHiring,
      education: job.education,
      language: job.language,
      jobResponsibilities: job.jobResponsibilities,
      jobDescription: job.finalDraft, // Use finalDraft as jobDescription for JobForm
      finalDraft: job.finalDraft, // Keep original for ATS jobs
      generatedJD: job.generatedJD, // Include generated JD if available
      socialMediaDescription: job.socialMediaDescription,
      benefits: job.benefits,
      careerGrowth: job.careerGrowth,
      salaryRange: job.salaryRange,
      currency: job.currency,
      status: job.status, // For JobForm status field
      metrics: job.metrics,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    };

    return {
      // Nested job object for JobForm compatibility
      job: jobData,

      // Also include job data at root level for backward compatibility
      ...jobData,

      // Additional data for the criteria page
      totalCandidates,
      recentCandidates, // Only recent candidates, not all
      matchRankCost,
    };
  }

  /**
   * Transform job data for findOne endpoint response
   */
  transformJobDetailResponse(
    job: Job,
    totalCandidates: number,
    recentCandidates: any[],
    matchRankCost: any,
  ) {
    const jobData = {
      id: job.id,
      jobType: job.jobType,
      jobTitle: job.jobType, // For JobForm compatibility - use jobType as jobTitle
      department: job.department,
      companyName: job.companyName,
      companyDescription: job.companyDescription,
      requirements: job.requirements,
      skills: job.skills,
      softSkills: job.softSkills,
      topCandidateThreshold: job.topCandidateThreshold,
      secondTierCandidateThreshold: job.secondTierCandidateThreshold,
      cultureFitQuestions: job.cultureFitQuestions,
      cultureFitDescription: job.cultureFitDescription,
      experienceLevel: job.experienceLevel,
      experience: job.experience,
      location: job.location,
      typeOfJob: job.typeOfJob,
      typeOfHiring: job.typeOfHiring,
      education: job.education,
      language: job.language,
      jobResponsibilities: job.jobResponsibilities,
      jobDescription: job.finalDraft, // Use finalDraft as jobDescription for JobForm
      finalDraft: job.finalDraft, // Keep original for ATS jobs
      generatedJD: job.generatedJD, // Include generated JD if available
      socialMediaDescription: job.socialMediaDescription,
      benefits: job.benefits,
      careerGrowth: job.careerGrowth,
      salaryRange: job.salaryRange,
      currency: job.currency,
      status: job.status, // For JobForm status field
      metrics: job.metrics,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      // Include related data for compatibility
      candidates: job.candidates,
      candidateEvaluations: job.candidateEvaluations,
      videoJDs: job.videoJDs,
      videoResponses: job.videoResponses,
      isPublished: job.isPublished,
      // Include additional data for TotalCandidatesSection
      matchRankCost,
      totalCandidates,
      recentCandidates,
    };

    return {
      // Nested job object for JobForm compatibility
      job: jobData,

      // Also include job data at root level for backward compatibility
      ...jobData,

      // Additional data for components
      totalCandidates,
      recentCandidates,
      matchRankCost,
    };
  }

  /**
   * Transform culture fit update response
   */
  transformCultureFitUpdateResponse(job: Job, updateData: any, processedQuestions: any[]) {
    return {
      success: true,
      data: job,
      message: 'Culture fit questions updated successfully',
      debug: {
        originalQuestions: updateData.cultureFitQuestions || [],
        processedQuestions: processedQuestions || [],
        savedQuestions: job.cultureFitQuestions || [],
      },
    };
  }

  /**
   * Process culture fit questions to ensure proper format
   */
  processCultureFitQuestions(questions?: any[]): any[] | undefined {
    return questions?.map((question) =>
      typeof question === 'string' ? ({ question } as any) : question,
    );
  }

  /**
   * Transform candidate video responses
   */
  transformCandidateVideoResponses(job: any, candidate: any) {
    const formattedVideoResponses =
      candidate.videoResponses?.map((response: any) => ({
        isExpired: false,
        id: response.id,
        questionId: response.questionId || response.id,
        question: response.question,
        videoUrl: response.videoUrl,
        duration: response.duration || 60,
        recordedAt: response.recordedAt,
        status: response.status || 'completed',
      })) || [];

    // Check if video intro email has been sent
    let videoIntroEmailSent = false;
    if (candidate.activityHistory && candidate.activityHistory.length > 0) {
      videoIntroEmailSent = candidate.activityHistory.some(
        (activity: any) =>
          activity.type === 'EMAIL_SENT' &&
          activity.metadata?.emailType === 'video_intro' &&
          activity.metadata?.jobId === job.id,
      );
    }

    return {
      job: {
        id: job.id,
        jobType: job.jobType,
        companyName: job.companyName,
        department: job.department,
        cultureFitQuestions: job.cultureFitQuestions || [],
      },
      candidate: {
        id: candidate.id,
        fullName: candidate.fullName,
        jobTitle: candidate.jobTitle,
        location: candidate.location,
        currentCompany: candidate.currentCompany,
        yearsOfExperience: candidate.yearsOfExperience,
        videoResponses: formattedVideoResponses,
        answeredQuestions: formattedVideoResponses.length,
        totalQuestions: job.cultureFitQuestions?.length || 0,
        status:
          formattedVideoResponses.length === (job.cultureFitQuestions?.length || 0)
            ? 'Completed'
            : 'In Progress',
        videoIntroEmailSent,
      },
    };
  }
}
