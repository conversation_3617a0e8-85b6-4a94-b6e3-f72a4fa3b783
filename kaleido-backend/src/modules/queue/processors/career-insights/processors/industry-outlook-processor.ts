import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class IndustryOutlookProcessor {
  private readonly logger = new Logger(IndustryOutlookProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { industry, location, timeframe, jobSeekerProfile } = params;

    // Determine relevant industry based on profile
    const relevantIndustry = CareerInsightHelpers.getIndustryFromProfile(jobSeekerProfile, industry);
    const industryRoleCount = CareerInsightHelpers.countIndustryRoles(
      jobSeekerProfile.experience,
      relevantIndustry
    );

    const prompt = `Analyze industry outlook tailored to this professional profile:

    Professional Context:
    - Current Role: ${CareerInsightHelpers.extractCurrentRole(jobSeekerProfile.experience)}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}
    - Industry Focus: ${relevantIndustry}
    - Location: ${location || jobSeekerProfile.location || 'Global'}
    - Timeframe: ${timeframe || 'next 5 years'}

    Current Capabilities:
    - Core Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills, 10)}
    - Years in Industry: ${industryRoleCount} roles
    - Education: ${CareerInsightHelpers.getEducationField(jobSeekerProfile.education)}
    - Certifications: ${jobSeekerProfile.certifications.length} relevant certifications

    Provide personalized industry outlook including:
    1. Industry growth projections and how they affect someone at their level
    2. Emerging technologies they should learn based on current skills
    3. Job market outlook for their experience level
    4. Skills becoming obsolete vs skills gaining importance
    5. Potential career pivots within the industry
    6. Impact of global trends (AI, automation, sustainability)
    7. Geographic considerations and remote work trends
    8. Salary growth projections for their role type
    9. Key industry players and companies to watch
    10. Actionable steps to position themselves for success`;

    const response = await this.aiService.generateResponse(prompt);
    const outlook = this.parser.parseIndustryOutlookResponse(response);

    return {
      aiInsights: {
        summary: outlook.summary,
        keyFindings: outlook.keyFindings,
        recommendations: outlook.recommendations,
      },
      detailedAnalysis: outlook.detailedAnalysis,
    };
  }
}