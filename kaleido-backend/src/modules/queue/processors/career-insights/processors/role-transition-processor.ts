import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class RoleTransitionProcessor {
  private readonly logger = new Logger(RoleTransitionProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { currentRole, targetRole, jobSeekerProfile, formData } = params;

    // Extract structured data from form
    const targetRoleFromForm = formData?.targetRole || targetRole;

    // Get current role from profile if not specified
    const actualCurrentRole =
      currentRole || CareerInsightHelpers.extractCurrentRole(jobSeekerProfile.experience);

    const prompt = `Provide detailed role transition guidance based on this profile:

    Transition Goal:
    - From: ${actualCurrentRole}
    - To: ${targetRoleFromForm || 'Next career level'}

    Professional Profile:
    - Name: ${jobSeekerProfile.firstName} ${jobSeekerProfile.lastName}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}
    - Location: ${jobSeekerProfile.location || 'Flexible'}

    Current Skillset:
    - Technical Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills)}
    - Certifications: ${CareerInsightHelpers.formatCertifications(jobSeekerProfile.certifications)}
    - Languages: ${CareerInsightHelpers.formatLanguages(jobSeekerProfile.languages)}

    Career History (last 3 roles):
    ${CareerInsightHelpers.formatExperience(jobSeekerProfile.experience, 3)}

    Education:
    ${CareerInsightHelpers.formatEducation(jobSeekerProfile.education)}

    Personal Values: ${CareerInsightHelpers.formatValues(jobSeekerProfile.myValues)}
    Work Preferences: ${jobSeekerProfile.preferences?.workEnvironment || 'Not specified'}

    Provide comprehensive transition guidance including:
    1. Transition difficulty assessment (EASY/MODERATE/DIFFICULT) with justification
    2. Transferable skills from current role that apply directly
    3. Critical skill gaps that must be addressed
    4. Month-by-month transition plan for next 12-18 months
    5. Specific courses, certifications, or training programs recommended
    6. Networking strategies and key connections to make
    7. Portfolio or project recommendations to demonstrate readiness
    8. Interview preparation tips specific to target role
    9. Potential obstacles and mitigation strategies
    10. Success metrics to track progress`;

    const response = await this.aiService.generateResponse(prompt);
    const transition = this.parser.parseRoleTransitionResponse(response);

    return {
      aiInsights: {
        strengths: transition.strengths || [],
        opportunities: transition.opportunities || [],
        challenges: transition.challenges || [],
        recommendations: transition.recommendations || [],
        confidenceScore: transition.confidenceScore || 85,
      },
      roleTransitionGuidance: {
        fromRole: actualCurrentRole,
        toRole: targetRoleFromForm || 'Target Role',
        transitionDifficulty: transition.difficulty || 'MODERATE',
        transferableSkills: transition.transferableSkills || [],
        newSkillsRequired: transition.newSkills || [],
        transitionPlan: transition.plan || [],
        successStories: transition.successStories,
      },
      detailedAnalysis: transition.detailedAnalysis || transition.summary,
    };
  }
}