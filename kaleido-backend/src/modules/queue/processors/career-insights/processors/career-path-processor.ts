import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class CareerPathProcessor {
  private readonly logger = new Logger(CareerPathProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { jobSeekerProfile } = params;

    // Build a comprehensive career history summary
    const careerHistory = CareerInsightHelpers.buildCareerHistory(jobSeekerProfile.experience);

    const prompt = `Recommend personalized career paths based on the following profile:

    Job Seeker Profile:
    - Name: ${jobSeekerProfile.firstName} ${jobSeekerProfile.lastName}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}
    - Location: ${jobSeekerProfile.location || 'Flexible'}

    Career History:
    ${careerHistory.map((job: any) => `- ${job.role} at ${job.company} (${job.duration})`).join('\n') || 'No experience listed'}

    Education Background:
    ${CareerInsightHelpers.formatEducation(jobSeekerProfile.education)}

    Technical Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills)}
    Languages: ${CareerInsightHelpers.formatLanguages(jobSeekerProfile.languages)}
    Certifications: ${CareerInsightHelpers.formatCertifications(jobSeekerProfile.certifications)}

    Personal Values & Interests: ${CareerInsightHelpers.formatValues(jobSeekerProfile.myValues)}
    Work Preferences: ${JSON.stringify(jobSeekerProfile.preferences) || 'Not specified'}

    Professional Summary: ${jobSeekerProfile.summary || 'Not provided'}

    Achievements:
    ${CareerInsightHelpers.formatAchievements(jobSeekerProfile.achievements)}

    Based on this comprehensive profile, provide:
    1. Top 3-4 recommended career paths that align with their experience, skills, and values
    2. For each path, explain why it's a good fit based on their background
    3. Detailed transition steps including specific roles to target
    4. Skills to develop and certifications to pursue for each path
    5. Expected timeline and career progression milestones
    6. Salary expectations based on their location and experience
    7. Industry trends that make each path attractive
    8. Potential challenges and how to overcome them`;

    // Use OpenAI for career path recommendations as they require nuanced understanding
    const response = await this.aiService.generateResponse(prompt, { requireHighQuality: true });
    const paths = this.parser.parseCareerPathResponse(response);

    return {
      aiInsights: {
        strengths: paths.strengths || [],
        opportunities: paths.opportunities || [],
        challenges: paths.challenges || [],
        recommendations: paths.recommendations || [],
        confidenceScore: paths.confidenceScore || 85,
      },
      careerPathRecommendation: {
        currentPosition: jobSeekerProfile.experience[0]?.title || 'Entry Level',
        recommendedPaths:
          paths.paths?.map((path: any) => ({
            pathName: path.title,
            steps: path.steps || [],
            totalDuration: path.timeline || '2-3 years',
            difficultyLevel: 'MODERATE' as const,
            successProbability: path.successProbability || 75,
          })) || [],
        immediateNextSteps: paths.immediateSteps || [],
      },
      detailedAnalysis: paths.detailedAnalysis || paths.summary,
    };
  }
}