import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CompanyTeamService } from '@modules/company/services/company-team.service';
import { CompanyMemberPermissions } from '@modules/company/entities/company-member.entity';

export const COMPANY_PERMISSION_KEY = 'companyPermission';
export const CompanyPermission = (permission: keyof CompanyMemberPermissions) =>
  Reflect.metadata(COMPANY_PERMISSION_KEY, permission);

@Injectable()
export class CompanyPermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private companyTeamService: CompanyTeamService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.get<keyof CompanyMemberPermissions>(
      COMPANY_PERMISSION_KEY,
      context.getHandler(),
    );

    // If no permission is required, allow access
    if (!requiredPermission) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const companyId = request.params.companyId;

    if (!user || !companyId) {
      throw new ForbiddenException('Access denied');
    }

    // Check if user has the required permission
    const hasPermission = await this.companyTeamService.hasPermission(
      companyId,
      user.sub,
      requiredPermission,
    );

    if (!hasPermission) {
      throw new ForbiddenException(
        `You do not have permission to ${requiredPermission.replace(/^can/, '').toLowerCase()}`,
      );
    }

    return true;
  }
}
