import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';

import { UserRole } from '../common/enums/role.enum';
import { ROLES_KEY } from '../shared/decorators/roles.decorator';
import { AuthService } from './auth.service';
import { IS_PUBLIC_KEY } from './public.decorator';

@Injectable()
export class Auth0Guard extends AuthGuard('jwt') implements CanActivate {
  private readonly logger = new Logger(Auth0Guard.name);

  constructor(
    private reflector: Reflector,
    private authService: AuthService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    // this.logger.debug(`Authenticating request to: ${request.method} ${request.url}`);
    // this.logger.debug('Auth header:', request.headers?.authorization);

    // Check if endpoint is public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      // this.logger.debug('Endpoint is public, skipping authentication');
      return true;
    }

    try {
      // First check JWT authentication
      const isAuthenticated = await super.canActivate(context);
      if (!isAuthenticated) {
        this.logger.error('JWT authentication failed');
        return false;
      }

      // this.logger.debug('JWT authentication successful');

      // Log the user object to see what we're getting from the JWT strategy
      if (request.user) {
        // this.logger.debug('User from JWT strategy:', {
        //   sub: request.user.sub,
        //   email: request.user.email,
        //   roles: request.user.roles,
        //   hasAccessToken: !!request.user.accessToken,
        //   fields: Object.keys(request.user),
        // });

        // // Log the full user object for debugging
        // this.logger.debug('Full user object:', request.user);

        // Check what entities the user already has
        const existingEntities = await this.authService.userHasAnyEntity(request.user.sub);

        // Get user roles from JWT
        const userRoles = request.user.roles || [];
        const hasJobSeekerRole = userRoles.includes(UserRole.JOB_SEEKER);
        const hasGraduateRole = userRoles.includes(UserRole.GRADUATE);

        this.logger.debug(
          `User ${request.user.sub} - Entity: ${existingEntities.type || 'none'}, DB Role: ${existingEntities.userRole || 'none'}, JWT Roles: ${userRoles.join(', ')}`,
        );

        // Only attempt to create job seeker profile if:
        // 1. User has job-seeker or graduate role in JWT
        // 2. User doesn't have an employer role in the database
        // 3. User doesn't already have a job-seeker entity
        if (
          (hasJobSeekerRole || hasGraduateRole) &&
          existingEntities.userRole !== UserRole.EMPLOYER &&
          existingEntities.type !== 'job-seeker'
        ) {
          try {
            const profile = await this.authService.createJobSeekerProfileIfNotExists(request.user);
            this.logger.debug(
              `Job seeker profile check completed: ${profile ? 'Profile exists/created' : 'Failed to create profile'}`,
            );
          } catch (error) {
            this.logger.error('Error checking/creating job seeker profile:', error);
            // Don't fail authentication if profile creation fails
          }
        }
      }

      // Then check roles if specified
      const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      if (!requiredRoles) {
        this.logger.debug('No roles required for this endpoint');
        return true;
      }

      const hasRole = await this.authService.hasRole(request.user, requiredRoles);
      this.logger.debug(`Role check result: ${hasRole}`);
      return hasRole;
    } catch (error) {
      this.logger.error('Authentication error:', error);
      return false;
    }
  }
}
