import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CompanyTeamService } from '@modules/company/services/company-team.service';

@Injectable()
export class AutoJoinCompanyInterceptor implements NestInterceptor {
  constructor(private companyTeamService: CompanyTeamService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Only process for authenticated users with email
    if (user && user.sub && user.email) {
      try {
        // Check if user should auto-join any company
        await this.companyTeamService.autoJoinCompany(user.sub, user.email);
      } catch (error) {
        // Log error but don't fail the request
        console.error('Auto-join company error:', error);
      }
    }

    return next.handle();
  }
}
