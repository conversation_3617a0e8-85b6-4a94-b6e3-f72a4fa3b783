import { passportJwtSecret } from 'jwks-rsa';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

import { UserRole } from '../common/enums/role.enum';
import { RolesService } from '../modules/roles/roles.service';
import { WHITELISTS_EMAIL_ADDRESSES } from '../shared/types/constants';
import { Auth0CacheService } from './auth0-cache.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private configService: ConfigService,
    private rolesService: RolesService,
    private auth0CacheService: Auth0CacheService,
  ) {
    const auth0Issuer = configService.get('AUTH0_ISSUER');
    // Remove any trailing slashes from the issuer and ensure domain format
    const cleanIssuer = auth0Issuer?.replace(/\/+$/, '');
    const jwksUri = `https://${cleanIssuer}/.well-known/jwks.json`;

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKeyProvider: passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri,
      }),
      issuer: `https://${cleanIssuer}/`,
      audience: configService.get('AUTH0_AUDIENCE'),
      algorithms: ['RS256'],
      passReqToCallback: true,
      // Note: Token expiration is controlled by Auth0, not by this setting
    });
  }

  private isWhitelistedEmail(email: string): boolean {
    return WHITELISTS_EMAIL_ADDRESSES.includes(email);
  }

  authenticate(req: any, options?: any) {
    const authHeader = req.headers?.authorization;
    if (!authHeader) {
      throw new UnauthorizedException('No authorization header');
    }

    try {
      super.authenticate(req, options);
    } catch (error: any) {
      this.logger.error('JWT authentication failed:', {
        error: error.message,
        stack: error.stack,
        name: error.name,
      });
      throw error;
    }
  }

  async validate(req: any, payload: any) {
    if (!payload.sub) {
      throw new UnauthorizedException('Invalid token payload - missing sub claim');
    }

    // Validate issuer
    const expectedIssuer = `https://${this.configService.get('AUTH0_ISSUER')?.replace(/\/+$/, '')}/`;
    if (payload.iss !== expectedIssuer) {
      this.logger.error(`Invalid issuer. Expected: ${expectedIssuer}, Got: ${payload.iss}`);
      throw new UnauthorizedException('Invalid token issuer');
    }

    // Validate audience (if configured)
    const expectedAudience = this.configService.get('AUTH0_AUDIENCE');
    if (expectedAudience) {
      const tokenAudience = Array.isArray(payload.aud) ? payload.aud : [payload.aud];
      if (!tokenAudience.includes(expectedAudience)) {
        this.logger.error(`Invalid audience. Expected: ${expectedAudience}, Got: ${payload.aud}`);
        throw new UnauthorizedException('Invalid token audience');
      }
    }

    // Extract the authorization header to get the full token early
    const authHeader = req.headers?.authorization;
    const token = authHeader ? authHeader.split(' ')[1] : null;

    // Check if user exists, if not create them
    let userRole = await this.rolesService.findByClientId(payload.sub);
    this.logger.debug(
      `Role lookup for ${payload.sub}: ${userRole ? `Found role: ${userRole.role}` : 'No role found'}`,
    );

    // Create a base user object early so it's available for early returns
    const user: Record<string, any> = {
      userId: payload.sub,
      sub: payload.sub,
      email: payload.email,
      roles: [], // Will be populated later
      accessToken: token,
    };

    if (!userRole) {
      // Get role from request query if available
      const requestedRole = req?.query?.role?.toLowerCase();

      // Check for onboarding context in headers (for mid-onboarding authentication)
      const onboardingRole = req?.headers?.['x-onboarding-role']?.toLowerCase();
      const onboardingContext = req?.headers?.['x-onboarding-context'];

      // Check for pending role from Auth0 callback cookie (highest priority)
      let pendingRole: string | null = null;
      try {
        const cookies = req?.headers?.cookie;
        if (cookies) {
          const pendingRoleCookie = cookies
            .split(';')
            .find((cookie: string) => cookie.trim().startsWith(`pendingRole_${payload.sub}=`));

          if (pendingRoleCookie) {
            const cookieValue = pendingRoleCookie.split('=')[1];
            const roleData = JSON.parse(decodeURIComponent(cookieValue));

            // Verify the cookie is recent (within 5 minutes) and for the correct user
            const isRecent = Date.now() - roleData.timestamp < 5 * 60 * 1000;
            const isCorrectUser = roleData.userId === payload.sub;

            if (isRecent && isCorrectUser && roleData.role) {
              pendingRole = roleData.role.toLowerCase();
              this.logger.log(
                `Found pending role from Auth0 callback cookie for ${payload.sub}: ${pendingRole}`,
              );
            } else {
              this.logger.debug(
                `Pending role cookie validation failed for ${payload.sub}: recent=${isRecent}, correctUser=${isCorrectUser}, hasRole=${!!roleData.role}`,
              );
            }
          }
        }
      } catch (error) {
        this.logger.warn(`Error parsing pending role cookie for ${payload.sub}:`, error);
      }

      // For whitelisted emails, set role as ADMIN
      // Don't default to any role - require explicit role assignment
      let initialRole = this.isWhitelistedEmail(payload.email) ? UserRole.ADMIN : null;

      this.logger.debug(
        `Email ${payload.email} whitelisted: ${this.isWhitelistedEmail(payload.email)}, initial role: ${initialRole}`,
      );

      // Priority order: pending role from Auth0 callback cookie > onboarding role > request query role
      // BUT: Never override admin role for whitelisted emails
      const roleToUse = pendingRole || onboardingRole || requestedRole;

      if (onboardingContext && onboardingRole) {
        this.logger.log(
          `Onboarding context detected for ${payload.sub}: context=${onboardingContext}, role=${onboardingRole}`,
        );
      }

      if (roleToUse && Object.values(UserRole).includes(roleToUse as UserRole)) {
        // Don't override admin role for whitelisted emails
        if (this.isWhitelistedEmail(payload.email) && initialRole === UserRole.ADMIN) {
          this.logger.log(
            `Whitelisted email ${payload.email} - keeping ADMIN role, ignoring pending/requested role: ${roleToUse}`,
          );
        } else {
          initialRole = roleToUse as UserRole;
          this.logger.log(
            `Using role from ${pendingRole ? 'Auth0 callback cookie' : onboardingRole ? 'onboarding context' : 'query parameter'} for ${payload.sub}: ${initialRole}`,
          );
        }
      } else if (roleToUse) {
        this.logger.warn(`Invalid role provided for ${payload.sub}: ${roleToUse}`);
      }

      // If no role was determined and user is not admin, we need to handle this carefully
      if (!initialRole) {
        // For new users without a role, create a placeholder that will be updated via sync endpoint
        this.logger.warn(
          `No role specified for new user ${payload.sub} (${payload.email}). Creating placeholder for role sync.`,
        );
        // Don't throw error - allow user to authenticate and sync role from localStorage
        // The frontend will call the sync-from-storage endpoint to set the correct role
        return user; // Return user without creating role - will be synced later
      }

      this.logger.log(
        `Creating new user role for ${payload.sub} with role: ${initialRole} (email: ${payload.email})`,
      );
      userRole = await this.rolesService.create({
        clientId: payload.sub,
        role: initialRole,
      });
      this.logger.log(`Successfully created user role for ${payload.sub}: ${userRole.role}`);

      // Clean up the pending role cookie after successful user creation
      if (pendingRole) {
        this.logger.log(
          `Pending role cookie from Auth0 callback was used for user creation: ${payload.sub} -> ${initialRole}`,
        );
        // Note: The cookie cleanup will be handled by the frontend PostLoginRoleHandler
      }
    }

    // For whitelisted emails, grant all roles
    const roles = this.isWhitelistedEmail(payload.email)
      ? Object.values(UserRole)
      : userRole
        ? [userRole.role]
        : [];

    this.logger.debug(`User ${payload.email} granted roles: ${JSON.stringify(roles)}`);

    // Update the user object with the correct roles
    user.roles = roles;

    // Log the payload for debugging
    // this.logger.debug('JWT payload:', {
    //   sub: payload.sub,
    //   email: payload.email,
    //   name: payload.name,
    //   fields: Object.keys(payload),
    // });

    try {
      // Try to fetch the user profile from Auth0 using the token
      if (token) {
        // Use the cache service to get the user profile
        const userInfo = await this.auth0CacheService.getUserProfile(token, payload.sub);

        if (userInfo) {
          // Merge the user info with our user object
          Object.assign(user, {
            // Basic user info
            name: userInfo.name,
            given_name: userInfo.given_name,
            family_name: userInfo.family_name,
            nickname: userInfo.nickname,
            picture: userInfo.picture,
            email: userInfo.email || payload.email, // Prefer email from userinfo
            email_verified: userInfo.email_verified,

            // Provider info
            provider: userInfo.sub?.split('|')[0] || payload.identities?.[0]?.provider,

            // Additional fields
            locale: userInfo.locale,
            updated_at: userInfo.updated_at,
            created_at: userInfo.created_at,

            // LinkedIn specific fields
            headline: userInfo.headline,
            industry: userInfo.industry,
            summary: userInfo.summary,
            positions: userInfo.positions,
            skills: userInfo.skills,
            educations: userInfo.educations,
            linkedInProfile: userInfo.linkedInProfile,

            // Additional metadata
            user_metadata: userInfo.user_metadata,
            app_metadata: userInfo.app_metadata,

            // Additional profile data
            fullName: userInfo.name,
            firstName: userInfo.given_name,
            lastName: userInfo.family_name,
            profileUrl: userInfo.profileUrl,
          });

          // Extract any additional fields from the userInfo
          Object.keys(userInfo).forEach((key) => {
            if (!user.hasOwnProperty(key)) {
              user[key] = userInfo[key];
            }
          });
        }
      }
    } catch (error: unknown) {
      // Log the error but don't fail authentication
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Error fetching user profile from Auth0:', errorMessage);
      // this.logger.debug('Continuing with limited user information from token payload');

      // Fall back to using the payload data
      Object.assign(user, {
        // Basic user info
        name: payload.name,
        given_name: payload.given_name,
        family_name: payload.family_name,
        nickname: payload.nickname,
        picture: payload.picture,
        email_verified: payload.email_verified,

        // Provider info
        provider: payload.identities?.[0]?.provider,

        // Additional fields
        locale: payload.locale,
        updated_at: payload.updated_at,
        created_at: payload.created_at,

        // Additional profile data
        fullName: payload.name,
        firstName: payload.given_name,
        lastName: payload.family_name,
      });
    }

    // Extract login count from token
    const namespace = 'https://kaleidotalent.com';
    user.logins_count = payload.logins_count || payload[`${namespace}/logins_count`] || 0;
    user.is_new_user = payload.is_new_user || payload[`${namespace}/is_new_user`] || false;

    // Extract any additional fields from the token payload
    // This ensures we capture all available data from Auth0
    Object.keys(payload).forEach((key) => {
      if (
        !user.hasOwnProperty(key) &&
        key !== 'iat' &&
        key !== 'exp' &&
        key !== 'aud' &&
        key !== 'iss'
      ) {
        user[key] = payload[key];
      }
    });

    // Log the final user object with all fields
    // this.logger.debug('JWT validation successful for user:', {
    //   userId: user.userId,
    //   email: user.email,
    //   roles: user.roles,
    //   hasAccessToken: !!user.accessToken,
    //   fields: Object.keys(user),
    // });

    return user;
  }
}
