import { Injectable, Logger } from '@nestjs/common';

import { UserRole } from '../common/enums/role.enum';
import { JobSeekerService } from '../modules/job-seeker/job-seeker.service';
import { CompanyService } from '../modules/company/company.service';
import { RolesService } from '../modules/roles/roles.service';
import { User } from '../shared/decorators/get-user.decorator';
import { Auth0ManagementService } from './auth0-management.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jobSeekerService: JobSeekerService,
    private readonly companyService: CompanyService,
    private readonly auth0ManagementService: Auth0ManagementService,
    private readonly rolesService: RolesService,
  ) {}

  async hasRole(user: User, requiredRoles: UserRole[]): Promise<boolean> {
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return requiredRoles.some((role) => user?.roles?.includes(role));
  }

  getUserId(user: User): string {
    return user?.userId;
  }

  /**
   * Check if user has any entity (company or job-seeker)
   * This prevents cross-entity creation
   */
  async userHasAnyEntity(
    userId: string,
  ): Promise<{ type: 'company' | 'job-seeker' | null; entity: any; userRole?: UserRole }> {
    try {
      // First check the user's role from the roles table
      const userRoleEntity = await this.rolesService.findByClientId(userId);

      if (!userRoleEntity) {
        this.logger.debug(`User ${userId} has no role assigned yet`);
        return { type: null, entity: null };
      }

      const userRole = userRoleEntity.role;
      this.logger.debug(`User ${userId} has role: ${userRole}`);

      // Based on the role, check for the appropriate entity
      if (userRole === UserRole.EMPLOYER) {
        try {
          const company = await this.companyService.findByClientIdOrNull(userId);
          if (company) {
            this.logger.debug(`User ${userId} has a company entity`);
            return { type: 'company', entity: company, userRole };
          }
        } catch (error) {
          // Company not found is expected for new employers
        }
      } else if (userRole === UserRole.JOB_SEEKER || userRole === UserRole.GRADUATE) {
        try {
          const jobSeeker = await this.jobSeekerService.getByClientId(userId);
          if (jobSeeker) {
            this.logger.debug(`User ${userId} has a job-seeker entity`);
            return { type: 'job-seeker', entity: jobSeeker, userRole };
          }
        } catch (error) {
          // Job seeker not found is expected for new job seekers
        }
      }

      // User has a role but no entity yet - this is normal during onboarding
      this.logger.debug(`User ${userId} with role ${userRole} has no entity yet`);
      return { type: null, entity: null, userRole };
    } catch (error) {
      this.logger.error(`Error checking entities for user ${userId}:`, error);
      return { type: null, entity: null };
    }
  }

  /**
   * Create a job seeker profile if one doesn't exist
   * This is called when a user logs in with Auth0 as a job seeker
   * @param user The authenticated user from Auth0
   * @returns The job seeker profile or null if creation failed
   */
  async createJobSeekerProfileIfNotExists(user: User): Promise<any> {
    if (!user || !user.userId) {
      this.logger.error('Cannot create job seeker profile: Invalid user data');
      return null;
    }

    try {
      // CRITICAL: Check user's role first
      const userRoleEntity = await this.rolesService.findByClientId(user.userId);
      if (userRoleEntity && userRoleEntity.role === UserRole.EMPLOYER) {
        this.logger.warn(`User ${user.userId} has employer role. NOT creating job seeker profile.`);
        return null;
      }

      // Check if profile exists
      const existingProfile = await this.jobSeekerService.getByClientId(user.userId);

      if (existingProfile) {
        this.logger.debug(`Job seeker profile already exists for user ${user.userId}`);
        return existingProfile;
      }

      // Double-check user role hasn't changed
      const roleDoubleCheck = await this.rolesService.findByClientId(user.userId);
      if (roleDoubleCheck && roleDoubleCheck.role === UserRole.EMPLOYER) {
        this.logger.error(
          `CRITICAL: User role changed to employer for user ${user.userId}. Aborting job seeker creation.`,
        );
        return null;
      }

      // Create a new profile with data from Auth0
      this.logger.debug(`Creating new job seeker profile for user ${user.userId}`);

      // Extract profile data from user object
      const profileData = {
        clientId: user.userId,
        email: user.email,
        firstName: user.firstName || user.given_name || (user.name ? user.name.split(' ')[0] : ''),
        lastName:
          user.lastName ||
          user.family_name ||
          (user.name ? user.name.split(' ').slice(1).join(' ') : ''),
        fullName: user.fullName || user.name || '',
        profilePicture: user.picture || '',
        // Add any other fields from the user object that are relevant
      };

      // Create the profile
      const newProfile = await this.jobSeekerService.create(profileData);
      this.logger.debug(`Created new job seeker profile with ID ${newProfile.id}`);

      return newProfile;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : '';
      this.logger.error(`Error creating job seeker profile: ${errorMessage}`, errorStack);
      return null;
    }
  }

  /**
   * Update user role in Auth0 app_metadata
   * @param userId The Auth0 user ID (sub)
   * @param role The role to set
   */
  async updateUserRole(userId: string, role: string): Promise<void> {
    await this.auth0ManagementService.updateUserRole(userId, role);
  }
}
