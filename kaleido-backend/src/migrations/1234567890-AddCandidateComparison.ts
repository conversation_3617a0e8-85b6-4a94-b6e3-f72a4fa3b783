import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCandidateComparison1234567890 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create candidate_comparisons table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS candidate_comparisons (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "clientId" UUID NOT NULL,
        "jobId" UUID NOT NULL,
        "candidateIds" JSONB NOT NULL,
        "comparisonTitle" VARCHAR(500) NOT NULL,
        "userPrompt" TEXT,
        "comparisonType" VARCHAR(255),
        "comparisonCriteria" JSONB NOT NULL,
        "comparisonResults" JSONB,
        "visualData" JSONB,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
        "performedBy" UUID,
        metadata JSONB,
        "createdAt" TIMESTAMP DEFAULT NOW(),
        "updatedAt" TIMESTAMP DEFAULT NOW(),
        "expiresAt" TIMESTAMP
      );
    `);

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX idx_comparison_client_job ON candidate_comparisons("clientId", "jobId");
    `);

    await queryRunner.query(`
      CREATE INDEX idx_comparison_status ON candidate_comparisons(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_comparison_created ON candidate_comparisons("createdAt");
    `);

    // Add foreign key constraints if needed
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      ADD CONSTRAINT fk_comparison_job 
      FOREIGN KEY ("jobId") 
      REFERENCES jobs(id) 
      ON DELETE CASCADE;
    `);

    // Create trigger to update updatedAt
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW."updatedAt" = NOW();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_candidate_comparisons_updated_at 
      BEFORE UPDATE ON candidate_comparisons 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS update_candidate_comparisons_updated_at ON candidate_comparisons;
    `);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_comparison_client_job`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_comparison_status`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_comparison_created`);

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS candidate_comparisons`);
  }
}