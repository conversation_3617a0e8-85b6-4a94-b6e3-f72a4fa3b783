import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDraftStatusToJob1753965000000 implements MigrationInterface {
  name = 'AddDraftStatusToJob1753965000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add DRAFT to the job_status_enum type
    await queryRunner.query(`
      ALTER TYPE "jobs_status_enum" RENAME TO "jobs_status_enum_old";
    `);

    await queryRunner.query(`
      CREATE TYPE "jobs_status_enum" AS ENUM(
        'DRAFT',
        'NEW',
        'APPLIED',
        'MATCHED',
        'OPEN',
        'CONTACTED',
        'INTERESTED',
        'NOT_INTERESTED',
        'INTERVIEWING',
        'HIRED',
        'REJECTED'
      );
    `);

    // Drop the default value before altering the type
    await queryRunner.query(`
      ALTER TABLE "jobs" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryRunner.query(`
      ALTER TABLE "jobs" 
      ALTER COLUMN "status" TYPE "jobs_status_enum" 
      USING "status"::"text"::"jobs_status_enum";
    `);

    // Re-add the default value after altering the type
    await queryRunner.query(`
      ALTER TABLE "jobs" ALTER COLUMN "status" SET DEFAULT 'NEW';
    `);

    await queryRunner.query(`
      DROP TYPE "jobs_status_enum_old";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove DRAFT from the job_status_enum type
    await queryRunner.query(`
      ALTER TYPE "jobs_status_enum" RENAME TO "jobs_status_enum_old";
    `);

    await queryRunner.query(`
      CREATE TYPE "jobs_status_enum" AS ENUM(
        'NEW',
        'APPLIED',
        'MATCHED',
        'OPEN',
        'CONTACTED',
        'INTERESTED',
        'NOT_INTERESTED',
        'INTERVIEWING',
        'HIRED',
        'REJECTED'
      );
    `);

    // First update any existing DRAFT status to NEW before changing the type
    await queryRunner.query(`
      UPDATE "jobs" SET "status" = 'NEW' WHERE "status" = 'DRAFT';
    `);

    // Drop the default value before altering the type
    await queryRunner.query(`
      ALTER TABLE "jobs" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryRunner.query(`
      ALTER TABLE "jobs" 
      ALTER COLUMN "status" TYPE "jobs_status_enum" 
      USING "status"::"text"::"jobs_status_enum";
    `);

    // Re-add the default value after altering the type
    await queryRunner.query(`
      ALTER TABLE "jobs" ALTER COLUMN "status" SET DEFAULT 'NEW';
    `);

    await queryRunner.query(`
      DROP TYPE "jobs_status_enum_old";
    `);
  }
}
