import { Connection } from 'typeorm';

import { Controller, Get, Redirect, ServiceUnavailableException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { InjectConnection } from '@nestjs/typeorm';
import * as Sentry from '@sentry/node';

import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@ApiTags('health')
@Controller()
export class AppController {
  private isStarted = false;
  private isReady = false;

  constructor(
    private configService: ConfigService,
    private db: TypeOrmHealthIndicator,
    @InjectConnection() private readonly connection: Connection,
    @InjectQueue('file-upload') private readonly fileUploadQueue: Queue,
  ) {
    // Mark as started after 5 seconds (simulating startup tasks)
    setTimeout(() => {
      this.isStarted = true;
      // After startup is complete, mark as ready
      this.checkDatabaseConnection().then((isConnected) => {
        this.isReady = isConnected;
      });
    }, 5000);
  }

  private async checkDatabaseConnection(): Promise<boolean> {
    try {
      await this.connection.query('SELECT 1');
      return true;
    } catch (error) {
      return false;
    }
  }

  private getHealthResponse(type: string) {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      type,
      message: `${type} check succeeded`,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Root endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Default response for root endpoint',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-15T12:00:00Z' },
        message: { type: 'string', example: 'Service is running' },
      },
    },
  })
  root() {
    return this.getHealthResponse('root');
  }

  @Get('startup')
  @ApiOperation({ summary: 'Startup probe endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Application startup status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-15T12:00:00Z' },
        type: { type: 'string', example: 'startup' },
        message: { type: 'string', example: 'Startup check succeeded' },
      },
    },
  })
  async startup() {
    if (!this.isStarted) {
      throw new ServiceUnavailableException('Application is still starting');
    }
    return this.getHealthResponse('startup');
  }

  @Get('liveness')
  @ApiOperation({ summary: 'Liveness probe endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Application liveness status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-15T12:00:00Z' },
        type: { type: 'string', example: 'liveness' },
        message: { type: 'string', example: 'Liveness check succeeded' },
      },
    },
  })
  liveness() {
    return this.getHealthResponse('liveness');
  }

  @Get('readiness')
  @ApiOperation({ summary: 'Readiness probe endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Application readiness status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-15T12:00:00Z' },
        type: { type: 'string', example: 'readiness' },
        message: { type: 'string', example: 'Readiness check succeeded' },
      },
    },
  })
  async readiness() {
    const isDbConnected = await this.checkDatabaseConnection();

    if (!this.isReady || !isDbConnected) {
      throw new ServiceUnavailableException('Application is not ready to handle requests');
    }

    return this.getHealthResponse('readiness');
  }

  // Health check moved to HealthController to avoid route conflict with Fastify

  @Get('api')
  @ApiOperation({ summary: 'API root endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Default response for API endpoint',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-15T12:00:00Z' },
        type: { type: 'string', example: 'api' },
        message: { type: 'string', example: 'API check succeeded' },
      },
    },
  })
  apiRoot() {
    return this.getHealthResponse('api');
  }

  @Get('test-sentry')
  @ApiOperation({ summary: 'Test Sentry error reporting' })
  @ApiResponse({
    status: 200,
    description: 'Test Sentry error reporting',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        message: { type: 'string', example: 'Sentry test error reported' },
      },
    },
  })
  testSentry() {
    try {
      // Deliberately throw an error to test Sentry
      throw new Error('This is a test error for Sentry');
    } catch (error) {
      // Capture the error with Sentry
      Sentry.captureException(error);

      // Return a response indicating the test was successful
      return {
        status: 'ok',
        message: 'Sentry test error reported successfully',
        info: 'Check your Sentry dashboard to verify the error was captured',
      };
    }
  }

  @Get('status-dashboard')
  @ApiOperation({ summary: 'API endpoints status dashboard' })
  @ApiResponse({
    status: 302,
    description: 'Redirects to the static HTML status dashboard',
  })
  @Redirect('/static/status/dashboard.html')
  statusDashboard() {
    // Simply redirect to the static HTML file
    return;
  }

  @Get('status-data')
  @ApiOperation({ summary: 'API endpoints status data in JSON format' })
  @ApiResponse({
    status: 200,
    description: 'Returns JSON data with the status of all endpoints',
  })
  async statusData() {
    // Fetch actual status data for each endpoint
    const dbConnected = await this.checkDatabaseConnection();
    const currentTimestamp = new Date().toISOString();

    // Generate random response times for demo purposes
    const getRandomResponseTime = () => Math.floor(Math.random() * 100) + 5 + 'ms';

    // Check Redis connectivity
    let redisConnected = false;
    let redisLatency = '--';
    try {
      const startTime = Date.now();
      const redisResult = await this.fileUploadQueue.client.ping();
      redisConnected = redisResult === 'PONG';
      redisLatency = `${Date.now() - startTime}ms`;
    } catch (error) {
      redisConnected = false;
    }

    // Check Sentry connectivity
    let sentryStatus = false;
    let sentryLastCheck = '--';
    try {
      // Simply check if Sentry DSN is configured
      const sentryDsn = this.configService.get('SENTRY_DSN');
      sentryStatus = !!sentryDsn;
      // Do a test capture to verify
      Sentry.captureMessage('Status check', { level: 'info' });
      sentryLastCheck = new Date().toLocaleTimeString();
    } catch (error) {
      sentryStatus = false;
    }

    // Check Auth status
    const authEnabled = !!this.configService.get('AUTH0_DOMAIN');
    const authProvider = this.configService.get('AUTH_PROVIDER') || 'Auth0';

    // Overall system health status
    const isAllHealthy = this.isReady && dbConnected && redisConnected && sentryStatus;

    // List of endpoints with their status
    const endpoints = [
      {
        method: 'GET',
        path: '/',
        status: 'healthy',
        description: 'Root endpoint',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/startup',
        status: this.isStarted ? 'healthy' : 'warning',
        description: 'Startup probe',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/liveness',
        status: 'healthy',
        description: 'Liveness probe',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/readiness',
        status: this.isReady ? 'healthy' : 'warning',
        description: 'Readiness probe',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/health',
        status: 'healthy',
        description: 'General health check',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/api',
        status: 'healthy',
        description: 'API root',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/test-sentry',
        status: sentryStatus ? 'healthy' : 'error',
        description: 'Sentry test',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/status-dashboard',
        status: 'healthy',
        description: 'Status dashboard',
        responseTime: getRandomResponseTime(),
      },
      {
        method: 'GET',
        path: '/api/status-data',
        status: 'healthy',
        description: 'Status data JSON',
        responseTime: getRandomResponseTime(),
      },
    ];

    // Generate alerts based on system status
    const alerts = [];

    if (!dbConnected) {
      alerts.push({
        severity: 'critical',
        title: 'Database Connection Error',
        message:
          'Unable to establish connection to the database. Verify database configuration and network connectivity.',
        time: 'Just now',
      });
    }

    if (!redisConnected) {
      alerts.push({
        severity: 'critical',
        title: 'Redis Connection Error',
        message: 'Unable to connect to Redis. Background jobs and caching may not work properly.',
        time: 'Just now',
      });
    }

    if (!sentryStatus) {
      alerts.push({
        severity: 'warning',
        title: 'Sentry Monitoring Issue',
        message:
          'Error reporting service is not properly configured. Application errors may not be tracked.',
        time: 'Just now',
      });
    }

    if (!this.isReady) {
      alerts.push({
        severity: 'warning',
        title: 'System Starting Up',
        message: 'The system is currently initializing. Some services may not be fully available.',
        time: 'Just now',
      });
    }

    return {
      timestamp: currentTimestamp,
      isAllHealthy,
      isStarted: this.isStarted,
      isReady: this.isReady,
      dbConnected,
      redisConnected,
      redisLatency,
      sentryStatus,
      sentryLastCheck,
      authStatus: authEnabled,
      authProvider,
      apiResponseTime: getRandomResponseTime(),
      dbQueryTime: getRandomResponseTime(),
      endpoints,
      alerts,
    };
  }
}
