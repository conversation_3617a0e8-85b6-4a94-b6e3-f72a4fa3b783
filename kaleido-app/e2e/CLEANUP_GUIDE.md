# Auth0 Test User Cleanup Guide

## Current Status

The Auth0 Management API cleanup is not working because the M2M (Machine-to-Machine) application needs to be granted access to the Management API.

### Error Details
```
error: 'access_denied',
error_description: '<PERSON><PERSON> is not authorized to access "https://headstart.uk.auth0.com/api/v2/". 
You need to create a "client-grant" associated to this API.'
```

## How to Fix

To enable automatic cleanup, you need to:

1. **Login to Auth0 Dashboard**
   - Go to https://manage.auth0.com/

2. **Navigate to Applications**
   - Find your M2M application (Client ID: `IaswtcaIRSFGbg3adTthWGDrQlCyrZML`)

3. **Go to APIs**
   - Navigate to APIs section
   - Find "Auth0 Management API"

4. **Authorize the M2M Application**
   - Click on "Machine to Machine Applications" tab
   - Find your M2M application and toggle it ON
   - Select the following scopes:
     - `read:users`
     - `delete:users`
     - `read:users_app_metadata`
   - Click "Update"

5. **Alternative: Create Client Grant via API**
   ```bash
   curl -X POST https://headstart.uk.auth0.com/api/v2/client-grants \
     -H "Authorization: Bearer YOUR_MANAGEMENT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "client_id": "IaswtcaIRSFGbg3adTthWGDrQlCyrZML",
       "audience": "https://headstart.uk.auth0.com/api/v2/",
       "scope": ["read:users", "delete:users"]
     }'
   ```

## Manual Cleanup Script

Until the M2M access is configured, you can manually clean up test users:

```typescript
// Run this test to manually list and clean test users
test.only('manual cleanup of test users', async () => {
  const testEmails = [
    '<EMAIL>',
    '<EMAIL>',
    // Add more test emails here
  ]
  
  for (const email of testEmails) {
    console.log(`Attempting to delete: ${email}`)
    await deleteAuth0User(email).catch(console.error)
  }
})
```

## Test User Pattern

All test users follow this pattern:
- Email: `test-{role}-{timestamp}-{random}@kaleidotalent.com`
- Password: `TestPassword123!`

You can search for these in the Auth0 dashboard using the pattern `*@kaleidotalent.com`

## Current Test Users Created

During our testing session, the following users were created and need cleanup:
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- And others from earlier test runs

## Once Fixed

After granting M2M access, the cleanup will work automatically in tests:
```
✅ Successfully deleted Auth0 user: auth0|123456 (<EMAIL>)
```