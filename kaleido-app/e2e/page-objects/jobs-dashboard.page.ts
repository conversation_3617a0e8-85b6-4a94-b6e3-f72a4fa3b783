import { Page, Locator } from '@playwright/test';

export class JobsDashboardPage {
  readonly page: Page;
  
  // Header Elements
  readonly pageTitle: Locator;
  readonly createJobButton: Locator;
  readonly refreshButton: Locator;
  readonly viewToggle: Locator;
  
  // Statistics Cards
  readonly totalJobsCard: Locator;
  readonly activeJobsCard: Locator;
  readonly totalCandidatesCard: Locator;
  readonly shortlistedCandidatesCard: Locator;
  readonly avgTimeToHireCard: Locator;
  readonly conversionRateCard: Locator;
  
  // Time Filter
  readonly timeFilterDropdown: Locator;
  readonly dailyOption: Locator;
  readonly weeklyOption: Locator;
  readonly monthlyOption: Locator;
  readonly yearlyOption: Locator;
  
  // Job Status Tabs
  readonly allJobsTab: Locator;
  readonly draftTab: Locator;
  readonly publishedTab: Locator;
  readonly closedTab: Locator;
  readonly activeTab: Locator;
  readonly tabCount: Locator;
  
  // Job List/Grid
  readonly jobCards: Locator;
  readonly jobRows: Locator;
  readonly noJobsMessage: Locator;
  readonly loadingIndicator: Locator;
  
  // Job Card Elements
  readonly jobTitle: Locator;
  readonly jobLocation: Locator;
  readonly jobType: Locator;
  readonly jobStatus: Locator;
  readonly candidateCount: Locator;
  readonly postedDate: Locator;
  readonly viewJobButton: Locator;
  readonly editJobButton: Locator;
  readonly duplicateJobButton: Locator;
  readonly moreOptionsButton: Locator;
  
  // Search and Filters
  readonly searchInput: Locator;
  readonly departmentFilter: Locator;
  readonly locationFilter: Locator;
  readonly jobTypeFilter: Locator;
  readonly dateRangeFilter: Locator;
  readonly clearFiltersButton: Locator;
  
  // Bulk Actions
  readonly selectAllCheckbox: Locator;
  readonly bulkActionsDropdown: Locator;
  readonly bulkPublishButton: Locator;
  readonly bulkCloseButton: Locator;
  readonly bulkDeleteButton: Locator;
  
  // Pagination
  readonly previousPageButton: Locator;
  readonly nextPageButton: Locator;
  readonly pageNumbers: Locator;
  readonly itemsPerPage: Locator;
  
  // View Options
  readonly gridViewButton: Locator;
  readonly listViewButton: Locator;
  readonly sortByDropdown: Locator;
  
  // Quick Actions Menu
  readonly quickActionsMenu: Locator;
  readonly publishJobOption: Locator;
  readonly unpublishJobOption: Locator;
  readonly closeJobOption: Locator;
  readonly deleteJobOption: Locator;
  readonly viewCandidatesOption: Locator;
  readonly viewAnalyticsOption: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Header Elements
    this.pageTitle = page.locator('h1:has-text("Jobs"), [data-testid="page-title"]');
    this.createJobButton = page.locator('button:has-text("Create Job"), button:has-text("Post a Job")');
    this.refreshButton = page.locator('button[aria-label="Refresh"]');
    this.viewToggle = page.locator('[data-testid="view-toggle"]');
    
    // Statistics Cards
    this.totalJobsCard = page.locator('[data-testid="total-jobs-card"], .stat-card:has-text("Total Jobs")');
    this.activeJobsCard = page.locator('[data-testid="active-jobs-card"], .stat-card:has-text("Active Jobs")');
    this.totalCandidatesCard = page.locator('[data-testid="total-candidates-card"], .stat-card:has-text("Total Candidates")');
    this.shortlistedCandidatesCard = page.locator('[data-testid="shortlisted-card"], .stat-card:has-text("Shortlisted")');
    this.avgTimeToHireCard = page.locator('[data-testid="time-to-hire-card"], .stat-card:has-text("Avg Time to Hire")');
    this.conversionRateCard = page.locator('[data-testid="conversion-rate-card"], .stat-card:has-text("Conversion Rate")');
    
    // Time Filter
    this.timeFilterDropdown = page.locator('select[name="timeFilter"], [data-testid="time-filter"]');
    this.dailyOption = page.locator('option[value="daily"]');
    this.weeklyOption = page.locator('option[value="weekly"]');
    this.monthlyOption = page.locator('option[value="monthly"]');
    this.yearlyOption = page.locator('option[value="yearly"]');
    
    // Job Status Tabs
    this.allJobsTab = page.locator('button[role="tab"]:has-text("All"), [data-testid="all-jobs-tab"]');
    this.draftTab = page.locator('button[role="tab"]:has-text("Draft"), [data-testid="draft-tab"]');
    this.publishedTab = page.locator('button[role="tab"]:has-text("Published"), [data-testid="published-tab"]');
    this.closedTab = page.locator('button[role="tab"]:has-text("Closed"), [data-testid="closed-tab"]');
    this.activeTab = page.locator('button[role="tab"][aria-selected="true"]');
    this.tabCount = page.locator('.tab-count, [data-testid="tab-count"]');
    
    // Job List/Grid
    this.jobCards = page.locator('[data-testid="job-card"], .job-card');
    this.jobRows = page.locator('tr[data-testid="job-row"], tbody tr');
    this.noJobsMessage = page.locator('[data-testid="no-jobs"], .empty-state');
    this.loadingIndicator = page.locator('[data-testid="loading"], .loading-spinner');
    
    // Job Card Elements (scoped selectors)
    this.jobTitle = page.locator('[data-testid="job-title"], .job-title');
    this.jobLocation = page.locator('[data-testid="job-location"], .job-location');
    this.jobType = page.locator('[data-testid="job-type"], .job-type');
    this.jobStatus = page.locator('[data-testid="job-status"], .status-badge');
    this.candidateCount = page.locator('[data-testid="candidate-count"], .candidate-count');
    this.postedDate = page.locator('[data-testid="posted-date"], .posted-date');
    this.viewJobButton = page.locator('button:has-text("View"), a:has-text("View")');
    this.editJobButton = page.locator('button:has-text("Edit")');
    this.duplicateJobButton = page.locator('button:has-text("Duplicate")');
    this.moreOptionsButton = page.locator('button[aria-label="More options"]');
    
    // Search and Filters
    this.searchInput = page.locator('input[placeholder*="Search jobs"]');
    this.departmentFilter = page.locator('select[name="department"], [data-testid="department-filter"]');
    this.locationFilter = page.locator('select[name="location"], [data-testid="location-filter"]');
    this.jobTypeFilter = page.locator('select[name="jobType"], [data-testid="job-type-filter"]');
    this.dateRangeFilter = page.locator('[data-testid="date-range-filter"]');
    this.clearFiltersButton = page.locator('button:has-text("Clear filters")');
    
    // Bulk Actions
    this.selectAllCheckbox = page.locator('input[type="checkbox"][data-testid="select-all"]');
    this.bulkActionsDropdown = page.locator('[data-testid="bulk-actions"]');
    this.bulkPublishButton = page.locator('button:has-text("Publish Selected")');
    this.bulkCloseButton = page.locator('button:has-text("Close Selected")');
    this.bulkDeleteButton = page.locator('button:has-text("Delete Selected")');
    
    // Pagination
    this.previousPageButton = page.locator('button[aria-label="Previous page"]');
    this.nextPageButton = page.locator('button[aria-label="Next page"]');
    this.pageNumbers = page.locator('[data-testid="page-number"]');
    this.itemsPerPage = page.locator('select[name="itemsPerPage"]');
    
    // View Options
    this.gridViewButton = page.locator('button[aria-label="Grid view"]');
    this.listViewButton = page.locator('button[aria-label="List view"]');
    this.sortByDropdown = page.locator('select[name="sortBy"], [data-testid="sort-dropdown"]');
    
    // Quick Actions Menu
    this.quickActionsMenu = page.locator('[data-testid="quick-actions-menu"]');
    this.publishJobOption = page.locator('button:has-text("Publish")').nth(1); // Avoid header button
    this.unpublishJobOption = page.locator('button:has-text("Unpublish")');
    this.closeJobOption = page.locator('button:has-text("Close Job")');
    this.deleteJobOption = page.locator('button:has-text("Delete")');
    this.viewCandidatesOption = page.locator('button:has-text("View Candidates")');
    this.viewAnalyticsOption = page.locator('button:has-text("View Analytics")');
  }

  async goto() {
    await this.page.goto('/jobs');
  }

  async waitForJobsToLoad() {
    await this.page.waitForLoadState('networkidle');
    // Wait for either jobs or empty state
    await this.page.waitForSelector('[data-testid="job-card"], [data-testid="no-jobs"]', { timeout: 10000 });
  }

  async createNewJob() {
    await this.createJobButton.click();
    await this.page.waitForURL('**/job-description-creation**');
  }

  async selectTimeFilter(period: 'daily' | 'weekly' | 'monthly' | 'yearly') {
    await this.timeFilterDropdown.selectOption(period);
    await this.page.waitForTimeout(1000); // Wait for data refresh
  }

  async selectStatusTab(status: 'all' | 'draft' | 'published' | 'closed') {
    const tabMap = {
      'all': this.allJobsTab,
      'draft': this.draftTab,
      'published': this.publishedTab,
      'closed': this.closedTab
    };
    
    await tabMap[status].click();
    await this.waitForJobsToLoad();
  }

  async getActiveTabName(): Promise<string> {
    return await this.activeTab.textContent() || '';
  }

  async getJobCount(): Promise<number> {
    const cards = await this.jobCards.count();
    if (cards > 0) return cards;
    
    const rows = await this.jobRows.count();
    return rows;
  }

  async searchJobs(query: string) {
    await this.searchInput.fill(query);
    await this.page.waitForTimeout(500); // Debounce
    await this.waitForJobsToLoad();
  }

  async filterByDepartment(department: string) {
    await this.departmentFilter.selectOption(department);
    await this.waitForJobsToLoad();
  }

  async filterByLocation(location: string) {
    await this.locationFilter.selectOption(location);
    await this.waitForJobsToLoad();
  }

  async filterByJobType(jobType: string) {
    await this.jobTypeFilter.selectOption(jobType);
    await this.waitForJobsToLoad();
  }

  async clearAllFilters() {
    await this.clearFiltersButton.click();
    await this.waitForJobsToLoad();
  }

  async viewJob(index: number = 0) {
    const viewButtons = await this.viewJobButton.all();
    if (viewButtons[index]) {
      await viewButtons[index].click();
      await this.page.waitForURL('**/jobs/**/manage');
    }
  }

  async editJob(index: number = 0) {
    const editButtons = await this.editJobButton.all();
    if (editButtons[index]) {
      await editButtons[index].click();
      await this.page.waitForURL('**/job-description-creation**');
    }
  }

  async duplicateJob(index: number = 0) {
    const duplicateButtons = await this.duplicateJobButton.all();
    if (duplicateButtons[index]) {
      await duplicateButtons[index].click();
      await this.page.waitForTimeout(1000);
    }
  }

  async openQuickActions(jobIndex: number = 0) {
    const moreButtons = await this.moreOptionsButton.all();
    if (moreButtons[jobIndex]) {
      await moreButtons[jobIndex].click();
      await this.quickActionsMenu.waitFor({ state: 'visible' });
    }
  }

  async publishJobFromList(jobIndex: number = 0) {
    await this.openQuickActions(jobIndex);
    await this.publishJobOption.click();
    await this.page.waitForTimeout(1000);
  }

  async viewCandidates(jobIndex: number = 0) {
    await this.openQuickActions(jobIndex);
    await this.viewCandidatesOption.click();
    await this.page.waitForURL('**/candidates');
  }

  async getStatistics() {
    return {
      totalJobs: await this.totalJobsCard.locator('.stat-value').textContent(),
      activeJobs: await this.activeJobsCard.locator('.stat-value').textContent(),
      totalCandidates: await this.totalCandidatesCard.locator('.stat-value').textContent(),
      shortlisted: await this.shortlistedCandidatesCard.locator('.stat-value').textContent(),
      avgTimeToHire: await this.avgTimeToHireCard.locator('.stat-value').textContent(),
      conversionRate: await this.conversionRateCard.locator('.stat-value').textContent()
    };
  }

  async getJobInfo(index: number = 0) {
    const cards = await this.jobCards.all();
    if (!cards[index]) return null;
    
    const card = cards[index];
    return {
      title: await card.locator('[data-testid="job-title"]').textContent(),
      location: await card.locator('[data-testid="job-location"]').textContent(),
      type: await card.locator('[data-testid="job-type"]').textContent(),
      status: await card.locator('[data-testid="job-status"]').textContent(),
      candidates: await card.locator('[data-testid="candidate-count"]').textContent(),
      postedDate: await card.locator('[data-testid="posted-date"]').textContent()
    };
  }

  async selectJob(index: number) {
    const checkboxes = await this.page.locator('input[type="checkbox"][data-testid="select-job"]').all();
    if (checkboxes[index]) {
      await checkboxes[index].check();
    }
  }

  async selectAllJobs() {
    await this.selectAllCheckbox.check();
  }

  async bulkPublish() {
    await this.bulkPublishButton.click();
    await this.page.waitForTimeout(2000);
  }

  async switchToListView() {
    await this.listViewButton.click();
    await this.page.waitForTimeout(500);
  }

  async switchToGridView() {
    await this.gridViewButton.click();
    await this.page.waitForTimeout(500);
  }

  async sortBy(criteria: string) {
    await this.sortByDropdown.selectOption(criteria);
    await this.waitForJobsToLoad();
  }

  async goToNextPage() {
    if (await this.nextPageButton.isEnabled()) {
      await this.nextPageButton.click();
      await this.waitForJobsToLoad();
    }
  }

  async hasJobs(): Promise<boolean> {
    const jobCount = await this.getJobCount();
    return jobCount > 0;
  }
}