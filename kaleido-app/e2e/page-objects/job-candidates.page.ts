import { Page, Locator } from '@playwright/test';

export class JobCandidatesPage {
  readonly page: Page;
  
  // Header Elements
  readonly pageTitle: Locator;
  readonly jobTitle: Locator;
  readonly backButton: Locator;
  readonly viewJobDetailsButton: Locator;
  readonly editCriteriaButton: Locator;
  
  // Status Manager (Upload/Scout/Match)
  readonly uploadResumeButton: Locator;
  readonly scoutCandidatesButton: Locator;
  readonly matchRankButton: Locator;
  readonly statusCards: Locator;
  readonly activeJobsIndicator: Locator;
  readonly uploadProgress: Locator;
  readonly scoutProgress: Locator;
  readonly matchProgress: Locator;
  
  // Filters and Sorting
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  readonly scoreFilter: Locator;
  readonly dateFilter: Locator;
  readonly sortDropdown: Locator;
  readonly clearFiltersButton: Locator;
  readonly filterCount: Locator;
  
  // Candidate List
  readonly candidateCards: Locator;
  readonly candidateRows: Locator; // Table view
  readonly noCandidatesMessage: Locator;
  readonly loadingSpinner: Locator;
  
  // Candidate Card Elements
  readonly candidateName: Locator;
  readonly candidateTitle: Locator;
  readonly candidateLocation: Locator;
  readonly matchScore: Locator;
  readonly matchBadge: Locator;
  readonly applicationStatus: Locator;
  readonly viewDetailsButton: Locator;
  readonly quickActionsMenu: Locator;
  
  // Bulk Actions
  readonly selectAllCheckbox: Locator;
  readonly bulkActionsBar: Locator;
  readonly bulkShortlistButton: Locator;
  readonly bulkRejectButton: Locator;
  readonly bulkMessageButton: Locator;
  readonly selectedCount: Locator;
  
  // Statistics
  readonly totalCandidatesCount: Locator;
  readonly shortlistedCount: Locator;
  readonly interviewedCount: Locator;
  readonly rejectedCount: Locator;
  readonly averageMatchScore: Locator;
  
  // Pagination
  readonly previousPageButton: Locator;
  readonly nextPageButton: Locator;
  readonly pageNumbers: Locator;
  readonly itemsPerPageSelect: Locator;
  readonly paginationInfo: Locator;
  
  // View Toggle
  readonly gridViewButton: Locator;
  readonly tableViewButton: Locator;
  readonly compactViewButton: Locator;
  
  // Upload Modal
  readonly uploadModal: Locator;
  readonly dropzoneArea: Locator;
  readonly fileInput: Locator;
  readonly uploadedFilesList: Locator;
  readonly startUploadButton: Locator;
  readonly cancelUploadButton: Locator;
  
  // Scout Modal
  readonly scoutModal: Locator;
  readonly scoutSourceSelect: Locator;
  readonly scoutKeywords: Locator;
  readonly scoutLocationInput: Locator;
  readonly startScoutButton: Locator;
  
  // Match/Rank Configuration
  readonly matchConfigModal: Locator;
  readonly matchCriteriaList: Locator;
  readonly topCandidateThreshold: Locator;
  readonly strongMatchThreshold: Locator;
  readonly startMatchingButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Header Elements
    this.pageTitle = page.locator('h1, [data-testid="page-title"]');
    this.jobTitle = page.locator('[data-testid="job-title"], .job-title');
    this.backButton = page.locator('button:has-text("Back")');
    this.viewJobDetailsButton = page.locator('button:has-text("View Job Details")');
    this.editCriteriaButton = page.locator('button:has-text("Edit Criteria")');
    
    // Status Manager
    this.uploadResumeButton = page.locator('button:has-text("Upload Resumes")');
    this.scoutCandidatesButton = page.locator('button:has-text("Scout Candidates")');
    this.matchRankButton = page.locator('button:has-text("Match & Rank")');
    this.statusCards = page.locator('[data-testid="status-card"], .status-card');
    this.activeJobsIndicator = page.locator('[data-testid="active-jobs"], .active-jobs');
    this.uploadProgress = page.locator('[data-testid="upload-progress"]');
    this.scoutProgress = page.locator('[data-testid="scout-progress"]');
    this.matchProgress = page.locator('[data-testid="match-progress"]');
    
    // Filters and Sorting
    this.searchInput = page.locator('input[placeholder*="Search candidates"]');
    this.statusFilter = page.locator('select[name="status"], [data-testid="status-filter"]');
    this.scoreFilter = page.locator('[data-testid="score-filter"], input[type="range"]');
    this.dateFilter = page.locator('[data-testid="date-filter"]');
    this.sortDropdown = page.locator('select[name="sort"], [data-testid="sort-dropdown"]');
    this.clearFiltersButton = page.locator('button:has-text("Clear filters")');
    this.filterCount = page.locator('[data-testid="filter-count"], .filter-count');
    
    // Candidate List
    this.candidateCards = page.locator('[data-testid="candidate-card"], .candidate-card');
    this.candidateRows = page.locator('tr[data-testid="candidate-row"], tbody tr');
    this.noCandidatesMessage = page.locator('[data-testid="no-candidates"], .empty-state');
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading-spinner');
    
    // Candidate Card Elements (scoped selectors)
    this.candidateName = page.locator('[data-testid="candidate-name"], .candidate-name');
    this.candidateTitle = page.locator('[data-testid="candidate-title"], .candidate-title');
    this.candidateLocation = page.locator('[data-testid="candidate-location"], .candidate-location');
    this.matchScore = page.locator('[data-testid="match-score"], .match-score');
    this.matchBadge = page.locator('[data-testid="match-badge"], .match-badge');
    this.applicationStatus = page.locator('[data-testid="application-status"], .status-badge');
    this.viewDetailsButton = page.locator('button:has-text("View Details")');
    this.quickActionsMenu = page.locator('[data-testid="quick-actions"], .quick-actions');
    
    // Bulk Actions
    this.selectAllCheckbox = page.locator('input[type="checkbox"][data-testid="select-all"]');
    this.bulkActionsBar = page.locator('[data-testid="bulk-actions"], .bulk-actions-bar');
    this.bulkShortlistButton = page.locator('button:has-text("Shortlist Selected")');
    this.bulkRejectButton = page.locator('button:has-text("Reject Selected")');
    this.bulkMessageButton = page.locator('button:has-text("Message Selected")');
    this.selectedCount = page.locator('[data-testid="selected-count"]');
    
    // Statistics
    this.totalCandidatesCount = page.locator('[data-testid="total-candidates"]');
    this.shortlistedCount = page.locator('[data-testid="shortlisted-count"]');
    this.interviewedCount = page.locator('[data-testid="interviewed-count"]');
    this.rejectedCount = page.locator('[data-testid="rejected-count"]');
    this.averageMatchScore = page.locator('[data-testid="average-match-score"]');
    
    // Pagination
    this.previousPageButton = page.locator('button[aria-label="Previous page"]');
    this.nextPageButton = page.locator('button[aria-label="Next page"]');
    this.pageNumbers = page.locator('[data-testid="page-number"]');
    this.itemsPerPageSelect = page.locator('select[name="itemsPerPage"]');
    this.paginationInfo = page.locator('[data-testid="pagination-info"]');
    
    // View Toggle
    this.gridViewButton = page.locator('button[aria-label="Grid view"]');
    this.tableViewButton = page.locator('button[aria-label="Table view"]');
    this.compactViewButton = page.locator('button[aria-label="Compact view"]');
    
    // Upload Modal
    this.uploadModal = page.locator('[role="dialog"]:has-text("Upload Resumes")');
    this.dropzoneArea = page.locator('[data-testid="dropzone"], .dropzone');
    this.fileInput = page.locator('input[type="file"][accept*="pdf"]');
    this.uploadedFilesList = page.locator('[data-testid="uploaded-files"]');
    this.startUploadButton = page.locator('button:has-text("Start Upload")');
    this.cancelUploadButton = page.locator('button:has-text("Cancel")');
    
    // Scout Modal
    this.scoutModal = page.locator('[role="dialog"]:has-text("Scout Candidates")');
    this.scoutSourceSelect = page.locator('select[name="scoutSource"]');
    this.scoutKeywords = page.locator('input[placeholder*="keywords"]');
    this.scoutLocationInput = page.locator('input[placeholder*="location"]');
    this.startScoutButton = page.locator('button:has-text("Start Scouting")');
    
    // Match/Rank Configuration
    this.matchConfigModal = page.locator('[role="dialog"]:has-text("Match & Rank")');
    this.matchCriteriaList = page.locator('[data-testid="match-criteria"]');
    this.topCandidateThreshold = page.locator('input[name="topThreshold"]');
    this.strongMatchThreshold = page.locator('input[name="strongThreshold"]');
    this.startMatchingButton = page.locator('button:has-text("Start Matching")');
  }

  async goto(jobId: string) {
    await this.page.goto(`/jobs/${jobId}/candidates`);
  }

  async waitForCandidatesToLoad() {
    await this.page.waitForLoadState('networkidle');
    // Wait for either candidates or empty state
    await this.page.waitForSelector('[data-testid="candidate-card"], [data-testid="no-candidates"]', { timeout: 10000 });
  }

  async getCandidateCount(): Promise<number> {
    const cards = await this.candidateCards.count();
    if (cards > 0) return cards;
    
    const rows = await this.candidateRows.count();
    return rows;
  }

  async searchCandidates(query: string) {
    await this.searchInput.fill(query);
    await this.page.waitForTimeout(500); // Debounce
    await this.waitForCandidatesToLoad();
  }

  async filterByStatus(status: string) {
    await this.statusFilter.selectOption(status);
    await this.waitForCandidatesToLoad();
  }

  async filterByScore(minScore: number) {
    await this.scoreFilter.fill(minScore.toString());
    await this.waitForCandidatesToLoad();
  }

  async sortBy(criteria: 'match-score' | 'date' | 'name') {
    await this.sortDropdown.selectOption(criteria);
    await this.waitForCandidatesToLoad();
  }

  async clearAllFilters() {
    await this.clearFiltersButton.click();
    await this.waitForCandidatesToLoad();
  }

  async viewCandidate(index: number = 0) {
    const viewButtons = await this.viewDetailsButton.all();
    if (viewButtons[index]) {
      await viewButtons[index].click();
      await this.page.waitForURL('**/candidates/**');
    }
  }

  async selectCandidate(index: number) {
    const checkboxes = await this.page.locator('input[type="checkbox"][data-testid="select-candidate"]').all();
    if (checkboxes[index]) {
      await checkboxes[index].check();
    }
  }

  async selectAllCandidates() {
    await this.selectAllCheckbox.check();
  }

  async bulkShortlist() {
    await this.bulkShortlistButton.click();
    await this.page.waitForTimeout(1000);
  }

  async uploadResumes(filePaths: string[]) {
    await this.uploadResumeButton.click();
    await this.uploadModal.waitFor({ state: 'visible' });
    
    // Upload files
    await this.fileInput.setInputFiles(filePaths);
    await this.startUploadButton.click();
    
    // Wait for upload to complete
    await this.page.waitForSelector('[data-testid="upload-complete"]', { timeout: 30000 });
  }

  async startScouting(keywords: string, location: string) {
    await this.scoutCandidatesButton.click();
    await this.scoutModal.waitFor({ state: 'visible' });
    
    await this.scoutKeywords.fill(keywords);
    await this.scoutLocationInput.fill(location);
    await this.startScoutButton.click();
    
    // Wait for scouting to start
    await this.scoutProgress.waitFor({ state: 'visible' });
  }

  async startMatching() {
    await this.matchRankButton.click();
    await this.matchConfigModal.waitFor({ state: 'visible' });
    
    // Configure thresholds if needed
    await this.topCandidateThreshold.fill('80');
    await this.strongMatchThreshold.fill('60');
    
    await this.startMatchingButton.click();
    
    // Wait for matching to start
    await this.matchProgress.waitFor({ state: 'visible' });
  }

  async waitForMatchingComplete() {
    // Wait for progress to reach 100% or complete status
    await this.page.waitForSelector('[data-testid="match-complete"]', { timeout: 60000 });
  }

  async getStatistics() {
    return {
      total: await this.totalCandidatesCount.textContent(),
      shortlisted: await this.shortlistedCount.textContent(),
      interviewed: await this.interviewedCount.textContent(),
      rejected: await this.rejectedCount.textContent(),
      averageScore: await this.averageMatchScore.textContent()
    };
  }

  async getCandidateInfo(index: number = 0) {
    const cards = await this.candidateCards.all();
    if (!cards[index]) return null;
    
    const card = cards[index];
    return {
      name: await card.locator('[data-testid="candidate-name"]').textContent(),
      title: await card.locator('[data-testid="candidate-title"]').textContent(),
      location: await card.locator('[data-testid="candidate-location"]').textContent(),
      score: await card.locator('[data-testid="match-score"]').textContent(),
      status: await card.locator('[data-testid="application-status"]').textContent()
    };
  }

  async switchToTableView() {
    await this.tableViewButton.click();
    await this.page.waitForTimeout(500);
  }

  async switchToGridView() {
    await this.gridViewButton.click();
    await this.page.waitForTimeout(500);
  }

  async goToNextPage() {
    if (await this.nextPageButton.isEnabled()) {
      await this.nextPageButton.click();
      await this.waitForCandidatesToLoad();
    }
  }

  async goToPreviousPage() {
    if (await this.previousPageButton.isEnabled()) {
      await this.previousPageButton.click();
      await this.waitForCandidatesToLoad();
    }
  }

  async hasActiveJobs(): Promise<boolean> {
    const activeCount = await this.activeJobsIndicator.textContent();
    return activeCount !== '0' && activeCount !== '';
  }

  async getActiveJobsCount(): Promise<number> {
    const text = await this.activeJobsIndicator.textContent();
    return parseInt(text?.replace(/\D/g, '') || '0');
  }
}