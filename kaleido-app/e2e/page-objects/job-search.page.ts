import { Page, Locator } from '@playwright/test';

export class JobSearchPage {
  readonly page: Page;
  
  // Search elements
  readonly searchInput: Locator;
  readonly locationInput: Locator;
  readonly searchButton: Locator;
  readonly keywordSearchInput: Locator;
  readonly locationSearchInput: Locator;
  
  // Filter elements
  readonly filtersSection: Locator;
  readonly jobTypeFilter: Locator;
  readonly salaryRangeFilter: Locator;
  readonly experienceLevelFilter: Locator;
  readonly remoteOnlyCheckbox: Locator;
  readonly datePostedFilter: Locator;
  readonly industryFilter: Locator;
  readonly clearFiltersButton: Locator;
  
  // View toggles
  readonly modernViewToggle: Locator;
  readonly tableViewToggle: Locator;
  readonly showAllJobsToggle: Locator;
  
  // Job listing elements
  readonly jobCards: Locator;
  readonly jobRows: Locator; // For table view
  readonly jobTitles: Locator;
  readonly companyNames: Locator;
  readonly jobLocations: Locator;
  readonly salaryInfo: Locator;
  readonly applyButtons: Locator;
  readonly viewDetailsButtons: Locator;
  
  // Pagination
  readonly previousPageButton: Locator;
  readonly nextPageButton: Locator;
  readonly pageNumbers: Locator;
  
  // Loading states
  readonly loadingSpinner: Locator;
  readonly noResultsMessage: Locator;
  
  // Job details modal
  readonly jobDetailsModal: Locator;
  readonly modalCloseButton: Locator;
  readonly modalApplyButton: Locator;
  readonly modalSaveButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Search elements
    this.searchInput = page.locator('input[placeholder*="job title"], input[placeholder*="keyword"], input[placeholder*="search"]').first();
    this.locationInput = page.locator('input[placeholder*="location"], input[placeholder*="city"]').first();
    this.searchButton = page.getByRole('button', { name: /search|find/i });
    this.keywordSearchInput = page.locator('[data-testid="keyword-search"], input[name="keyword"]');
    this.locationSearchInput = page.locator('[data-testid="location-search"], input[name="location"]');
    
    // Filter elements
    this.filtersSection = page.locator('[data-testid="filters-section"], .filters-section, aside');
    this.jobTypeFilter = page.locator('select[name="jobType"], [data-testid="job-type-filter"]');
    this.salaryRangeFilter = page.locator('[data-testid="salary-filter"], input[name*="salary"]');
    this.experienceLevelFilter = page.locator('[data-testid="experience-filter"], select[name="experience"]');
    this.remoteOnlyCheckbox = page.locator('input[type="checkbox"][name*="remote"]');
    this.datePostedFilter = page.locator('[data-testid="date-posted-filter"], select[name="datePosted"]');
    this.industryFilter = page.locator('[data-testid="industry-filter"], select[name="industry"]');
    this.clearFiltersButton = page.getByRole('button', { name: /clear.*filter/i });
    
    // View toggles
    this.modernViewToggle = page.getByRole('button', { name: /modern.*view/i });
    this.tableViewToggle = page.getByRole('button', { name: /table.*view/i });
    this.showAllJobsToggle = page.locator('input[type="checkbox"][name*="showAll"], [data-testid="show-all-toggle"]');
    
    // Job listing elements
    this.jobCards = page.locator('[data-testid="job-card"], .job-card');
    this.jobRows = page.locator('tr[data-testid="job-row"], tbody tr');
    this.jobTitles = page.locator('[data-testid="job-title"], h3.job-title, a.job-title');
    this.companyNames = page.locator('[data-testid="company-name"], .company-name');
    this.jobLocations = page.locator('[data-testid="job-location"], .job-location');
    this.salaryInfo = page.locator('[data-testid="salary-info"], .salary-info');
    this.applyButtons = page.getByRole('button', { name: /apply/i });
    this.viewDetailsButtons = page.getByRole('button', { name: /view.*detail/i });
    
    // Pagination
    this.previousPageButton = page.getByRole('button', { name: /previous/i });
    this.nextPageButton = page.getByRole('button', { name: /next/i });
    this.pageNumbers = page.locator('[data-testid="page-number"], .page-number');
    
    // Loading states
    this.loadingSpinner = page.locator('.loading, .spinner, [data-testid="loading"]');
    this.noResultsMessage = page.locator('[data-testid="no-results"], .no-results');
    
    // Job details modal
    this.jobDetailsModal = page.locator('[role="dialog"], .modal, [data-testid="job-details-modal"]');
    this.modalCloseButton = page.locator('[role="dialog"] button[aria-label="Close"], .modal button.close');
    this.modalApplyButton = page.locator('[role="dialog"] button:has-text("Apply")');
    this.modalSaveButton = page.locator('[role="dialog"] button:has-text("Save")');
  }

  async goto() {
    await this.page.goto('/job-search');
  }

  async searchJobs(keyword: string, location?: string) {
    await this.searchInput.fill(keyword);
    if (location && await this.locationInput.isVisible()) {
      await this.locationInput.fill(location);
    }
    await this.searchButton.click();
    await this.waitForSearchResults();
  }

  async waitForSearchResults() {
    // Wait for loading to complete
    await this.page.waitForLoadState('networkidle');
    
    // Wait for either job cards or no results message
    await this.page.waitForSelector(
      '[data-testid="job-card"], .job-card, [data-testid="no-results"], .no-results',
      { timeout: 10000 }
    );
  }

  async filterByJobType(jobType: string) {
    await this.jobTypeFilter.selectOption(jobType);
    await this.waitForSearchResults();
  }

  async filterBySalaryRange(min: number, max: number) {
    const minInput = this.page.locator('input[name*="minSalary"], input[placeholder*="Min"]');
    const maxInput = this.page.locator('input[name*="maxSalary"], input[placeholder*="Max"]');
    
    await minInput.fill(min.toString());
    await maxInput.fill(max.toString());
    await this.waitForSearchResults();
  }

  async filterByExperience(level: string) {
    await this.experienceLevelFilter.selectOption(level);
    await this.waitForSearchResults();
  }

  async toggleRemoteOnly() {
    await this.remoteOnlyCheckbox.click();
    await this.waitForSearchResults();
  }

  async clearAllFilters() {
    if (await this.clearFiltersButton.isVisible()) {
      await this.clearFiltersButton.click();
      await this.waitForSearchResults();
    }
  }

  async getJobCount(): Promise<number> {
    // Try different selectors for job count
    const jobCards = await this.jobCards.count();
    if (jobCards > 0) return jobCards;
    
    const jobRows = await this.jobRows.count();
    return jobRows;
  }

  async getJobTitles(): Promise<string[]> {
    await this.jobTitles.first().waitFor({ state: 'visible', timeout: 5000 }).catch(() => {});
    const titles = await this.jobTitles.allTextContents();
    return titles.filter(title => title.trim() !== '');
  }

  async viewJobDetails(index: number = 0) {
    const viewButtons = await this.viewDetailsButtons.all();
    if (viewButtons[index]) {
      await viewButtons[index].click();
      await this.jobDetailsModal.waitFor({ state: 'visible' });
    }
  }

  async applyToJob(index: number = 0) {
    const applyButtons = await this.applyButtons.all();
    if (applyButtons[index]) {
      await applyButtons[index].click();
    }
  }

  async closeJobDetailsModal() {
    if (await this.jobDetailsModal.isVisible()) {
      await this.modalCloseButton.click();
      await this.jobDetailsModal.waitFor({ state: 'hidden' });
    }
  }

  async switchToTableView() {
    if (await this.tableViewToggle.isVisible()) {
      await this.tableViewToggle.click();
      await this.page.waitForTimeout(500); // Wait for view transition
    }
  }

  async switchToModernView() {
    if (await this.modernViewToggle.isVisible()) {
      await this.modernViewToggle.click();
      await this.page.waitForTimeout(500); // Wait for view transition
    }
  }

  async goToNextPage() {
    if (await this.nextPageButton.isEnabled()) {
      await this.nextPageButton.click();
      await this.waitForSearchResults();
    }
  }

  async goToPreviousPage() {
    if (await this.previousPageButton.isEnabled()) {
      await this.previousPageButton.click();
      await this.waitForSearchResults();
    }
  }

  async isNoResultsDisplayed(): Promise<boolean> {
    return await this.noResultsMessage.isVisible();
  }

  async getSelectedFilters(): Promise<Record<string, any>> {
    const filters: Record<string, any> = {};
    
    // Get job type if selected
    if (await this.jobTypeFilter.isVisible()) {
      filters.jobType = await this.jobTypeFilter.inputValue();
    }
    
    // Get remote only status
    if (await this.remoteOnlyCheckbox.isVisible()) {
      filters.remoteOnly = await this.remoteOnlyCheckbox.isChecked();
    }
    
    // Add other filters as needed
    
    return filters;
  }
}