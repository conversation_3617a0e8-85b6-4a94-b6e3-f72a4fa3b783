import { Page, Locator } from '@playwright/test';

export class UnifiedJobViewPage {
  readonly page: Page;
  
  // Tab Navigation
  readonly jobDetailsTab: Locator;
  readonly jdToneTab: Locator;
  readonly videoJDTab: Locator;
  readonly videoIntroTab: Locator;
  readonly activeTab: Locator;
  readonly tabIndicator: Locator;
  
  // Header Actions
  readonly viewAssessmentButton: Locator;
  readonly editJobButton: Locator;
  readonly publishButton: Locator;
  readonly publishDropdown: Locator;
  readonly moreActionsButton: Locator; // Mobile menu
  
  // Publish/Unpublish Actions
  readonly publishDropdownToggle: Locator;
  readonly publishConfirmButton: Locator;
  readonly unpublishConfirmButton: Locator;
  readonly publishToJobboardOption: Locator;
  readonly publishToLinkedInOption: Locator;
  readonly publishToTwitterOption: Locator;
  readonly publishToFacebookOption: Locator;
  readonly publishToInstagramOption: Locator;
  readonly platformCheckbox: Locator;
  readonly externalPlatformsToggle: Locator;
  readonly connectSocialMediaButton: Locator;
  
  // Job Details Elements
  readonly companyAvatar: Locator;
  readonly jobStatus: Locator;
  readonly jobLocation: Locator;
  readonly salaryRange: Locator;
  readonly experienceLevel: Locator;
  readonly jobType: Locator;
  readonly candidateStats: Locator;
  readonly totalCandidatesCount: Locator;
  readonly topTierThreshold: Locator;
  readonly secondTierThreshold: Locator;
  readonly heroSection: Locator;
  readonly jobTitle: Locator;
  readonly companyName: Locator;
  readonly postedDate: Locator;
  readonly department: Locator;
  readonly hiringType: Locator;
  readonly companyDescription: Locator;
  readonly jobResponsibilities: Locator;
  readonly skillsSection: Locator;
  readonly educationSection: Locator;
  readonly languagesSection: Locator;
  readonly softSkillsSection: Locator;
  readonly benefitsSection: Locator;
  readonly careerGrowthSection: Locator;
  
  // Video JD Tab Elements
  readonly videoPlayer: Locator;
  readonly videoScript: Locator;
  readonly avatarSelector: Locator;
  readonly voiceSelector: Locator;
  readonly backgroundSelector: Locator;
  readonly generateVideoButton: Locator;
  readonly downloadVideoButton: Locator;
  readonly videoProgress: Locator;
  readonly videoHistoryButton: Locator;
  readonly videoHistoryModal: Locator;
  readonly leftColumn: Locator;
  readonly rightColumn: Locator;
  readonly loadingDisplay: Locator;
  
  // JD Tone Tab Elements
  readonly toneSelector: Locator;
  readonly professionalTone: Locator;
  readonly friendlyTone: Locator;
  readonly casualTone: Locator;
  readonly enthusiasticTone: Locator;
  readonly formalTone: Locator;
  readonly conversationalTone: Locator;
  readonly likeFor5YearOldTone: Locator;
  readonly genZTone: Locator;
  readonly generateJDButton: Locator;
  readonly regenerateToneButton: Locator;
  readonly tonePreview: Locator;
  readonly saveToneButton: Locator;
  readonly editModeToggle: Locator;
  readonly jdTextarea: Locator;
  readonly jdMarkdownPreview: Locator;
  readonly toneGuide: Locator;
  
  // Video Intro Questions Tab Elements
  readonly videoQuestionsList: Locator;
  readonly addQuestionButton: Locator;
  readonly questionInput: Locator;
  readonly questionTextarea: Locator;
  readonly questionDurationSlider: Locator;
  readonly removeQuestionButton: Locator;
  readonly saveQuestionsButton: Locator;
  readonly questionSuggestions: Locator;
  readonly questionSuggestionsButton: Locator;
  readonly questionSuggestionsPopup: Locator;
  readonly questionSuggestionsClose: Locator;
  readonly culturalFitCards: Locator;
  readonly candidateResponses: Locator;
  readonly paginationControls: Locator;
  readonly saveChangesSlider: Locator;
  
  // Loading States
  readonly loadingSpinner: Locator;
  readonly errorMessage: Locator;
  readonly successMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Tab Navigation
    this.jobDetailsTab = page.locator('button:has-text("Job Details")');
    this.jdToneTab = page.locator('button:has-text("JD Tone")');
    this.videoJDTab = page.locator('button:has-text("Video JD")');
    this.videoIntroTab = page.locator('button:has-text("Video Intro")');
    this.activeTab = page.locator('.h-[2px].bg-gradient-to-r.from-purple-600.to-pink-600').locator('..');
    this.tabIndicator = page.locator('.h-[2px].bg-gradient-to-r.from-purple-600.to-pink-600');
    
    // Header Actions
    this.viewAssessmentButton = page.locator('button:has-text("View Assessment")');
    this.editJobButton = page.locator('button:has-text("Edit Job")');
    this.publishButton = page.locator('button').filter({ hasText: /Publish to|Published/ });
    this.publishDropdown = page.locator('.absolute.top-full.right-0.mt-2.w-80');
    this.moreActionsButton = page.locator('button').filter({ has: page.locator('svg.lucide-more-vertical') });
    
    // Publish/Unpublish Actions
    this.publishDropdownToggle = page.locator('button:has-text("Publish to"), button:has-text("Published")');
    this.publishConfirmButton = page.locator('button:has-text("Publish Now")');
    this.unpublishConfirmButton = page.locator('button:has-text("Unpublish")').last();
    this.publishToJobboardOption = page.locator('text=Our Job Board');
    this.publishToLinkedInOption = page.locator('text=LinkedIn');
    this.publishToTwitterOption = page.locator('text=Twitter/X');
    this.publishToFacebookOption = page.locator('text=Facebook');
    this.publishToInstagramOption = page.locator('text=Instagram');
    this.platformCheckbox = page.locator('input[type="checkbox"]');
    this.externalPlatformsToggle = page.locator('button:has-text("External Platforms")');
    this.connectSocialMediaButton = page.locator('button:has-text("Go to Settings")');
    
    // Job Details Elements
    this.companyAvatar = page.locator('.w-28.h-28.rounded-2xl.bg-white\\/20');
    this.jobStatus = page.locator('.flex.items-center.gap-2.px-4.py-2.rounded-full');
    this.jobLocation = page.locator('text=MapPin').locator('xpath=following-sibling::span');
    this.salaryRange = page.locator('text=DollarSign').locator('xpath=following-sibling::span');
    this.experienceLevel = page.locator('text=Star').locator('xpath=following-sibling::span');
    this.jobType = page.locator('text=Clock').locator('xpath=following-sibling::span');
    this.candidateStats = page.locator('.absolute.bottom-4.right-8');
    this.totalCandidatesCount = page.locator('text=Users2').locator('xpath=following-sibling::div/span');
    this.topTierThreshold = page.locator('text=Trophy').locator('xpath=following-sibling::div/span');
    this.secondTierThreshold = page.locator('text=Medal').locator('xpath=following-sibling::div/span');
    this.heroSection = page.locator('.relative.h-\\[280px\\].overflow-hidden');
    this.jobTitle = page.locator('h1');
    this.companyName = page.locator('text=Building').locator('xpath=following-sibling::span');
    this.postedDate = page.locator('text=Posted').locator('xpath=following-sibling::p');
    this.department = page.locator('text=Department').locator('xpath=following-sibling::p');
    this.hiringType = page.locator('text=Hiring Type').locator('xpath=following-sibling::p');
    this.companyDescription = page.locator('text=About').locator('xpath=following-sibling::p');
    this.jobResponsibilities = page.locator('text=Key Responsibilities').locator('xpath=following::div');
    this.skillsSection = page.locator('text=Required Skills').locator('xpath=following::div');
    this.educationSection = page.locator('text=Education Requirements').locator('xpath=following::ul');
    this.languagesSection = page.locator('text=Language Requirements').locator('xpath=following::ul');
    this.softSkillsSection = page.locator('text=Soft Skills').locator('xpath=following::div');
    this.benefitsSection = page.locator('text=Benefits Package').locator('xpath=following::ul');
    this.careerGrowthSection = page.locator('text=Career Growth').locator('xpath=following::ul');
    
    // Video JD Tab Elements
    this.videoPlayer = page.locator('video, [data-testid="video-player"]');
    this.videoScript = page.locator('[data-testid="video-script"], .video-script');
    this.avatarSelector = page.locator('[data-testid="avatar-selector"]');
    this.voiceSelector = page.locator('[data-testid="voice-selector"]');
    this.backgroundSelector = page.locator('[data-testid="background-selector"]');
    this.generateVideoButton = page.locator('button:has-text("Generate Video")');
    this.downloadVideoButton = page.locator('button:has-text("Download Video")');
    this.videoProgress = page.locator('[data-testid="video-progress"]');
    this.videoHistoryButton = page.locator('button:has-text("View History")');
    this.videoHistoryModal = page.locator('[role="dialog"]:has-text("Video History")');
    this.leftColumn = page.locator('.grid .xl\\:grid-cols-2 > div').first();
    this.rightColumn = page.locator('.grid .xl\\:grid-cols-2 > div').last();
    this.loadingDisplay = page.locator('[data-testid="loading-display"]');
    
    // JD Tone Tab Elements
    this.toneSelector = page.locator('.grid.grid-cols-4.lg\\:grid-cols-8.gap-2');
    this.professionalTone = page.locator('button:has-text("Professional")');
    this.friendlyTone = page.locator('button:has-text("Friendly")');
    this.casualTone = page.locator('button:has-text("Casual")');
    this.enthusiasticTone = page.locator('button:has-text("Enthusiastic")');
    this.formalTone = page.locator('button:has-text("Formal")');
    this.conversationalTone = page.locator('button:has-text("Conversational")');
    this.likeFor5YearOldTone = page.locator('button:has-text("5 year old")');
    this.genZTone = page.locator('button:has-text("Gen Z")');
    this.generateJDButton = page.locator('button:has-text("Generate JD")');
    this.regenerateToneButton = page.locator('button:has-text("Regenerate JD")');
    this.tonePreview = page.locator('.min-h-\\[400px\\].p-6.rounded-xl.bg-gray-900\\/50');
    this.saveToneButton = page.locator('button:has-text("Save Changes")');
    this.editModeToggle = page.locator('button:has-text("Edit"), button:has-text("Preview")');
    this.jdTextarea = page.locator('textarea.w-full.min-h-\\[400px\\]');
    this.jdMarkdownPreview = page.locator('.min-h-\\[400px\\].p-6.rounded-xl.bg-gray-900\\/50.overflow-y-auto');
    this.toneGuide = page.locator('.bg-gradient-to-br.from-white\\/\\[0\\.01\\]');
    
    // Video Intro Questions Tab Elements
    this.videoQuestionsList = page.locator('.space-y-4');
    this.addQuestionButton = page.locator('button:has-text("Add Another Question")');
    this.questionInput = page.locator('input[placeholder*="question"]');
    this.questionTextarea = page.locator('textarea[placeholder*="engaging question"]');
    this.questionDurationSlider = page.locator('.MuiSlider-root');
    this.removeQuestionButton = page.locator('button:has([data-testid="trash-icon"])');
    this.saveQuestionsButton = page.locator('button:has-text("Save Questions")');
    this.questionSuggestions = page.locator('[data-testid="question-suggestions"]');
    this.questionSuggestionsButton = page.locator('button:has-text("Get Question Suggestions")');
    this.questionSuggestionsPopup = page.locator('.absolute.bottom-full.left-0.right-0.mb-4');
    this.questionSuggestionsClose = page.locator('button:has([data-testid="x-icon"])');
    this.culturalFitCards = page.locator('[data-testid="cultural-fit-card"]');
    this.candidateResponses = page.locator('.lg\\:col-span-1 .space-y-3');
    this.paginationControls = page.locator('.flex.items-center.gap-2');
    this.saveChangesSlider = page.locator('[data-testid="save-changes-slider"]');
    
    // Loading States
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading, .spinner');
    this.errorMessage = page.locator('[role="alert"], .error-message');
    this.successMessage = page.locator('.success-message, [data-testid="success-toast"]');
  }

  async goto(jobId: string) {
    await this.page.goto(`/jobs/${jobId}/manage`);
  }

  async waitForJobToLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.jobTitle.waitFor({ state: 'visible', timeout: 10000 });
  }

  async selectTab(tabName: 'job-details' | 'jd-tone' | 'video-jd' | 'video-intro') {
    const tabMap = {
      'job-details': this.jobDetailsTab,
      'jd-tone': this.jdToneTab,
      'video-jd': this.videoJDTab,
      'video-intro': this.videoIntroTab
    };
    
    await tabMap[tabName].click();
    await this.page.waitForTimeout(500); // Wait for tab transition
  }

  async getCurrentTab(): Promise<string> {
    const activeTabText = await this.activeTab.textContent() || '';
    return activeTabText.toLowerCase().replace(/\s+/g, '-');
  }

  async viewAssessment() {
    await this.viewAssessmentButton.click();
    await this.page.waitForURL('**/candidates');
  }

  async editJob() {
    await this.editJobButton.click();
    await this.page.waitForURL('**/job-description-creation**');
  }

  async publishJob(platforms: string[] = ['jobboard']) {
    await this.publishButton.click();
    await this.publishDropdown.waitFor({ state: 'visible' });
    
    // Select platforms
    for (const platform of platforms) {
      const platformMap: Record<string, Locator> = {
        'jobboard': this.publishToJobboardOption,
        'linkedin': this.publishToLinkedInOption,
        'twitter': this.publishToTwitterOption,
        'facebook': this.publishToFacebookOption,
        'instagram': this.publishToInstagramOption
      };
      
      if (platformMap[platform]) {
        await platformMap[platform].click();
      }
    }
    
    await this.publishConfirmButton.click();
    await this.successMessage.waitFor({ state: 'visible', timeout: 10000 });
  }

  async unpublishJob() {
    await this.publishButton.click();
    await this.publishDropdown.waitFor({ state: 'visible' });
    await this.unpublishConfirmButton.click();
    await this.successMessage.waitFor({ state: 'visible', timeout: 10000 });
  }

  async isJobPublished(): Promise<boolean> {
    return await this.jobStatus.locator('text=Live').isVisible();
  }

  async getJobDetails() {
    return {
      title: await this.jobTitle.textContent(),
      company: await this.companyName.textContent(),
      location: await this.jobLocation.textContent(),
      salary: await this.salaryRange.textContent(),
      type: await this.jobType.textContent()
    };
  }

  async getCandidateStats() {
    return {
      total: await this.totalCandidatesCount.textContent(),
      topTier: await this.topTierThreshold.textContent(),
      secondTier: await this.secondTierThreshold.textContent()
    };
  }

  async getVideoIntroQuestions(): Promise<string[]> {
    const questions = await this.videoQuestionsList.locator('textarea').allTextContents();
    return questions.filter(q => q.trim() !== '');
  }

  async addVideoIntroQuestion(question: string, duration: number) {
    await this.addQuestionButton.click();
    
    // Fill the new question
    const questions = await this.questionTextarea.all();
    const lastQuestion = questions[questions.length - 1];
    await lastQuestion.fill(question);
    
    // Set duration using slider
    const sliders = await this.questionDurationSlider.all();
    const lastSlider = sliders[sliders.length - 1];
    // Set slider value (this might need adjustment based on actual slider implementation)
    await lastSlider.click();
  }

  // Video JD Tab Methods
  async openVideoHistory() {
    await this.videoHistoryButton.click();
    await this.videoHistoryModal.waitFor({ state: 'visible' });
  }

  async closeVideoHistory() {
    await this.page.keyboard.press('Escape');
    await this.videoHistoryModal.waitFor({ state: 'hidden' });
  }

  // JD Tone Tab Methods
  async selectTone(tone: 'professional' | 'friendly' | 'casual' | 'enthusiastic' | 'formal' | 'conversational' | 'like for a 5 year old' | 'gen z') {
    const toneMap = {
      'professional': this.professionalTone,
      'friendly': this.friendlyTone,
      'casual': this.casualTone,
      'enthusiastic': this.enthusiasticTone,
      'formal': this.formalTone,
      'conversational': this.conversationalTone,
      'like for a 5 year old': this.likeFor5YearOldTone,
      'gen z': this.genZTone
    };
    
    await toneMap[tone].click();
  }

  async generateJobDescription() {
    await this.generateJDButton.click();
    // Wait for generation to complete
    await this.page.waitForSelector('text=generated successfully', { timeout: 30000 });
  }

  async regenerateTone() {
    await this.regenerateToneButton.click();
    // Wait for regeneration to complete
    await this.page.waitForSelector('text=generated successfully', { timeout: 30000 });
  }

  async toggleEditMode() {
    await this.editModeToggle.click();
  }

  async editJobDescription(content: string) {
    await this.toggleEditMode();
    await this.jdTextarea.fill(content);
  }

  async getTonePreview(): Promise<string> {
    return await this.tonePreview.textContent() || '';
  }

  // Video Intro Questions Tab Methods
  async openQuestionSuggestions(questionIndex: number = 0) {
    const buttons = await this.questionSuggestionsButton.all();
    if (buttons[questionIndex]) {
      await buttons[questionIndex].click();
      await this.questionSuggestionsPopup.waitFor({ state: 'visible' });
    }
  }

  async closeQuestionSuggestions() {
    await this.questionSuggestionsClose.click();
    await this.questionSuggestionsPopup.waitFor({ state: 'hidden' });
  }

  async selectQuestionSuggestion(suggestionText: string) {
    await this.page.locator(`text=${suggestionText}`).click();
  }

  async removeQuestion(questionIndex: number) {
    const removeButtons = await this.removeQuestionButton.all();
    if (removeButtons[questionIndex]) {
      await removeButtons[questionIndex].click();
    }
  }

  async setQuestionDuration(questionIndex: number, duration: number) {
    const sliders = await this.questionDurationSlider.all();
    if (sliders[questionIndex]) {
      // Click on the slider to set the value
      // This is a simplified approach - actual implementation may vary
      await sliders[questionIndex].click();
    }
  }

  async getCandidateResponsesCount(): Promise<number> {
    const responses = await this.candidateResponses.locator('.backdrop-blur-xl').count();
    return responses;
  }

  async navigateCandidateResponsesPage(direction: 'next' | 'previous') {
    const button = direction === 'next' ? 
      this.paginationControls.locator('button').last() :
      this.paginationControls.locator('button').first();
    
    if (await button.isEnabled()) {
      await button.click();
    }
  }

  // Publish Dropdown Methods
  async selectPlatformForPublishing(platform: 'jobboard' | 'linkedin' | 'twitter' | 'facebook' | 'instagram') {
    const platformMap = {
      'jobboard': this.publishToJobboardOption,
      'linkedin': this.publishToLinkedInOption,
      'twitter': this.publishToTwitterOption,
      'facebook': this.publishToFacebookOption,
      'instagram': this.publishToInstagramOption
    };
    
    await platformMap[platform].click();
  }

  async toggleExternalPlatforms() {
    await this.externalPlatformsToggle.click();
  }

  async goToSocialMediaSettings() {
    await this.connectSocialMediaButton.click();
    await this.page.waitForURL('**/company-settings?tab=social-media');
  }

  // Job Details Methods
  async getJobTitle(): Promise<string> {
    return await this.jobTitle.textContent() || '';
  }

  async getCompanyName(): Promise<string> {
    return await this.companyName.textContent() || '';
  }

  async getJobLocation(): Promise<string> {
    return await this.jobLocation.textContent() || '';
  }

  async getSalaryRange(): Promise<string> {
    return await this.salaryRange.textContent() || '';
  }

  async getExperienceLevel(): Promise<string> {
    return await this.experienceLevel.textContent() || '';
  }

  async getJobType(): Promise<string> {
    return await this.jobType.textContent() || '';
  }

  async getPostedDate(): Promise<string> {
    return await this.postedDate.textContent() || '';
  }

  async getDepartment(): Promise<string> {
    return await this.department.textContent() || '';
  }

  async getHiringType(): Promise<string> {
    return await this.hiringType.textContent() || '';
  }

  async getCompanyDescription(): Promise<string> {
    return await this.companyDescription.textContent() || '';
  }

  async getJobResponsibilities(): Promise<string[]> {
    return await this.jobResponsibilities.locator('span').allTextContents();
  }

  async getSkills(): Promise<string[]> {
    return await this.skillsSection.locator('span').allTextContents();
  }

  async getEducationRequirements(): Promise<string[]> {
    return await this.educationSection.locator('li span').allTextContents();
  }

  async getLanguageRequirements(): Promise<string[]> {
    return await this.languagesSection.locator('li span').allTextContents();
  }

  async getSoftSkills(): Promise<string[]> {
    return await this.softSkillsSection.locator('span').allTextContents();
  }

  async getBenefits(): Promise<string[]> {
    return await this.benefitsSection.locator('li span').allTextContents();
  }

  async getCareerGrowthOpportunities(): Promise<string[]> {
    return await this.careerGrowthSection.locator('li span').allTextContents();
  }

  // Candidate Stats Methods
  async getTotalCandidatesCount(): Promise<number> {
    const text = await this.totalCandidatesCount.textContent();
    return parseInt(text?.replace(/\D/g, '') || '0');
  }

  async getTopTierThreshold(): Promise<number> {
    const text = await this.topTierThreshold.textContent();
    return parseInt(text?.replace(/\D/g, '') || '0');
  }

  async getSecondTierThreshold(): Promise<number> {
    const text = await this.secondTierThreshold.textContent();
    return parseInt(text?.replace(/\D/g, '') || '0');
  }

  // Mobile-specific methods
  async isMobileView(): Promise<boolean> {
    const mobileMenu = this.page.locator('.md\\:hidden .relative button');
    return await mobileMenu.isVisible();
  }

  async openMobileMenu() {
    const mobileMenuButton = this.page.locator('.md\\:hidden .relative button');
    await mobileMenuButton.click();
  }

  async closeMobileMenu() {
    await this.page.keyboard.press('Escape');
  }
}