import { Page, Locator } from '@playwright/test';

export class UnifiedCandidateViewPage {
  readonly page: Page;
  
  // Tab Navigation
  readonly overviewTab: Locator;
  readonly matchAnalysisTab: Locator;
  readonly videoIntroTab: Locator;
  readonly profileTab: Locator;
  readonly applicationStatusTab: Locator;
  readonly activeTab: Locator;
  
  // Match Analysis Sub-tabs
  readonly overviewSubTab: Locator;
  readonly skillsExperienceSubTab: Locator;
  readonly locationSubTab: Locator;
  readonly teamFitSubTab: Locator;
  readonly recommendationSubTab: Locator;
  
  // Header Actions
  readonly editCriteriaButton: Locator;
  readonly candidateStats: Locator;
  
  // Overview Tab Content
  readonly candidateName: Locator;
  readonly candidateTitle: Locator;
  readonly candidateLocation: Locator;
  readonly candidateEmail: Locator;
  readonly candidatePhone: Locator;
  readonly matchScore: Locator;
  readonly matchScoreCard: Locator;
  readonly matchRank: Locator;
  readonly matchBadge: Locator;
  readonly quickSummary: Locator;
  
  // Match Analysis Content
  readonly matchReasoning: Locator;
  readonly skillsMatchScore: Locator;
  readonly experienceScore: Locator;
  readonly locationScore: Locator;
  readonly overallMatchChart: Locator;
  readonly areasOfStrength: Locator;
  readonly areasForImprovement: Locator;
  readonly missingRequirements: Locator;
  readonly interviewFocusAreas: Locator;
  readonly hiringRecommendation: Locator;
  
  // Application Status Tab Content
  readonly currentStatus: Locator;
  readonly statusDropdown: Locator;
  readonly statusUpdateButton: Locator;
  readonly statusTimeline: Locator;
  readonly statusNotes: Locator;
  readonly addNoteButton: Locator;
  readonly noteInput: Locator;
  readonly saveNoteButton: Locator;
  
  // Video Intro Tab Content
  readonly videoPlayer: Locator;
  readonly videoQuestions: Locator;
  readonly videoResponses: Locator;
  readonly downloadVideoButton: Locator;
  readonly videoTranscript: Locator;
  
  // Profile Tab Content
  readonly resumeViewer: Locator;
  readonly downloadResumeButton: Locator;
  readonly workExperience: Locator;
  readonly education: Locator;
  readonly skills: Locator;
  readonly languages: Locator;
  readonly certifications: Locator;
  readonly linkedInProfile: Locator;
  readonly githubProfile: Locator;
  readonly portfolio: Locator;
  
  // Action Buttons
  readonly shortlistButton: Locator;
  readonly rejectButton: Locator;
  readonly scheduleInterviewButton: Locator;
  readonly sendMessageButton: Locator;
  readonly addTagButton: Locator;
  readonly shareProfileButton: Locator;
  
  // Loading States
  readonly loadingSpinner: Locator;
  readonly errorMessage: Locator;
  readonly successMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Tab Navigation
    this.overviewTab = page.locator('button:has-text("Overview")');
    this.matchAnalysisTab = page.locator('button:has-text("Match Analysis")');
    this.videoIntroTab = page.locator('button:has-text("Video Intro")');
    this.profileTab = page.locator('button:has-text("Profile")');
    this.applicationStatusTab = page.locator('button:has-text("Application Status")');
    this.activeTab = page.locator('.h-[2px].bg-gradient-to-r.from-purple-600.to-pink-600').locator('..');
    
    // Match Analysis Sub-tabs
    this.overviewSubTab = page.locator('button:has-text("Overview")').nth(1);
    this.skillsExperienceSubTab = page.locator('button:has-text("Skills")');
    this.locationSubTab = page.locator('button:has-text("Location")');
    this.teamFitSubTab = page.locator('button:has-text("Team Fit")');
    this.recommendationSubTab = page.locator('button:has-text("Final Summary")');
    
    // Header Actions
    this.editCriteriaButton = page.locator('button:has-text("Edit Criteria")');
    this.candidateStats = page.locator('[data-testid="candidate-stats"]');
    
    // Overview Tab Content
    this.candidateName = page.locator('[data-testid="candidate-name"], h1, h2').first();
    this.candidateTitle = page.locator('[data-testid="candidate-title"], .candidate-title');
    this.candidateLocation = page.locator('[data-testid="candidate-location"], .location').first();
    this.candidateEmail = page.locator('[data-testid="candidate-email"], a[href^="mailto:"]');
    this.candidatePhone = page.locator('[data-testid="candidate-phone"], a[href^="tel:"]');
    this.matchScore = page.locator('[data-testid="match-score"], .match-score');
    this.matchScoreCard = page.locator('.animated-score-card, [data-testid="score-card"]');
    this.matchRank = page.locator('[data-testid="match-rank"], .rank');
    this.matchBadge = page.locator('.match-badge, [data-testid="match-badge"]');
    this.quickSummary = page.locator('[data-testid="quick-summary"], .summary');
    
    // Match Analysis Content
    this.matchReasoning = page.locator('.match-reasoning, [data-testid="match-reasoning"]');
    this.skillsMatchScore = page.locator('[data-testid="skills-match-score"]');
    this.experienceScore = page.locator('[data-testid="experience-score"]');
    this.locationScore = page.locator('[data-testid="location-score"]');
    this.overallMatchChart = page.locator('[data-testid="match-chart"], canvas');
    this.areasOfStrength = page.locator('[data-testid="areas-of-strength"], .strengths');
    this.areasForImprovement = page.locator('[data-testid="areas-for-improvement"], .improvements');
    this.missingRequirements = page.locator('[data-testid="missing-requirements"], .missing-requirements');
    this.interviewFocusAreas = page.locator('[data-testid="interview-focus-areas"]');
    this.hiringRecommendation = page.locator('[data-testid="hiring-recommendation"]');
    
    // Application Status Tab Content
    this.currentStatus = page.locator('[data-testid="current-status"], .status-badge');
    this.statusDropdown = page.locator('select[name="status"], [data-testid="status-dropdown"]');
    this.statusUpdateButton = page.locator('button:has-text("Update Status")');
    this.statusTimeline = page.locator('[data-testid="status-timeline"], .timeline');
    this.statusNotes = page.locator('[data-testid="status-notes"], .notes');
    this.addNoteButton = page.locator('button:has-text("Add Note")');
    this.noteInput = page.locator('textarea[placeholder*="note"], [data-testid="note-input"]');
    this.saveNoteButton = page.locator('button:has-text("Save Note")');
    
    // Video Intro Tab Content
    this.videoPlayer = page.locator('video, [data-testid="video-player"]');
    this.videoQuestions = page.locator('[data-testid="video-questions"]');
    this.videoResponses = page.locator('[data-testid="video-responses"]');
    this.downloadVideoButton = page.locator('button:has-text("Download Video")');
    this.videoTranscript = page.locator('[data-testid="video-transcript"]');
    
    // Profile Tab Content
    this.resumeViewer = page.locator('[data-testid="resume-viewer"], iframe');
    this.downloadResumeButton = page.locator('button:has-text("Download Resume")');
    this.workExperience = page.locator('[data-testid="work-experience"], .experience');
    this.education = page.locator('[data-testid="education"], .education');
    this.skills = page.locator('[data-testid="skills"], .skills');
    this.languages = page.locator('[data-testid="languages"], .languages');
    this.certifications = page.locator('[data-testid="certifications"], .certifications');
    this.linkedInProfile = page.locator('a[href*="linkedin.com"]');
    this.githubProfile = page.locator('a[href*="github.com"]');
    this.portfolio = page.locator('[data-testid="portfolio"], .portfolio');
    
    // Action Buttons
    this.shortlistButton = page.locator('button:has-text("Shortlist")');
    this.rejectButton = page.locator('button:has-text("Reject")');
    this.scheduleInterviewButton = page.locator('button:has-text("Schedule Interview")');
    this.sendMessageButton = page.locator('button:has-text("Send Message")');
    this.addTagButton = page.locator('button:has-text("Add Tag")');
    this.shareProfileButton = page.locator('button:has-text("Share Profile")');
    
    // Loading States
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading, .spinner');
    this.errorMessage = page.locator('[role="alert"], .error-message');
    this.successMessage = page.locator('.success-message, [data-testid="success-toast"]');
  }

  async goto(jobId: string, candidateId: string) {
    await this.page.goto(`/jobs/${jobId}/candidates/${candidateId}`);
  }

  async waitForCandidateToLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.candidateName.waitFor({ state: 'visible', timeout: 10000 });
  }

  async selectTab(tabName: 'overview' | 'match-analysis' | 'video-intro' | 'profile' | 'status') {
    const tabMap = {
      'overview': this.overviewTab,
      'match-analysis': this.matchAnalysisTab,
      'video-intro': this.videoIntroTab,
      'profile': this.profileTab,
      'status': this.applicationStatusTab
    };
    
    await tabMap[tabName].click();
    await this.page.waitForTimeout(500); // Wait for tab transition
  }

  async selectMatchAnalysisSubTab(subTab: 'overview' | 'skills-experience' | 'location' | 'team-fit' | 'recommendation') {
    await this.selectTab('match-analysis');
    
    const subTabMap = {
      'overview': this.overviewSubTab,
      'skills-experience': this.skillsExperienceSubTab,
      'location': this.locationSubTab,
      'team-fit': this.teamFitSubTab,
      'recommendation': this.recommendationSubTab
    };
    
    await subTabMap[subTab].click();
    await this.page.waitForTimeout(300);
  }

  async getMatchScore(): Promise<number> {
    const scoreText = await this.matchScore.textContent();
    return parseInt(scoreText?.replace(/\D/g, '') || '0');
  }

  async getMatchRank(): Promise<number> {
    const rankText = await this.matchRank.textContent();
    return parseInt(rankText?.replace(/\D/g, '') || '0');
  }

  async getMatchBadgeType(): Promise<string> {
    const badge = await this.matchBadge.textContent();
    if (badge?.includes('Top Candidate')) return 'top';
    if (badge?.includes('Strong Match')) return 'strong';
    if (badge?.includes('Good Match')) return 'good';
    return 'standard';
  }

  async getCandidateInfo() {
    return {
      name: await this.candidateName.textContent(),
      title: await this.candidateTitle.textContent(),
      location: await this.candidateLocation.textContent(),
      email: await this.candidateEmail.textContent(),
      phone: await this.candidatePhone.textContent()
    };
  }

  async getMatchAnalysis() {
    await this.selectTab('match-analysis');
    
    return {
      reasoning: await this.matchReasoning.textContent(),
      skillsScore: await this.skillsMatchScore.textContent(),
      experienceScore: await this.experienceScore.textContent(),
      locationScore: await this.locationScore.textContent()
    };
  }

  async getStrengthsAndWeaknesses() {
    await this.selectMatchAnalysisSubTab('skills-experience');
    
    const strengths = await this.areasOfStrength.locator('li').allTextContents();
    const improvements = await this.areasForImprovement.locator('li').allTextContents();
    const missing = await this.missingRequirements.locator('li').allTextContents();
    
    return { strengths, improvements, missing };
  }

  async updateApplicationStatus(newStatus: string) {
    await this.selectTab('status');
    await this.statusDropdown.selectOption(newStatus);
    await this.statusUpdateButton.click();
    await this.successMessage.waitFor({ state: 'visible' });
  }

  async addNote(noteText: string) {
    await this.selectTab('status');
    await this.addNoteButton.click();
    await this.noteInput.fill(noteText);
    await this.saveNoteButton.click();
    await this.successMessage.waitFor({ state: 'visible' });
  }

  async shortlistCandidate() {
    await this.shortlistButton.click();
    await this.successMessage.waitFor({ state: 'visible' });
  }

  async rejectCandidate() {
    await this.rejectButton.click();
    // Handle confirmation dialog if present
    const confirmButton = this.page.locator('button:has-text("Confirm")');
    if (await confirmButton.isVisible({ timeout: 2000 })) {
      await confirmButton.click();
    }
    await this.successMessage.waitFor({ state: 'visible' });
  }

  async scheduleInterview() {
    await this.scheduleInterviewButton.click();
    // This would typically open a scheduling modal
    await this.page.waitForTimeout(1000);
  }

  async hasVideoIntro(): Promise<boolean> {
    await this.selectTab('video-intro');
    return await this.videoPlayer.isVisible();
  }

  async getVideoQuestions(): Promise<string[]> {
    await this.selectTab('video-intro');
    return await this.videoQuestions.locator('.question').allTextContents();
  }

  async viewProfile() {
    await this.selectTab('profile');
    await this.resumeViewer.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {});
  }

  async getSkills(): Promise<string[]> {
    await this.selectTab('profile');
    return await this.skills.locator('.skill-tag').allTextContents();
  }

  async getExperience(): Promise<Array<{company: string, title: string}>> {
    await this.selectTab('profile');
    const experiences = await this.workExperience.locator('.experience-item').all();
    
    const experienceList = [];
    for (const exp of experiences) {
      const company = await exp.locator('.company').textContent();
      const title = await exp.locator('.title').textContent();
      experienceList.push({ company: company || '', title: title || '' });
    }
    
    return experienceList;
  }

  async editMatchingCriteria() {
    await this.editCriteriaButton.click();
    await this.page.waitForURL('**/edit**');
  }
}