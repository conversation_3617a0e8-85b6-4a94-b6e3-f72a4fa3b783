import { Page, Locator } from '@playwright/test';

export class JobSeekerOnboardingPage {
  readonly page: Page;
  
  // Step navigation
  readonly nextButton: Locator;
  readonly backButton: Locator;
  readonly skipButton: Locator;
  readonly progressIndicator: Locator;
  readonly closeButton: Locator;
  
  // Resume Upload Step
  readonly resumeUploadArea: Locator;
  readonly resumeFileInput: Locator;
  readonly uploadedFileName: Locator;
  readonly parseResumeButton: Locator;
  
  // Personal Information Step
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly emailInput: Locator;
  readonly phoneInput: Locator;
  readonly locationInput: Locator;
  readonly linkedInInput: Locator;
  readonly websiteInput: Locator;
  
  // Professional Summary Step
  readonly summaryTextarea: Locator;
  readonly headlineInput: Locator;
  readonly yearsOfExperienceInput: Locator;
  
  // Experience Step
  readonly addExperienceButton: Locator;
  readonly jobTitleInput: Locator;
  readonly companyNameInput: Locator;
  readonly experienceStartDateInput: Locator;
  readonly experienceEndDateInput: Locator;
  readonly currentlyWorkingCheckbox: Locator;
  readonly jobDescriptionTextarea: Locator;
  
  // Education Step
  readonly addEducationButton: Locator;
  readonly schoolNameInput: Locator;
  readonly degreeInput: Locator;
  readonly fieldOfStudyInput: Locator;
  readonly educationStartDateInput: Locator;
  readonly educationEndDateInput: Locator;
  readonly currentlyStudyingCheckbox: Locator;
  
  // Skills & Languages Step
  readonly skillsInput: Locator;
  readonly languagesInput: Locator;
  readonly addSkillButton: Locator;
  readonly addLanguageButton: Locator;
  
  // Preferences Step
  readonly jobTypesCheckboxes: Locator;
  readonly remotePreferenceRadios: Locator;
  readonly minSalaryInput: Locator;
  readonly maxSalaryInput: Locator;
  readonly salaryPeriodSelect: Locator;
  readonly locationPreferencesInput: Locator;
  
  // Work Availability Step
  readonly immediatelyAvailableCheckbox: Locator;
  readonly noticePeriodInput: Locator;
  
  // Values Step
  readonly valuesCheckboxes: Locator;
  readonly valuesSearchInput: Locator;
  
  // Portfolio Step (optional)
  readonly portfolioUrlInput: Locator;
  readonly addPortfolioItemButton: Locator;
  
  // Video Introduction Step
  readonly recordVideoButton: Locator;
  readonly uploadVideoButton: Locator;
  readonly videoPreview: Locator;
  
  // Privacy Settings Step
  readonly profileVisibilityRadios: Locator;
  readonly allowMessagesCheckbox: Locator;
  readonly showSalaryCheckbox: Locator;
  readonly showContactCheckbox: Locator;
  
  // ID Verification Step (optional)
  readonly idUploadArea: Locator;
  readonly idFileInput: Locator;
  
  // Completion
  readonly completeSetupButton: Locator;
  readonly completionMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Step navigation
    this.nextButton = page.getByRole('button', { name: /^(next|continue)$/i });
    this.backButton = page.getByRole('button', { name: /^(back|previous)$/i });
    this.skipButton = page.getByRole('button', { name: /^skip$/i });
    this.progressIndicator = page.getByTestId('step-progress');
    this.closeButton = page.getByRole('button', { name: /close/i }).or(page.locator('button[aria-label="Close"]'));
    
    // Resume Upload Step
    this.resumeUploadArea = page.locator('[data-testid="resume-upload-area"], .dropzone');
    this.resumeFileInput = page.locator('input[type="file"][accept*="pdf"]');
    this.uploadedFileName = page.locator('[data-testid="uploaded-file-name"]');
    this.parseResumeButton = page.getByRole('button', { name: /parse|extract/i });
    
    // Personal Information Step
    this.firstNameInput = page.locator('input[name="firstName"], input[placeholder*="First"]');
    this.lastNameInput = page.locator('input[name="lastName"], input[placeholder*="Last"]');
    this.emailInput = page.locator('input[name="email"], input[type="email"]');
    this.phoneInput = page.locator('input[name="phone"], input[type="tel"]');
    this.locationInput = page.locator('input[name="location"], input[placeholder*="Location"]');
    this.linkedInInput = page.locator('input[name="linkedIn"], input[placeholder*="LinkedIn"]');
    this.websiteInput = page.locator('input[name="website"], input[placeholder*="Website"]');
    
    // Professional Summary Step
    this.summaryTextarea = page.locator('textarea[name="summary"], textarea[placeholder*="summary"]');
    this.headlineInput = page.locator('input[name="headline"], input[placeholder*="headline"]');
    this.yearsOfExperienceInput = page.locator('input[name="yearsOfExperience"], input[type="number"]');
    
    // Experience Step
    this.addExperienceButton = page.getByRole('button', { name: /add.*experience/i });
    this.jobTitleInput = page.locator('input[name="jobTitle"], input[placeholder*="Job Title"]');
    this.companyNameInput = page.locator('input[name="companyName"], input[placeholder*="Company"]');
    this.experienceStartDateInput = page.locator('input[name="startDate"]').first();
    this.experienceEndDateInput = page.locator('input[name="endDate"]').first();
    this.currentlyWorkingCheckbox = page.locator('input[type="checkbox"][name*="current"]');
    this.jobDescriptionTextarea = page.locator('textarea[name="description"], textarea[placeholder*="Description"]');
    
    // Education Step
    this.addEducationButton = page.getByRole('button', { name: /add.*education/i });
    this.schoolNameInput = page.locator('input[name="schoolName"], input[placeholder*="School"]');
    this.degreeInput = page.locator('input[name="degree"], input[placeholder*="Degree"]');
    this.fieldOfStudyInput = page.locator('input[name="fieldOfStudy"], input[placeholder*="Field"]');
    this.educationStartDateInput = page.locator('input[name="startDate"]').last();
    this.educationEndDateInput = page.locator('input[name="endDate"]').last();
    this.currentlyStudyingCheckbox = page.locator('input[type="checkbox"][name*="current"]').last();
    
    // Skills & Languages Step
    this.skillsInput = page.locator('input[placeholder*="skill"]');
    this.languagesInput = page.locator('input[placeholder*="language"]');
    this.addSkillButton = page.getByRole('button', { name: /add.*skill/i });
    this.addLanguageButton = page.getByRole('button', { name: /add.*language/i });
    
    // Preferences Step
    this.jobTypesCheckboxes = page.locator('input[type="checkbox"][name*="jobType"]');
    this.remotePreferenceRadios = page.locator('input[type="radio"][name*="remote"]');
    this.minSalaryInput = page.locator('input[name*="minSalary"], input[placeholder*="Min"]');
    this.maxSalaryInput = page.locator('input[name*="maxSalary"], input[placeholder*="Max"]');
    this.salaryPeriodSelect = page.locator('select[name*="salaryPeriod"]');
    this.locationPreferencesInput = page.locator('input[placeholder*="location preference"]');
    
    // Work Availability Step
    this.immediatelyAvailableCheckbox = page.locator('input[type="checkbox"][name*="immediately"]');
    this.noticePeriodInput = page.locator('input[name*="noticePeriod"]');
    
    // Values Step
    this.valuesCheckboxes = page.locator('input[type="checkbox"][name*="value"]');
    this.valuesSearchInput = page.locator('input[placeholder*="Search values"]');
    
    // Portfolio Step
    this.portfolioUrlInput = page.locator('input[placeholder*="portfolio"]');
    this.addPortfolioItemButton = page.getByRole('button', { name: /add.*portfolio/i });
    
    // Video Introduction Step
    this.recordVideoButton = page.getByRole('button', { name: /record.*video/i });
    this.uploadVideoButton = page.getByRole('button', { name: /upload.*video/i });
    this.videoPreview = page.locator('video, [data-testid="video-preview"]');
    
    // Privacy Settings Step
    this.profileVisibilityRadios = page.locator('input[type="radio"][name*="visibility"]');
    this.allowMessagesCheckbox = page.locator('input[type="checkbox"][name*="messages"]');
    this.showSalaryCheckbox = page.locator('input[type="checkbox"][name*="salary"]');
    this.showContactCheckbox = page.locator('input[type="checkbox"][name*="contact"]');
    
    // ID Verification Step
    this.idUploadArea = page.locator('[data-testid="id-upload-area"]');
    this.idFileInput = page.locator('input[type="file"][accept*="image"]');
    
    // Completion
    this.completeSetupButton = page.getByRole('button', { name: /complete|finish/i });
    this.completionMessage = page.locator('[data-testid="completion-message"], .completion-message');
  }

  async goto() {
    await this.page.goto('/jobseeker-onboarding');
  }

  async waitForOnboardingToLoad() {
    // Wait for the loading state to complete
    await this.page.waitForLoadState('networkidle');
    
    // Wait for either the skip button or the first input to be visible
    // This ensures the onboarding slider has fully loaded
    await this.page.waitForSelector(
      'button:has-text("Skip"), button:has-text("Next"), input[type="file"], input[name="firstName"]',
      { timeout: 30000 }
    );
  }

  async uploadResume(filePath: string) {
    await this.resumeFileInput.setInputFiles(filePath);
    await this.page.waitForTimeout(1000); // Wait for upload processing
  }

  async fillPersonalInfo(data: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
    linkedIn?: string;
    website?: string;
  }) {
    await this.firstNameInput.fill(data.firstName);
    await this.lastNameInput.fill(data.lastName);
    await this.emailInput.fill(data.email);
    
    if (data.phone) await this.phoneInput.fill(data.phone);
    if (data.location) await this.locationInput.fill(data.location);
    if (data.linkedIn) await this.linkedInInput.fill(data.linkedIn);
    if (data.website) await this.websiteInput.fill(data.website);
  }

  async fillProfessionalSummary(data: {
    summary: string;
    headline?: string;
    yearsOfExperience?: number;
  }) {
    await this.summaryTextarea.fill(data.summary);
    
    if (data.headline) await this.headlineInput.fill(data.headline);
    if (data.yearsOfExperience !== undefined) {
      await this.yearsOfExperienceInput.fill(data.yearsOfExperience.toString());
    }
  }

  async addExperience(data: {
    jobTitle: string;
    companyName: string;
    startDate: string;
    endDate?: string;
    currentlyWorking?: boolean;
    description?: string;
  }) {
    await this.addExperienceButton.click();
    await this.jobTitleInput.fill(data.jobTitle);
    await this.companyNameInput.fill(data.companyName);
    await this.experienceStartDateInput.fill(data.startDate);
    
    if (data.currentlyWorking) {
      await this.currentlyWorkingCheckbox.check();
    } else if (data.endDate) {
      await this.experienceEndDateInput.fill(data.endDate);
    }
    
    if (data.description) {
      await this.jobDescriptionTextarea.fill(data.description);
    }
  }

  async selectJobTypes(types: string[]) {
    for (const type of types) {
      await this.page.locator(`input[type="checkbox"][value="${type}"]`).check();
    }
  }

  async setRemotePreference(preference: 'onsite' | 'remote' | 'hybrid') {
    await this.page.locator(`input[type="radio"][value="${preference}"]`).check();
  }

  async setSalaryExpectations(min: number, max: number, period: 'yearly' | 'monthly' | 'hourly' = 'yearly') {
    await this.minSalaryInput.fill(min.toString());
    await this.maxSalaryInput.fill(max.toString());
    await this.salaryPeriodSelect.selectOption(period);
  }

  async selectValues(values: string[]) {
    for (const value of values) {
      const checkbox = this.page.locator(`input[type="checkbox"][value="${value}"]`);
      if (await checkbox.isVisible()) {
        await checkbox.check();
      } else {
        // Search for value if not visible
        await this.valuesSearchInput.fill(value);
        await this.page.waitForTimeout(500);
        await checkbox.check();
        await this.valuesSearchInput.clear();
      }
    }
  }

  async setPrivacySettings(settings: {
    profileVisibility?: 'public' | 'private' | 'employers-only';
    allowMessages?: boolean;
    showSalary?: boolean;
    showContact?: boolean;
  }) {
    if (settings.profileVisibility) {
      await this.page.locator(`input[type="radio"][value="${settings.profileVisibility}"]`).check();
    }
    
    if (settings.allowMessages !== undefined) {
      settings.allowMessages ? 
        await this.allowMessagesCheckbox.check() : 
        await this.allowMessagesCheckbox.uncheck();
    }
    
    if (settings.showSalary !== undefined) {
      settings.showSalary ? 
        await this.showSalaryCheckbox.check() : 
        await this.showSalaryCheckbox.uncheck();
    }
    
    if (settings.showContact !== undefined) {
      settings.showContact ? 
        await this.showContactCheckbox.check() : 
        await this.showContactCheckbox.uncheck();
    }
  }

  async goToNextStep() {
    await this.nextButton.click();
    await this.page.waitForTimeout(500); // Wait for animation
  }

  async goToPreviousStep() {
    await this.backButton.click();
    await this.page.waitForTimeout(500); // Wait for animation
  }

  async skipStep() {
    if (await this.skipButton.isVisible()) {
      await this.skipButton.click();
      await this.page.waitForTimeout(500); // Wait for animation
    }
  }

  async completeSetup() {
    await this.completeSetupButton.click();
  }

  async waitForCompletion() {
    await this.completionMessage.waitFor({ state: 'visible', timeout: 10000 });
  }

  async getCurrentStepTitle(): Promise<string> {
    const titleElement = this.page.locator('h2, h3').first();
    return await titleElement.textContent() || '';
  }

  async isOnStep(stepTitle: string): Promise<boolean> {
    const currentTitle = await this.getCurrentStepTitle();
    return currentTitle.toLowerCase().includes(stepTitle.toLowerCase());
  }
}