# E2E Tests with <PERSON><PERSON>

This directory contains end-to-end tests for the Kaleido Talent application using Playwright.

## Setup

1. Install Playwright and its dependencies:
```bash
pnpm playwright:install
pnpm playwright:install-deps
```

2. Set up environment variables for testing (create a `.env.test.local` file):
```bash
# Test user credentials (optional - tests will still run without them)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!
TEST_EMPLOYER_EMAIL=<EMAIL>
TEST_JOB_SEEKER_EMAIL=<EMAIL>

# Auth0 configuration (should match your .env.local)
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
```

## Running Tests

### Run all tests
```bash
pnpm test:e2e
```

### Run tests with UI (recommended for development)
```bash
pnpm test:e2e:ui
```

### Run tests in headed mode (see browser)
```bash
pnpm test:e2e:headed
```

### Debug tests
```bash
pnpm test:e2e:debug
```

### Run specific test file
```bash
pnpm test:e2e e2e/tests/login-flow.spec.ts
```

### Run tests in specific browser
```bash
pnpm test:e2e --project=chromium
pnpm test:e2e --project=firefox
pnpm test:e2e --project=webkit
```

## Test Structure

- `tests/` - Contains all test files
  - `login-flow.spec.ts` - Tests for landing page and login flows
  - `homepage.spec.ts` - Tests for homepage functionality
  - `navigation.spec.ts` - Tests for navigation
- `fixtures/` - Custom test fixtures and setup
  - `test-base.ts` - Base test configuration
- `utils/` - Helper functions
  - `auth-helpers.ts` - Authentication helper functions
  - `test-helpers.ts` - General test utilities
- `global-setup.ts` - Global setup before all tests
- `global-teardown.ts` - Global cleanup after all tests

## Writing Tests

### Basic Test Structure
```typescript
import { test, expect } from '@playwright/test'

test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should do something', async ({ page }) => {
    await page.click('button')
    await expect(page.locator('h1')).toBeVisible()
  })
})
```

### Testing Login Flows
The login flow tests handle Auth0 authentication. They:
1. Navigate to the landing page
2. Select a role (Employer/Job Seeker)
3. Click "Get Started"
4. Handle Auth0 redirect
5. Complete login (if credentials provided)
6. Verify successful redirect

### Best Practices
1. Use data-testid attributes for reliable element selection
2. Wait for network idle when appropriate
3. Use proper assertions with expect()
4. Handle both mobile and desktop viewports
5. Clean up test data after tests

## Troubleshooting

### Tests timing out
- Increase timeout in playwright.config.ts
- Check if the dev server is running
- Verify network connectivity

### Auth0 login issues
- Ensure test credentials are correct
- Check Auth0 domain configuration
- Verify allowed callback URLs in Auth0

### Element not found
- Use Playwright UI mode to debug selectors
- Check if elements are rendered conditionally
- Add proper wait conditions

## CI/CD Integration

Tests are configured to run in CI with:
- Parallel execution disabled for stability
- 2 retries on failure
- HTML, JSON, and JUnit reporters
- Video recording on failure