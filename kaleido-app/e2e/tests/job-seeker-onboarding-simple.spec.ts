import { test, expect } from '@playwright/test';
import { 
  createTestUser, 
  registerWithAuth0, 
  deleteAuth0User,
  waitForDashboard 
} from '../utils/auth-helpers';
import { navigateToAuthWithRole } from '../utils/landing-helpers';

test.describe('Job Seeker Onboarding - Simple Test', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' };

  test.beforeAll(() => {
    testUser = createTestUser('job-seeker');
    console.log(`Test user created: ${testUser.email}`);
  });

  test.afterAll(async () => {
    console.log(`Cleaning up test user: ${testUser.email}`);
    await deleteAuth0User(testUser.email).catch(console.error);
  });

  test('should register and reach onboarding page', async ({ page }) => {
    // Navigate to home
    await page.goto('/');
    
    // Navigate to auth for job seeker
    await navigateToAuthWithRole(page, 'job-seeker');
    
    // Register with Auth0
    await registerWithAuth0(page, testUser);
    
    // Wait for dashboard/onboarding
    await waitForDashboard(page, 'job-seeker');
    
    // Verify we're on onboarding
    const currentUrl = page.url();
    console.log('Current URL after registration:', currentUrl);
    expect(currentUrl).toContain('onboarding');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check if we can see some onboarding content
    const pageContent = await page.content();
    
    // Try to find skip button or next button
    const skipButton = page.getByRole('button', { name: /skip/i });
    const nextButton = page.getByRole('button', { name: /next|continue/i });
    
    // If skip button is visible, click it
    if (await skipButton.isVisible({ timeout: 5000 }).catch(() => false)) {
      console.log('Found skip button, clicking it');
      await skipButton.click();
    } else if (await nextButton.isVisible({ timeout: 5000 }).catch(() => false)) {
      console.log('Found next button, onboarding is loaded');
    } else {
      console.log('No skip or next button found, checking page content');
      // Log some content for debugging
      const heading = await page.locator('h1, h2, h3').first().textContent().catch(() => 'No heading found');
      console.log('Page heading:', heading);
    }
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'test-results/job-seeker-onboarding-simple.png' });
  });
});