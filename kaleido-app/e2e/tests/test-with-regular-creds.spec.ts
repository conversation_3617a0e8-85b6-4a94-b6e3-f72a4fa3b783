import { test } from '@playwright/test'
import { createTestUser, registerWithAuth0 } from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'
import axios from 'axios'

test.describe('Test Auth0 Cleanup with Regular App', () => {
  test('try using regular app credentials', async ({ page }) => {
    const testUser = createTestUser('job-seeker')
    console.log(`Test user: ${testUser.email}`)
    
    // Try with regular app credentials
    const regularClientId = process.env.AUTH0_CLIENT_ID || 'tr1zfyNSsdLm8e98li2G91HN36DGqrwj'
    const regularClientSecret = process.env.AUTH0_CLIENT_SECRET || '****************************************************************'
    
    console.log('Trying with regular app credentials...')
    console.log('Client ID:', regularClientId)
    
    try {
      const response = await axios.post('https://headstart.uk.auth0.com/oauth/token', {
        client_id: regularClientId,
        client_secret: regularClientSecret,
        audience: 'https://headstart.uk.auth0.com/api/v2/',
        grant_type: 'client_credentials',
        scope: 'delete:users read:users'
      })
      
      console.log('✅ Got token with regular app!')
      console.log('Token:', response.data.access_token.substring(0, 50) + '...')
    } catch (error: any) {
      console.error('Regular app also failed:', error.response?.data)
    }
    
    // Also test the M2M directly
    console.log('\n=== Testing M2M directly ===')
    try {
      // First, let's try to get ANY token from M2M app
      const m2mResponse = await axios.post('https://headstart.uk.auth0.com/oauth/token', {
        client_id: 'IaswtcaIRSFGbg3adTthWGDrQlCyrZML',
        client_secret: '****************************************************************',
        audience: 'https://staging.kaleidotalent.com', // Try with your API audience
        grant_type: 'client_credentials'
      })
      
      console.log('✅ M2M works with staging API audience!')
      console.log('Token:', m2mResponse.data.access_token.substring(0, 50) + '...')
      console.log('Scopes:', m2mResponse.data.scope)
    } catch (error: any) {
      console.error('M2M with staging API failed:', error.response?.data)
    }
  })
})