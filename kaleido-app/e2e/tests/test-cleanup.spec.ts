import { test } from '@playwright/test'
import { createTestUser, registerWithAuth0, deleteAuth0User } from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Test Auth0 Cleanup', () => {
  test('should create a user and then delete it', async ({ page }) => {
    const testUser = createTestUser('job-seeker')
    console.log(`Test user: ${testUser.email}`)
    
    // Step 1: Register the user
    console.log('=== REGISTRATION ===')
    await page.goto('/')
    await navigateToAuthWithRole(page, 'job-seeker')
    
    console.log('Registering new user...')
    await registerWithAuth0(page, testUser)
    
    // Wait for redirect
    await page.waitForURL(/onboarding/, { timeout: 30000 })
    console.log('✅ User registered successfully')
    
    // Step 2: Delete the user
    console.log('\n=== CLEANUP ===')
    console.log('Attempting to delete user from Auth0...')
    await deleteAuth0User(testUser.email)
    
    console.log('\n✅ Test completed')
  })
})