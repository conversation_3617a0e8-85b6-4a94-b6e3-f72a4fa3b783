import { test, expect } from '@playwright/test';
import { JobsDashboardPage } from '../../page-objects/jobs-dashboard.page';
import { UnifiedJobViewPage } from '../../page-objects/unified-job-view.page';
import { JobCandidatesPage } from '../../page-objects/job-candidates.page';

test.describe('Job Posting and Management Flow', () => {
  let jobsDashboard: JobsDashboardPage;
  let unifiedJobView: UnifiedJobViewPage;
  let jobCandidates: JobCandidatesPage;

  test.beforeEach(async ({ page }) => {
    jobsDashboard = new JobsDashboardPage(page);
    unifiedJobView = new UnifiedJobViewPage(page);
    jobCandidates = new JobCandidatesPage(page);
  });

  test('should display jobs dashboard with statistics', async ({ page }) => {
    // Navigate to jobs dashboard
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Verify page title
    await expect(jobsDashboard.pageTitle).toBeVisible();
    
    // Verify statistics cards are displayed
    await expect(jobsDashboard.totalJobsCard).toBeVisible();
    await expect(jobsDashboard.activeJobsCard).toBeVisible();
    await expect(jobsDashboard.totalCandidatesCard).toBeVisible();
    
    // Get and log statistics
    const stats = await jobsDashboard.getStatistics();
    console.log('Dashboard Statistics:', stats);
    
    // Verify create job button is visible
    await expect(jobsDashboard.createJobButton).toBeVisible();
  });

  test('should filter jobs by status', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Get initial job count
    const allJobsCount = await jobsDashboard.getJobCount();
    
    // Filter by published jobs
    await jobsDashboard.selectStatusTab('published');
    const publishedCount = await jobsDashboard.getJobCount();
    
    // Filter by draft jobs
    await jobsDashboard.selectStatusTab('draft');
    const draftCount = await jobsDashboard.getJobCount();
    
    // Verify counts are different (or could be 0)
    expect(publishedCount).toBeLessThanOrEqual(allJobsCount);
    expect(draftCount).toBeLessThanOrEqual(allJobsCount);
  });

  test('should search for jobs', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Search for a specific job
    await jobsDashboard.searchJobs('Software Engineer');
    
    // Verify search results
    const searchResults = await jobsDashboard.getJobCount();
    if (searchResults > 0) {
      const firstJob = await jobsDashboard.getJobInfo(0);
      expect(firstJob?.title?.toLowerCase()).toContain('software');
    }
  });

  test('should view job details', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Check if there are any jobs
    const hasJobs = await jobsDashboard.hasJobs();
    if (!hasJobs) {
      test.skip();
      return;
    }
    
    // Get first job info
    const firstJob = await jobsDashboard.getJobInfo(0);
    console.log('First job:', firstJob);
    
    // View the job
    await jobsDashboard.viewJob(0);
    
    // Wait for job details to load
    await unifiedJobView.waitForJobToLoad();
    
    // Verify we're on the job details page
    expect(page.url()).toContain('/jobs/');
    expect(page.url()).toContain('/manage');
    
    // Verify job details are displayed
    const jobDetails = await unifiedJobView.getJobDetails();
    expect(jobDetails.title).toBeTruthy();
    
    // Test all job detail sections
    const jobTitle = await unifiedJobView.getJobTitle();
    const companyName = await unifiedJobView.getCompanyName();
    const location = await unifiedJobView.getJobLocation();
    const salaryRange = await unifiedJobView.getSalaryRange();
    const experienceLevel = await unifiedJobView.getExperienceLevel();
    const jobType = await unifiedJobView.getJobType();
    
    console.log('Job Details:', {
      title: jobTitle,
      company: companyName,
      location: location,
      salary: salaryRange,
      experience: experienceLevel,
      type: jobType
    });
    
    // Test additional sections if they exist
    const responsibilities = await unifiedJobView.getJobResponsibilities();
    const skills = await unifiedJobView.getSkills();
    const benefits = await unifiedJobView.getBenefits();
    
    console.log('Additional Details:', {
      responsibilities: responsibilities.length,
      skills: skills.length,
      benefits: benefits.length
    });
    
    // Test candidate stats
    const totalCandidates = await unifiedJobView.getTotalCandidatesCount();
    const topTier = await unifiedJobView.getTopTierThreshold();
    const secondTier = await unifiedJobView.getSecondTierThreshold();
    
    console.log('Candidate Stats:', {
      total: totalCandidates,
      topTier: topTier,
      secondTier: secondTier
    });
  });

  test('should navigate through job detail tabs', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Skip if no jobs
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Test tab navigation
    const tabs: Array<'job-details' | 'jd-tone' | 'video-jd' | 'video-intro'> = 
      ['job-details', 'jd-tone', 'video-jd', 'video-intro'];
    
    for (const tab of tabs) {
      await unifiedJobView.selectTab(tab);
      
      // Verify tab is active
      const currentTab = await unifiedJobView.getCurrentTab();
      expect(currentTab).toContain(tab.replace('-', ' ').toLowerCase());
      
      // Verify URL is updated
      expect(page.url()).toContain(`tab=${tab}`);
    }
  });

  test('should publish and unpublish a job', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Find an unpublished job or skip test
    await jobsDashboard.selectStatusTab('draft');
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View the draft job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Check initial publish status
    const isInitiallyPublished = await unifiedJobView.isJobPublished();
    
    if (!isInitiallyPublished) {
      // Open publish dropdown
      await unifiedJobView.publishDropdownToggle.click();
      await unifiedJobView.publishDropdown.waitFor({ state: 'visible' });
      
      // Select jobboard platform
      await unifiedJobView.selectPlatformForPublishing('jobboard');
      
      // Test external platforms toggle
      if (await unifiedJobView.externalPlatformsToggle.isVisible()) {
        await unifiedJobView.toggleExternalPlatforms();
        
        // Test connect social media button
        if (await unifiedJobView.connectSocialMediaButton.isVisible()) {
          console.log('Social media connection available');
        }
      }
      
      // Publish the job
      await unifiedJobView.publishJob(['jobboard']);
      
      // Verify job is published
      const isNowPublished = await unifiedJobView.isJobPublished();
      expect(isNowPublished).toBe(true);
      
      // Unpublish the job
      await unifiedJobView.unpublishJob();
      
      // Verify job is unpublished
      const isFinallyPublished = await unifiedJobView.isJobPublished();
      expect(isFinallyPublished).toBe(false);
    }
  });

  test('should view job candidates from job details', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Find a job with candidates
    await jobsDashboard.selectStatusTab('published');
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View the job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Get candidate stats
    const stats = await unifiedJobView.getCandidateStats();
    console.log('Candidate stats:', stats);
    
    // View assessment (candidates)
    await unifiedJobView.viewAssessment();
    
    // Verify we're on candidates page
    expect(page.url()).toContain('/candidates');
    
    // Wait for candidates to load
    await jobCandidates.waitForCandidatesToLoad();
  });

  test('should handle job editing', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Click edit job
    await unifiedJobView.editJob();
    
    // Verify we're redirected to job creation/edit page
    expect(page.url()).toContain('job-description-creation');
    expect(page.url()).toContain('edit=true');
  });

  test('should update job tone settings', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Navigate to JD Tone tab
    await unifiedJobView.selectTab('jd-tone');
    
    // Get current tone preview
    const initialTone = await unifiedJobView.getTonePreview();
    
    // Select a different tone
    await unifiedJobView.selectTone('friendly');
    
    // Generate job description with new tone
    if (await unifiedJobView.generateJDButton.isVisible()) {
      await unifiedJobView.generateJobDescription();
      
      // Get new tone preview
      const newTone = await unifiedJobView.getTonePreview();
      
      // Verify tone has changed
      expect(newTone).not.toBe(initialTone);
      
      // Test edit mode toggle
      await unifiedJobView.toggleEditMode();
      expect(await unifiedJobView.jdTextarea.isVisible()).toBe(true);
      
      // Test editing job description
      await unifiedJobView.editJobDescription('Updated job description content');
      
      // Toggle back to preview
      await unifiedJobView.toggleEditMode();
      expect(await unifiedJobView.jdMarkdownPreview.isVisible()).toBe(true);
    }
  });

  test('should manage video intro questions', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Navigate to Video Intro tab
    await unifiedJobView.selectTab('video-intro');
    
    // Get initial questions
    const initialQuestions = await unifiedJobView.getVideoIntroQuestions();
    console.log('Initial questions:', initialQuestions);
    
    // Test question suggestions
    if (await unifiedJobView.questionSuggestionsButton.isVisible()) {
      await unifiedJobView.openQuestionSuggestions(0);
      
      // Select a suggestion
      await unifiedJobView.selectQuestionSuggestion('Tell us about your experience');
      
      // Close suggestions popup
      await unifiedJobView.closeQuestionSuggestions();
    }
    
    // Add a new question
    const newQuestion = 'What makes you interested in this position?';
    await unifiedJobView.addVideoIntroQuestion(newQuestion, 1.5);
    
    // Test duration setting
    await unifiedJobView.setQuestionDuration(0, 2);
    
    // Get updated questions
    const updatedQuestions = await unifiedJobView.getVideoIntroQuestions();
    
    // Verify new question was added
    expect(updatedQuestions.length).toBeGreaterThan(initialQuestions.length);
    
    // Test candidate responses section
    const responsesCount = await unifiedJobView.getCandidateResponsesCount();
    console.log('Candidate responses count:', responsesCount);
    
    // Test pagination if there are responses
    if (responsesCount > 5) {
      await unifiedJobView.navigateCandidateResponsesPage('next');
      await unifiedJobView.navigateCandidateResponsesPage('previous');
    }
  });

  test('should handle mobile view', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Verify mobile menu is visible
    const isMobile = await unifiedJobView.isMobileView();
    expect(isMobile).toBe(true);
    
    // Open mobile menu
    await unifiedJobView.openMobileMenu();
    
    // Verify menu options are visible
    await expect(page.locator('text=View Assessment')).toBeVisible();
    await expect(page.locator('text=Edit Job')).toBeVisible();
  });

  test('should test all tone options', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Navigate to JD Tone tab
    await unifiedJobView.selectTab('jd-tone');
    
    // Test all tone options
    const tones: Array<'professional' | 'friendly' | 'casual' | 'enthusiastic' | 'formal' | 'conversational' | 'like for a 5 year old' | 'gen z'> = [
      'professional', 'friendly', 'casual', 'enthusiastic', 'formal', 'conversational', 'like for a 5 year old', 'gen z'
    ];
    
    for (const tone of tones) {
      // Select the tone
      await unifiedJobView.selectTone(tone);
      
      // Verify the tone is selected (button should have active styling)
      await page.waitForTimeout(500); // Wait for UI to update
      
      console.log(`Selected tone: ${tone}`);
    }
    
    // Test tone guide visibility
    await expect(unifiedJobView.toneGuide).toBeVisible();
  });

  test('should handle time filter changes', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Get initial statistics
    const weeklyStats = await jobsDashboard.getStatistics();
    console.log('Weekly stats:', weeklyStats);
    
    // Change to monthly view
    await jobsDashboard.selectTimeFilter('monthly');
    
    // Get monthly statistics
    const monthlyStats = await jobsDashboard.getStatistics();
    console.log('Monthly stats:', monthlyStats);
    
    // Stats might be different (or the same)
    // The main test is that the filter change doesn't break the page
    expect(monthlyStats.totalJobs).toBeTruthy();
  });

  test('should manage video JD features', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View first job
    await jobsDashboard.viewJob(0);
    await unifiedJobView.waitForJobToLoad();
    
    // Navigate to Video JD tab
    await unifiedJobView.selectTab('video-jd');
    
    // Test video history if available
    if (await unifiedJobView.videoHistoryButton.isVisible()) {
      await unifiedJobView.openVideoHistory();
      
      // Verify history modal opened
      await expect(unifiedJobView.videoHistoryModal).toBeVisible();
      
      // Close history modal
      await unifiedJobView.closeVideoHistory();
    }
    
    // Test left and right columns are visible
    await expect(unifiedJobView.leftColumn).toBeVisible();
    await expect(unifiedJobView.rightColumn).toBeVisible();
    
    // Test video player if available
    if (await unifiedJobView.videoPlayer.isVisible()) {
      console.log('Video player is available');
    }
    
    // Test generate video button if available
    if (await unifiedJobView.generateVideoButton.isVisible()) {
      console.log('Generate video functionality available');
    }
  });

  test('should bulk select and manage jobs', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    const jobCount = await jobsDashboard.getJobCount();
    if (jobCount < 2) {
      test.skip();
      return;
    }
    
    // Select first two jobs
    await jobsDashboard.selectJob(0);
    await jobsDashboard.selectJob(1);
    
    // Verify bulk actions bar appears
    await expect(jobsDashboard.bulkActionsDropdown).toBeVisible();
    
    // Select all jobs
    await jobsDashboard.selectAllJobs();
    
    
    // Verify all jobs are selected
    const selectedCheckboxes = await page.locator('input[type="checkbox"]:checked').count();
    expect(selectedCheckboxes).toBeGreaterThan(2); // Including select all checkbox
  });
});