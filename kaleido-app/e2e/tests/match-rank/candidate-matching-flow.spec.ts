import { test, expect } from '@playwright/test';
import { JobCandidatesPage } from '../../page-objects/job-candidates.page';
import { UnifiedCandidateViewPage } from '../../page-objects/unified-candidate-view.page';
import { JobsDashboardPage } from '../../page-objects/jobs-dashboard.page';
import { UnifiedJobViewPage } from '../../page-objects/unified-job-view.page';

test.describe('Candidate Matching and Ranking Flow', () => {
  let jobCandidates: JobCandidatesPage;
  let candidateView: UnifiedCandidateViewPage;
  let jobsDashboard: JobsDashboardPage;
  let jobView: UnifiedJobViewPage;
  
  // Test data
  const testResumeFiles = [
    './e2e/fixtures/test-files/senior-developer-resume.pdf',
    './e2e/fixtures/test-files/junior-developer-resume.pdf',
    './e2e/fixtures/test-files/product-manager-resume.pdf'
  ];

  test.beforeEach(async ({ page }) => {
    jobCandidates = new JobCandidatesPage(page);
    candidateView = new UnifiedCandidateViewPage(page);
    jobsDashboard = new JobsDashboardPage(page);
    jobView = new UnifiedJobViewPage(page);
  });

  test('should upload resumes for a job', async ({ page }) => {
    // First, navigate to a job
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    // Find a published job
    await jobsDashboard.selectStatusTab('published');
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    // View candidates for the first job
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Upload resumes
    await jobCandidates.uploadResumes(testResumeFiles);
    
    // Verify upload completed
    await expect(page.locator('[data-testid="upload-complete"]')).toBeVisible({ timeout: 30000 });
    
    // Refresh to see new candidates
    await page.reload();
    await jobCandidates.waitForCandidatesToLoad();
    
    // Verify candidates were added
    const candidateCount = await jobCandidates.getCandidateCount();
    expect(candidateCount).toBeGreaterThan(0);
  });

  test('should run match and rank process', async ({ page }) => {
    // Navigate to a job with candidates
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    await jobsDashboard.selectStatusTab('published');
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Check if there are candidates
    const candidateCount = await jobCandidates.getCandidateCount();
    if (candidateCount === 0) {
      test.skip();
      return;
    }
    
    // Start matching process
    await jobCandidates.startMatching();
    
    // Wait for matching to complete
    await jobCandidates.waitForMatchingComplete();
    
    // Verify candidates now have match scores
    const firstCandidate = await jobCandidates.getCandidateInfo(0);
    expect(firstCandidate?.score).toBeTruthy();
    expect(parseInt(firstCandidate?.score || '0')).toBeGreaterThan(0);
  });

  test('should display candidates sorted by match score', async ({ page }) => {
    // Navigate to job with matched candidates
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Sort by match score
    await jobCandidates.sortBy('match-score');
    
    // Get scores of first few candidates
    const scores = [];
    const count = Math.min(3, await jobCandidates.getCandidateCount());
    
    for (let i = 0; i < count; i++) {
      const candidate = await jobCandidates.getCandidateInfo(i);
      if (candidate?.score) {
        scores.push(parseInt(candidate.score));
      }
    }
    
    // Verify descending order
    for (let i = 1; i < scores.length; i++) {
      expect(scores[i]).toBeLessThanOrEqual(scores[i - 1]);
    }
  });

  test('should filter candidates by match score', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    const initialCount = await jobCandidates.getCandidateCount();
    
    // Filter by high match score (80+)
    await jobCandidates.filterByScore(80);
    
    const filteredCount = await jobCandidates.getCandidateCount();
    
    // Verify filter applied
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    
    // Verify all visible candidates have high scores
    if (filteredCount > 0) {
      const firstCandidate = await jobCandidates.getCandidateInfo(0);
      const score = parseInt(firstCandidate?.score || '0');
      expect(score).toBeGreaterThanOrEqual(80);
    }
  });

  test('should view detailed candidate match analysis', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    const candidateCount = await jobCandidates.getCandidateCount();
    if (candidateCount === 0) {
      test.skip();
      return;
    }
    
    // View first candidate
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Verify candidate details are displayed
    const candidateInfo = await candidateView.getCandidateInfo();
    expect(candidateInfo.name).toBeTruthy();
    
    // Check match score
    const matchScore = await candidateView.getMatchScore();
    expect(matchScore).toBeGreaterThan(0);
    
    // Check match rank
    const matchRank = await candidateView.getMatchRank();
    expect(matchRank).toBeGreaterThan(0);
    
    // View match analysis tab
    await candidateView.selectTab('match-analysis');
    
    // Get match analysis details
    const matchAnalysis = await candidateView.getMatchAnalysis();
    expect(matchAnalysis.reasoning).toBeTruthy();
    expect(matchAnalysis.skillsScore).toBeTruthy();
  });

  test('should navigate through match analysis sub-tabs', async ({ page }) => {
    // Navigate to a candidate with match data
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Navigate to match analysis
    await candidateView.selectTab('match-analysis');
    
    // Test all sub-tabs
    const subTabs: Array<'overview' | 'skills-experience' | 'location' | 'team-fit' | 'recommendation'> = 
      ['overview', 'skills-experience', 'location', 'team-fit', 'recommendation'];
    
    for (const subTab of subTabs) {
      await candidateView.selectMatchAnalysisSubTab(subTab);
      
      // Verify URL is updated
      expect(page.url()).toContain(`subtab=${subTab}`);
      
      // Wait for content to load
      await page.waitForTimeout(500);
    }
  });

  test('should display candidate strengths and weaknesses', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Navigate to skills analysis
    await candidateView.selectMatchAnalysisSubTab('skills-experience');
    
    // Get strengths and weaknesses
    const analysis = await candidateView.getStrengthsAndWeaknesses();
    
    console.log('Candidate Analysis:', {
      strengths: analysis.strengths,
      improvements: analysis.improvements,
      missing: analysis.missing
    });
    
    // Verify at least some analysis is present
    expect(analysis.strengths.length + analysis.improvements.length + analysis.missing.length).toBeGreaterThan(0);
  });

  test('should update candidate status', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Update application status
    await candidateView.updateApplicationStatus('Shortlisted');
    
    // Add a note
    await candidateView.addNote('Strong technical skills, good cultural fit. Schedule for technical interview.');
    
    // Verify success messages
    await expect(candidateView.successMessage).toBeVisible();
  });

  test('should shortlist top candidates', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    // Filter by high scores
    await jobCandidates.filterByScore(80);
    
    // Select all filtered candidates
    await jobCandidates.selectAllCandidates();
    
    // Bulk shortlist
    await jobCandidates.bulkShortlist();
    
    // Verify action completed
    await expect(page.locator('text=Candidates shortlisted')).toBeVisible({ timeout: 5000 });
  });

  test('should handle candidate profile viewing', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // View profile tab
    await candidateView.viewProfile();
    
    // Get candidate skills
    const skills = await candidateView.getSkills();
    console.log('Candidate skills:', skills);
    
    // Get experience
    const experience = await candidateView.getExperience();
    console.log('Candidate experience:', experience);
    
    // Verify profile data exists
    expect(skills.length + experience.length).toBeGreaterThan(0);
  });

  test('should check for video introductions', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Check if candidate has video intro
    const hasVideo = await candidateView.hasVideoIntro();
    
    if (hasVideo) {
      // Get video questions
      const questions = await candidateView.getVideoQuestions();
      console.log('Video interview questions:', questions);
      
      expect(questions.length).toBeGreaterThan(0);
    }
  });

  test('should scout for candidates', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Start scouting
    await jobCandidates.startScouting('software engineer python', 'San Francisco');
    
    // Check if scouting is in progress
    const hasActiveJobs = await jobCandidates.hasActiveJobs();
    expect(hasActiveJobs).toBe(true);
    
    // Get active jobs count
    const activeJobs = await jobCandidates.getActiveJobsCount();
    expect(activeJobs).toBeGreaterThan(0);
  });

  test('should display candidate match badges', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Look for top candidates
    await jobCandidates.sortBy('match-score');
    
    const candidateCount = await jobCandidates.getCandidateCount();
    if (candidateCount === 0) {
      test.skip();
      return;
    }
    
    // Check first candidate
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Get match badge type
    const badgeType = await candidateView.getMatchBadgeType();
    console.log('Candidate badge type:', badgeType);
    
    // Get match score to verify badge is appropriate
    const score = await candidateView.getMatchScore();
    
    if (score >= 80) {
      expect(['top', 'strong']).toContain(badgeType);
    } else if (score >= 60) {
      expect(['strong', 'good']).toContain(badgeType);
    }
  });

  test('should edit matching criteria', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Click edit criteria
    await jobCandidates.editCriteriaButton.click();
    
    // Verify navigation to edit page
    expect(page.url()).toContain('/edit');
  });
});