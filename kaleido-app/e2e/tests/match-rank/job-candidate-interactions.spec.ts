import { test, expect } from '@playwright/test';
import { JobCandidatesPage } from '../../page-objects/job-candidates.page';
import { UnifiedCandidateViewPage } from '../../page-objects/unified-candidate-view.page';
import { JobsDashboardPage } from '../../page-objects/jobs-dashboard.page';
import { UnifiedJobViewPage } from '../../page-objects/unified-job-view.page';

test.describe('Job-Candidate Interaction Features', () => {
  let jobCandidates: JobCandidatesPage;
  let candidateView: UnifiedCandidateViewPage;
  let jobsDashboard: JobsDashboardPage;
  let jobView: UnifiedJobViewPage;

  test.beforeEach(async ({ page }) => {
    jobCandidates = new JobCandidatesPage(page);
    candidateView = new UnifiedCandidateViewPage(page);
    jobsDashboard = new JobsDashboardPage(page);
    jobView = new UnifiedJobViewPage(page);
  });

  test('should send message to candidate', async ({ page }) => {
    // Navigate to a job with candidates
    await jobsDashboard.goto();
    await jobsDashboard.waitForJobsToLoad();
    
    await jobsDashboard.selectStatusTab('published');
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    // View first candidate
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Click send message
    await candidateView.sendMessageButton.click();
    
    // Wait for message modal
    const messageModal = page.locator('[role="dialog"]:has-text("Send Message")');
    await expect(messageModal).toBeVisible({ timeout: 5000 });
    
    // Fill message details
    const subjectInput = page.locator('input[placeholder*="subject"]');
    const messageInput = page.locator('textarea[placeholder*="message"]');
    
    await subjectInput.fill('Regarding your application for Software Engineer position');
    await messageInput.fill('Thank you for your interest in our Software Engineer position. We were impressed with your background and would like to move forward with your application.');
    
    // Send message
    const sendButton = page.locator('button:has-text("Send")').last();
    await sendButton.click();
    
    // Verify success
    await expect(candidateView.successMessage).toBeVisible();
  });

  test('should bulk message multiple candidates', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    const candidateCount = await jobCandidates.getCandidateCount();
    if (candidateCount < 2) {
      test.skip();
      return;
    }
    
    // Select first two candidates
    await jobCandidates.selectCandidate(0);
    await jobCandidates.selectCandidate(1);
    
    // Click bulk message
    await jobCandidates.bulkMessageButton.click();
    
    // Wait for message modal
    const messageModal = page.locator('[role="dialog"]:has-text("Message Selected Candidates")');
    await expect(messageModal).toBeVisible({ timeout: 5000 });
    
    // Fill template message
    const templateSelect = page.locator('select[name="template"]');
    const messageInput = page.locator('textarea[placeholder*="message"]');
    
    // Select a template if available
    if (await templateSelect.isVisible()) {
      await templateSelect.selectOption({ index: 1 });
    } else {
      await messageInput.fill('We have reviewed your application and would like to discuss next steps.');
    }
    
    // Send bulk message
    const sendButton = page.locator('button:has-text("Send to 2 candidates")');
    await sendButton.click();
    
    // Verify success
    await expect(page.locator('text=Messages sent successfully')).toBeVisible({ timeout: 10000 });
  });

  test('should schedule interview with candidate', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Filter for shortlisted candidates
    await jobCandidates.filterByStatus('shortlisted');
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      // Try without filter
      await jobCandidates.clearAllFilters();
      if ((await jobCandidates.getCandidateCount()) === 0) {
        test.skip();
        return;
      }
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Schedule interview
    await candidateView.scheduleInterviewButton.click();
    
    // Wait for scheduling modal
    const schedulingModal = page.locator('[role="dialog"]:has-text("Schedule Interview")');
    await expect(schedulingModal).toBeVisible({ timeout: 5000 });
    
    // Fill interview details
    const interviewType = page.locator('select[name="interviewType"]');
    const dateInput = page.locator('input[type="date"]');
    const timeInput = page.locator('input[type="time"]');
    const durationSelect = page.locator('select[name="duration"]');
    const interviewerInput = page.locator('input[placeholder*="interviewer"]');
    const notesInput = page.locator('textarea[placeholder*="notes"]');
    
    // Get tomorrow's date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateStr = tomorrow.toISOString().split('T')[0];
    
    await interviewType.selectOption('technical');
    await dateInput.fill(dateStr);
    await timeInput.fill('14:00');
    await durationSelect.selectOption('60');
    await interviewerInput.fill('John Smith');
    await notesInput.fill('Technical interview focusing on system design and coding skills');
    
    // Schedule the interview
    const scheduleButton = page.locator('button:has-text("Schedule Interview")').last();
    await scheduleButton.click();
    
    // Verify success
    await expect(candidateView.successMessage).toBeVisible();
    
    // Verify status updated
    await candidateView.selectTab('status');
    await expect(page.locator('text=Interview Scheduled')).toBeVisible();
  });

  test('should manage interview feedback', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Filter for interviewed candidates
    await jobCandidates.filterByStatus('interviewed');
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Navigate to status tab
    await candidateView.selectTab('status');
    
    // Look for interview feedback section
    const feedbackButton = page.locator('button:has-text("Add Interview Feedback")');
    if (await feedbackButton.isVisible()) {
      await feedbackButton.click();
      
      // Fill feedback form
      const ratingSelect = page.locator('select[name="rating"]');
      const technicalSkills = page.locator('input[name="technicalSkills"]');
      const communication = page.locator('input[name="communication"]');
      const cultureFit = page.locator('input[name="cultureFit"]');
      const feedbackText = page.locator('textarea[name="feedback"]');
      const recommendationSelect = page.locator('select[name="recommendation"]');
      
      await ratingSelect.selectOption('4');
      await technicalSkills.fill('4');
      await communication.fill('5');
      await cultureFit.fill('4');
      await feedbackText.fill('Strong technical background with excellent communication skills. Demonstrated good problem-solving abilities and would be a great addition to the team.');
      await recommendationSelect.selectOption('hire');
      
      // Submit feedback
      const submitButton = page.locator('button:has-text("Submit Feedback")');
      await submitButton.click();
      
      // Verify success
      await expect(candidateView.successMessage).toBeVisible();
    }
  });

  test('should make job offer to candidate', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Look for high-scoring candidates
    await jobCandidates.filterByScore(85);
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      await jobCandidates.clearAllFilters();
      if ((await jobCandidates.getCandidateCount()) === 0) {
        test.skip();
        return;
      }
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Look for make offer button
    const makeOfferButton = page.locator('button:has-text("Make Offer")');
    if (await makeOfferButton.isVisible()) {
      await makeOfferButton.click();
      
      // Wait for offer modal
      const offerModal = page.locator('[role="dialog"]:has-text("Make Job Offer")');
      await expect(offerModal).toBeVisible({ timeout: 5000 });
      
      // Fill offer details
      const salaryInput = page.locator('input[name="salary"]');
      const startDateInput = page.locator('input[name="startDate"]');
      const offerExpiryInput = page.locator('input[name="offerExpiry"]');
      const benefitsTextarea = page.locator('textarea[name="benefits"]');
      
      // Set dates
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() + 1);
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 7);
      
      await salaryInput.fill('120000');
      await startDateInput.fill(startDate.toISOString().split('T')[0]);
      await offerExpiryInput.fill(expiryDate.toISOString().split('T')[0]);
      await benefitsTextarea.fill('Health insurance, 401k matching, flexible work arrangements, professional development budget');
      
      // Send offer
      const sendOfferButton = page.locator('button:has-text("Send Offer")');
      await sendOfferButton.click();
      
      // Verify success
      await expect(candidateView.successMessage).toBeVisible();
      
      // Verify status updated
      await candidateView.selectTab('status');
      await expect(page.locator('text=Offer Extended')).toBeVisible();
    } else {
      // Update status manually
      await candidateView.updateApplicationStatus('Offer Extended');
    }
  });

  test('should add tags to candidates', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Add tags
    await candidateView.addTagButton.click();
    
    // Wait for tag input
    const tagInput = page.locator('input[placeholder*="tag"]');
    await tagInput.waitFor({ state: 'visible' });
    
    // Add multiple tags
    const tags = ['remote-ready', 'senior-level', 'strong-communicator'];
    for (const tag of tags) {
      await tagInput.fill(tag);
      await tagInput.press('Enter');
      await page.waitForTimeout(300);
    }
    
    // Verify tags are displayed
    for (const tag of tags) {
      await expect(page.locator(`.tag:has-text("${tag}")`)).toBeVisible();
    }
  });

  test('should share candidate profile with team', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Share profile
    await candidateView.shareProfileButton.click();
    
    // Wait for share modal
    const shareModal = page.locator('[role="dialog"]:has-text("Share Profile")');
    await expect(shareModal).toBeVisible({ timeout: 5000 });
    
    // Fill share details
    const emailInput = page.locator('input[placeholder*="email"]');
    const messageInput = page.locator('textarea[placeholder*="message"]');
    
    await emailInput.fill('<EMAIL>, <EMAIL>');
    await messageInput.fill('Please review this candidate profile for the Software Engineer position. They have strong technical skills and seem like a great fit for our team.');
    
    // Include match analysis
    const includeAnalysisCheckbox = page.locator('input[type="checkbox"][name="includeAnalysis"]');
    if (await includeAnalysisCheckbox.isVisible()) {
      await includeAnalysisCheckbox.check();
    }
    
    // Share
    const shareButton = page.locator('button:has-text("Share")').last();
    await shareButton.click();
    
    // Verify success
    await expect(page.locator('text=Profile shared successfully')).toBeVisible({ timeout: 5000 });
  });

  test('should track candidate pipeline status changes', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Navigate to status tab
    await candidateView.selectTab('status');
    
    // Get initial status
    const initialStatus = await candidateView.currentStatus.textContent();
    console.log('Initial status:', initialStatus);
    
    // Update through pipeline stages
    const stages = ['Screening', 'Shortlisted', 'Interview Scheduled', 'Interviewed', 'Offer Extended'];
    
    for (const stage of stages) {
      // Check if stage is available in dropdown
      const options = await candidateView.statusDropdown.locator('option').allTextContents();
      
      if (options.includes(stage)) {
        await candidateView.updateApplicationStatus(stage);
        
        // Add note for the stage
        await candidateView.addNote(`Candidate moved to ${stage} stage. ${stage === 'Shortlisted' ? 'Strong technical background.' : ''}${stage === 'Interviewed' ? 'Positive interview feedback.' : ''}`);
        
        // Verify timeline updated
        await expect(candidateView.statusTimeline).toContainText(stage);
        
        await page.waitForTimeout(1000); // Wait between status changes
      }
    }
    
    // Verify complete timeline is visible
    const timelineItems = await candidateView.statusTimeline.locator('.timeline-item').count();
    expect(timelineItems).toBeGreaterThan(1);
  });

  test('should handle candidate rejection with feedback', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    // Filter for low-scoring candidates
    await jobCandidates.sortBy('match-score');
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    // Get the last candidate (lowest score)
    const candidateCount = await jobCandidates.getCandidateCount();
    await jobCandidates.viewCandidate(candidateCount - 1);
    await candidateView.waitForCandidateToLoad();
    
    // Reject candidate
    await candidateView.rejectButton.click();
    
    // Handle rejection reason modal if it appears
    const rejectionModal = page.locator('[role="dialog"]:has-text("Rejection Reason")');
    if (await rejectionModal.isVisible({ timeout: 2000 })) {
      const reasonSelect = page.locator('select[name="reason"]');
      const feedbackInput = page.locator('textarea[name="feedback"]');
      const sendEmailCheckbox = page.locator('input[type="checkbox"][name="sendEmail"]');
      
      await reasonSelect.selectOption('not-qualified');
      await feedbackInput.fill('Thank you for your interest. While we were impressed with your enthusiasm, we are looking for candidates with more experience in cloud technologies for this particular role.');
      
      // Check if we should send rejection email
      if (await sendEmailCheckbox.isVisible()) {
        await sendEmailCheckbox.check();
      }
      
      // Confirm rejection
      const confirmButton = page.locator('button:has-text("Confirm Rejection")');
      await confirmButton.click();
    }
    
    // Verify success
    await expect(candidateView.successMessage).toBeVisible();
    
    // Verify status updated
    await candidateView.selectTab('status');
    await expect(page.locator('text=Rejected')).toBeVisible();
  });

  test('should export candidate data', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    // Look for export button
    const exportButton = page.locator('button:has-text("Export")');
    if (await exportButton.isVisible()) {
      await exportButton.click();
      
      // Wait for export modal
      const exportModal = page.locator('[role="dialog"]:has-text("Export Candidates")');
      await expect(exportModal).toBeVisible({ timeout: 5000 });
      
      // Select export options
      const formatSelect = page.locator('select[name="format"]');
      const includeScoresCheckbox = page.locator('input[name="includeScores"]');
      const includeNotesCheckbox = page.locator('input[name="includeNotes"]');
      const includeStatusCheckbox = page.locator('input[name="includeStatus"]');
      
      await formatSelect.selectOption('csv');
      await includeScoresCheckbox.check();
      await includeNotesCheckbox.check();
      await includeStatusCheckbox.check();
      
      // Start export with download handling
      const downloadPromise = page.waitForEvent('download');
      const exportConfirmButton = page.locator('button:has-text("Export")').last();
      await exportConfirmButton.click();
      
      // Wait for download
      const download = await downloadPromise;
      console.log('Downloaded file:', await download.suggestedFilename());
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/candidates.*\.csv/);
    }
  });

  test('should handle candidate communications history', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Look for communications tab
    const communicationsTab = page.locator('button:has-text("Communications")');
    if (await communicationsTab.isVisible()) {
      await communicationsTab.click();
      
      // Wait for communications to load
      await page.waitForTimeout(1000);
      
      // Check for communication history
      const emailsSent = page.locator('[data-testid="email-sent"]');
      const messagesReceived = page.locator('[data-testid="message-received"]');
      
      const emailCount = await emailsSent.count();
      const messageCount = await messagesReceived.count();
      
      console.log(`Communication history: ${emailCount} emails sent, ${messageCount} messages received`);
      
      // If there are communications, verify we can view details
      if (emailCount > 0) {
        const firstEmail = emailsSent.first();
        await firstEmail.click();
        
        // Verify email details modal
        const emailModal = page.locator('[role="dialog"]:has-text("Email Details")');
        await expect(emailModal).toBeVisible({ timeout: 2000 });
        
        // Close modal
        await page.keyboard.press('Escape');
      }
    }
  });

  test('should track candidate activity and engagement', async ({ page }) => {
    await jobsDashboard.goto();
    await jobsDashboard.selectStatusTab('published');
    
    if (!(await jobsDashboard.hasJobs())) {
      test.skip();
      return;
    }
    
    await jobsDashboard.viewCandidates(0);
    await jobCandidates.waitForCandidatesToLoad();
    
    if ((await jobCandidates.getCandidateCount()) === 0) {
      test.skip();
      return;
    }
    
    await jobCandidates.viewCandidate(0);
    await candidateView.waitForCandidateToLoad();
    
    // Look for activity/engagement metrics
    const activitySection = page.locator('[data-testid="candidate-activity"], .activity-section');
    if (await activitySection.isVisible()) {
      // Check various engagement metrics
      const profileViews = page.locator('[data-testid="profile-views"]');
      const lastActive = page.locator('[data-testid="last-active"]');
      const responseRate = page.locator('[data-testid="response-rate"]');
      
      if (await profileViews.isVisible()) {
        const views = await profileViews.textContent();
        console.log('Profile views:', views);
      }
      
      if (await lastActive.isVisible()) {
        const lastActiveTime = await lastActive.textContent();
        console.log('Last active:', lastActiveTime);
      }
      
      if (await responseRate.isVisible()) {
        const rate = await responseRate.textContent();
        console.log('Response rate:', rate);
      }
    }
    
    // Check for activity timeline
    const activityTimeline = page.locator('[data-testid="activity-timeline"], .timeline');
    if (await activityTimeline.isVisible()) {
      const activities = await activityTimeline.locator('.activity-item').all();
      console.log(`Found ${activities.length} activity items`);
    }
  });
});