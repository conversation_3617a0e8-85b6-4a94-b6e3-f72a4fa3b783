import { test, expect } from '@playwright/test'
import { createTestUser, registerWithAuth0, deleteAuth0User } from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Logout Redirect Test', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' }

  test.beforeAll(() => {
    testUser = createTestUser('job-seeker')
    console.log(`Test user: ${testUser.email}`)
  })

  test.afterAll(async () => {
    console.log(`Cleaning up test user: ${testUser.email}`)
    await deleteAuth0User(testUser.email).catch(console.error)
  })

  test('should register user and test logout redirect', async ({ page }) => {
    // Step 1: Register
    console.log('=== REGISTRATION ===')
    await page.goto('/')
    await navigateToAuthWithRole(page, 'job-seeker')
    
    console.log('Registering new user...')
    await registerWithAuth0(page, testUser)
    
    // Should be on onboarding
    await page.waitForURL(/onboarding/, { timeout: 30000 })
    const afterRegisterUrl = page.url()
    console.log('After registration URL:', afterRegisterUrl)
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/before-logout.png' })
    
    // Step 2: Test logout
    console.log('\n=== TESTING LOGOUT ===')
    
    // Navigate to logout endpoint - expect redirect
    try {
      await page.goto('/api/auth/logout', { waitUntil: 'networkidle' })
    } catch (e) {
      // ERR_ABORTED is expected as logout endpoint redirects
      console.log('Logout redirect initiated (expected behavior)')
    }
    
    // Wait for Auth0 logout and redirect back
    await page.waitForTimeout(5000)
    
    // Check where we ended up
    const afterLogoutUrl = page.url()
    console.log('After logout URL:', afterLogoutUrl)
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/after-logout.png', fullPage: true })
    
    // Check if we're on home page
    if (afterLogoutUrl.includes('localhost:3000')) {
      // We're back on our app
      if (!afterLogoutUrl.endsWith('/')) {
        console.log('Not on home page, navigating there...')
        await page.goto('/')
      }
      
      // Verify we can see the landing page
      await expect(page.locator('text=Job Seekers').first()).toBeVisible({ timeout: 10000 })
      console.log('✅ Successfully on landing page after logout')
    } else {
      console.log('Still on Auth0 or another domain')
    }
  })
})