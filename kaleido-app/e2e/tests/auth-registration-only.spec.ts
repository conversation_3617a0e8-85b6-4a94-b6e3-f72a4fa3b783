import { test, expect } from '@playwright/test'
import { createTestUser, deleteAuth0User } from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Registration Flow Test', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' }

  test.beforeEach(() => {
    testUser = createTestUser('job-seeker')
    console.log(`Test user: ${testUser.email}`)
  })

  test.afterEach(async () => {
    console.log(`Attempting cleanup for: ${testUser.email}`)
    await deleteAuth0User(testUser.email)
  })

  test('should navigate to Auth0 and show registration form', async ({ page }) => {
    // Navigate to home
    await page.goto('/')
    
    // Navigate directly to auth
    await navigateToAuthWithRole(page, 'job-seeker')
    
    // Should redirect to Auth0
    await page.waitForURL(/.*auth0.*/, { timeout: 30000 })
    console.log('Reached Auth0 page')
    
    // Take screenshot of Auth0 page
    await page.screenshot({ path: 'test-results/auth0-login-page.png', fullPage: true })
    
    // Check for email input
    const emailInput = page.locator('input[type="email"], input[name="username"], input[name="email"]')
    await expect(emailInput).toBeVisible({ timeout: 10000 })
    
    // Check for password input
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    await expect(passwordInput).toBeVisible({ timeout: 10000 })
    
    // Look for sign up link
    const signUpLink = page.locator('a:has-text("Sign up"), a:has-text("Don\'t have an account?"), button:has-text("Sign up")')
    
    if (await signUpLink.isVisible({ timeout: 5000 }).catch(() => false)) {
      console.log('Found sign up link, clicking it...')
      await signUpLink.click()
      await page.waitForTimeout(2000)
      
      // Take screenshot after clicking sign up
      await page.screenshot({ path: 'test-results/auth0-signup-page.png', fullPage: true })
    }
    
    // Fill in the form
    console.log('Filling registration form...')
    await emailInput.fill(testUser.email)
    await passwordInput.fill(testUser.password)
    
    // Look for confirm password field
    const confirmPasswordField = page.locator('input[name="confirmPassword"], input[name="confirm-password"], input[placeholder*="Confirm"]').first()
    if (await confirmPasswordField.isVisible({ timeout: 2000 }).catch(() => false)) {
      console.log('Found confirm password field')
      await confirmPasswordField.fill(testUser.password)
    }
    
    // Take screenshot before submitting
    await page.screenshot({ path: 'test-results/auth0-form-filled.png', fullPage: true })
    
    // Find and click submit button
    const submitButton = page.locator('button[type="submit"], button[name="submit"], button:has-text("Continue"), button:has-text("Sign Up")').first()
    await expect(submitButton).toBeVisible()
    
    console.log('Submitting form...')
    await submitButton.click()
    
    // Wait a bit to see what happens
    await page.waitForTimeout(5000)
    
    // Take screenshot after submit
    await page.screenshot({ path: 'test-results/auth0-after-submit.png', fullPage: true })
    
    // Log current URL
    const currentUrl = page.url()
    console.log('Current URL after submit:', currentUrl)
    
    // Check if we're still on Auth0 (might have additional steps)
    if (currentUrl.includes('auth0')) {
      console.log('Still on Auth0, checking for any additional forms or errors...')
      
      // Check for error messages
      const errorMessage = page.locator('div[role="alert"], .alert, .error, [class*="error"]')
      if (await errorMessage.isVisible({ timeout: 2000 }).catch(() => false)) {
        const errorText = await errorMessage.textContent()
        console.log('Error message found:', errorText)
      }
      
      // Check for consent screen
      const consentButton = page.locator('button:has-text("Accept"), button:has-text("Authorize")')
      if (await consentButton.isVisible({ timeout: 2000 }).catch(() => false)) {
        console.log('Found consent screen, accepting...')
        await consentButton.click()
        await page.waitForTimeout(3000)
      }
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/auth0-final-state.png', fullPage: true })
    console.log('Final URL:', page.url())
  })
})