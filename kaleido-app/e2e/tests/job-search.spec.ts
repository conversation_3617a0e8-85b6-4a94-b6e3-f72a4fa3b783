import { test, expect } from '@playwright/test';
import { JobSearchPage } from '../page-objects/job-search.page';
import { 
  createTestUser, 
  loginWithAuth0,
  deleteAuth0User
} from '../utils/auth-helpers';

test.describe('Job Search Functionality', () => {
  let jobSearchPage: JobSearchPage;
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' };

  test.beforeAll(() => {
    // Create a test user that has already completed onboarding
    testUser = createTestUser('job-seeker');
    console.log(`Test user created: ${testUser.email}`);
  });

  test.afterAll(async () => {
    console.log(`Cleaning up test user: ${testUser.email}`);
    await deleteAuth0User(testUser.email).catch(console.error);
  });

  test.beforeEach(async ({ page }) => {
    jobSearchPage = new JobSearchPage(page);
  });

  test('should display job search page without authentication', async ({ page }) => {
    // Navigate to job search page
    await jobSearchPage.goto();
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Verify key elements are visible
    await expect(jobSearchPage.searchInput).toBeVisible();
    
    // Check if there are any jobs displayed by default
    await jobSearchPage.waitForSearchResults();
    
    const jobCount = await jobSearchPage.getJobCount();
    console.log(`Found ${jobCount} jobs on initial load`);
    
    // Verify search functionality is available
    await expect(jobSearchPage.searchButton).toBeVisible();
  });

  test('should search for jobs by keyword', async ({ page }) => {
    await jobSearchPage.goto();
    
    // Search for software engineer jobs
    await jobSearchPage.searchJobs('software engineer');
    
    // Get job titles
    const jobTitles = await jobSearchPage.getJobTitles();
    console.log(`Found ${jobTitles.length} jobs matching "software engineer"`);
    
    // Verify at least some jobs are found (or no results message is shown)
    if (jobTitles.length > 0) {
      // Check that at least one job title contains relevant keywords
      const relevantJob = jobTitles.some(title => 
        title.toLowerCase().includes('software') || 
        title.toLowerCase().includes('engineer') ||
        title.toLowerCase().includes('developer')
      );
      expect(relevantJob).toBeTruthy();
    } else {
      // Check for no results message
      const noResults = await jobSearchPage.isNoResultsDisplayed();
      expect(noResults).toBeTruthy();
    }
  });

  test('should search for jobs by location', async ({ page }) => {
    await jobSearchPage.goto();
    
    // Search for jobs in San Francisco
    await jobSearchPage.searchJobs('', 'San Francisco');
    
    // Wait for results
    await jobSearchPage.waitForSearchResults();
    
    // Get job count
    const jobCount = await jobSearchPage.getJobCount();
    console.log(`Found ${jobCount} jobs in San Francisco`);
    
    // Verify location filter is applied (check URL or filter state)
    const currentUrl = page.url();
    expect(currentUrl).toContain('location');
  });

  test('should filter jobs by job type', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Get initial job count
    const initialCount = await jobSearchPage.getJobCount();
    
    // Apply job type filter if available
    try {
      await jobSearchPage.filterByJobType('full-time');
      
      // Get filtered job count
      const filteredCount = await jobSearchPage.getJobCount();
      console.log(`Jobs before filter: ${initialCount}, after filter: ${filteredCount}`);
      
      // Verify filter is applied
      const filters = await jobSearchPage.getSelectedFilters();
      expect(filters.jobType).toBeTruthy();
    } catch (error) {
      console.log('Job type filter not available or different implementation');
    }
  });

  test('should toggle remote only filter', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Check if remote filter is available
    if (await jobSearchPage.remoteOnlyCheckbox.isVisible()) {
      // Get initial job count
      const initialCount = await jobSearchPage.getJobCount();
      
      // Toggle remote only
      await jobSearchPage.toggleRemoteOnly();
      
      // Get filtered count
      const remoteCount = await jobSearchPage.getJobCount();
      console.log(`Total jobs: ${initialCount}, Remote jobs: ${remoteCount}`);
      
      // Verify filter is applied
      const isChecked = await jobSearchPage.remoteOnlyCheckbox.isChecked();
      expect(isChecked).toBeTruthy();
    } else {
      console.log('Remote filter not available on this page');
    }
  });

  test('should view job details', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Check if there are jobs to view
    const jobCount = await jobSearchPage.getJobCount();
    
    if (jobCount > 0) {
      // Try to view details of the first job
      const viewButtons = await jobSearchPage.viewDetailsButtons.all();
      
      if (viewButtons.length > 0) {
        await jobSearchPage.viewJobDetails(0);
        
        // Verify modal is open
        await expect(jobSearchPage.jobDetailsModal).toBeVisible();
        
        // Close modal
        await jobSearchPage.closeJobDetailsModal();
        
        // Verify modal is closed
        await expect(jobSearchPage.jobDetailsModal).not.toBeVisible();
      } else {
        // Click on job title if no view button
        const firstJobTitle = jobSearchPage.jobTitles.first();
        if (await firstJobTitle.isVisible()) {
          await firstJobTitle.click();
          
          // Check if we navigated to job details page or modal opened
          await page.waitForTimeout(1000);
          const currentUrl = page.url();
          const hasNavigated = currentUrl !== await jobSearchPage.goto();
          const modalVisible = await jobSearchPage.jobDetailsModal.isVisible();
          
          expect(hasNavigated || modalVisible).toBeTruthy();
        }
      }
    } else {
      console.log('No jobs available to test job details functionality');
    }
  });

  test('should switch between modern and table views', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Check if view toggle is available
    if (await jobSearchPage.tableViewToggle.isVisible()) {
      // Switch to table view
      await jobSearchPage.switchToTableView();
      
      // Verify table rows are visible
      const tableRows = await jobSearchPage.jobRows.count();
      expect(tableRows).toBeGreaterThan(0);
      
      // Switch back to modern view
      if (await jobSearchPage.modernViewToggle.isVisible()) {
        await jobSearchPage.switchToModernView();
        
        // Verify job cards are visible
        const jobCards = await jobSearchPage.jobCards.count();
        expect(jobCards).toBeGreaterThan(0);
      }
    } else {
      console.log('View toggle not available on this page');
    }
  });

  test('should handle pagination', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Check if pagination is available
    if (await jobSearchPage.nextPageButton.isVisible() && await jobSearchPage.nextPageButton.isEnabled()) {
      // Get job titles from first page
      const firstPageTitles = await jobSearchPage.getJobTitles();
      
      // Go to next page
      await jobSearchPage.goToNextPage();
      
      // Get job titles from second page
      const secondPageTitles = await jobSearchPage.getJobTitles();
      
      // Verify we have different jobs
      const differentJobs = firstPageTitles[0] !== secondPageTitles[0];
      expect(differentJobs).toBeTruthy();
      
      // Go back to previous page
      await jobSearchPage.goToPreviousPage();
      
      // Verify we're back on first page
      const backToFirstPageTitles = await jobSearchPage.getJobTitles();
      expect(backToFirstPageTitles[0]).toBe(firstPageTitles[0]);
    } else {
      console.log('Pagination not available or all jobs fit on one page');
    }
  });

  test('should clear all filters', async ({ page }) => {
    await jobSearchPage.goto();
    await jobSearchPage.waitForSearchResults();
    
    // Apply some filters
    await jobSearchPage.searchJobs('developer', 'New York');
    
    // Check if clear filters button is available
    if (await jobSearchPage.clearFiltersButton.isVisible()) {
      // Clear all filters
      await jobSearchPage.clearAllFilters();
      
      // Verify search inputs are cleared
      const searchValue = await jobSearchPage.searchInput.inputValue();
      const locationValue = await jobSearchPage.locationInput.inputValue();
      
      expect(searchValue).toBe('');
      expect(locationValue).toBe('');
    } else {
      console.log('Clear filters button not available');
    }
  });

  test('should handle no search results', async ({ page }) => {
    await jobSearchPage.goto();
    
    // Search for something unlikely to return results
    await jobSearchPage.searchJobs('xyzabc123notarealjob');
    
    // Check for no results message
    const noResults = await jobSearchPage.isNoResultsDisplayed();
    
    if (!noResults) {
      // If no explicit no results message, check job count
      const jobCount = await jobSearchPage.getJobCount();
      expect(jobCount).toBe(0);
    } else {
      expect(noResults).toBeTruthy();
    }
  });
});