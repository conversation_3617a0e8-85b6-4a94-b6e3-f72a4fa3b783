import { test, expect } from '@playwright/test'

test.describe('Basic Landing Page Tests', () => {
  test('should load landing page', async ({ page }) => {
    // Navigate to landing page
    const response = await page.goto('/')
    
    // Check response is successful
    expect(response?.status()).toBe(200)
    
    // Wait for any content to be visible
    await page.waitForLoadState('domcontentloaded')
    
    // Check page has rendered something
    const body = page.locator('body')
    await expect(body).toBeVisible()
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'test-results/landing-page.png', fullPage: true })
  })

  test('should have correct page title', async ({ page }) => {
    await page.goto('/')
    
    // Check page title
    await expect(page).toHaveTitle(/Kaleido|Headstart/i, { timeout: 10000 })
  })

  test('should render landing page sections', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Wait a bit for animations
    await page.waitForTimeout(3000)
    
    // Debug: log all visible text
    const visibleText = await page.locator('body').innerText()
    console.log('Visible text on page:', visibleText)
    
    // Check for any section titles - more flexible matching
    const hasEmployers = await page.locator('text=Employer').count() > 0
    const hasJobSeekers = await page.locator('text=Job Seeker').count() > 0
    const hasGraduates = await page.locator('text=Graduate').count() > 0
    
    expect(hasEmployers || hasJobSeekers || hasGraduates).toBeTruthy()
  })
})