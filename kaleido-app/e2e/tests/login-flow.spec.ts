import { test, expect } from '@playwright/test'
import { getTestUser, loginWithAuth0 } from '../utils/auth-helpers'
import { activateLandingSection, clickGetStartedButton, navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Landing Page Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to landing page
    await page.goto('/')
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
  })

  test('should display all three role sections', async ({ page }) => {
    // Wait for content to be visible
    await page.waitForTimeout(2000) // Give animations time to complete
    
    // Check for Employers section - try different selectors
    const employersText = page.locator('text=Employers').first()
    await expect(employersText).toBeVisible({ timeout: 10000 })
    
    // Check for Job Seekers section
    const jobSeekersText = page.locator('text=Job Seekers').first()
    await expect(jobSeekersText).toBeVisible({ timeout: 10000 })
    
    // Check for Graduates section
    const graduatesText = page.locator('text=Graduates').first()
    await expect(graduatesText).toBeVisible({ timeout: 10000 })
    
    // Check that at least one section is active (visible description)
    const descriptions = page.locator('p.text-white\\/90')
    await expect(descriptions).toHaveCount(3, { timeout: 10000 })
  })

  test('should show correct logo', async ({ page }) => {
    // Check for Kaleido logo
    await expect(page.locator('img[alt="Kaleido Talent Icon"]').first()).toBeVisible()
    await expect(page.locator('img[alt="Kaleido Talent Logo"]').first()).toBeVisible()
  })

  test.describe('Job Seeker Login Flow', () => {
    test('should navigate to Auth0 login when clicking Job Seekers Get Started', async ({ page, context }) => {
      // Use the helper function to activate section
      await activateLandingSection(page, 'Job Seekers')
      
      // Click Get Started button
      await clickGetStartedButton(page)
      
      // Wait for navigation to Auth0
      await page.waitForURL(/.*auth0.*|.*api\/auth\/login.*/, { timeout: 30000 })
      
      // Verify localStorage has the correct role
      const pendingRole = await page.evaluate(() => localStorage.getItem('pendingUserRole'))
      expect(pendingRole).toBe('job-seeker')
    })

    test('should navigate to Auth0 using direct navigation', async ({ page }) => {
      // Alternative approach - navigate directly
      await navigateToAuthWithRole(page, 'job-seeker')
      
      // Should redirect to Auth0
      await page.waitForURL(/.*auth0.*/, { timeout: 30000 })
      
      // Verify we're on Auth0 login page
      await expect(page.locator('input[type="email"], input[name="username"]')).toBeVisible({ timeout: 10000 })
    })

    test('should complete full login flow for job seeker', async ({ page, browser }) => {
      // Create a new context to ensure clean state
      const context = await browser.newContext()
      const newPage = await context.newPage()
      
      await newPage.goto('/')
      await newPage.waitForLoadState('networkidle')
      
      // Activate Job Seekers section
      const isMobile = await newPage.evaluate(() => window.innerWidth <= 768)
      
      if (isMobile) {
        await newPage.locator('text=Job Seekers').first().tap()
      } else {
        await newPage.locator('text=Job Seekers').first().hover()
      }
      
      await newPage.waitForTimeout(1000)
      
      // Click Get Started
      await newPage.locator('button:has-text("Get Started")').first().click()
      
      // Handle Auth0 login (if redirected to Auth0)
      try {
        await newPage.waitForURL(/.*auth0.*/, { timeout: 10000 })
        
        // If we have test credentials, complete the login
        if (process.env.TEST_JOB_SEEKER_EMAIL && process.env.TEST_USER_PASSWORD) {
          const jobSeeker = getTestUser('job-seeker')
          await loginWithAuth0(newPage, jobSeeker)
          
          // Should redirect to dashboard after successful login
          await expect(newPage).toHaveURL(/.*dashboard.*/, { timeout: 30000 })
        }
      } catch (e) {
        // If no Auth0 redirect, check if we're on the login API endpoint
        await expect(newPage).toHaveURL(/.*api\/auth\/login.*/)
      }
      
      await context.close()
    })
  })

  test.describe('Employer Login Flow', () => {
    test('should navigate to Auth0 login when clicking Employers Get Started', async ({ page }) => {
      // Use the helper function to activate section
      await activateLandingSection(page, 'Employers')
      
      // Click Get Started button
      await clickGetStartedButton(page)
      
      // Wait for navigation to Auth0
      await page.waitForURL(/.*auth0.*|.*api\/auth\/login.*/, { timeout: 30000 })
      
      // Verify localStorage has the correct role
      const pendingRole = await page.evaluate(() => localStorage.getItem('pendingUserRole'))
      expect(pendingRole).toBe('employer')
    })

    test('should navigate to Auth0 using direct navigation', async ({ page }) => {
      // Alternative approach - navigate directly
      await navigateToAuthWithRole(page, 'employer')
      
      // Should redirect to Auth0
      await page.waitForURL(/.*auth0.*/, { timeout: 30000 })
      
      // Verify we're on Auth0 login page
      await expect(page.locator('input[type="email"], input[name="username"]')).toBeVisible({ timeout: 10000 })
    })

    test('should complete full login flow for employer', async ({ page, browser }) => {
      // Create a new context to ensure clean state
      const context = await browser.newContext()
      const newPage = await context.newPage()
      
      await newPage.goto('/')
      await newPage.waitForLoadState('networkidle')
      
      // Activate Employers section
      const isMobile = await newPage.evaluate(() => window.innerWidth <= 768)
      
      if (isMobile) {
        await newPage.locator('text=Employers').first().tap()
      } else {
        await newPage.locator('text=Employers').first().hover()
      }
      
      await newPage.waitForTimeout(1000)
      
      // Click Get Started
      await newPage.locator('button:has-text("Get Started")').first().click()
      
      // Handle Auth0 login (if redirected to Auth0)
      try {
        await newPage.waitForURL(/.*auth0.*/, { timeout: 10000 })
        
        // If we have test credentials, complete the login
        if (process.env.TEST_EMPLOYER_EMAIL && process.env.TEST_USER_PASSWORD) {
          const employer = getTestUser('employer')
          await loginWithAuth0(newPage, employer)
          
          // Should redirect to dashboard after successful login
          await expect(newPage).toHaveURL(/.*dashboard.*/, { timeout: 30000 })
        }
      } catch (e) {
        // If no Auth0 redirect, check if we're on the login API endpoint
        await expect(newPage).toHaveURL(/.*api\/auth\/login.*/)
      }
      
      await context.close()
    })
  })

  test.describe('Graduates Section', () => {
    test('should show Coming Soon for graduates', async ({ page }) => {
      // Hover over Graduates section
      const graduatesSection = page.locator('text=Graduates').first()
      
      const isMobile = await page.evaluate(() => window.innerWidth <= 768)
      
      if (isMobile) {
        await graduatesSection.tap()
      } else {
        await graduatesSection.hover()
      }
      
      await page.waitForTimeout(1000)
      
      // Should show Coming Soon button
      const comingSoonButton = page.locator('button:has-text("Coming Soon")')
      await expect(comingSoonButton).toBeVisible()
      
      // Button should be disabled
      await expect(comingSoonButton).toHaveAttribute('class', /cursor-not-allowed/)
    })
  })

  test.describe('Mobile Navigation', () => {
    test('should show navigation arrows on mobile', async ({ page, viewport }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      await page.goto('/')
      await page.waitForLoadState('networkidle')
      
      // Should see down arrow initially (starts at middle section)
      const downArrow = page.locator('button:has(svg.lucide-chevron-down)')
      await expect(downArrow).toBeVisible()
      
      // Click down arrow to go to Graduates
      await downArrow.click()
      await page.waitForTimeout(500) // Wait for animation
      
      // Should now see up arrow
      const upArrow = page.locator('button:has(svg.lucide-chevron-up)')
      await expect(upArrow).toBeVisible()
      
      // Click up arrow to go back
      await upArrow.click()
      await page.waitForTimeout(500)
      
      // Should see both arrows in middle section
      await expect(downArrow).toBeVisible()
      await expect(upArrow).toBeVisible()
    })
  })
})