import { test, expect } from '@playwright/test';
import { 
  createTestUser, 
  registerWithAuth0, 
  deleteAuth0User,
  waitForDashboard 
} from '../utils/auth-helpers';
import { navigateToAuthWithRole } from '../utils/landing-helpers';

test.describe('Job Seeker Onboarding - Debug', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' };

  test.beforeAll(() => {
    testUser = createTestUser('job-seeker');
    console.log(`Test user created: ${testUser.email}`);
  });

  test.afterAll(async () => {
    console.log(`Cleaning up test user: ${testUser.email}`);
    await deleteAuth0User(testUser.email).catch(console.error);
  });

  test('debug onboarding flow step by step', async ({ page }) => {
    // Navigate to home
    await page.goto('/');
    
    // Navigate to auth for job seeker
    await navigateToAuthWithRole(page, 'job-seeker');
    
    // Register with Auth0
    await registerWithAuth0(page, testUser);
    
    // Wait for dashboard/onboarding
    await waitForDashboard(page, 'job-seeker');
    
    // Verify we're on onboarding
    const currentUrl = page.url();
    console.log('Current URL after registration:', currentUrl);
    expect(currentUrl).toContain('onboarding');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Extra wait for React to render
    
    // Debug: Take screenshot of initial state
    await page.screenshot({ path: 'test-results/debug-1-initial.png' });
    
    // Find all buttons on the page
    const buttons = await page.locator('button').all();
    console.log(`Found ${buttons.length} buttons on the page`);
    
    for (let i = 0; i < buttons.length; i++) {
      const text = await buttons[i].textContent();
      const isVisible = await buttons[i].isVisible();
      console.log(`Button ${i}: "${text}" - Visible: ${isVisible}`);
    }
    
    // Look for specific onboarding elements
    const skipButton = page.locator('button:has-text("Skip")');
    const nextButton = page.locator('button:has-text("Next")');
    const continueButton = page.locator('button:has-text("Continue")');
    const uploadArea = page.locator('.dropzone, [data-testid="resume-upload-area"], input[type="file"]');
    
    console.log('Skip button visible:', await skipButton.isVisible().catch(() => false));
    console.log('Next button visible:', await nextButton.isVisible().catch(() => false));
    console.log('Continue button visible:', await continueButton.isVisible().catch(() => false));
    console.log('Upload area visible:', await uploadArea.isVisible().catch(() => false));
    
    // Try to find any step indicator
    const stepIndicators = await page.locator('[data-testid*="step"], .step-indicator, .progress').all();
    console.log(`Found ${stepIndicators.length} step indicators`);
    
    // Check for any loading states
    const loadingElements = await page.locator('.loading, .spinner, [data-loading="true"]').all();
    console.log(`Found ${loadingElements.length} loading elements`);
    
    // Look for the main content area
    const mainContent = await page.locator('main, [role="main"], .main-content').first();
    if (await mainContent.count() > 0) {
      const mainText = await mainContent.textContent();
      console.log('Main content preview:', mainText?.substring(0, 200) + '...');
    }
    
    // If we found a skip button, click it
    if (await skipButton.isVisible()) {
      console.log('Clicking Skip button');
      await skipButton.click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'test-results/debug-2-after-skip.png' });
      
      // Check what's visible after skip
      const firstNameInput = page.locator('input[name="firstName"], input[placeholder*="First"]');
      const emailInput = page.locator('input[type="email"]');
      
      console.log('First name input visible:', await firstNameInput.isVisible().catch(() => false));
      console.log('Email input visible:', await emailInput.isVisible().catch(() => false));
      
      // List all input fields
      const inputs = await page.locator('input').all();
      console.log(`Found ${inputs.length} input fields`);
      for (let i = 0; i < Math.min(inputs.length, 10); i++) {
        const name = await inputs[i].getAttribute('name');
        const placeholder = await inputs[i].getAttribute('placeholder');
        const type = await inputs[i].getAttribute('type');
        console.log(`Input ${i}: name="${name}", placeholder="${placeholder}", type="${type}"`);
      }
    }
    
    // If we found a continue button, click it
    else if (await continueButton.isVisible()) {
      console.log('Clicking Continue button');
      await continueButton.click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'test-results/debug-2-after-continue.png' });
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/debug-3-final.png' });
  });
});