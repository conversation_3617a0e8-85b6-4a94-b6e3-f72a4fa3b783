import { test, expect } from '@playwright/test'
import { 
  createTestUser, 
  registerWithAuth0, 
  loginWithAuth0, 
  logout, 
  deleteAuth0User,
  waitForDashboard 
} from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Direct Authentication Flow', () => {
  test.describe.configure({ mode: 'serial' })
  
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' }

  test.describe('Job Seeker Direct Auth Flow', () => {
    // Generate a unique test user for this test run
    test.beforeAll(() => {
      testUser = createTestUser('job-seeker')
      console.log(`Test user created: ${testUser.email}`)
    })

    // Clean up the test user after all tests
    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email)
    })

    test('should complete full auth flow using direct navigation', async ({ page }) => {
      // Step 1: Navigate directly to auth with role
      console.log('Navigating to Auth0 with job-seeker role...')
      await page.goto('/')
      await navigateToAuthWithRole(page, 'job-seeker')
      
      // Step 2: Register with Auth0
      console.log('Registering new user...')
      await registerWithAuth0(page, testUser)
      
      // Step 3: Verify we're redirected to dashboard
      await waitForDashboard(page, 'job-seeker')
      console.log('Successfully registered and redirected to dashboard')
      
      // Take a screenshot of the dashboard
      await page.screenshot({ path: 'test-results/job-seeker-dashboard-direct-register.png' })
      
      // Step 4: Logout
      console.log('Logging out...')
      await logout(page)
      
      // Verify we're back at the landing page
      await expect(page).toHaveURL('/')
      console.log('Successfully logged out')
      
      // Step 5: Login again using direct navigation
      console.log('Logging in again...')
      await navigateToAuthWithRole(page, 'job-seeker')
      
      // Login with existing credentials
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Verify we're back at the dashboard
      await waitForDashboard(page, 'job-seeker')
      console.log('Successfully logged in again')
      
      // Take a screenshot of the dashboard after login
      await page.screenshot({ path: 'test-results/job-seeker-dashboard-direct-login.png' })
    })
  })

  test.describe('Employer Direct Auth Flow', () => {
    // Generate a unique test user for this test run
    test.beforeAll(() => {
      testUser = createTestUser('employer')
      console.log(`Test user created: ${testUser.email}`)
    })

    // Clean up the test user after all tests
    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email)
    })

    test('should complete full auth flow using direct navigation', async ({ page }) => {
      // Step 1: Navigate directly to auth with role
      console.log('Navigating to Auth0 with employer role...')
      await page.goto('/')
      await navigateToAuthWithRole(page, 'employer')
      
      // Step 2: Register with Auth0
      console.log('Registering new employer...')
      await registerWithAuth0(page, testUser)
      
      // Step 3: Verify we're redirected to dashboard
      await waitForDashboard(page, 'employer')
      console.log('Successfully registered and redirected to employer dashboard')
      
      // Take a screenshot of the dashboard
      await page.screenshot({ path: 'test-results/employer-dashboard-direct-register.png' })
      
      // Step 4: Logout
      console.log('Logging out...')
      await logout(page)
      
      // Verify we're back at the landing page
      await expect(page).toHaveURL('/')
      console.log('Successfully logged out')
      
      // Step 5: Login again using direct navigation
      console.log('Logging in again...')
      await navigateToAuthWithRole(page, 'employer')
      
      // Login with existing credentials
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Verify we're back at the dashboard
      await waitForDashboard(page, 'employer')
      console.log('Successfully logged in again')
      
      // Take a screenshot of the dashboard after login
      await page.screenshot({ path: 'test-results/employer-dashboard-direct-login.png' })
    })
  })
})