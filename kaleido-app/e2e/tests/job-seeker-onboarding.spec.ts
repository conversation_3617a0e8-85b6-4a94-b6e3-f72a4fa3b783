import { test, expect } from '@playwright/test';
import { JobSeekerOnboardingPage } from '../page-objects/job-seeker-onboarding.page';
import { createTestUser, registerWithAuth0, loginWithAuth0, waitForDashboard } from '../utils/auth-helpers';
import { navigateToAuthWithRole } from '../utils/landing-helpers';

test.describe('Job Seeker Onboarding Flow', () => {
  let onboardingPage: JobSeekerOnboardingPage;
  
  test.beforeEach(async ({ page }) => {
    onboardingPage = new JobSeekerOnboardingPage(page);
  });

  test('should complete full job seeker onboarding process', async ({ page }) => {
    // Generate unique user data
    const userData = createTestUser('job-seeker');
    
    // Navigate to landing page and register as job seeker
    await page.goto('/');
    await navigateToAuthWithRole(page, 'job-seeker');
    
    // Handle Auth0 registration
    await registerWithAuth0(page, userData);
    
    // Wait for redirect to onboarding page
    await waitForDashboard(page, 'job-seeker');
    const currentUrl = page.url();
    expect(currentUrl).toContain('onboarding');
    
    // Wait for onboarding to fully load
    await onboardingPage.waitForOnboardingToLoad();
    
    // Step 1: Resume Upload (Skip for now as we need PDF)
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      // If we can't skip, upload a test file
      const testFilePath = './e2e/fixtures/test-files/test-resume.txt';
      await onboardingPage.uploadResume(testFilePath);
      await onboardingPage.goToNextStep();
    }
    
    // Step 2: Personal Information
    await expect(onboardingPage.firstNameInput).toBeVisible({ timeout: 10000 });
    await onboardingPage.fillPersonalInfo({
      firstName: 'John',
      lastName: 'Doe',
      email: userData.email,
      phone: '+****************',
      location: 'San Francisco, CA',
      linkedIn: 'https://linkedin.com/in/johndoe',
      website: 'https://johndoe.dev'
    });
    await onboardingPage.goToNextStep();
    
    // Step 3: Professional Summary
    await expect(onboardingPage.summaryTextarea).toBeVisible({ timeout: 5000 });
    await onboardingPage.fillProfessionalSummary({
      summary: 'Experienced software engineer with expertise in full-stack development and cloud technologies.',
      headline: 'Senior Software Engineer',
      yearsOfExperience: 5
    });
    await onboardingPage.goToNextStep();
    
    // Step 4: Experience
    await expect(onboardingPage.addExperienceButton).toBeVisible({ timeout: 5000 });
    await onboardingPage.addExperience({
      jobTitle: 'Senior Software Engineer',
      companyName: 'TechCorp Inc.',
      startDate: '2021-01-01',
      currentlyWorking: true,
      description: 'Led development of microservices architecture and mentored junior developers.'
    });
    await onboardingPage.goToNextStep();
    
    // Step 5: Education
    await expect(onboardingPage.addEducationButton).toBeVisible({ timeout: 5000 });
    // Skip education for faster test
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      await onboardingPage.goToNextStep();
    }
    
    // Step 6: Skills & Languages
    // Skip for now - these might require special handling
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      await onboardingPage.goToNextStep();
    }
    
    // Step 7: Job Preferences
    await expect(page.getByText(/job preferences|what.*looking/i)).toBeVisible({ timeout: 5000 });
    await onboardingPage.selectJobTypes(['full-time', 'contract']);
    await onboardingPage.setRemotePreference('hybrid');
    await onboardingPage.setSalaryExpectations(80000, 120000, 'yearly');
    await onboardingPage.goToNextStep();
    
    // Step 8: Work Availability
    await expect(onboardingPage.immediatelyAvailableCheckbox).toBeVisible({ timeout: 5000 });
    await onboardingPage.immediatelyAvailableCheckbox.check();
    await onboardingPage.goToNextStep();
    
    // Step 9: Values (Skip)
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      await onboardingPage.goToNextStep();
    }
    
    // Step 10: Portfolio (Skip)
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      await onboardingPage.goToNextStep();
    }
    
    // Step 11: Video Introduction (Skip)
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    } else {
      await onboardingPage.goToNextStep();
    }
    
    // Step 12: Privacy Settings
    await expect(page.getByText(/privacy|visibility/i)).toBeVisible({ timeout: 5000 });
    await onboardingPage.setPrivacySettings({
      profileVisibility: 'employers-only',
      allowMessages: true,
      showSalary: false,
      showContact: true
    });
    await onboardingPage.goToNextStep();
    
    // Step 13: ID Verification (Skip if optional)
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    }
    
    // Complete Setup
    await expect(onboardingPage.completeSetupButton).toBeVisible({ timeout: 5000 });
    await onboardingPage.completeSetup();
    
    // Verify redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: 15000 });
    
    // Verify user is on the dashboard
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.getByRole('heading', { name: /dashboard|welcome/i })).toBeVisible();
  });

  test('should allow skipping optional steps', async ({ page }) => {
    // Generate unique user data
    const userData = createTestUser('job-seeker');
    
    // Navigate and register
    await page.goto('/');
    await navigateToAuthWithRole(page, 'job-seeker');
    await registerWithAuth0(page, userData);
    
    // Wait for onboarding
    await waitForDashboard(page, 'job-seeker');
    expect(page.url()).toContain('onboarding');
    
    // Skip all optional steps and fill only required fields
    let stepCount = 0;
    const maxSteps = 15; // Safety limit
    
    while (stepCount < maxSteps) {
      // Check if we can skip current step
      if (await onboardingPage.skipButton.isVisible()) {
        await onboardingPage.skipStep();
      } else if (await onboardingPage.completeSetupButton.isVisible()) {
        // We're at the final step
        await onboardingPage.completeSetup();
        break;
      } else {
        // Fill required fields based on visible inputs
        if (await onboardingPage.firstNameInput.isVisible()) {
          await onboardingPage.fillPersonalInfo({
            firstName: 'Test',
            lastName: 'User',
            email: userData.email
          });
        } else if (await onboardingPage.summaryTextarea.isVisible()) {
          await onboardingPage.fillProfessionalSummary({
            summary: 'Test summary for quick onboarding test.'
          });
        }
        
        await onboardingPage.goToNextStep();
      }
      
      stepCount++;
      await page.waitForTimeout(500); // Small delay between steps
    }
    
    // Verify we completed onboarding
    await page.waitForURL('**/dashboard', { timeout: 15000 });
    await expect(page).toHaveURL(/\/dashboard/);
  });

  test('should save progress when navigating back', async ({ page }) => {
    // Generate unique user data
    const userData = createTestUser('job-seeker');
    
    // Navigate and register
    await page.goto('/');
    await navigateToAuthWithRole(page, 'job-seeker');
    await registerWithAuth0(page, userData);
    
    // Wait for onboarding
    await waitForDashboard(page, 'job-seeker');
    expect(page.url()).toContain('onboarding');
    
    // Skip to personal info step
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    }
    
    // Fill personal information
    await expect(onboardingPage.firstNameInput).toBeVisible({ timeout: 10000 });
    const testData = {
      firstName: 'SaveTest',
      lastName: 'User',
      email: userData.email,
      phone: '+****************'
    };
    
    await onboardingPage.fillPersonalInfo(testData);
    await onboardingPage.goToNextStep();
    
    // Go back to personal info
    await onboardingPage.goToPreviousStep();
    
    // Verify data is still there
    await expect(onboardingPage.firstNameInput).toHaveValue(testData.firstName);
    await expect(onboardingPage.lastNameInput).toHaveValue(testData.lastName);
    await expect(onboardingPage.phoneInput).toHaveValue(testData.phone);
  });

  test('should handle closing and returning to onboarding', async ({ page, context }) => {
    // Generate unique user data
    const userData = createTestUser('job-seeker');
    
    // Navigate and register
    await page.goto('/');
    await navigateToAuthWithRole(page, 'job-seeker');
    await registerWithAuth0(page, userData);
    
    // Wait for onboarding
    await waitForDashboard(page, 'job-seeker');
    expect(page.url()).toContain('onboarding');
    
    // Fill some data
    if (await onboardingPage.skipButton.isVisible()) {
      await onboardingPage.skipStep();
    }
    
    await expect(onboardingPage.firstNameInput).toBeVisible({ timeout: 10000 });
    await onboardingPage.fillPersonalInfo({
      firstName: 'Persistent',
      lastName: 'Data',
      email: userData.email
    });
    
    // Close onboarding
    await onboardingPage.closeButton.click({ force: true });
    
    // Should redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    // Navigate back to onboarding
    await page.goto('/jobseeker-onboarding');
    
    // Should redirect to dashboard since onboarding was closed
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    // Verify we're on dashboard
    await expect(page).toHaveURL(/\/dashboard/);
  });
});