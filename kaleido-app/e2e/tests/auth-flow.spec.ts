import { test, expect } from '@playwright/test'
import { 
  createTestUser, 
  registerWithAuth0, 
  loginWithAuth0, 
  logout, 
  deleteAuth0User,
  waitForDashboard 
} from '../utils/auth-helpers'
import { activateLandingSection, clickGetStartedButton } from '../utils/landing-helpers'

test.describe('Complete Authentication Flow', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' }

  test.describe('Job Seeker Authentication Flow', () => {
    // Generate a unique test user for this test run
    test.beforeAll(() => {
      testUser = createTestUser('job-seeker')
      console.log(`Test user created: ${testUser.email}`)
    })

    // Clean up the test user after all tests
    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email)
    })

    test('should complete full auth flow: register, logout, and login', async ({ page, context }) => {
      // Step 1: Navigate to landing page
      await page.goto('/')
      await page.waitForLoadState('networkidle')
      
      // Step 2: Click on Job Seekers section and Get Started
      await activateLandingSection(page, 'Job Seekers')
      await clickGetStartedButton(page)
      
      // Step 3: Register with Auth0
      console.log('Registering new user...')
      await registerWithAuth0(page, testUser)
      
      // Step 4: Verify we're redirected to dashboard
      await waitForDashboard(page, 'job-seeker')
      console.log('Successfully registered and redirected to dashboard')
      
      // Take a screenshot of the dashboard
      await page.screenshot({ path: 'test-results/job-seeker-dashboard-after-register.png' })
      
      // Step 5: Logout
      console.log('Logging out...')
      await logout(page)
      
      // Verify we're back at the landing page
      await expect(page).toHaveURL('/')
      console.log('Successfully logged out')
      
      // Step 6: Login again with the same credentials
      console.log('Logging in again...')
      
      // Click Job Seekers Get Started again
      await activateLandingSection(page, 'Job Seekers')
      await clickGetStartedButton(page)
      
      // Login with existing credentials
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Verify we're back at the dashboard
      await waitForDashboard(page, 'job-seeker')
      console.log('Successfully logged in again')
      
      // Take a screenshot of the dashboard after login
      await page.screenshot({ path: 'test-results/job-seeker-dashboard-after-login.png' })
    })
  })

  test.describe('Employer Authentication Flow', () => {
    // Generate a unique test user for this test run
    test.beforeAll(() => {
      testUser = createTestUser('employer')
      console.log(`Test user created: ${testUser.email}`)
    })

    // Clean up the test user after all tests
    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email)
    })

    test('should complete full auth flow: register, logout, and login', async ({ page, context }) => {
      // Step 1: Navigate to landing page
      await page.goto('/')
      await page.waitForLoadState('networkidle')
      
      // Step 2: Click on Employers section and Get Started
      await activateLandingSection(page, 'Employers')
      await clickGetStartedButton(page)
      
      // Step 3: Register with Auth0
      console.log('Registering new employer...')
      await registerWithAuth0(page, testUser)
      
      // Step 4: Verify we're redirected to dashboard
      await waitForDashboard(page, 'employer')
      console.log('Successfully registered and redirected to employer dashboard')
      
      // Take a screenshot of the dashboard
      await page.screenshot({ path: 'test-results/employer-dashboard-after-register.png' })
      
      // Step 5: Logout
      console.log('Logging out...')
      await logout(page)
      
      // Verify we're back at the landing page
      await expect(page).toHaveURL('/')
      console.log('Successfully logged out')
      
      // Step 6: Login again with the same credentials
      console.log('Logging in again...')
      
      // Click Employers Get Started again
      await activateLandingSection(page, 'Employers')
      await clickGetStartedButton(page)
      
      // Login with existing credentials
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Verify we're back at the dashboard
      await waitForDashboard(page, 'employer')
      console.log('Successfully logged in again')
      
      // Take a screenshot of the dashboard after login
      await page.screenshot({ path: 'test-results/employer-dashboard-after-login.png' })
    })
  })
})

test.describe('Auth0 User Cleanup', () => {
  test.skip('should delete a test user from Auth0', async () => {
    // This is a utility test to manually clean up users if needed
    const testEmail = '<EMAIL>'
    await deleteAuth0User(testEmail)
    console.log(`Deleted user: ${testEmail}`)
  })
})