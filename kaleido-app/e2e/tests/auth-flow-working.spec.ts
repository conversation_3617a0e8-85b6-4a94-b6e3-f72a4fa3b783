import { test, expect } from '@playwright/test'
import { 
  createTestUser, 
  registerWithAuth0, 
  loginWithAuth0, 
  deleteAuth0User,
  waitForDashboard 
} from '../utils/auth-helpers'
import { navigateToAuthWithRole } from '../utils/landing-helpers'

test.describe('Working Authentication Flow', () => {
  let testUser: { email: string; password: string; role: 'employer' | 'job-seeker' }

  test.describe('Job Seeker Auth Flow', () => {
    test.beforeAll(() => {
      testUser = createTestUser('job-seeker')
      console.log(`Test user created: ${testUser.email}`)
    })

    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email).catch(console.error)
    })

    test('should register, logout and login', async ({ page }) => {
      // Step 1: Register new user
      console.log('=== REGISTRATION ===')
      await page.goto('/')
      await navigateToAuthWithRole(page, 'job-seeker')
      
      console.log('Registering new user...')
      await registerWithAuth0(page, testUser)
      
      // Should be on onboarding or dashboard
      await waitForDashboard(page, 'job-seeker')
      const afterRegisterUrl = page.url()
      console.log('After registration URL:', afterRegisterUrl)
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/job-seeker-after-register.png' })
      
      // Step 2: Logout
      console.log('\n=== LOGOUT ===')
      await page.goto('/api/auth/logout')
      await page.waitForTimeout(3000)
      
      const afterLogoutUrl = page.url()
      console.log('After logout URL:', afterLogoutUrl)
      
      // Navigate to home if not there
      if (!afterLogoutUrl.endsWith('/')) {
        await page.goto('/')
      }
      
      // Verify we can see the landing page
      await expect(page.locator('text=Job Seekers').first()).toBeVisible({ timeout: 10000 })
      console.log('Successfully logged out and on landing page')
      
      // Step 3: Login again
      console.log('\n=== LOGIN ===')
      await navigateToAuthWithRole(page, 'job-seeker')
      
      console.log('Logging in with existing user...')
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Should be back on dashboard/onboarding
      await waitForDashboard(page, 'job-seeker')
      const afterLoginUrl = page.url()
      console.log('After login URL:', afterLoginUrl)
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/job-seeker-after-login.png' })
      
      console.log('\n✅ Full auth flow completed successfully!')
    })
  })

  test.describe('Employer Auth Flow', () => {
    test.beforeAll(() => {
      testUser = createTestUser('employer')
      console.log(`Test user created: ${testUser.email}`)
    })

    test.afterAll(async () => {
      console.log(`Cleaning up test user: ${testUser.email}`)
      await deleteAuth0User(testUser.email).catch(console.error)
    })

    test('should register, logout and login', async ({ page }) => {
      // Step 1: Register new user
      console.log('=== REGISTRATION ===')
      await page.goto('/')
      await navigateToAuthWithRole(page, 'employer')
      
      console.log('Registering new employer...')
      await registerWithAuth0(page, testUser)
      
      // Should be on onboarding or dashboard
      await waitForDashboard(page, 'employer')
      const afterRegisterUrl = page.url()
      console.log('After registration URL:', afterRegisterUrl)
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/employer-after-register.png' })
      
      // Step 2: Logout
      console.log('\n=== LOGOUT ===')
      await page.goto('/api/auth/logout')
      await page.waitForTimeout(3000)
      
      const afterLogoutUrl = page.url()
      console.log('After logout URL:', afterLogoutUrl)
      
      // Navigate to home if not there
      if (!afterLogoutUrl.endsWith('/')) {
        await page.goto('/')
      }
      
      // Verify we can see the landing page
      await expect(page.locator('text=Employers').first()).toBeVisible({ timeout: 10000 })
      console.log('Successfully logged out and on landing page')
      
      // Step 3: Login again
      console.log('\n=== LOGIN ===')
      await navigateToAuthWithRole(page, 'employer')
      
      console.log('Logging in with existing user...')
      await loginWithAuth0(page, testUser.email, testUser.password)
      
      // Should be back on dashboard/onboarding
      await waitForDashboard(page, 'employer')
      const afterLoginUrl = page.url()
      console.log('After login URL:', afterLoginUrl)
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/employer-after-login.png' })
      
      console.log('\n✅ Full auth flow completed successfully!')
    })
  })
})