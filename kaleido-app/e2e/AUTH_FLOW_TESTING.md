# Auth Flow E2E Testing Guide

This guide explains how to run and maintain the authentication flow E2E tests.

## Overview

The auth flow tests cover the complete authentication journey:
1. **Registration** - New user signs up with @kaleidotalent.com email
2. **Logout** - User logs out from the application
3. **Login** - User logs back in with the same credentials

## Running the Tests

### Quick Start
```bash
# Run auth flow tests
pnpm test:e2e:auth

# Or run directly
pnpm test:e2e e2e/tests/auth-flow.spec.ts
```

### Interactive Mode (Recommended for Debugging)
```bash
# Run with UI to see tests in action
pnpm test:e2e:ui

# Then select auth-flow.spec.ts from the UI
```

## Test Structure

### Test Users
- Each test run creates unique test users with timestamps
- Email format: `test-{role}-{timestamp}-{random}@kaleidotalent.com`
- Password: `TestPassword123!`
- Roles tested: `job-seeker` and `employer`

### Auth0 Integration
The tests use your actual Auth0 configuration from `.env.local`:
- `AUTH0_ISSUER_BASE_URL` - Your Auth0 domain
- `AUTH0_CLIENT_ID` - Your Auth0 application ID
- `AUTH0_CLIENT_SECRET` - For Auth0 Management API (cleanup)

### User Cleanup
- Test users are automatically deleted from Auth0 after tests complete
- Uses Auth0 Management API for cleanup
- Database cleanup should be added when backend supports it

## What the Tests Do

### Job Seeker Flow Test
1. Navigate to landing page
2. Click "Job Seekers" section
3. Click "Get Started" button
4. Register new user in Auth0
5. Verify redirect to job-seeker dashboard
6. Take screenshot of dashboard
7. Logout from application
8. Verify redirect to landing page
9. Login again with same credentials
10. Verify redirect back to dashboard
11. Take final screenshot

### Employer Flow Test
Same as above but for the Employer role.

## Screenshots

After running tests, check `test-results/` for:
- `job-seeker-dashboard-after-register.png`
- `job-seeker-dashboard-after-login.png`
- `employer-dashboard-after-register.png`
- `employer-dashboard-after-login.png`

## Troubleshooting

### Tests Failing at Registration
- Check Auth0 allows @kaleidotalent.com emails
- Verify Auth0 connection settings
- Check if signup is enabled in Auth0

### Tests Failing at Login
- Ensure Auth0 callback URLs include test environment
- Check if user was created successfully
- Verify password policy matches test password

### Cleanup Not Working
- Ensure AUTH0_CLIENT_SECRET is set in .env.local
- Check Auth0 Management API is enabled
- Verify client has delete:users scope

## Environment Setup

1. Ensure `.env.local` has all required Auth0 variables:
```env
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret
AUTH0_AUDIENCE='https://your-api.com'
```

2. Auth0 Application Settings:
- Allowed Callback URLs: Include `http://localhost:3000/*`
- Allowed Logout URLs: Include `http://localhost:3000`
- Allowed Web Origins: Include `http://localhost:3000`

3. Auth0 Connection Settings:
- Enable username/password authentication
- Allow signup
- No email verification required for testing

## Extending the Tests

To add more auth flow tests:

1. Add new test scenarios in `auth-flow.spec.ts`
2. Use helper functions from `utils/auth-helpers.ts`
3. Follow the pattern: Register → Action → Logout → Login → Verify

Example:
```typescript
test('should update profile after login', async ({ page }) => {
  // Register user
  // Update profile
  // Logout
  // Login
  // Verify profile changes persist
})
```

## Best Practices

1. **Always clean up test users** - Use afterAll hooks
2. **Use unique emails** - Include timestamp in email
3. **Take screenshots** - Helps debug failures
4. **Wait for animations** - Auth0 has transitions
5. **Handle both mobile and desktop** - Tests are responsive

## Manual Cleanup

If automatic cleanup fails, you can manually delete test users:

```typescript
// Run this test to clean up a specific user
test.only('manual cleanup', async () => {
  await deleteAuth0User('<EMAIL>')
})
```

Or use Auth0 Dashboard to search and delete test users.