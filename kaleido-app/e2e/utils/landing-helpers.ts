import { Page } from '@playwright/test'

/**
 * Activates a section on the landing page by handling hover/click appropriately
 */
export async function activateLandingSection(page: Page, sectionName: string) {
  // Wait for animations to settle
  await page.waitForTimeout(2000)
  
  // Find the section
  const section = page.locator(`text="${sectionName}"`).first()
  await section.waitFor({ state: 'visible', timeout: 10000 })
  
  // Check if mobile
  const isMobile = await page.evaluate(() => window.innerWidth <= 768)
  
  if (isMobile) {
    // On mobile, click/tap the section
    await section.tap()
  } else {
    // On desktop, we need to handle the hover differently
    // First try clicking on the section container instead of hovering
    const sectionContainer = section.locator('xpath=ancestor::div[contains(@class, "relative")]').first()
    
    try {
      // Try to click on the section to activate it
      await sectionContainer.click({ force: true })
    } catch (e) {
      // If click fails, try hover with force
      await section.hover({ force: true })
    }
  }
  
  // Wait for the section to become active
  await page.waitForTimeout(1500)
}

/**
 * Clicks the Get Started button for the active section
 */
export async function clickGetStartedButton(page: Page) {
  // Look for visible Get Started button
  const getStartedButton = page.locator('button:has-text("Get Started")').and(page.locator(':visible'))
  
  // Wait for button to be visible and clickable
  await getStartedButton.waitFor({ state: 'visible', timeout: 5000 })
  
  // Click the button with force to bypass nextjs-portal
  await getStartedButton.click({ force: true })
}

/**
 * Alternative approach: Navigate directly to auth with role parameter
 */
export async function navigateToAuthWithRole(page: Page, role: string) {
  // Store role in localStorage
  await page.evaluate((r) => {
    localStorage.setItem('pendingUserRole', r);
    localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());
  }, role);
  
  // Navigate directly to auth endpoint
  const params = new URLSearchParams({
    returnTo: '/dashboard',
    role: role,
    screen_hint: 'signup'
  });
  
  await page.goto(`/api/auth/login?${params.toString()}`);
}