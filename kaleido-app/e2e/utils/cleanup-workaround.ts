import axios from 'axios'

/**
 * Alternative cleanup approach using your backend API
 * This assumes your backend has proper Auth0 Management API access
 */
export async function deleteUserViaBackend(email: string): Promise<void> {
  try {
    // Option 1: Call your backend endpoint if it exists
    const backendUrl = process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api'
    
    try {
      // Try to delete via your backend (if such endpoint exists)
      await axios.delete(`${backendUrl}/admin/users`, {
        params: { email },
        headers: {
          // Add auth header if needed
          'X-Test-Cleanup': 'true'
        }
      })
      console.log(`✅ Deleted user via backend: ${email}`)
      return
    } catch (backendError) {
      console.log('Backend cleanup not available, trying other methods...')
    }
    
    // Option 2: Manual cleanup notice
    console.warn(`
====================================
MANUAL CLEANUP REQUIRED
====================================
User created: ${email}

To delete this user:
1. Go to Auth0 Dashboard: https://manage.auth0.com/
2. Navigate to User Management > Users
3. Search for: ${email}
4. Click on the user and select "Delete"

Or use Auth0 CLI:
auth0 users delete "auth0|USER_ID"
====================================
    `)
    
    // Keep track of users that need cleanup
    if (typeof window !== 'undefined') {
      const pendingCleanup = JSON.parse(localStorage.getItem('e2e-pending-cleanup') || '[]')
      pendingCleanup.push({ email, createdAt: new Date().toISOString() })
      localStorage.setItem('e2e-pending-cleanup', JSON.stringify(pendingCleanup))
    }
    
  } catch (error) {
    console.error('Cleanup error:', error)
  }
}

/**
 * List all users pending cleanup
 */
export function listPendingCleanup(): void {
  if (typeof window !== 'undefined') {
    const pendingCleanup = JSON.parse(localStorage.getItem('e2e-pending-cleanup') || '[]')
    if (pendingCleanup.length > 0) {
      console.log('\n=== USERS PENDING CLEANUP ===')
      pendingCleanup.forEach((user: any) => {
        console.log(`- ${user.email} (created: ${user.createdAt})`)
      })
      console.log('=============================\n')
    }
  }
}

/**
 * Clear the pending cleanup list
 */
export function clearPendingCleanup(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('e2e-pending-cleanup')
    console.log('✅ Cleared pending cleanup list')
  }
}