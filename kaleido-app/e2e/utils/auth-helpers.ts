import { Page } from '@playwright/test'
import axios from 'axios'

// Use the domain from env or default
const AUTH0_DOMAIN_ENV = process.env.AUTH0_DOMAIN || process.env.AUTH0_ISSUER_BASE_URL || 'https://headstart.uk.auth0.com'
export const AUTH0_DOMAIN = AUTH0_DOMAIN_ENV.replace(/^https?:\/\//, '').replace(/\/$/, '') // Clean domain
export const AUTH0_CLIENT_ID = process.env.AUTH0_CLIENT_ID
export const AUTH0_CLIENT_SECRET = process.env.AUTH0_CLIENT_SECRET
export const AUTH0_AUDIENCE = process.env.AUTH0_AUDIENCE || 'https://staging.kaleidotalent.com'

// M2M credentials for Management API
export const AUTH0_M2M_CLIENT_ID = process.env.AUTH0_M2M_CLIENT_ID || 'IaswtcaIRSFGbg3adTthWGDrQlCyrZML'
export const AUTH0_M2M_CLIENT_SECRET = process.env.AUTH0_M2M_CLIENT_SECRET || '****************************************************************'

export interface TestUser {
  email: string
  password: string
  role: 'employer' | 'job-seeker' | 'graduate'
}

/**
 * Generates a unique test user email
 */
export function generateTestUserEmail(role: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `test-${role}-${timestamp}-${random}@kaleidotalent.com`
}

/**
 * Creates a test user with a specific role
 */
export function createTestUser(role: 'employer' | 'job-seeker'): TestUser {
  return {
    email: generateTestUserEmail(role),
    password: 'TestPassword123!',
    role
  }
}

/**
 * Handles Auth0 Universal Login for registration
 */
export async function registerWithAuth0(page: Page, user: TestUser) {
  // Wait for Auth0 login page
  await page.waitForURL(/.*auth0.*/, { timeout: 30000 })
  
  // Check if we're on signup or login page
  const signUpLink = page.locator('a:has-text("Sign up"), a:has-text("Don\'t have an account?")')
  
  if (await signUpLink.isVisible({ timeout: 5000 })) {
    await signUpLink.click()
    await page.waitForTimeout(1000)
  }
  
  // Fill in registration form
  await page.fill('input[name="email"], input[name="username"], input[type="email"]', user.email)
  await page.fill('input[name="password"], input[type="password"]', user.password)
  
  // Some Auth0 configs have confirm password field
  const confirmPasswordField = page.locator('input[name="confirmPassword"], input[name="confirm-password"]')
  if (await confirmPasswordField.isVisible({ timeout: 1000 }).catch(() => false)) {
    await confirmPasswordField.fill(user.password)
  }
  
  // Submit form
  await page.click('button[type="submit"], button[name="submit"], button[value="default"]')
  
  // Wait for potential consent screen
  await page.waitForTimeout(3000)
  
  // Handle consent screen if present
  const consentButton = page.locator('button:has-text("Accept"), button:has-text("Authorize")')
  if (await consentButton.isVisible({ timeout: 2000 }).catch(() => false)) {
    await consentButton.click()
  }
  
  // Wait for redirect back to app - could be dashboard or onboarding
  await page.waitForURL(/^((?!auth0).)*$/, { timeout: 30000 })
}

/**
 * Handles Auth0 Universal Login for login
 */
export async function loginWithAuth0(page: Page, email: string, password: string) {
  // Wait for Auth0 login page
  await page.waitForURL(/.*auth0.*/, { timeout: 30000 })
  
  // Fill in login credentials
  await page.fill('input[name="email"], input[name="username"], input[type="email"]', email)
  await page.fill('input[name="password"], input[type="password"]', password)
  
  // Click login button
  await page.click('button[type="submit"], button[name="submit"], button[value="default"]')
  
  // Wait for redirect back to app
  await page.waitForURL(/^((?!auth0).)*$/, { timeout: 30000 })
}

/**
 * Logs out from the application
 */
export async function logout(page: Page) {
  // Navigate to logout endpoint with returnTo parameter
  const logoutUrl = `/api/auth/logout?returnTo=${encodeURIComponent('http://localhost:3000/')}`
  await page.goto(logoutUrl)
  
  // Wait for Auth0 logout and redirect back
  await page.waitForTimeout(3000)
  
  // Should redirect to home page after logout
  const currentUrl = page.url()
  if (!currentUrl.includes('localhost:3000')) {
    // If we're still on Auth0, wait for redirect
    await page.waitForURL(/localhost:3000/, { timeout: 10000 })
  }
  
  // Ensure we're on the home page
  if (!page.url().endsWith('/')) {
    await page.goto('/')
  }
}

/**
 * Gets Auth0 Management API access token
 */
export async function getAuth0ManagementToken(): Promise<string> {
  // Use M2M credentials for Management API access
  if (!AUTH0_M2M_CLIENT_ID || !AUTH0_M2M_CLIENT_SECRET) {
    throw new Error('Auth0 M2M credentials not configured. Please set AUTH0_M2M_CLIENT_ID and AUTH0_M2M_CLIENT_SECRET')
  }
  
  console.log('Getting Auth0 Management API token...')
  console.log('Domain:', AUTH0_DOMAIN)
  console.log('M2M Client ID:', AUTH0_M2M_CLIENT_ID)
  
  const tokenRequest = {
    client_id: AUTH0_M2M_CLIENT_ID,
    client_secret: AUTH0_M2M_CLIENT_SECRET,
    audience: `https://${AUTH0_DOMAIN}/api/v2/`,
    grant_type: 'client_credentials',
    scope: 'delete:users read:users'
  }
  
  console.log('Token request:', JSON.stringify({...tokenRequest, client_secret: '[REDACTED]'}, null, 2))
  
  try {
    const response = await axios.post(`https://${AUTH0_DOMAIN}/oauth/token`, tokenRequest)
    
    console.log('✅ Successfully obtained Management API token')
    return response.data.access_token
  } catch (error: any) {
    console.error('Failed to get Management API token:')
    console.error('Status:', error.response?.status)
    console.error('Response:', JSON.stringify(error.response?.data, null, 2))
    throw error
  }
}

/**
 * Deletes a user from Auth0
 */
export async function deleteAuth0User(email: string): Promise<void> {
  try {
    const token = await getAuth0ManagementToken()
    
    // First, search for the user
    const searchResponse = await axios.get(
      `https://${AUTH0_DOMAIN}/api/v2/users-by-email`,
      {
        params: { email },
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    )
    
    if (searchResponse.data.length === 0) {
      console.log(`User ${email} not found in Auth0`)
      return
    }
    
    // Delete each user found (there might be multiple with same email in different connections)
    for (const user of searchResponse.data) {
      await axios.delete(
        `https://${AUTH0_DOMAIN}/api/v2/users/${user.user_id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      )
      console.log(`✅ Successfully deleted Auth0 user: ${user.user_id} (${email})`)
    }
  } catch (error: any) {
    if (error.response?.status === 403) {
      console.warn('Auth0 Management API access denied.')
      console.warn('The M2M app needs a client grant for the Management API.')
      console.warn(`
To fix this:
1. Go to: https://manage.auth0.com/dashboard/eu/headstart/apis
2. Click on "Auth0 Management API"
3. Go to "Machine to Machine Applications" tab
4. Find app: IaswtcaIRSFGbg3adTthWGDrQlCyrZML
5. Toggle it ON and grant scopes: read:users, delete:users
6. Click "Update"
      `)
      
      // Log manual cleanup required
      console.warn(`
====================================
MANUAL CLEANUP REQUIRED
====================================
User created: ${email}

To delete this user manually:
1. Go to Auth0 Dashboard: https://manage.auth0.com/
2. Navigate to User Management > Users
3. Search for: ${email}
4. Click on the user and select "Delete"
====================================
      `)
    } else {
      console.error('Error deleting Auth0 user:', error.message || error)
    }
    // Don't throw - cleanup is best effort
  }
}

/**
 * Saves authentication state to file
 */
export async function saveAuthState(page: Page, path: string) {
  await page.context().storageState({ path })
}

/**
 * Waits for the user to be redirected to their role-specific dashboard or onboarding
 */
export async function waitForDashboard(page: Page, role: string) {
  // First time users might be redirected to onboarding
  const onboardingUrls: Record<string, RegExp> = {
    'employer': /\/company-onboarding/,
    'job-seeker': /\/jobseeker-onboarding/,
    'graduate': /\/graduate-onboarding/
  }
  
  const dashboardUrls: Record<string, RegExp> = {
    'employer': /\/employer\/dashboard/,
    'job-seeker': /\/job-seeker\/dashboard/,
    'graduate': /\/graduate\/dashboard/
  }
  
  // Wait for either onboarding or dashboard
  await page.waitForURL(
    new RegExp(`(${onboardingUrls[role]?.source || 'onboarding'}|${dashboardUrls[role]?.source || 'dashboard'})`), 
    { timeout: 30000 }
  )
}