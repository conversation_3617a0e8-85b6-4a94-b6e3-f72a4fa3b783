const fs = require('fs');
const path = require('path');

console.log('=== Pre-build optimization script ===');

// 1. Clean old cache files
const cacheDir = path.join(process.cwd(), '.next/cache');
const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

function cleanOldCache(dir) {
  if (!fs.existsSync(dir)) return;
  
  let filesDeleted = 0;
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    try {
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        cleanOldCache(filePath);
      } else if (stat.mtime.getTime() < oneWeekAgo) {
        fs.unlinkSync(filePath);
        filesDeleted++;
      }
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error.message);
    }
  });
  
  if (filesDeleted > 0) {
    console.log(`Cleaned ${filesDeleted} old cache files from ${dir}`);
  }
}

// 2. Create cache directories if they don't exist
const cacheDirs = [
  '.next/cache',
  '.next/cache/webpack',
  '.next/cache/fetch-cache',
  'node_modules/.cache'
];

cacheDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`Created cache directory: ${dir}`);
  }
});

// 3. Clean old cache
console.log('Cleaning old cache files...');
cleanOldCache(cacheDir);

// 4. Log environment info
console.log('\nBuild environment:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
console.log(`NODE_OPTIONS: ${process.env.NODE_OPTIONS || 'not set'}`);
console.log(`Available memory: ${Math.round(require('os').freemem() / 1024 / 1024)}MB`);
console.log(`CPUs: ${require('os').cpus().length}`);

// 5. Check for large dependencies
const nodeModulesPath = path.join(process.cwd(), 'node_modules');
const largeDeps = [];

if (fs.existsSync(nodeModulesPath)) {
  const checkDirSize = (dir) => {
    let size = 0;
    try {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory() && !file.startsWith('.')) {
          size += checkDirSize(filePath);
        } else if (stat.isFile()) {
          size += stat.size;
        }
      });
    } catch (error) {
      // Ignore errors
    }
    return size;
  };

  const deps = fs.readdirSync(nodeModulesPath);
  deps.forEach(dep => {
    if (dep.startsWith('.') || dep.startsWith('@')) return;
    const depPath = path.join(nodeModulesPath, dep);
    try {
      const stat = fs.statSync(depPath);
      if (stat.isDirectory()) {
        const size = checkDirSize(depPath);
        if (size > 50 * 1024 * 1024) { // > 50MB
          largeDeps.push({ name: dep, size: Math.round(size / 1024 / 1024) });
        }
      }
    } catch (error) {
      // Ignore errors
    }
  });

  if (largeDeps.length > 0) {
    console.log('\nLarge dependencies detected:');
    largeDeps.sort((a, b) => b.size - a.size).forEach(dep => {
      console.log(`  ${dep.name}: ${dep.size}MB`);
    });
  }
}

console.log('\n=== Pre-build optimization complete ===\n');