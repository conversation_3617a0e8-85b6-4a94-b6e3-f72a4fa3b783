#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to help set up Auth0 M2M client grant for Management API
 * Run: node scripts/setup-auth0-m2m.js
 */

const axios = require('axios');

async function setupM2MClientGrant() {
  console.log('Auth0 M2M Client Grant Setup');
  console.log('============================\n');

  // Configuration
  const domain = 'headstart.uk.auth0.com';
  const m2mClientId = 'IaswtcaIRSFGbg3adTthWGDrQlCyrZML';
  
  console.log('To set up the M2M client grant, you need to:');
  console.log('\n1. First, get a Management API token with grant:create scope');
  console.log('   - This usually requires using the Auth0 Dashboard');
  console.log('   - Or using an existing M2M app that already has this permission\n');

  console.log('2. Once you have a token, run this curl command:\n');

  const curlCommand = `curl -X POST https://${domain}/api/v2/client-grants \\
  -H "Authorization: Bearer YOUR_MANAGEMENT_API_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "client_id": "${m2mClientId}",
    "audience": "https://${domain}/api/v2/",
    "scope": ["read:users", "delete:users", "read:users_app_metadata", "delete:users_app_metadata"]
  }'`;

  console.log(curlCommand);
  
  console.log('\n3. Or use the Auth0 Dashboard:');
  console.log(`   - Go to: https://manage.auth0.com/dashboard/eu/headstart/apis`);
  console.log('   - Click on "Auth0 Management API"');
  console.log('   - Go to "Machine to Machine Applications" tab');
  console.log(`   - Find app with Client ID: ${m2mClientId}`);
  console.log('   - Toggle it ON');
  console.log('   - Select scopes: read:users, delete:users');
  console.log('   - Click "Update"\n');

  console.log('4. To verify it worked, check existing grants:');
  console.log(`   curl -X GET https://${domain}/api/v2/client-grants?client_id=${m2mClientId} \\`);
  console.log('     -H "Authorization: Bearer YOUR_MANAGEMENT_API_TOKEN"\n');

  // Try to check current status
  console.log('Checking current M2M app status...');
  try {
    const tokenResponse = await axios.post(`https://${domain}/oauth/token`, {
      client_id: m2mClientId,
      client_secret: '****************************************************************',
      audience: `https://${domain}/api/v2/`,
      grant_type: 'client_credentials'
    });
    console.log('✅ M2M app has Management API access!');
  } catch (error) {
    if (error.response?.data?.error === 'access_denied') {
      console.log('❌ M2M app does NOT have Management API access yet');
      console.log('   Please follow the steps above to grant access');
    } else {
      console.log('Error checking status:', error.response?.data || error.message);
    }
  }
}

setupM2MClientGrant().catch(console.error);