#!/bin/bash

# Run the auth flow tests with proper reporting
echo "🚀 Running Authentication Flow Tests..."
echo "This will test: Registration → Logout → Login"
echo ""

# Run the tests
pnpm test:e2e e2e/tests/auth-flow.spec.ts --project=chromium --reporter=list

# Check if tests passed
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ All auth flow tests passed!"
    echo "Check test-results/ for screenshots:"
    echo "  - job-seeker-dashboard-after-register.png"
    echo "  - job-seeker-dashboard-after-login.png"
    echo "  - employer-dashboard-after-register.png"
    echo "  - employer-dashboard-after-login.png"
else
    echo ""
    echo "❌ Some tests failed. Check the test report for details."
    echo "Run 'pnpm test:e2e:ui' to debug interactively."
fi