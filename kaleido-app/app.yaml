name: kaleido-app
region: lon
services:
- name: web
  environment_slug: node-js
  github:
    branch: production
    deploy_on_push: true
    repo: your-repo/kaleido-app
  build_command: |
    echo "Starting optimized build process..."
    # Install pnpm
    npm install -g pnpm@8.15.1
    # Install dependencies with cache
    pnpm install --frozen-lockfile --prefer-offline
    # Run optimized build
    NODE_OPTIONS='--max-old-space-size=8192' NEXT_TELEMETRY_DISABLED=1 pnpm build
    # Clean up unnecessary files
    rm -rf src/ components/ pages/ app/ .git/ node_modules/.cache
    # Prune dev dependencies
    pnpm prune --prod
  run_command: pnpm start
  http_port: 3000
  instance_count: 2
  instance_size_slug: professional-l
  source_dir: /
  envs:
  - key: NODE_ENV
    value: production
  - key: NEXT_TELEMETRY_DISABLED
    value: "1"
  - key: NODE_OPTIONS
    value: "--max-old-space-size=4096"