{"permissions": {"allow": ["<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(pnpm check-types:*)", "Bash(pnpm test:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(for:*)", "Bash(do if [ -f \"$file\" ])", "Bash(then echo \"STILL EXISTS: $file\")", "Bash(else echo \"DELETED: $file\")", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(npm install:*)", "Bash(pnpm add:*)", "<PERSON><PERSON>(curl:*)", "Bash(open http://localhost:3000/job-seeker/fd4549b9-5d97-4bd2-b218-e34bab9c28c8/profile)", "Bash(npm test:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(echo:*)", "Bash(pnpm list:*)", "Bash(pnpm build:*)", "Bash(pnpm tsc:*)"], "deny": []}}