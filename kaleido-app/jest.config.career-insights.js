const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  displayName: 'Career Insights Tests',
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup/jest.setup.js'],
  testEnvironment: 'jsdom',
  
  // Test file patterns - specifically for career insights
  testMatch: [
    '<rootDir>/src/__tests__/career-insights/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/src/__tests__/components/career-insights/**/*.test.{js,jsx,ts,tsx}',
  ],
  
  // Module name mapping for absolute imports
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/stores/(.*)$': '<rootDir>/src/stores/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
  },
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/app/career-insights/**/*.{js,jsx,ts,tsx}',
    'src/components/career-insights/**/*.{js,jsx,ts,tsx}',
    'src/stores/careerInsightsStore.ts',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/*.config.{js,jsx,ts,tsx}',
  ],
  
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage/career-insights',
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  
  // Test timeout
  testTimeout: 10000,
  
  // Clear mocks automatically between tests
  clearMocks: true,
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
  ],
  
  // Module paths to ignore for transforming
  transformIgnorePatterns: [
    '/node_modules/(?!(react-markdown|remark-*|unified|unist-*|vfile|bail|is-*|trough|mdast-*|micromark|decode-named-character-reference|character-entities|property-information|hast-*|space-separated-tokens|comma-separated-tokens|web-namespaces|zwitch|html-void-elements))',
  ],
  
  // Global setup/teardown
  globalSetup: undefined,
  globalTeardown: undefined,
  
  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname',
  ],
  
  // Verbose output
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Mock configuration
  mocks: {
    'next/navigation': '<rootDir>/src/__tests__/setup/__mocks__/next-navigation.js',
    'framer-motion': '<rootDir>/src/__tests__/setup/__mocks__/framer-motion.js',
  },
};

// Create the final Jest config
module.exports = createJestConfig(customJestConfig);