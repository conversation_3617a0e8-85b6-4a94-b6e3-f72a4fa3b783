# Copy this file to .env.test.local and fill in your test credentials

# Test user credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=YourTestPassword123!
TEST_EMPLOYER_EMAIL=<EMAIL>
TEST_JOB_SEEKER_EMAIL=<EMAIL>

# Auth0 configuration (should match your .env.local)
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret

# Playwright specific
PLAYWRIGHT_BASE_URL=http://localhost:3000

# Optional: Slow down tests for debugging (in milliseconds)
# PLAYWRIGHT_SLOW_MO=500