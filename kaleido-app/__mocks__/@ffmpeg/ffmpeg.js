// Mock for @ffmpeg/ffmpeg package for Jest tests
class MockFFmpeg {
  constructor() {
    this.loaded = false;
    this.listeners = {};
  }

  async load(config) {
    this.loaded = true;
    return Promise.resolve();
  }

  async writeFile(name, data) {
    return Promise.resolve();
  }

  async readFile(name) {
    // Return a mock Uint8Array that represents video data
    return Promise.resolve(new Uint8Array([1, 2, 3, 4]));
  }

  async deleteFile(name) {
    return Promise.resolve();
  }

  async exec(args) {
    // Simulate progress events
    if (this.listeners.progress) {
      setTimeout(() => this.listeners.progress({ progress: 0.5 }), 10);
      setTimeout(() => this.listeners.progress({ progress: 1.0 }), 20);
    }
    return Promise.resolve();
  }

  on(event, callback) {
    this.listeners[event] = callback;
  }

  off(event) {
    delete this.listeners[event];
  }

  async terminate() {
    this.loaded = false;
    this.listeners = {};
    return Promise.resolve();
  }
}

module.exports = {
  FFmpeg: MockFFmpeg,
};