// Mock for @ffmpeg/util package for Jest tests

const fetchFile = async (input) => {
  // Mock implementation that returns a Uint8Array representing file data
  if (typeof input === 'string') {
    // Mock URL fetch
    return Promise.resolve(new Uint8Array([1, 2, 3, 4, 5]));
  } else if (input instanceof File || input instanceof Blob) {
    // Mock File/Blob processing
    return Promise.resolve(new Uint8Array([1, 2, 3, 4, 5]));
  }
  return Promise.resolve(new Uint8Array([1, 2, 3, 4, 5]));
};

module.exports = {
  fetchFile,
};