# Next.js Build Optimization Guide

## Current Issues Identified

1. **Large node_modules (2.1GB)** - Heavy dependencies impacting build time
2. **TypeScript strict mode disabled** - Missing type checking optimizations
3. **No build caching configured** - Rebuilding everything from scratch
4. **Single CPU usage** - Not utilizing available cores
5. **Source maps in production** - Slowing down builds
6. **No webpack caching** - Re-processing all modules

## Implemented Optimizations

### 1. Enhanced Build Configuration

Created `next.config.optimized.js` with:
- **SWC minifier** enabled for faster minification
- **Webpack filesystem caching** for incremental builds
- **Multi-core builds** (cpus: 4 instead of 1)
- **Module concatenation** for smaller bundles
- **Optimized chunk splitting** for better caching
- **Deterministic module IDs** for consistent builds

### 2. Custom Cache Handler

Created `cache-handler.js` for:
- File-based caching instead of memory
- Persistent cache between builds
- Automatic cache expiration

### 3. Build Script Optimizations

Update your package.json:

```json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=8192' next build",
    "build:fast": "NEXT_TELEMETRY_DISABLED=1 NODE_OPTIONS='--max-old-space-size=8192' next build --profile",
    "build:analyze": "ANALYZE=true next build",
    "prebuild": "node scripts/prebuild.js",
    "postbuild": "node scripts/postbuild.js"
  }
}
```

### 4. Pre-build Script

Create `scripts/prebuild.js`:

```javascript
const fs = require('fs');
const path = require('path');

// Clean old cache if needed
const cacheDir = path.join(process.cwd(), '.next/cache');
const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

function cleanOldCache(dir) {
  if (!fs.existsSync(dir)) return;
  
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      cleanOldCache(filePath);
    } else if (stat.mtime.getTime() < oneWeekAgo) {
      fs.unlinkSync(filePath);
    }
  });
}

console.log('Cleaning old cache files...');
cleanOldCache(cacheDir);
console.log('Cache cleanup complete');
```

### 5. Docker Optimization

Created `.dockerignore` to exclude unnecessary files from Docker context.

For production builds in Docker:

```dockerfile
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy dependency files
COPY package.json pnpm-lock.yaml ./
RUN corepack enable && pnpm install --frozen-lockfile

FROM node:20-alpine AS builder
WORKDIR /app

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Use build cache mount
RUN --mount=type=cache,target=/app/.next/cache \
    NODE_OPTIONS='--max-old-space-size=8192' \
    pnpm build

FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

## Additional Recommendations

### 1. Dependency Optimization

Large dependencies to consider replacing/optimizing:
- `aws-sdk` (use @aws-sdk/client-* for specific services)
- `puppeteer` (consider puppeteer-core with external Chrome)
- Multiple UI libraries (@mui, @material-tailwind, etc.)

### 2. Environment-Specific Builds

```bash
# Development (faster builds, no optimization)
NODE_ENV=development next build

# Production (optimized, but slower)
NODE_ENV=production next build
```

### 3. CI/CD Optimization

For your CI/CD pipeline:

```yaml
# Example GitHub Actions
- name: Cache Next.js build
  uses: actions/cache@v3
  with:
    path: |
      .next/cache
      node_modules/.cache
    key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
    restore-keys: |
      ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-
```

### 4. Server Configuration

For your Digital Ocean server:

```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=8192"

# Use PM2 with cluster mode
pm2 start ecosystem.config.js --env production
```

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'kaleido-app',
    script: 'node_modules/.bin/next',
    args: 'start',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    max_memory_restart: '2G',
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true
  }]
};
```

### 5. Monitoring Build Performance

Add build analysis:

```bash
# Install bundle analyzer
pnpm add -D @next/bundle-analyzer

# Update next.config.js
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer(nextConfig);

# Run analysis
ANALYZE=true pnpm build
```

## Expected Improvements

With these optimizations, you should see:
- **50-70% reduction** in build time on subsequent builds
- **Better memory usage** during builds
- **Improved caching** between deployments
- **Faster cold starts** in production

## Migration Steps

1. Back up your current `next.config.js`
2. Copy `next.config.optimized.js` to `next.config.js`
3. Test the build locally: `pnpm build`
4. Monitor build times and memory usage
5. Deploy to staging first
6. Roll out to production

## Troubleshooting

If builds fail:
1. Clear all caches: `rm -rf .next node_modules/.cache`
2. Reinstall dependencies: `pnpm install`
3. Disable optimizations one by one to identify issues
4. Check server resources (CPU, memory, disk space)