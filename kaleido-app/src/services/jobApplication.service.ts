import apiHelper from '@/lib/apiHelper';
import { CandidateStatus } from '@/types/candidate.types';

interface JobApplicationStatus {
  jobId: string;
  status: CandidateStatus;
  lastUpdated: string;
  statusTimeline?: Array<{
    status: CandidateStatus;
    timestamp: string;
    note?: string;
  }>;
  withdrawn?: boolean;
  activityHistory?: Array<{
    id: string;
    type: string;
    metadata: Record<string, any>;
    timestamp: string;
    description: string;
    performedBy?: string;
  }>;
}

/**
 * Fetches the application status for a specific job
 * @param jobId The ID of the job to get the application status for
 * @returns The job application status including status timeline
 */
export const getJobApplicationStatus = async (jobId: string): Promise<JobApplicationStatus> => {
  try {
    const response = await apiHelper.get(`/jobs/${jobId}/application-status`);
    return response;
  } catch (error) {
    console.error('Error fetching job application status:', error);
    // Return a default status if the API call fails
    return {
      jobId,
      status: CandidateStatus.APPLIED, // Default to APPLIED status
      lastUpdated: new Date().toISOString(),
      statusTimeline: [],
    };
  }
};
