import { getJobApplicationStatus } from '../jobApplication.service';
import { CandidateStatus } from '../../types/candidate.types';
import apiHelper from '../../lib/apiHelper';

// Mock the apiHelper
jest.mock('../../lib/apiHelper');

const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;

describe('jobApplication.service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getJobApplicationStatus', () => {
    test('returns correct API response with proper CandidateStatus types', async () => {
      const mockResponse = {
        jobId: 'test-job-id',
        status: 'OFFER_EXTENDED',
        lastUpdated: '2025-07-28T15:18:10.337Z',
        statusTimeline: [
          {
            status: 'MATCHED',
            timestamp: '2025-07-28T04:22:56.978Z',
            note: 'Initial status',
          },
          {
            status: 'OFFER_EXTENDED',
            timestamp: '2025-07-28T04:23:57.144Z',
            note: 'Offer extended',
          },
        ],
        activityHistory: [],
      };

      mockApiHelper.get.mockResolvedValue(mockResponse);

      const result = await getJobApplicationStatus('test-job-id');

      expect(mockApiHelper.get).toHaveBeenCalledWith('/jobs/test-job-id/application-status');
      expect(result).toEqual(mockResponse);

      // Verify the response can be typed correctly
      expect(result.status).toBe('OFFER_EXTENDED');
      expect(result.statusTimeline).toHaveLength(2);
      expect(result.statusTimeline![0].status).toBe('MATCHED');
      expect(result.statusTimeline![1].status).toBe('OFFER_EXTENDED');
    });

    test('includes all expected CandidateStatus values in interface', async () => {
      // Test that our service interface supports all the status values we fixed
      const statusValues = [
        'APPLIED',
        'MATCHED',
        'SHORTLISTED',
        'INTERVIEWING',
        'OFFER_PENDING_APPROVAL',
        'OFFER_APPROVED', // This was missing before our fix
        'OFFER_EXTENDED',
        'OFFER_ACCEPTED',
        'HIRED',
      ];

      for (const status of statusValues) {
        const mockResponse = {
          jobId: 'test-job-id',
          status,
          lastUpdated: '2025-07-28T15:18:10.337Z',
          statusTimeline: [
            {
              status,
              timestamp: '2025-07-28T04:22:56.978Z',
              note: `Status: ${status}`,
            },
          ],
          activityHistory: [],
        };

        mockApiHelper.get.mockResolvedValue(mockResponse);

        const result = await getJobApplicationStatus('test-job-id');

        expect(result.status).toBe(status);
        expect(result.statusTimeline![0].status).toBe(status);
      }
    });

    test('handles API errors gracefully', async () => {
      const apiError = new Error('API Error');
      mockApiHelper.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await getJobApplicationStatus('test-job-id');

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching job application status:', apiError);

      // Should return default status on error
      expect(result).toEqual({
        jobId: 'test-job-id',
        status: CandidateStatus.APPLIED,
        lastUpdated: expect.any(String),
        statusTimeline: [],
      });

      consoleSpy.mockRestore();
    });

    test('handles missing statusTimeline gracefully', async () => {
      const mockResponse = {
        jobId: 'test-job-id',
        status: 'OFFER_EXTENDED',
        lastUpdated: '2025-07-28T15:18:10.337Z',
        // No statusTimeline
        activityHistory: [],
      };

      mockApiHelper.get.mockResolvedValue(mockResponse);

      const result = await getJobApplicationStatus('test-job-id');

      expect(result.status).toBe('OFFER_EXTENDED');
      expect(result.statusTimeline).toBeUndefined();
    });

    test('handles missing activityHistory gracefully', async () => {
      const mockResponse = {
        jobId: 'test-job-id',
        status: 'OFFER_EXTENDED',
        lastUpdated: '2025-07-28T15:18:10.337Z',
        statusTimeline: [],
        // No activityHistory
      };

      mockApiHelper.get.mockResolvedValue(mockResponse);

      const result = await getJobApplicationStatus('test-job-id');

      expect(result.status).toBe('OFFER_EXTENDED');
      expect(result.activityHistory).toBeUndefined();
    });

    test('preserves withdrawn status correctly', async () => {
      const mockResponse = {
        jobId: 'test-job-id',
        status: 'WITHDRAWN',
        lastUpdated: '2025-07-28T15:18:10.337Z',
        statusTimeline: [
          {
            status: 'APPLIED',
            timestamp: '2025-07-28T04:22:56.978Z',
            note: 'Application submitted',
          },
          {
            status: 'WITHDRAWN',
            timestamp: '2025-07-28T04:23:57.144Z',
            note: 'Application withdrawn',
          },
        ],
        withdrawn: true,
        activityHistory: [],
      };

      mockApiHelper.get.mockResolvedValue(mockResponse);

      const result = await getJobApplicationStatus('test-job-id');

      expect(result.status).toBe('WITHDRAWN');
      expect(result.withdrawn).toBe(true);
      expect(result.statusTimeline).toHaveLength(2);
    });

    test('supports complete offer flow progression', async () => {
      // Test the complete offer flow that was problematic before our fix
      const offerFlowStatuses = [
        'INTERVIEWING',
        'OFFER_PENDING_APPROVAL',
        'OFFER_APPROVED', // This status was missing from the incomplete enum
        'OFFER_EXTENDED',
        'OFFER_ACCEPTED',
        'HIRED',
      ];

      const mockResponse = {
        jobId: 'test-job-id',
        status: 'OFFER_EXTENDED',
        lastUpdated: '2025-07-28T15:18:10.337Z',
        statusTimeline: offerFlowStatuses.map((status, index) => ({
          status,
          timestamp: `2025-07-28T04:2${3 + index}:00.000Z`,
          note: `Status changed to ${status}`,
        })),
        activityHistory: [],
      };

      mockApiHelper.get.mockResolvedValue(mockResponse);

      const result = await getJobApplicationStatus('test-job-id');

      expect(result.status).toBe('OFFER_EXTENDED');
      expect(result.statusTimeline).toHaveLength(6);

      // Verify all statuses are preserved correctly
      result.statusTimeline!.forEach((item, index) => {
        expect(item.status).toBe(offerFlowStatuses[index]);
      });
    });
  });

  describe('Type Safety', () => {
    test('service interface enforces correct CandidateStatus type', () => {
      // This test verifies that our import fix ensures proper typing
      // by testing compilation - if the wrong enum was imported,
      // these assignments would cause TypeScript errors

      const validStatuses: string[] = [
        CandidateStatus.APPLIED,
        CandidateStatus.MATCHED,
        CandidateStatus.SHORTLISTED,
        CandidateStatus.INTERVIEWING,
        CandidateStatus.OFFER_PENDING_APPROVAL,
        CandidateStatus.OFFER_APPROVED, // This should be available after our fix
        CandidateStatus.OFFER_EXTENDED,
        CandidateStatus.OFFER_ACCEPTED,
        CandidateStatus.HIRED,
        CandidateStatus.REJECTED,
        CandidateStatus.WITHDRAWN,
        CandidateStatus.CULTURAL_FIT_ANSWERED,
      ];

      // If import was wrong, OFFER_APPROVED and others wouldn't be available
      expect(validStatuses).toContain('OFFER_APPROVED');
      expect(validStatuses).toContain('OFFER_EXTENDED');
      expect(validStatuses).toContain('CULTURAL_FIT_ANSWERED');
    });

    test('CandidateStatus enum has all expected values', () => {
      // Verify that the complete enum is accessible (proving correct import)
      const expectedStatuses = [
        'NEW',
        'APPLIED',
        'MATCHED',
        'CONTACTED',
        'INTERESTED',
        'NOT_INTERESTED',
        'INTERVIEWING',
        'OFFER_PENDING_APPROVAL',
        'OFFER_APPROVED',
        'OFFER_REJECTED',
        'OFFER_EXTENDED',
        'OFFER_ACCEPTED',
        'OFFER_DECLINED',
        'HIRE_PENDING_APPROVAL',
        'HIRE_APPROVED',
        'HIRED',
        'REJECTED',
        'WITHDRAWN',
        'CULTURAL_FIT_ANSWERED',
        'SHORTLISTED',
      ];

      expectedStatuses.forEach(status => {
        expect(Object.values(CandidateStatus)).toContain(status);
      });

      // Specifically verify the statuses that were missing from incomplete enum
      expect(CandidateStatus.OFFER_APPROVED).toBe('OFFER_APPROVED');
      expect(CandidateStatus.OFFER_EXTENDED).toBe('OFFER_EXTENDED');
      expect(CandidateStatus.CULTURAL_FIT_ANSWERED).toBe('CULTURAL_FIT_ANSWERED');
    });
  });
});
