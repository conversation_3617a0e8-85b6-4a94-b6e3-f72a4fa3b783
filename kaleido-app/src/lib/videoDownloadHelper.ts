import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';
import { VideoDownloadJob } from '@/stores/videoDownloadJobsStore';
import { getVideoConverter } from './videoConverter';

export interface DownloadOptions {
  platform: string;
  label: string;
  videoJDId: string;
  videoUrl: string;
  quality?: string;
  filename?: string;
}

export interface DownloadCallbacks {
  addDownloadJob: (job: Omit<VideoDownloadJob, 'createdAt' | 'progress'>) => void;
  setDownloadingState?: (platform: string | null) => void;
  updateJobProgress?: (jobId: string, progress: number) => void;
}

/**
 * Check if video URL is a WebM format that needs conversion
 */
const isWebMVideo = (videoUrl: string): boolean => {
  return videoUrl.toLowerCase().includes('.webm') || videoUrl.toLowerCase().includes('webm');
};

/**
 * Generate a unique job ID for client-side conversion
 */
const generateJobId = (): string => {
  return `client-conversion-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

/**
 * Download converted video blob
 */
const downloadBlob = (blob: Blob, filename: string): void => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Shared video download logic with client-side conversion support
 * For completed videos, uses client-side FFmpeg conversion with progress tracking
 * Falls back to backend API for videos that need server-side processing
 */
export const handleVideoDownload = async (
  options: DownloadOptions,
  callbacks: DownloadCallbacks
): Promise<void> => {
  const { platform, label, videoJDId, videoUrl, quality = 'high', filename } = options;
  const { addDownloadJob, setDownloadingState, updateJobProgress } = callbacks;

  // For now, skip client-side conversion due to CORS issues and go straight to blob download
  // TODO: Re-enable when CORS issues are resolved
  if (videoUrl && isWebMVideo(videoUrl)) {
    // Skip conversion, go straight to blob download
    return handleDirectBlobDownload(options, callbacks);
  }

  // Fallback to existing backend API approach
  return handleBackendConversion(options, callbacks);
};

/**
 * Handle direct blob download without conversion (for CORS-restricted videos)
 */
const handleDirectBlobDownload = async (
  options: DownloadOptions,
  callbacks: DownloadCallbacks
): Promise<void> => {
  const { platform, label, videoUrl } = options;
  const { addDownloadJob, setDownloadingState, updateJobProgress } = callbacks;

  const jobId = generateJobId();
  const filename = `video-${platform.toLowerCase()}.webm`;

  if (setDownloadingState) {
    setDownloadingState(platform);
  }

  // Add job to track progress
  addDownloadJob({
    id: jobId,
    status: 'active',
    platform: platform.toLowerCase(),
    result: { filename },
  });

  try {
    updateJobProgress?.(jobId, 10);

    showToast({
      message: `Downloading your video for ${label}...`,
      isSuccess: true,
    });

    updateJobProgress?.(jobId, 30);

    // Fetch the video as blob
    const response = await fetch(videoUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch video');
    }

    updateJobProgress?.(jobId, 70);

    const blob = await response.blob();
    updateJobProgress?.(jobId, 90);

    // Download the blob
    downloadBlob(blob, filename);
    updateJobProgress?.(jobId, 100);

    showToast({
      message: `Your video for ${label} is downloading (original format).`,
      isSuccess: true,
    });
  } catch (error) {
    console.error('Direct blob download failed:', error);
    showToast({
      message: 'Failed to download video. Please try again.',
      isSuccess: false,
    });
  } finally {
    if (setDownloadingState) {
      setDownloadingState(null);
    }
  }
};

/**
 * Handle client-side video conversion with progress tracking
 */
const handleClientSideConversion = async (
  options: DownloadOptions,
  callbacks: DownloadCallbacks
): Promise<void> => {
  const { platform, label, videoUrl, quality = 'high' } = options;
  const { addDownloadJob, setDownloadingState, updateJobProgress } = callbacks;

  const jobId = generateJobId();
  const filename = `video-${platform.toLowerCase()}.mp4`;

  if (setDownloadingState) {
    setDownloadingState(platform);
  }

  // Add job to track progress
  addDownloadJob({
    id: jobId,
    status: 'active',
    platform: platform.toLowerCase(),
    result: { filename },
  });

  try {
    showToast({
      message: `Converting your video for ${label}...`,
      isSuccess: true,
    });

    const converter = getVideoConverter();

    // Convert video with progress tracking
    const convertedBlob = await converter.convertForPlatform(videoUrl, {
      platform: platform.toLowerCase(),
      quality: quality as 'high' | 'medium' | 'low',
      onProgress: progress => {
        updateJobProgress?.(jobId, progress);
      },
    });

    // Download the converted video
    downloadBlob(convertedBlob, filename);

    // Update job to completed
    if (updateJobProgress) {
      updateJobProgress(jobId, 100);
    }

    showToast({
      message: `Your video optimized for ${label} is downloading.`,
      isSuccess: true,
    });
  } catch (error) {
    console.error('Client-side conversion failed:', error);

    // Fall back to direct download of original video
    try {
      // Fetch the video as blob to force download (handles CORS issues)
      const response = await fetch(videoUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch video');
      }

      const blob = await response.blob();
      downloadBlob(blob, `video-${platform.toLowerCase()}.webm`);

      showToast({
        message: `Conversion failed. Your video is downloading (original format).`,
        isSuccess: true,
      });
    } catch (fallbackError) {
      console.error('Fallback download failed:', fallbackError);
      showToast({
        message: 'Failed to download video. The video URL may have CORS restrictions.',
        isSuccess: false,
      });
    }
  } finally {
    if (setDownloadingState) {
      setDownloadingState(null);
    }
  }
};

/**
 * Handle backend video conversion (existing logic)
 */
const handleBackendConversion = async (
  options: DownloadOptions,
  callbacks: DownloadCallbacks
): Promise<void> => {
  const { platform, label, videoJDId, videoUrl, quality = 'high', filename } = options;
  const { addDownloadJob, setDownloadingState } = callbacks;

  if (setDownloadingState) {
    setDownloadingState(platform);
  }

  try {
    // Request video processing/download using the backend API
    const response = await apiHelper.post(`/video-jd/${videoJDId}/download-platform`, {
      platform: platform.toLowerCase(),
      quality,
      filename: filename || `video-${platform.toLowerCase()}`,
      videoUrl,
    });

    if (response.processing && response.jobId) {
      // Video needs processing - add job to status manager
      addDownloadJob({
        id: response.jobId,
        status: 'queued',
        platform: platform.toLowerCase(),
        result: {
          filename: response.filename || `video-${platform.toLowerCase()}.mp4`,
        },
      });

      showToast({
        message: `Converting your video for ${label}. This may take a moment...`,
        isSuccess: true,
      });

      if (setDownloadingState) {
        setDownloadingState(null);
      }
    } else if (response.downloadUrl) {
      // Video is ready for immediate download
      downloadBlob(
        await fetch(response.downloadUrl).then(r => r.blob()),
        response.filename || `video-${platform.toLowerCase()}.mp4`
      );

      showToast({
        message: `Your video optimized for ${label} is downloading.`,
        isSuccess: true,
      });

      if (setDownloadingState) {
        setDownloadingState(null);
      }
    } else {
      throw new Error('No download URL or processing job returned');
    }
  } catch (error) {
    console.error('Backend conversion failed:', error);

    if (setDownloadingState) {
      setDownloadingState(null);
    }

    // Fallback to direct download of original video
    if (videoUrl) {
      try {
        // Fetch the video as blob to force download (handles CORS issues)
        const response = await fetch(videoUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch video');
        }

        const blob = await response.blob();
        downloadBlob(blob, `video-${platform.toLowerCase()}.webm`);

        showToast({
          message: `Your video is downloading (original format).`,
          isSuccess: true,
        });
      } catch (fallbackError) {
        console.error('Fallback download failed:', fallbackError);
        showToast({
          message: 'Failed to download video. The video URL may have CORS restrictions.',
          isSuccess: false,
        });
      }
    } else {
      showToast({
        message: 'Failed to download video. Please try again.',
        isSuccess: false,
      });
    }
  }
};

/**
 * Helper function to get active download job for a platform
 */
export const getActiveDownloadJob = (
  downloadJobs: Record<string, VideoDownloadJob>,
  platform: string
): VideoDownloadJob | undefined => {
  return Object.values(downloadJobs).find(
    (job: VideoDownloadJob) =>
      job.platform === platform && (job.status === 'queued' || job.status === 'active')
  );
};

/**
 * Platform configurations that map to backend platform specifications
 */
export const PLATFORM_CONFIGS = {
  stories: {
    id: 'stories',
    label: 'Stories',
    description: 'Instagram Stories, TikTok, Reels',
    aspectRatio: '9:16',
    dimensions: '1080x1920',
  },
  linkedin: {
    id: 'linkedin',
    label: 'LinkedIn',
    description: 'LinkedIn posts and videos',
    aspectRatio: '16:9',
    dimensions: '1920x1080',
  },
  instagram: {
    id: 'instagram',
    label: 'Instagram',
    description: 'Instagram posts, Facebook',
    aspectRatio: '1:1',
    dimensions: '1080x1080',
  },
  facebook: {
    id: 'facebook',
    label: 'Facebook',
    description: 'Facebook posts and videos',
    aspectRatio: '16:9',
    dimensions: '1920x1080',
  },
} as const;

export type PlatformId = keyof typeof PLATFORM_CONFIGS;
