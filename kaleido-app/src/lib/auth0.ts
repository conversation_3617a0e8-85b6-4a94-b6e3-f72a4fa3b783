import { initAuth0 } from '@auth0/nextjs-auth0';

if (!process.env.AUTH0_SECRET) {
  throw new Error('AUTH0_SECRET is not set. Please generate one using: openssl rand -hex 32');
}

if (!process.env.AUTH0_BASE_URL) {
  throw new Error('AUTH0_BASE_URL is not set. Please generate one using: openssl rand -hex 32');
}

if (!process.env.AUTH0_ISSUER_BASE_URL) {
  throw new Error('AUTH0_ISSUER_BASE_URL is not set');
}

if (!process.env.AUTH0_CLIENT_ID) {
  throw new Error('AUTH0_CLIENT_ID is not set');
}

if (!process.env.AUTH0_CLIENT_SECRET) {
  throw new Error('AUTH0_CLIENT_SECRET is not set');
}

export const auth0 = initAuth0({
  secret: process.env.AUTH0_SECRET,
  issuerBaseURL: process.env.AUTH0_ISSUER_BASE_URL,
  baseURL: process.env.AUTH0_BASE_URL,
  clientID: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET,
  routes: {
    callback: '/api/auth/callback',
    login: '/api/auth/login',
    postLogoutRedirect: process.env.AUTH0_BASE_URL || 'http://localhost:3000',
  },
  authorizationParams: {
    response_type: 'code',
    scope:
      process.env.AUTH0_SCOPE ||
      'openid profile email offline_access r_emailaddress r_liteprofile r_basicprofile',
    audience: process.env.AUTH0_AUDIENCE,
  },
  session: {
    absoluteDuration: 30 * 24 * 60 * 60, // 30 days in seconds (1 month)
    rolling: true,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      httpOnly: true,
    },
  },
});
