/**
 * Suppress expected Auth0 errors during logout
 * The Auth0 SDK makes a request to /api/auth/me after logout which returns 401
 * This is expected behavior but creates noise in the console
 */
export function suppressAuth0LogoutErrors() {
  if (typeof window === 'undefined') return;

  // Store original console.error
  const originalConsoleError = console.error;

  // Override console.error to filter out expected Auth0 logout errors
  console.error = (...args) => {
    // Check if this is the expected 401 error during logout
    const errorString = args.join(' ');

    // Skip logging if:
    // 1. It's a 401 error on /api/auth/me
    // 2. The window is navigating (likely during logout)
    if (
      errorString.includes('401') &&
      errorString.includes('/api/auth/me') &&
      (window.location.pathname === '/' || document.hidden)
    ) {
      return; // Suppress this error
    }

    // Log all other errors normally
    originalConsoleError.apply(console, args);
  };

  // Also intercept fetch errors for /api/auth/me during logout
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);

      // Check if this is a requiresLogout response
      if (
        response.status === 401 &&
        typeof args[0] === 'string' &&
        args[0].includes('/api/auth/me')
      ) {
        // Clone response to read body without consuming it
        const clonedResponse = response.clone();
        try {
          const data = await clonedResponse.json();
          if (data.requiresLogout) {
            // Don't suppress - let it handle the logout
            return response;
          }
        } catch {
          // If we can't parse the response, continue with normal flow
        }

        // If it's a 401 on /api/auth/me and we're likely logging out, don't throw
        if (
          window.location.pathname === '/' ||
          sessionStorage.getItem('auth_session_ready') !== 'true'
        ) {
          // Return a mock response instead of throwing
          return new Response(JSON.stringify({ user: null }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          });
        }
      }

      return response;
    } catch (error) {
      throw error;
    }
  };
}
