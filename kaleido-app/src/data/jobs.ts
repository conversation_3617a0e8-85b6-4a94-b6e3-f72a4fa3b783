'use client';

import Chance from 'chance';

import { JOB_TYPES } from '@/constants/jobOptions';
import { ExperienceLevel, JobStatus } from '@/entities';
import { IJob } from '@/entities/interfaces';

const chance = new Chance();

const noLogoGroup = ['Halo Sports', 'Mantra Inc', 'Incognito', 'Nexus'];

const companies = [
  'Google',
  'Microsoft',
  'Facebook',
  'Twitter',
  'Amazon',
  'Apple',
  'Netflix',
  'Airbnb',
  'Spotify',
  'Slack',
  'Salesforce',
  'Uber',
  'Lyft',
  'Reddit',
  'Reeds',
].concat(noLogoGroup);

const jobTitles = [
  'Engineering Manager',
  'Software Engineer',
  'Senior Developer',
  'Web Developer',
  'QA Tester',
  'Product Designer',
  'Data Analyst',
  'Project Manager',
  'DevOps Engineer',
  'UI/UX Designer',
];

const companyValues = [
  'Innovation',
  'Integrity',
  'Teamwork',
  'Customer Focus',
  'Excellence',
  'Sustainability',
  'Diversity',
  'Accountability',
  'Passion',
  'Respect',
];

const culturalFits = [
  'Fast-paced',
  'Collaborative',
  'Flexible',
  'Results-driven',
  'Creative',
  'Entrepreneurial',
  'Inclusive',
  'Data-driven',
  'Customer-centric',
  'Innovative',
];

const educationLevels = [
  "Bachelor's Degree",
  "Master's Degree",
  'PhD',
  'High School Diploma',
  "Associate's Degree",
  'Certification',
  'Vocational Training',
];

const languages = [
  'English',
  'Spanish',
  'Mandarin',
  'French',
  'German',
  'Arabic',
  'Hindi',
  'Japanese',
  'Portuguese',
  'Russian',
];

const softSkills = [
  'Communication',
  'Problem-solving',
  'Adaptability',
  'Time management',
  'Teamwork',
  'Leadership',
  'Creativity',
  'Critical thinking',
  'Emotional intelligence',
];

const benefits = [
  'Health insurance',
  '401(k) matching',
  'Flexible work hours',
  'Remote work options',
  'Professional development',
  'Paid time off',
  'Parental leave',
  'Gym membership',
  'Stock options',
  'Company-sponsored events',
];

const careerGrowthOpportunities = [
  'Mentorship programs',
  'Leadership training',
  'Cross-functional projects',
  'International assignments',
  'Continuous learning initiatives',
  'Career pathing',
  'Skill development workshops',
  'Tuition reimbursement',
  'Internal promotions',
];

const locations = [
  'Remote',
  'New York, NY',
  'San Francisco, CA',
  'Austin, TX',
  'London, UK',
  'Dubai, UAE',
  'Tokyo, Japan',
  'Sydney, Australia',
  'Berlin, Germany',
  'Paris, France',
  'Mumbai, India',
  'Toronto, Canada',
];

const companyDescriptions = [
  "We are a leading technology company focused on innovation and user experience. Our mission is to create products that make people's lives easier and more connected.",
  'As a global leader in healthcare solutions, we strive to improve patient outcomes through cutting-edge research and development of medical technologies.',
  'Our financial services firm is dedicated to helping individuals and businesses achieve their financial goals through personalized advice and innovative solutions.',
  'We are an educational technology company committed to making learning accessible and engaging for students of all ages through interactive digital platforms.',
  'As a pioneering manufacturing company, we combine traditional craftsmanship with modern technology to produce high-quality goods for a global market.',
  'Our retail business is built on the principle of providing exceptional customer service and a wide range of products to meet diverse consumer needs.',
];

const jobResponsibilities = [
  [
    'Develop and maintain software applications',
    'Collaborate with cross-functional teams to define and implement new features',
    'Participate in code reviews and contribute to best practices',
    'Troubleshoot and debug complex software issues',
    'Stay up-to-date with emerging technologies and industry trends',
  ],
  [
    'Analyze user requirements and translate them into technical specifications',
    'Design and implement database structures',
    'Ensure software quality through thorough testing and validation',
    'Optimize application performance and scalability',
    'Provide technical support and documentation for software products',
  ],
  [
    'Lead and mentor a team of software developers',
    'Manage project timelines and deliverables',
    'Coordinate with stakeholders to gather and prioritize product requirements',
    'Implement agile methodologies to improve development processes',
    'Conduct regular performance reviews and provide feedback to team members',
  ],
  [
    'Create and maintain user interface designs',
    'Conduct user research and usability testing',
    'Collaborate with product managers to define product vision and strategy',
    'Develop wireframes, prototypes, and high-fidelity mockups',
    'Ensure consistency in design across all product features',
  ],
  [
    'Analyze large datasets to extract meaningful insights',
    'Develop and maintain data pipelines and ETL processes',
    'Create visualizations and dashboards to communicate data insights',
    'Collaborate with business teams to identify areas for data-driven improvements',
    'Implement machine learning models to solve business problems',
  ],
];

const requiredSkills = [
  [
    'Proficiency in one or more programming languages (e.g., Java, Python, C++)',
    'Experience with web development frameworks (e.g., React, Angular, Vue.js)',
    'Strong understanding of database systems and SQL',
    'Familiarity with cloud platforms (e.g., AWS, Azure, Google Cloud)',
    'Knowledge of software design patterns and architecture principles',
  ],
  [
    'Experience with agile development methodologies',
    'Strong problem-solving and analytical skills',
    'Excellent communication and teamwork abilities',
    'Familiarity with version control systems (e.g., Git)',
    'Understanding of software testing and quality assurance practices',
  ],
  [
    'Proficiency in data structures and algorithms',
    'Experience with DevOps practices and tools',
    'Knowledge of cybersecurity principles and best practices',
    'Familiarity with containerization technologies (e.g., Docker, Kubernetes)',
    'Understanding of RESTful API design and implementation',
  ],
  [
    'Experience with mobile app development (iOS and/or Android)',
    'Proficiency in UI/UX design principles and tools',
    'Knowledge of responsive web design and cross-browser compatibility',
    'Familiarity with server-side technologies and databases',
    'Understanding of web accessibility standards and best practices',
  ],
  [
    'Strong knowledge of data analysis and statistical methods',
    'Proficiency in data visualization tools (e.g., Tableau, Power BI)',
    'Experience with big data technologies (e.g., Hadoop, Spark)',
    'Knowledge of machine learning algorithms and frameworks',
    'Familiarity with data warehousing concepts and ETL processes',
  ],
];

export function generateJob(): IJob {
  const company: string = chance.pickone(companies);
  const jobType = chance.pickone(jobTitles);
  const industry = chance.pickone(JOB_TYPES);

  return {
    id: chance.guid(),
    title: jobType,
    jobTitle: jobType,
    matchedCandidatesCount: 0,
    candidatesCount: 0,
    totalCandidates: 0,
    jobType,
    companyName: company,
    companyWebsite: '',
    department: industry,
    experienceLevel: chance.pickone([
      ExperienceLevel.JUNIOR,
      ExperienceLevel.MID,
      ExperienceLevel.SENIOR,
      ExperienceLevel.LEAD,
    ]),
    isGraduateRole: chance.bool({ likelihood: 20 }), // 20% chance of being a graduate role
    status: JobStatus.NEW,
    companyDescription: chance.pickone(companyDescriptions),

    skills: chance.pickone(requiredSkills).flat(),
    experience: chance.pickone([
      'Entry-level',
      '1-3 years',
      '3-5 years',
      '5-10 years',
      '10+ years',
    ]),
    jobResponsibilities: chance.pickone(jobResponsibilities),
    hiringManagerDescription: chance.sentence(),
    companyValues: chance.pickset(companyValues, chance.integer({ min: 3, max: 5 })),
    culturalFit: chance.pickset(culturalFits, chance.integer({ min: 3, max: 5 })),
    education: chance.pickset(educationLevels, chance.integer({ min: 1, max: 3 })),
    language: chance.pickset(languages, chance.integer({ min: 1, max: 3 })),
    softSkills: chance.pickset(softSkills, chance.integer({ min: 3, max: 6 })),
    location: [chance.pickone(locations)],

    salaryRange: `${chance.integer({
      min: 50,
      max: 200,
    })}k - ${chance.integer({ min: 201, max: 300 })}k`,
    benefits: chance.pickset(benefits, chance.integer({ min: 3, max: 6 })),
    careerGrowth: chance.pickset(careerGrowthOpportunities, chance.integer({ min: 2, max: 4 })),
    cultureFitDescription: chance.pickone([
      'We value collaboration and teamwork.',
      'Our culture promotes continuous learning and growth.',
      'We believe in work-life balance and flexibility.',
      'Innovation and creativity are at the core of our values.',
      'We foster an inclusive and diverse work environment.',
    ]),
    finalDraft: '',
    topCandidateThreshold: chance.integer({ min: 70, max: 90 }),
    secondTierCandidateThreshold: chance.integer({ min: 50, max: 69 }),

    requirements: chance.pickset(requiredSkills, chance.integer({ min: 3, max: 5 })).flat(),
    candidates: [],
  };
}
