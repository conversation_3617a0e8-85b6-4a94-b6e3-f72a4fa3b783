'use client';

import { Suspense, useCallback, useEffect, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import DraftModal from '@/components/JobDescriptionCreation/DraftModal';
import { useCompanyData } from '@/hooks/useCompanyData';
import { useCurrentUser } from '@/services/user.service';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useUser } from '@auth0/nextjs-auth0/client';

import { steps } from './constants';
import { JobsProviderProps } from './types';

// Create a separate component that uses useSearchParams
function JobsProviderInner({ children, navigationConfig }: JobsProviderProps) {
  const { user, isLoading: userLoading } = useUser();
  const searchParams = useSearchParams();
  const router = useRouter();
  const currentUser = useCurrentUser();
  const [showDraftModal, setShowDraftModal] = useState(false);
  const [draftData, setDraftData] = useState<{
    step: number;
    stepName: string;
    timestamp: string;
    jobTitle?: string;
  }>({
    step: 0,
    stepName: '',
    timestamp: '',
  });

  // Access the jobs store
  const {
    setIsHydrated,
    setUserRole,
    setCompany,
    userRole,
    isHydrated,
    job,
    activeStep,
    setActiveStep,
    clearJobData,
    updateURLAndStorage,
    setFullJobDescription,
  } = useJobsStore();

  // Use the centralized company data hook
  const { company } = useCompanyData({
    skipInitialFetch: !isHydrated || !currentUser?.sub || userRole !== 'employer',
    onlyForEmployers: true,
  });

  // Check for existing draft in localStorage
  const checkForDraft = useCallback(() => {
    if (typeof window === 'undefined') return null;

    try {
      const savedJob = localStorage.getItem('job');
      const savedStep = localStorage.getItem('activeStep');

      if (savedJob && savedStep) {
        const parsedJob = JSON.parse(savedJob);
        const stepIndex = parseInt(savedStep, 10);

        // Check if there's meaningful data beyond step 1 (Basic Job Information)
        // Only show draft modal if user has progressed to step 2+ or has content from step 2+
        const hasStep2PlusContent = Boolean(
          // Step 1 content (Skills & Responsibilities)
          (parsedJob.skills && parsedJob.skills.length > 0) ||
            (parsedJob.jobResponsibilities && parsedJob.jobResponsibilities.length > 0) ||
            // Step 2+ content (Candidate Qualifications and beyond)
            parsedJob.experience ||
            parsedJob.workLocation ||
            parsedJob.benefits ||
            parsedJob.hiringManagerName ||
            parsedJob.hiringManagerDescription
        );

        // Also check if user has progressed to step 2+ (index 1+)
        const hasProgressedBeyondBasics = stepIndex >= 1;

        const hasMeaningfulData = hasStep2PlusContent || hasProgressedBeyondBasics;

        // Only return the draft if it has meaningful data
        if (hasMeaningfulData) {
          // Consider any step as a valid draft (including step 0)
          const draft = {
            job: parsedJob,
            step: stepIndex,
            stepName: steps[stepIndex]?.name || 'Unknown Step',
            timestamp: parsedJob.updatedAt || new Date().toISOString(),
            jobTitle: parsedJob.title || 'Untitled Job',
          };
          return draft;
        } else {
          // Clear the localStorage since there's no meaningful data
          localStorage.removeItem('job');
          localStorage.removeItem('step');
          localStorage.removeItem('activeStep');
          return null;
        }
      }
      return null;
    } catch (error) {
      console.error('Error checking for draft:', error);
      return null;
    }
  }, []);

  // Handle continuing a draft
  const handleContinueDraft = useCallback(() => {
    try {
      // Use the new loadDraftData method from jobsStore
      const { loadDraftData } = useJobsStore.getState();
      const draftLoaded = loadDraftData();

      if (draftLoaded) {
        // Always redirect to the first step (Basic Job Information)
        // This ensures the form fields are properly populated
        setActiveStep(0);

        // Use shallow routing to avoid full page reload
        const firstStepName = 'basic-job-information';
        const url = `/job-description-creation?step=${firstStepName}&nav=true`;

        // Use replace to avoid adding to history stack
        router.replace(url);
      } else {
        // If no draft, just go to the first step
        setActiveStep(0);

        // Use shallow routing to avoid full page reload
        const firstStepName = 'basic-job-information';
        const url = `/job-description-creation?step=${firstStepName}&nav=true`;

        // Use replace to avoid adding to history stack
        router.replace(url);
      }
    } catch (error) {
      console.error('JobsContext: Error loading draft', error);

      // If error, just go to the first step
      setActiveStep(0);

      // Use a timeout to ensure the state is updated before redirecting
      setTimeout(() => {
        const firstStepName = 'basic-job-information';
        router.push(`/job-description-creation?step=${firstStepName}&nav=true`);
      }, 100);
    }
  }, [setActiveStep, router]);

  // Handle starting a new job description
  const handleStartNew = useCallback(() => {
    // Clear any existing job data
    clearJobData();

    // Set to first step
    setActiveStep(0);

    // Use shallow routing to avoid full page reload
    const firstStepName = 'basic-job-information';
    const url = `/job-description-creation?step=${firstStepName}&nav=true`;

    // Use replace to avoid adding to history stack
    router.replace(url);
  }, [clearJobData, setActiveStep, router]);

  // Check for draft when navigating to job creation page
  useEffect(() => {
    if (!isHydrated) return;

    const currentPath = window.location.pathname;

    // Check if we're in edit mode from URL or localStorage
    const isEditingFromUrl = searchParams?.get('edit') === 'true' || searchParams?.get('jobId');
    const isEditingFromStorage = localStorage.getItem('isEditingJob') === 'true';
    const isEditingJob = isEditingFromUrl || isEditingFromStorage;

    // Only check for drafts when directly navigating to job creation page
    // and there's no step parameter in the URL
    // AND we're not in edit mode
    if (
      currentPath === '/job-description-creation' &&
      !searchParams?.get('step') &&
      !isEditingJob
    ) {
      const draft = checkForDraft();

      if (draft) {
        const newDraftData = {
          step: draft.step,
          stepName: draft.stepName,
          timestamp: draft.timestamp,
          jobTitle: draft.jobTitle,
        };
        setDraftData(newDraftData);

        // Use a timeout to ensure the state is updated before showing the modal
        setTimeout(() => {
          setShowDraftModal(true);
        }, 100);
      }
    }
  }, [isHydrated, checkForDraft, searchParams]);

  // Update company in store when it changes
  useEffect(() => {
    if (company && userRole === 'employer') {
      // Cast to the expected CompanyData type from the store
      setCompany(company as any);
    }
  }, [company, userRole, setCompany]);

  // Handle hydration and job loading for edit mode
  useEffect(() => {
    if (!userLoading && isHydrated) {
      const loadEditingJob = async () => {
        // Check if we have a jobId parameter in the URL
        const jobId = searchParams?.get('jobId');
        const isEdit = searchParams?.get('edit') === 'true';

        if (jobId && isEdit) {
          // Check if we've already loaded this job
          const loadedEditJobId = sessionStorage.getItem('loadedEditJobId');
          if (loadedEditJobId === jobId) {
            // Already loaded this job, skip
            return;
          }

          try {
            // Mark this job as being loaded
            sessionStorage.setItem('loadedEditJobId', jobId);

            // Fetch the job data from the backend
            const { fetchJobById, setJob, clearJobData } = useJobsStore.getState();

            // Clear any existing data first
            clearJobData();

            // Small delay to ensure clean state
            await new Promise(resolve => setTimeout(resolve, 100));

            // Fetch the job
            const jobData: any = await fetchJobById(jobId);

            if (jobData) {
              // Update localStorage for consistency
              localStorage.setItem('job', JSON.stringify(jobData));
              localStorage.setItem('isEditingJob', 'true');
              localStorage.setItem('editingJobId', jobId);

              // Set the job data in the store
              setJob(jobData);

              // Force a re-render of the jobs store subscribers
              useJobsStore.setState({ job: jobData });
            } else {
              console.error('Failed to fetch job data for editing');
              // Clear the loaded flag on error
              sessionStorage.removeItem('loadedEditJobId');
            }
          } catch (error) {
            console.error('Error loading job for editing:', error);
            // Clear the loaded flag on error
            sessionStorage.removeItem('loadedEditJobId');
          }
        } else {
          // Not in edit mode, clear the loaded flag
          sessionStorage.removeItem('loadedEditJobId');

          // Check if we're in edit mode from localStorage (backward compatibility)
          const isEditingJob = localStorage.getItem('isEditingJob') === 'true';
          if (isEditingJob) {
            // Load the draft data from localStorage
            const { loadDraftData } = useJobsStore.getState();
            loadDraftData();
          }
        }
      };

      // Call async function
      loadEditingJob().catch(error => {
        console.error('Error in loadEditingJob:', error);
      });
    }
  }, [userLoading, isHydrated, searchParams]);

  // Separate effect for initial hydration
  useEffect(() => {
    if (!userLoading && !isHydrated) {
      // Load job data from localStorage if available and not in edit mode
      const isEditingFromUrl = searchParams?.get('edit') === 'true' || searchParams?.get('jobId');
      const isEditingFromStorage = localStorage.getItem('isEditingJob') === 'true';
      const isEditingJob = isEditingFromUrl || isEditingFromStorage;

      if (!isEditingJob) {
        const { loadDraftData } = useJobsStore.getState();
        loadDraftData();
      }

      setIsHydrated(true);
    }
  }, [userLoading, isHydrated, setIsHydrated, searchParams]);

  // Handle step navigation from URL
  useEffect(() => {
    if (!isHydrated) return;

    const step = searchParams?.get('step');
    const nav = searchParams?.get('nav');
    const currentPath = window.location.pathname;

    const handleNavigation = () => {
      if (step === 'published') {
        // Check if we have a valid job completion flag
        const hasCompletedJob = sessionStorage.getItem('jobCompleted') === 'true';
        const successStepIndex = steps.findIndex(s => s.path === 'published');

        // Only show success if we have a legitimate completion flag
        // This prevents the success page from showing when navigating back
        if (hasCompletedJob) {
          if (successStepIndex !== -1) {
            setActiveStep(successStepIndex);
          }
        } else {
          // No completion flag means invalid access - redirect to jobs page
          router.replace('/jobs');
        }
        return;
      }

      if (step) {
        const stepIndex = steps.findIndex(s => s.name.toLowerCase().replace(/\s+/g, '-') === step);

        if (stepIndex !== -1 && stepIndex !== activeStep) {
          // Special handling for basic-job-information step
          if (step === 'basic-job-information' && !nav) {
            // If navigating to basic job info without nav=true, check if we should clear data
            // This happens when someone navigates directly or after completing a job
            // Check if we're in edit mode first
            const isEditingFromUrl =
              searchParams?.get('edit') === 'true' || searchParams?.get('jobId');
            const isEditingFromStorage = localStorage.getItem('isEditingJob') === 'true';
            const isEditingJob = isEditingFromUrl || isEditingFromStorage;

            if (!isEditingJob) {
              const hasValidDraft = checkForDraft();

              if (!hasValidDraft) {
                // No valid draft, so clear everything and start fresh
                sessionStorage.removeItem('jobCompleted');
                localStorage.removeItem('job');
                localStorage.removeItem('step');
                localStorage.removeItem('activeStep');
                clearJobData(); // Also clear the Zustand store
              }
            }
          }

          setActiveStep(stepIndex);
        }
        return;
      }

      // Handle direct navigation to job creation page without step parameter
      if (
        currentPath === '/job-description-creation' &&
        !step &&
        !nav // Only reset if not navigated programmatically
      ) {
        // Always reset to step 0 when accessing job creation without step parameter
        // This prevents the Success component from showing when navigating back
        if (!showDraftModal) {
          // Clear any completion flags and job data since we're starting fresh
          sessionStorage.removeItem('jobCompleted');
          localStorage.removeItem('job');
          localStorage.removeItem('step');
          localStorage.removeItem('activeStep');
          setActiveStep(0);
          updateURLAndStorage(0);
          router.replace(window.location.href);
        }
      }
    };

    handleNavigation();
  }, [
    searchParams,
    isHydrated,
    job,
    activeStep,
    setActiveStep,
    clearJobData,
    router,
    updateURLAndStorage,
    showDraftModal,
  ]);

  return (
    <>
      {children}

      {/* Draft Modal */}
      <DraftModal
        isOpen={showDraftModal}
        onClose={() => {
          setShowDraftModal(false);
        }}
        onContinueDraft={handleContinueDraft}
        onStartNew={handleStartNew}
        draftData={draftData}
      />
    </>
  );
}

// Wrap the inner provider with Suspense
export function JobsProvider({ children, navigationConfig }: JobsProviderProps) {
  return (
    <Suspense fallback={<div>Loading jobs...</div>}>
      <JobsProviderInner navigationConfig={navigationConfig}>{children}</JobsProviderInner>
    </Suspense>
  );
}

// Export a hook to access job store throughout app
export const useJobs = useJobsStore;

// Utility function to preserve only the tab parameter in URL
export const preserveTabParameter = (router: any, searchParams: URLSearchParams | null) => {
  const baseUrl = window.location.pathname;
  const tabParam = searchParams?.get('tab');

  if (tabParam) {
    // Preserve only the tab parameter
    router.replace(`${baseUrl}?tab=${tabParam}`);
  } else {
    // No tab parameter exists, just use the base URL
    router.replace(baseUrl);
  }
};

// Utility function to check for existing draft
export const checkForExistingDraft = () => {
  if (typeof window === 'undefined') return null;

  try {
    const savedJob = localStorage.getItem('job');
    const savedStep = localStorage.getItem('activeStep');

    if (savedJob && savedStep) {
      const parsedJob = JSON.parse(savedJob);
      const stepIndex = parseInt(savedStep, 10);

      // Check if there's meaningful data beyond step 1 (Basic Job Information)
      // Only consider as draft if user has progressed to step 2+ or has content from step 2+
      const hasStep2PlusContent = Boolean(
        // Step 1 content (Skills & Responsibilities)
        (parsedJob.skills && parsedJob.skills.length > 0) ||
          (parsedJob.jobResponsibilities && parsedJob.jobResponsibilities.length > 0) ||
          // Step 2+ content (Candidate Qualifications and beyond)
          parsedJob.experience ||
          parsedJob.workLocation ||
          parsedJob.benefits ||
          parsedJob.hiringManagerName ||
          parsedJob.hiringManagerDescription
      );

      // Also check if user has progressed to step 2+ (index 1+)
      const hasProgressedBeyondBasics = stepIndex >= 1;

      const hasMeaningfulData = hasStep2PlusContent || hasProgressedBeyondBasics;

      // Only return the draft if it has meaningful data
      if (hasMeaningfulData) {
        // Consider any step as a valid draft (including step 0)
        const draft = {
          job: parsedJob,
          step: stepIndex,
          stepName: steps[stepIndex]?.name || 'Unknown Step',
          timestamp: parsedJob.updatedAt || new Date().toISOString(),
          jobTitle: parsedJob.title || 'Untitled Job',
        };

        return draft;
      } else {
        // Clear the localStorage since there's no meaningful data
        localStorage.removeItem('job');
        localStorage.removeItem('step');
        localStorage.removeItem('activeStep');
        return null;
      }
    }
    return null;
  } catch (error) {
    console.error('Error checking for draft:', error);
    return null;
  }
};
