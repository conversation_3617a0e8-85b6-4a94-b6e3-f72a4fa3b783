import { useCallback, useEffect, useState } from 'react';

import { generateJob } from '@/data/jobs';
import { IJob } from '@/entities/interfaces';
import { useCompanyStore } from '@/stores/companyStore';
import { formatToHumanReadable } from '@/utils/formatters';

import { ExperienceLevel, JobStatus } from '@/entities';
import {
  CompanyData,
  JobDescriptionData,
  JobOperationsReturn,
  UpdateJobDescriptionFn,
} from './types';

const initialJobState: JobDescriptionData = {
  clientId: '',
  title: '',
  jobType: '',
  companyName: '',
  department: '',
  skills: [],
  experience: '',
};

const isCompanyData = (data: any): data is CompanyData => {
  return data && typeof data === 'object' && 'clientId' in data && 'id' in data;
};

export const useJobOperations = (clientId?: string): JobOperationsReturn => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [company, setCompany] = useState<CompanyData | null>(null);
  const [job, setJobDescription] = useState<JobDescriptionData>(initialJobState);

  // Load saved job data from localStorage on mount
  useEffect(() => {
    const savedJob = localStorage.getItem('job');
    if (savedJob) {
      try {
        const parsedJob = JSON.parse(savedJob) as JobDescriptionData;
        setJobDescription(parsedJob);
      } catch (error) {
        console.error('Error parsing saved job:', error);
        localStorage.removeItem('job');
      }
    }
  }, []);

  const fetchJobs = useCallback(() => {
    setLoading(true);
    try {
      const storedJobs = localStorage.getItem('generatedJobs');
      if (storedJobs) {
        setJobs(JSON.parse(storedJobs));
      } else {
        const fetchedJobs = Array.from({ length: 15 }, () => ({
          ...generateJob(),
          title: '',
          finalDraft: '',
          topCandidateThreshold: 0,
          secondTierCandidateThreshold: 0,
          requirements: [],
          candidates: [],
        })) as IJob[];
        setJobs(fetchedJobs);
        localStorage.setItem('generatedJobs', JSON.stringify(fetchedJobs));
      }
      setError(null);
    } catch (err) {
      setError('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  }, []);

  const addJob = useCallback((job: IJob) => {
    setJobs(prevJobs => {
      const newJobs = [job, ...prevJobs];
      localStorage.setItem('generatedJobs', JSON.stringify(newJobs));
      return newJobs;
    });
  }, []);

  const updateJob = useCallback((id: string, updatedJob: Partial<IJob>) => {
    setJobs(prevJobs => {
      const newJobs = prevJobs.map(job => (job.id === id ? { ...job, ...updatedJob } : job));
      localStorage.setItem('generatedJobs', JSON.stringify(newJobs));
      return newJobs;
    });
  }, []);

  const deleteJob = useCallback((id: string) => {
    setJobs(prevJobs => {
      const newJobs = prevJobs.filter(job => job.id !== id);
      localStorage.setItem('generatedJobs', JSON.stringify(newJobs));
      return newJobs;
    });
  }, []);

  const updateJobDescription = useCallback<UpdateJobDescriptionFn>((key, value) => {
    setJobDescription(prev => {
      const formattedValue = Array.isArray(value)
        ? value.map(v => formatToHumanReadable(v))
        : typeof value === 'string'
          ? formatToHumanReadable(value)
          : value;

      const updated = { ...prev, [key]: formattedValue };
      localStorage.setItem('job', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const setFullJobDescription = useCallback((data: JobDescriptionData) => {
    const formattedData = Object.entries(data).reduce<JobDescriptionData>((acc, [key, value]) => {
      const typedKey: any = key as keyof JobDescriptionData;
      if (Array.isArray(value)) {
        acc[typedKey] = value.map(v => formatToHumanReadable(v as string)) as any;
      } else if (typeof value === 'string') {
        acc[typedKey] = formatToHumanReadable(value) as any;
      } else {
        acc[typedKey] = value as any;
      }
      return acc;
    }, {} as JobDescriptionData);

    setJobDescription(formattedData);
    localStorage.setItem('job', JSON.stringify(formattedData));
  }, []);

  const fetchCompanyData = useCallback(async (clientId?: string) => {
    try {
      if (!clientId) return;

      const cachedUser = localStorage.getItem(`userRole_${clientId}`);
      if (cachedUser) {
        const userData = JSON.parse(cachedUser) as { role?: string };
        if (userData.role !== 'employer') {
          return;
        }
      }

      // Use the company store instead of direct API call
      const { fetchCompany } = useCompanyStore.getState();
      const response = await fetchCompany(false, clientId);

      if (response && isCompanyData(response)) {
        setCompany(response);
      } else {
        console.error('Invalid company data received from store');
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
    }
  }, []);

  const clearJobData = useCallback(() => {
    localStorage.removeItem('job');
    localStorage.removeItem('activeStep');
    if (clientId) {
      localStorage.removeItem(`company_${clientId}`);
    }
    setJobDescription(initialJobState);
    setCompany(null);
  }, [clientId]);

  const addJobFromDescription = useCallback(() => {
    const storedJobDescription = localStorage.getItem('job');
    if (storedJobDescription) {
      const jobData = JSON.parse(storedJobDescription);
      const formatted = Object.entries(jobData).reduce<Record<string, string | string[]>>(
        (acc, [key, value]) => {
          acc[key] = Array.isArray(value)
            ? value.map(v => formatToHumanReadable(v as string))
            : formatToHumanReadable(value as string);
          return acc;
        },
        {}
      );

      const newJob: IJob = {
        id: '', // This will be assigned by the backend when created
        title: String(formatted.title || ''),
        jobTitle: String(formatted.title || ''),
        matchedCandidatesCount: 0,
        candidatesCount: 0,
        totalCandidates: 0,
        jobType: String(formatted.jobType || ''),
        companyName: String(formatted.companyName || ''),
        companyWebsite: formatted.companyWebsite ? String(formatted.companyWebsite) : undefined,
        department: String(formatted.industry || formatted.department || ''),
        experienceLevel: ExperienceLevel.MID, // Default to MID level
        isGraduateRole: false, // Default to false
        status: JobStatus.NEW, // Default to NEW status
        companyDescription: formatted.companyDescription
          ? String(formatted.companyDescription)
          : undefined,
        skills: Array.isArray(formatted.skills) ? formatted.skills : [],
        experience: String(formatted.experience || ''),
        jobResponsibilities: Array.isArray(formatted.jobResponsibilities)
          ? formatted.jobResponsibilities
          : [],
        hiringManagerDescription: formatted.hiringManagerDescription
          ? String(formatted.hiringManagerDescription)
          : undefined,
        companyValues: Array.isArray(formatted.companyValues) ? formatted.companyValues : [],
        culturalFit: Array.isArray(formatted.culturalFit) ? formatted.culturalFit : [],
        education: Array.isArray(formatted.education) ? formatted.education : [],
        language: Array.isArray(formatted.language) ? formatted.language : [],
        softSkills: Array.isArray(formatted.softSkills) ? formatted.softSkills : [],
        location: Array.isArray(formatted.location) ? formatted.location : [],
        salaryRange: formatted.salaryRange ? String(formatted.salaryRange) : undefined,
        benefits: Array.isArray(formatted.benefits) ? formatted.benefits : [],
        careerGrowth: Array.isArray(formatted.careerGrowth) ? formatted.careerGrowth : [],
        cultureFitDescription: formatted.cultureFitDescription
          ? String(formatted.cultureFitDescription)
          : undefined,
        finalDraft: '',
        topCandidateThreshold: 0,
        secondTierCandidateThreshold: 0,
        requirements: [],
        candidates: [],
      };
      addJob(newJob);
    }
  }, [addJob]);

  return {
    jobs,
    loading,
    error,
    job,
    company,
    setCompany,
    fetchJobs,
    addJob,
    updateJob,
    deleteJob,
    updateJobDescription,
    setFullJobDescription,
    fetchCompanyData,
    clearJobData,
    addJobFromDescription,
  };
};
