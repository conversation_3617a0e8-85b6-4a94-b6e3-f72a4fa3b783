import { act, renderHook } from '@testing-library/react';
import { useOnboardingStore } from '@/stores/onboardingStore';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';

// Mock apiHelper
jest.mock('@/lib/apiHelper', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
  },
}));

describe('OnboardingStore', () => {
  beforeEach(() => {
    // Reset the store before each test
    const { result } = renderHook(() => useOnboardingStore());
    act(() => {
      result.current.reset();
    });
    jest.clearAllMocks();
  });

  describe('Company Onboarding State Management', () => {
    test('should correctly determine if company needs onboarding', () => {
      const { result } = renderHook(() => useOnboardingStore());

      // No company data - should need onboarding
      expect(result.current.getCompanyNeedsOnboarding()).toBe(true);

      // Set company with onboardingRequired = true
      act(() => {
        result.current.setCompany({
          id: '1',
          clientId: 'test-client',
          companyName: 'Test Company',
          companyWebsite: 'https://test.com',
          industry: 'Technology',
          size: '11-50',
          location: 'New York',
          contactEmail: '<EMAIL>',
          phoneNumber: '+1234567890',
          logo: '',
          description: 'Test company',
          onboardingRequired: true,
        });
      });

      expect(result.current.getCompanyNeedsOnboarding()).toBe(true);

      // Set company with onboardingRequired = false
      act(() => {
        result.current.setCompany({
          ...result.current.company!,
          onboardingRequired: false,
        });
      });

      expect(result.current.getCompanyNeedsOnboarding()).toBe(false);
    });

    test('should mark company onboarding as complete', () => {
      const { result } = renderHook(() => useOnboardingStore());

      // Set initial company state
      act(() => {
        result.current.setCompany({
          id: '1',
          clientId: 'test-client',
          companyName: 'Test Company',
          companyWebsite: 'https://test.com',
          industry: 'Technology',
          size: '11-50',
          location: 'New York',
          contactEmail: '<EMAIL>',
          phoneNumber: '+1234567890',
          logo: '',
          description: 'Test company',
          onboardingRequired: true,
        });
      });

      expect(result.current.company?.onboardingRequired).toBe(true);

      // Mark onboarding as complete
      act(() => {
        result.current.markCompanyOnboardingComplete();
      });

      expect(result.current.company?.onboardingRequired).toBe(false);
    });

    test('should fetch company data and update state', async () => {
      const mockCompanyData = {
        id: '1',
        clientId: 'test-client',
        companyName: 'Fetched Company',
        companyWebsite: 'https://fetched.com',
        industry: 'Finance',
        size: '51-200',
        location: 'San Francisco',
        contactEmail: '<EMAIL>',
        phoneNumber: '+9876543210',
        logo: 'https://logo.url',
        description: 'Fetched company description',
        onboardingRequired: false,
      };

      (apiHelper.get as jest.Mock).mockResolvedValue(mockCompanyData);

      const { result } = renderHook(() => useOnboardingStore());

      await act(async () => {
        await result.current.fetchCompany();
      });

      expect(apiHelper.get).toHaveBeenCalledWith('/companies/client');
      expect(result.current.company).toEqual(mockCompanyData);
      expect(result.current.hasCheckedOnboarding).toBe(true);
    });

    test('should handle company fetch errors gracefully', async () => {
      (apiHelper.get as jest.Mock).mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useOnboardingStore());

      await act(async () => {
        await result.current.fetchCompany();
      });

      expect(result.current.company).toBeNull();
      expect(result.current.hasCheckedOnboarding).toBe(true);
      expect(result.current.error).toBe('Network error'); // Non-404 errors are stored
    });

    test('should handle 404 errors for new employers', async () => {
      const error = new Error('Not found');
      (error as any).response = { status: 404 };
      (apiHelper.get as jest.Mock).mockRejectedValue(error);

      const { result } = renderHook(() => useOnboardingStore());

      await act(async () => {
        await result.current.fetchCompany();
      });

      expect(result.current.company).toBeNull();
      expect(result.current.hasCheckedOnboarding).toBe(true);
      expect(result.current.error).toBeNull(); // 404s are not treated as errors for new employers
    });
  });

  describe('Onboarding Redirect Logic', () => {
    test('should return correct redirect path for employer needing onboarding', async () => {
      const mockCompanyData = {
        id: '1',
        clientId: 'test-client',
        companyName: 'Test Company',
        companyWebsite: 'https://test.com',
        industry: 'Technology',
        size: '11-50',
        location: 'New York',
        contactEmail: '<EMAIL>',
        phoneNumber: '+1234567890',
        logo: '',
        description: 'Test company',
        onboardingRequired: true,
      };

      (apiHelper.get as jest.Mock).mockResolvedValue(mockCompanyData);

      const { result } = renderHook(() => useOnboardingStore());

      act(() => {
        result.current.setUserRole(UserRole.EMPLOYER);
      });

      // Fetch company data to set hasCheckedOnboarding
      await act(async () => {
        await result.current.fetchCompany();
      });

      const redirectPath = result.current.shouldRedirectToOnboarding();
      expect(redirectPath).toBe('/company-onboarding');
    });

    test('should return null for employer with completed onboarding', () => {
      const { result } = renderHook(() => useOnboardingStore());

      act(() => {
        result.current.setUserRole(UserRole.EMPLOYER);
        result.current.setCompany({
          id: '1',
          clientId: 'test-client',
          companyName: 'Test Company',
          companyWebsite: 'https://test.com',
          industry: 'Technology',
          size: '11-50',
          location: 'New York',
          contactEmail: '<EMAIL>',
          phoneNumber: '+1234567890',
          logo: '',
          description: 'Test company',
          onboardingRequired: false,
        });
        // Simulate that onboarding check has been done
        result.current.fetchCompany();
      });

      const redirectPath = result.current.shouldRedirectToOnboarding();
      expect(redirectPath).toBeNull();
    });

    test('should return null if onboarding has not been checked yet', () => {
      const { result } = renderHook(() => useOnboardingStore());

      act(() => {
        result.current.setUserRole(UserRole.EMPLOYER);
        // Don't set hasCheckedOnboarding
      });

      const redirectPath = result.current.shouldRedirectToOnboarding();
      expect(redirectPath).toBeNull();
    });

    test('should handle job seeker redirect logic', async () => {
      const mockProfileData = {
        id: '1',
        userId: 'user-1',
        clientId: 'client-1',
        firstName: 'John',
        lastName: 'Doe',
        fullName: 'John Doe',
        email: '<EMAIL>',
        hasCompletedOnboarding: false,
        onboardingProgress: {
          overall: { completed: false, percentage: 0, completedAt: null },
          basicInfo: {
            completed: false,
            percentage: 0,
            completedAt: null,
            requiredFields: [],
            completedFields: [],
          },
          professionalInfo: {
            completed: false,
            percentage: 0,
            completedAt: null,
            requiredFields: [],
            completedFields: [],
          },
          preferences: {
            completed: false,
            percentage: 0,
            completedAt: null,
            requiredFields: [],
            completedFields: [],
          },
          additionalInfo: {
            completed: false,
            percentage: 0,
            completedAt: null,
            requiredFields: [],
            completedFields: [],
          },
          lastUpdated: new Date().toISOString(),
        },
      };

      (apiHelper.get as jest.Mock).mockResolvedValue(mockProfileData);

      const { result } = renderHook(() => useOnboardingStore());

      act(() => {
        result.current.setUserRole(UserRole.JOB_SEEKER);
      });

      // Fetch profile data to set hasCheckedOnboarding
      await act(async () => {
        await result.current.fetchProfile();
      });

      const redirectPath = result.current.shouldRedirectToOnboarding();
      expect(redirectPath).toBe('/jobseeker-onboarding');
    });
  });

  describe('State Reset', () => {
    test('should reset all state when reset is called', () => {
      const { result } = renderHook(() => useOnboardingStore());

      // Set some state
      act(() => {
        result.current.setUserRole(UserRole.EMPLOYER);
        result.current.setCompany({
          id: '1',
          clientId: 'test-client',
          companyName: 'Test Company',
          companyWebsite: 'https://test.com',
          industry: 'Technology',
          size: '11-50',
          location: 'New York',
          contactEmail: '<EMAIL>',
          phoneNumber: '+1234567890',
          logo: '',
          description: 'Test company',
          onboardingRequired: true,
        });
      });

      expect(result.current.company).not.toBeNull();
      expect(result.current.userRole).toBe(UserRole.EMPLOYER);

      // Reset the store
      act(() => {
        result.current.reset();
      });

      expect(result.current.company).toBeNull();
      expect(result.current.profile).toBeNull();
      expect(result.current.userRole).toBeNull();
      expect(result.current.hasCheckedOnboarding).toBe(false);
      expect(result.current.shouldShowSetupSlider).toBe(false);
    });
  });

  describe('Loading States', () => {
    test('should manage loading state during fetch operations', async () => {
      const { result } = renderHook(() => useOnboardingStore());

      expect(result.current.isLoading).toBe(false);

      // Mock a delayed response
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      (apiHelper.get as jest.Mock).mockReturnValue(promise);

      // Start fetching without awaiting
      let fetchPromise: Promise<void>;
      act(() => {
        fetchPromise = result.current.fetchCompany();
      });

      // Check loading state is true immediately after starting fetch
      expect(result.current.isLoading).toBe(true);

      // Resolve the promise
      act(() => {
        resolvePromise!({
          id: '1',
          companyName: 'Test',
          onboardingRequired: false,
        });
      });

      await act(async () => {
        await fetchPromise!;
      });

      // Loading should be false after completion
      expect(result.current.isLoading).toBe(false);
    });
  });
});
