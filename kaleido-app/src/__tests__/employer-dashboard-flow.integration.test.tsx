import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useRouter } from 'next/router';
import '@testing-library/jest-dom';

import DashboardPage from '@/pages/dashboard';
import { Dashboard } from '@/components/Dashboard/Dashboard';
import { useAuthStore } from '@/stores/authStore';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { useCompanyStore } from '@/stores/companyStore';
import { useEnhancedUserData } from '@/hooks/useEnhancedUserData';
import { UserRole } from '@/types/roles';

// Mock modules
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@/stores/authStore');
jest.mock('@/stores/onboardingStore');
jest.mock('@/stores/companyStore');
jest.mock('@/hooks/useEnhancedUserData');
jest.mock('@/hooks/useAuthSync', () => ({
  useAuthSync: jest.fn(),
}));

// Mock components that might cause issues in tests
jest.mock('@/components/Dashboard/Employer/EmployerDashboard', () => ({
  __esModule: true,
  default: ({ stats }: any) => (
    <div data-testid="employer-dashboard">
      Employer Dashboard
      <div data-testid="open-jobs-count">{stats?.jobs?.statusBreakdown?.open || 0}</div>
      <div data-testid="total-candidates">{stats?.candidates?.total || 0}</div>
    </div>
  ),
}));

jest.mock('@/components/Dashboard/JobSeeker/JobSeekerDashboard', () => ({
  __esModule: true,
  default: () => <div data-testid="jobseeker-dashboard">JobSeeker Dashboard</div>,
}));

jest.mock('@/components/Dashboard/Graduate/GraduateDashboard', () => ({
  __esModule: true,
  default: () => <div data-testid="graduate-dashboard">Graduate Dashboard</div>,
}));

jest.mock('@/components/Layouts/ColourfulLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader">Loading...</div>,
}));

jest.mock('@/components/steps/layout/AppLayout', () => ({
  __esModule: true,
  default: ({ children, isLoading }: any) => (
    <div data-testid="app-layout">
      {isLoading ? <div data-testid="app-loading">Loading...</div> : children}
    </div>
  ),
}));

jest.mock('@/contexts/jobSearch/JobSearchContext', () => ({
  JobSearchProvider: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/Auth/RoleBasedRedirectHandler', () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock('@/components/JobSeeker/JobSeekerSetupSlider', () => ({
  JobSeekerSetupSlider: () => <div data-testid="jobseeker-setup">JobSeeker Setup</div>,
}));

jest.mock('@/hooks/useProfileCompletion', () => ({
  useProfileCompletion: () => ({
    isModalOpen: false,
    missingFields: [],
    closeModal: jest.fn(),
    handleComplete: jest.fn(),
  }),
}));

jest.mock('@/hooks/useProactiveProfileValidation', () => ({
  __esModule: true,
  default: () => ({
    shouldShowAlert: false,
    validation: null,
    dismissAlert: jest.fn(),
  }),
}));

// Mock Auth0
jest.mock('@auth0/nextjs-auth0/client', () => ({
  useUser: jest.fn(() => ({
    user: null,
    error: null,
    isLoading: false,
  })),
  UserProvider: ({ children }: any) => children,
}));

describe('Employer Dashboard Flow Integration', () => {
  const mockRouter = {
    query: {},
    replace: jest.fn(),
    push: jest.fn(),
    pathname: '/dashboard',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    // Reset cookies
    document.cookie = '';
  });

  describe('New Employer First Login', () => {
    test('should redirect new employer to company onboarding page', async () => {
      // Setup mocks for a new employer without company data
      (useAuthStore as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: {
          user: {
            sub: 'new-employer-123',
            email: '<EMAIL>',
          },
        },
      });

      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {},
        },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      (useOnboardingStore as jest.Mock).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
        shouldRedirectToOnboarding: jest.fn().mockReturnValue('/company-onboarding'),
        getCompanyNeedsOnboarding: jest.fn().mockReturnValue(true),
      });

      (useCompanyStore as jest.Mock).mockReturnValue({
        company: null,
        fetchCompany: jest.fn().mockResolvedValue(null),
      });

      // Render dashboard
      render(<DashboardPage />);

      // Should show the dashboard component (not job seeker setup)
      await waitFor(() => {
        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
      });

      // Verify user role was set
      expect(useOnboardingStore().setUserRole).toHaveBeenCalledWith(UserRole.EMPLOYER);
    });
  });

  describe('Existing Employer with Completed Onboarding', () => {
    test('should show employer dashboard directly without any onboarding', async () => {
      const mockCompanyData = {
        id: 'company-123',
        companyName: 'Established Company',
        onboardingRequired: false,
      };

      // Set cookie to indicate onboarding is complete
      document.cookie = 'onboardingComplete_existing-employer-123=true';

      (useAuthStore as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: {
          user: {
            sub: 'existing-employer-123',
            email: '<EMAIL>',
          },
        },
      });

      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {
            jobs: {
              statusBreakdown: { open: 5 },
            },
            candidates: {
              total: 25,
            },
          },
        },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      (useOnboardingStore as jest.Mock).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
        shouldRedirectToOnboarding: jest.fn().mockReturnValue(null),
        getCompanyNeedsOnboarding: jest.fn().mockReturnValue(false),
      });

      (useCompanyStore as jest.Mock).mockReturnValue({
        company: mockCompanyData,
        fetchCompany: jest.fn().mockResolvedValue(mockCompanyData),
      });

      // Render the Dashboard component directly
      render(<Dashboard />);

      // Should show employer dashboard
      await waitFor(() => {
        expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();
      });

      // Verify dashboard stats are displayed
      expect(screen.getByTestId('open-jobs-count')).toHaveTextContent('5');
      expect(screen.getByTestId('total-candidates')).toHaveTextContent('25');

      // Should not show job seeker components
      expect(screen.queryByTestId('jobseeker-dashboard')).not.toBeInTheDocument();
      expect(screen.queryByTestId('jobseeker-setup')).not.toBeInTheDocument();
    });
  });

  describe('Role-based Dashboard Rendering', () => {
    test('should render correct dashboard based on user role', async () => {
      const baseAuthStore = {
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: {
          user: {
            sub: 'user-123',
            email: '<EMAIL>',
          },
        },
      };

      const baseOnboardingStore = {
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      };

      // Test employer dashboard
      (useAuthStore as jest.Mock).mockReturnValue(baseAuthStore);
      (useOnboardingStore as jest.Mock).mockReturnValue(baseOnboardingStore);
      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: { userRole: UserRole.EMPLOYER, dashboardStats: {} },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      const { rerender } = render(<Dashboard />);
      expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();

      // Test job seeker dashboard
      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: { userRole: UserRole.JOB_SEEKER, dashboardStats: {} },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      rerender(<Dashboard />);
      expect(screen.getByTestId('jobseeker-dashboard')).toBeInTheDocument();

      // Test graduate dashboard
      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: { userRole: UserRole.GRADUATE, dashboardStats: {} },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      rerender(<Dashboard />);
      expect(screen.getByTestId('graduate-dashboard')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    test('should show loader while fetching user data', async () => {
      (useAuthStore as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: { user: { sub: 'user-123' } },
      });

      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
        isFallback: false,
      });

      (useOnboardingStore as jest.Mock).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: false,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      render(<Dashboard />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should show error state when user data fails to load', async () => {
      (useAuthStore as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: { user: { sub: 'user-123' } },
      });

      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: null,
        isLoading: false,
        error: new Error('Failed to load user data'),
        isFallback: false,
      });

      (useOnboardingStore as jest.Mock).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: false,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      render(<Dashboard />);

      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
        expect(
          screen.getByText('Failed to load dashboard data. Please try refreshing the page.')
        ).toBeInTheDocument();
      });
    });
  });

  describe('Cookie-based Onboarding Prevention', () => {
    test('should not show onboarding if cookie is set', async () => {
      // Set the onboarding complete cookie
      document.cookie = 'onboardingComplete_cookie-test-123=true; path=/';

      (useAuthStore as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
        session: {
          user: {
            sub: 'cookie-test-123',
            email: '<EMAIL>',
          },
        },
      });

      (useEnhancedUserData as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {},
        },
        isLoading: false,
        error: null,
        isFallback: false,
      });

      (useOnboardingStore as jest.Mock).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
        shouldRedirectToOnboarding: jest.fn().mockReturnValue(null),
        getCompanyNeedsOnboarding: jest.fn().mockReturnValue(false),
      });

      render(<Dashboard />);

      // Should show the dashboard
      await waitFor(() => {
        expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();
      });

      // Should not attempt to redirect
      expect(mockRouter.push).not.toHaveBeenCalledWith('/company-onboarding');
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/company-onboarding');
    });
  });
});
