import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useParams, useRouter } from 'next/navigation';
import CareerInsightDetailPage from '@/app/career-insights/[id]/page';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { InsightType, InsightStatus } from '@/lib/career-insights/config';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(),
}));

// Mock the career insights store
jest.mock('@/stores/careerInsightsStore', () => ({
  useCareerInsightsStore: jest.fn(),
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock components
jest.mock('@/components/steps/layout/BaseLayout', () => {
  return function MockBaseLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="base-layout">{children}</div>;
  };
});

jest.mock('@/components/Layouts/ColourfulLoader', () => {
  return function MockColourfulLoader() {
    return <div data-testid="colourful-loader">Loading...</div>;
  };
});

jest.mock('@/components/career-insights/AnalysisRenderer', () => {
  return function MockAnalysisRenderer({ content }: { content: string }) {
    return <div data-testid="analysis-renderer">{content}</div>;
  };
});

jest.mock('@/components/career-insights/ModernRecommendations', () => {
  return function MockModernRecommendations({ aiInsights }: any) {
    return (
      <div data-testid="modern-recommendations">
        <div>Recommendations: {aiInsights.recommendations.length}</div>
        <div>Confidence: {aiInsights.confidenceScore}%</div>
      </div>
    );
  };
});

jest.mock('@/components/career-insights/LearningResourcesDisplay', () => {
  return function MockLearningResourcesDisplay({ resources, title }: any) {
    return (
      <div data-testid="learning-resources">
        <div>{title}</div>
        <div>Resources: {resources.length}</div>
      </div>
    );
  };
});

jest.mock('@/components/career-insights/CareerPathDisplay', () => {
  return function MockCareerPathDisplay({ paths, currentPosition }: any) {
    return (
      <div data-testid="career-path-display">
        <div>Current: {currentPosition}</div>
        <div>Paths: {paths.length}</div>
      </div>
    );
  };
});

// Mock PDF components
jest.mock('@react-pdf/renderer', () => ({
  PDFDownloadLink: ({ children }: any) => <div data-testid="pdf-download">{children}</div>,
}));

jest.mock('@/components/pdf/CareerInsightsPdf', () => {
  return function MockCareerInsightsPdf() {
    return <div>PDF Component</div>;
  };
});

const mockPush = jest.fn();
const mockFetchInsightById = jest.fn().mockResolvedValue(undefined);

const mockSkillGapInsight = {
  id: '1',
  jobSeekerId: 'job-seeker-1',
  type: InsightType.SKILL_GAP_ANALYSIS,
  status: InsightStatus.READY,
  title: 'Skill Gap Analysis Report',
  summary: 'Comprehensive analysis of your skill gaps and learning opportunities',
  detailedAnalysis: 'Detailed analysis content here...',
  viewCount: 12,
  createdAt: '2023-10-01T10:00:00Z',
  updatedAt: '2023-10-01T10:00:00Z',
  validUntil: '2024-01-01T00:00:00Z',
  aiInsights: {
    strengths: ['Strong problem-solving skills', 'Good communication'],
    opportunities: ['Growing demand for cloud skills', 'Remote work trending'],
    challenges: ['Skill gap in modern frameworks', 'Need certifications'],
    recommendations: [
      {
        priority: 'HIGH' as const,
        action: 'You should learn React and modern frontend frameworks',
        expectedOutcome: 'Qualify for senior frontend roles',
        timeframe: '3 months',
      },
      {
        priority: 'MEDIUM' as const,
        action: 'You should pursue AWS certification',
        expectedOutcome: 'Increase cloud competency',
        timeframe: '2 months',
      },
    ],
    confidenceScore: 85,
  },
  skillGapAnalysis: {
    currentSkills: ['JavaScript', 'HTML', 'CSS'],
    targetRole: 'Senior Frontend Developer',
    skillGaps: [
      {
        skillName: 'React',
        currentLevel: 'BEGINNER' as const,
        targetLevel: 'ADVANCED' as const,
        priority: 'HIGH' as const,
        learningResources: [
          {
            title: 'React Official Documentation',
            type: 'TUTORIAL' as const,
            provider: 'React Team',
            url: 'https://react.dev',
            estimatedDuration: '2 weeks',
            cost: 'Free',
          },
          {
            title: 'Advanced React Course',
            type: 'COURSE' as const,
            provider: 'Udemy',
            url: 'https://udemy.com/react-course',
            estimatedDuration: '6 weeks',
            cost: '$49.99',
          },
        ],
        timeToAchieve: '3 months',
        marketDemand: 90,
      },
    ],
    estimatedTimeToClose: '6 months',
    overallReadinessScore: 65,
  },
};

const mockCareerPathInsight = {
  id: '2',
  jobSeekerId: 'job-seeker-1',
  type: InsightType.CAREER_PATH_RECOMMENDATION,
  status: InsightStatus.READY,
  title: 'Career Path Recommendations',
  summary: 'Personalized career paths based on your profile',
  detailedAnalysis: 'Career path analysis...',
  viewCount: 8,
  createdAt: '2023-10-02T10:00:00Z',
  updatedAt: '2023-10-02T10:00:00Z',
  aiInsights: {
    strengths: ['Technical background', 'Leadership potential'],
    opportunities: ['Management track available', 'Specialization options'],
    challenges: ['Need more experience', 'Market competition'],
    recommendations: [
      {
        priority: 'HIGH' as const,
        action: 'You should focus on leadership development',
        expectedOutcome: 'Prepare for management roles',
        timeframe: '6 months',
      },
    ],
    confidenceScore: 88,
  },
  careerPathRecommendation: {
    currentPosition: 'Mid-Level Developer',
    recommendedPaths: [
      {
        pathName: 'Technical Leadership Track',
        steps: [
          {
            title: 'Senior Developer',
            description: 'Technical expert and mentor',
            timeline: '1-2 years',
            requirements: ['5+ years experience', 'Mentoring skills'],
            averageSalaryRange: {
              min: 100000,
              max: 140000,
              currency: 'USD',
            },
            growthPotential: 'HIGH' as const,
            marketDemand: 85,
          },
        ],
        totalDuration: '2-3 years',
        difficultyLevel: 'MODERATE' as const,
        successProbability: 80,
      },
    ],
    immediateNextSteps: [
      'You should update your resume',
      'You should start mentoring junior developers',
      'You should learn system design',
    ],
  },
};

describe('CareerInsightDetailPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    (useParams as jest.Mock).mockReturnValue({
      id: '1',
    });
  });

  describe('Loading and Error States', () => {
    it('shows loading spinner when insight is being fetched', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: null,
        isLoading: true,
        fetchInsightById: mockFetchInsightById,
      });

      render(<CareerInsightDetailPage />);

      expect(screen.getByTestId('colourful-loader')).toBeInTheDocument();
    });

    it('calls fetchInsightById with correct ID on mount', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });

      render(<CareerInsightDetailPage />);

      expect(mockFetchInsightById).toHaveBeenCalledWith('1');
    });
  });

  describe('Skill Gap Analysis Display', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it('renders insight header with correct information', () => {
      render(<CareerInsightDetailPage />);

      expect(screen.getByText('Skill Gap Analysis Report')).toBeInTheDocument();
      expect(screen.getByText('12 views')).toBeInTheDocument();
      expect(screen.getByText(/Valid until/)).toBeInTheDocument();
    });

    it('displays summary card when summary exists', () => {
      render(<CareerInsightDetailPage />);

      expect(screen.getByText('Key Insights')).toBeInTheDocument();
      expect(
        screen.getByText('Comprehensive analysis of your skill gaps and learning opportunities')
      ).toBeInTheDocument();
    });

    it('renders tab navigation correctly', () => {
      render(<CareerInsightDetailPage />);

      expect(screen.getByText('Detailed Analysis')).toBeInTheDocument();
      expect(screen.getByText('Recommendations')).toBeInTheDocument();
      expect(screen.getByText('Skills Gap')).toBeInTheDocument();
    });

    it.skip('shows skills breakdown tab for skill gap analysis', async () => {
      render(<CareerInsightDetailPage />);

      const skillsTab = screen.getByText('Skills Gap');
      fireEvent.click(skillsTab);

      await waitFor(() => {
        expect(screen.getByText('Skills Analysis')).toBeInTheDocument();
        expect(screen.getByText('Overall Readiness')).toBeInTheDocument();
        expect(screen.getByText('65%')).toBeInTheDocument();
      });
    });

    it.skip('displays skill gaps with learning resources', async () => {
      render(<CareerInsightDetailPage />);

      const skillsTab = screen.getByText('Skills Gap');
      fireEvent.click(skillsTab);

      await waitFor(() => {
        expect(screen.getByText('React')).toBeInTheDocument();
        expect(screen.getByText('HIGH Priority')).toBeInTheDocument();
        expect(screen.getByText('Current: BEGINNER → Target: ADVANCED')).toBeInTheDocument();
        expect(screen.getByText('Market Demand:')).toBeInTheDocument();
        expect(screen.getByText('90%')).toBeInTheDocument();
      });
    });

    it.skip('shows learning resources for each skill gap', async () => {
      render(<CareerInsightDetailPage />);

      const skillsTab = screen.getByText('Skills Gap');
      fireEvent.click(skillsTab);

      await waitFor(() => {
        expect(screen.getByText('Learning Resources')).toBeInTheDocument();
        expect(screen.getByText('React Official Documentation')).toBeInTheDocument();
        expect(screen.getByText('Advanced React Course')).toBeInTheDocument();
        expect(screen.getByText('Free')).toBeInTheDocument();
        expect(screen.getByText('$49.99')).toBeInTheDocument();
      });
    });

    it.skip('opens external links when resource buttons are clicked', async () => {
      const mockOpen = jest.fn();
      Object.defineProperty(window, 'open', {
        writable: true,
        value: mockOpen,
      });

      render(<CareerInsightDetailPage />);

      const skillsTab = screen.getByText('Skills Gap');
      fireEvent.click(skillsTab);

      await waitFor(() => {
        const visitButtons = screen.getAllByText('Visit');
        fireEvent.click(visitButtons[0]);

        expect(mockOpen).toHaveBeenCalledWith('https://react.dev', '_blank');
      });
    });
  });

  describe('Career Path Recommendations Display', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockCareerPathInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it.skip('shows career paths tab for career path insights', async () => {
      render(<CareerInsightDetailPage />);

      const careerPathsTab = screen.getByText('Career Paths');
      fireEvent.click(careerPathsTab);

      await waitFor(() => {
        expect(screen.getByTestId('career-path-display')).toBeInTheDocument();
        expect(screen.getByText('Current: Mid-Level Developer')).toBeInTheDocument();
        expect(screen.getByText('Paths: 1')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation and Actions', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it('navigates back to insights list when back button is clicked', () => {
      render(<CareerInsightDetailPage />);

      const backButton = screen.getByText('Back to Insights');
      fireEvent.click(backButton);

      expect(mockPush).toHaveBeenCalledWith('/career-insights');
    });

    it('handles share functionality', () => {
      const mockShare = jest.fn();
      const mockClipboard = {
        writeText: jest.fn(),
      };

      Object.defineProperty(navigator, 'share', {
        writable: true,
        value: mockShare,
      });

      Object.defineProperty(navigator, 'clipboard', {
        writable: true,
        value: mockClipboard,
      });

      render(<CareerInsightDetailPage />);

      const shareButtons = screen.getAllByRole('button');
      const shareButton = shareButtons.find(
        button => button.querySelector('svg') && button.getAttribute('title') === 'Share'
      );

      if (shareButton) {
        fireEvent.click(shareButton);
        expect(mockShare).toHaveBeenCalled();
      }
    });

    it('shows PDF download functionality', () => {
      render(<CareerInsightDetailPage />);

      expect(screen.getByTestId('pdf-download')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it.skip('switches between tabs correctly', async () => {
      render(<CareerInsightDetailPage />);

      // Default should be analysis tab
      expect(screen.getByTestId('analysis-renderer')).toBeInTheDocument();

      // Switch to recommendations tab
      const recommendationsTab = screen.getByText('Recommendations');
      fireEvent.click(recommendationsTab);

      await waitFor(() => {
        expect(screen.getByTestId('modern-recommendations')).toBeInTheDocument();
        expect(screen.getByText('Recommendations: 2')).toBeInTheDocument();
        expect(screen.getByText('Confidence: 85%')).toBeInTheDocument();
      });
    });

    it.skip('maintains tab state when switching', async () => {
      render(<CareerInsightDetailPage />);

      const skillsTab = screen.getByText('Skills Gap');
      fireEvent.click(skillsTab);

      await waitFor(() => {
        expect(screen.getByText('Skills Analysis')).toBeInTheDocument();
      });

      const recommendationsTab = screen.getByText('Recommendations');
      fireEvent.click(recommendationsTab);

      await waitFor(() => {
        expect(screen.getByTestId('modern-recommendations')).toBeInTheDocument();
      });

      // Switch back to skills tab
      fireEvent.click(skillsTab);

      await waitFor(() => {
        expect(screen.getByText('Skills Analysis')).toBeInTheDocument();
      });
    });
  });

  describe('Different Insight Types', () => {
    it('shows appropriate tabs for different insight types', () => {
      // Test skill gap analysis
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });

      const { rerender } = render(<CareerInsightDetailPage />);

      expect(screen.getByText('Skills Gap')).toBeInTheDocument();

      // Test career path recommendations
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockCareerPathInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });

      rerender(<CareerInsightDetailPage />);

      expect(screen.getByText('Career Paths')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it('renders properly on mobile devices', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<CareerInsightDetailPage />);

      expect(screen.getByText('Skill Gap Analysis Report')).toBeInTheDocument();
    });

    it('renders properly on desktop', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });

      render(<CareerInsightDetailPage />);

      expect(screen.getByText('Skill Gap Analysis Report')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: mockSkillGapInsight,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });
    });

    it('has proper heading structure', () => {
      render(<CareerInsightDetailPage />);

      expect(
        screen.getByRole('heading', { name: /Skill Gap Analysis Report/i })
      ).toBeInTheDocument();
    });

    it('has accessible tab navigation', () => {
      render(<CareerInsightDetailPage />);

      const tabs = screen.getAllByRole('tab');
      expect(tabs.length).toBeGreaterThan(0);

      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });

    it('supports keyboard navigation', () => {
      render(<CareerInsightDetailPage />);

      const firstTab = screen.getAllByRole('tab')[0];
      firstTab.focus();

      expect(document.activeElement).toBe(firstTab);
    });
  });

  describe('Error Handling', () => {
    it('handles missing insight data gracefully', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: {
          id: '1',
          type: InsightType.SKILL_GAP_ANALYSIS,
          status: InsightStatus.READY,
          title: 'Incomplete Insight',
          // Missing required fields
        },
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });

      expect(() => render(<CareerInsightDetailPage />)).not.toThrow();
    });

    it('handles fetch error gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockFetchInsightById.mockRejectedValue(new Error('Network error'));

      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        currentInsight: null,
        isLoading: false,
        fetchInsightById: mockFetchInsightById,
      });

      render(<CareerInsightDetailPage />);

      expect(mockFetchInsightById).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});
