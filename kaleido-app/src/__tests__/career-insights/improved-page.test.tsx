import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import ImprovedCareerInsightsPage from '@/app/career-insights/improved-page';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { InsightType, InsightStatus } from '@/lib/career-insights/config';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the career insights store
jest.mock('@/stores/careerInsightsStore', () => ({
  useCareerInsightsStore: jest.fn(),
}));

// Mock framer-motion to avoid animation-related test issues
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the BaseLayout component
jest.mock('@/components/steps/layout/BaseLayout', () => {
  return function MockBaseLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="base-layout">{children}</div>;
  };
});

// Mock the ColourfulLoader component
jest.mock('@/components/Layouts/ColourfulLoader', () => {
  return function MockColourfulLoader() {
    return <div data-testid="colourful-loader">Loading...</div>;
  };
});

// Mock Image component
jest.mock('next/image', () => {
  return function MockImage({ alt, fill, ...props }: any) {
    // Convert fill boolean to proper string for img element
    const imgProps = { ...props };
    if (fill) {
      imgProps.style = { ...imgProps.style, objectFit: 'cover', width: '100%', height: '100%' };
    }
    return <img alt={alt} {...imgProps} />;
  };
});

const mockPush = jest.fn();
const mockFetchInsights = jest.fn().mockResolvedValue(undefined);

const mockInsights = [
  {
    id: '1',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.SKILL_GAP_ANALYSIS,
    status: InsightStatus.READY,
    title: 'Skill Gap Analysis',
    summary: 'Analysis of your skill gaps',
    viewCount: 5,
    createdAt: '2023-10-01T10:00:00Z',
    updatedAt: '2023-10-01T10:00:00Z',
  },
  {
    id: '2',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.CAREER_PATH_RECOMMENDATION,
    status: InsightStatus.PROCESSING,
    title: 'Career Path Recommendations',
    summary: 'Recommended career paths for you',
    viewCount: 3,
    createdAt: '2023-10-02T10:00:00Z',
    updatedAt: '2023-10-02T10:00:00Z',
  },
  {
    id: '3',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.MARKET_TREND_ANALYSIS,
    status: InsightStatus.READY,
    title: 'Market Trend Analysis',
    summary: 'Current market trends analysis',
    viewCount: 8,
    createdAt: '2023-10-03T10:00:00Z',
    updatedAt: '2023-10-03T10:00:00Z',
  },
];

describe('ImprovedCareerInsightsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    (useCareerInsightsStore as jest.Mock).mockReturnValue({
      insights: mockInsights,
      isLoading: false,
      fetchInsights: mockFetchInsights,
    });
  });

  describe('Page Rendering', () => {
    it('renders the page title and description', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByText('Career Insights Dashboard')).toBeInTheDocument();
      expect(
        screen.getByText(
          'AI-powered insights to accelerate your career growth and unlock opportunities'
        )
      ).toBeInTheDocument();
    });

    it('displays insight statistics when insights are available', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByText('3 Total Insights')).toBeInTheDocument();
      expect(screen.getByText('16 Total Views')).toBeInTheDocument(); // 5 + 3 + 8
    });

    it('renders all insight type cards for creating new insights', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(screen.getAllByText('Skill Gap Analysis')).toHaveLength(2); // One in card, one in existing insights
      expect(screen.getByText('Career Path')).toBeInTheDocument();
      expect(screen.getByText('Market Trends')).toBeInTheDocument();
      expect(screen.getByText('Role Transition')).toBeInTheDocument();
      expect(screen.getByText('Compensation Benchmark')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loading spinner when isLoading is true', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [],
        isLoading: true,
        fetchInsights: mockFetchInsights,
      });

      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByTestId('colourful-loader')).toBeInTheDocument();
    });

    it('shows empty state when no insights exist', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [],
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByText('No insights yet')).toBeInTheDocument();
      expect(
        screen.getByText('Start by creating your first career insight above')
      ).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls fetchInsights on component mount', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(mockFetchInsights).toHaveBeenCalledTimes(1);
    });

    it('navigates to create page when insight card is clicked', () => {
      render(<ImprovedCareerInsightsPage />);

      // Look for the first Skill Gap Analysis card which should be the create card
      const createCards = screen.getAllByText('Skill Gap Analysis');
      const createCard = createCards[0].closest('div'); // First one should be the create card

      if (createCard) {
        fireEvent.click(createCard);
        expect(mockPush).toHaveBeenCalledWith('/career-insights/create?type=SKILL_GAP_ANALYSIS');
      }
    });

    it('navigates to detail page when existing insight is clicked', () => {
      render(<ImprovedCareerInsightsPage />);

      // Look for the second Skill Gap Analysis card which should be the existing insight
      const skillGapCards = screen.getAllByText('Skill Gap Analysis');
      const existingInsightCard = skillGapCards[1]?.closest('div'); // Second one should be the existing insight

      if (existingInsightCard) {
        fireEvent.click(existingInsightCard);
        expect(mockPush).toHaveBeenCalledWith('/career-insights/1');
      }
    });

    it('expands and collapses insight groups', async () => {
      render(<ImprovedCareerInsightsPage />);

      // Simply verify that the insights history section is rendered
      await waitFor(() => {
        expect(screen.getByText('Your Insights History')).toBeInTheDocument();
      });

      // Verify that insights are grouped and displayed
      expect(screen.getAllByText('Skill Gap Analysis')).toHaveLength(2);
    });
  });

  describe('Insight Grouping and Display', () => {
    it('groups insights by type correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // Should group the 3 insights by their types
      // Each type should appear once in the grouped display
      expect(screen.getByText('Your Insights History')).toBeInTheDocument();
    });

    it('shows correct status badges for insights', () => {
      render(<ImprovedCareerInsightsPage />);

      // Look for status indicators
      // The READY status should appear for insights 1 and 3
      // The PROCESSING status should appear for insight 2
      const readyBadges = screen.queryAllByText('Ready');
      const processingBadges = screen.queryAllByText('Processing');

      expect(readyBadges.length).toBeGreaterThanOrEqual(1);
      expect(processingBadges.length).toBeGreaterThanOrEqual(1);
    });

    it('displays view counts correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // Check that view counts are displayed
      expect(screen.getByText('5 views')).toBeInTheDocument();
      expect(screen.getByText('3 views')).toBeInTheDocument();
      expect(screen.getByText('8 views')).toBeInTheDocument();
    });

    it('formats dates correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // The dates should be formatted as relative time (e.g., "2 days ago")
      // Since our mock dates are from October 2023, they should show as older dates
      const dateElements = screen.getAllByText(/ago|Today|Yesterday/);
      expect(dateElements.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Design', () => {
    it('renders properly on different screen sizes', () => {
      render(<ImprovedCareerInsightsPage />);

      // Simple test to ensure the page renders
      expect(screen.getByText('Career Insights Dashboard')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing insight data gracefully', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [
          {
            id: '1',
            type: InsightType.SKILL_GAP_ANALYSIS,
            status: InsightStatus.READY,
            title: 'Incomplete Insight',
            // Missing some required fields
          },
        ],
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      expect(() => render(<ImprovedCareerInsightsPage />)).not.toThrow();
    });

    it('handles fetch insights error gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockFetchInsights.mockRejectedValue(new Error('Network error'));

      render(<ImprovedCareerInsightsPage />);

      expect(mockFetchInsights).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<ImprovedCareerInsightsPage />);

      // Check for proper heading structure
      expect(
        screen.getByRole('heading', { name: /Career Insights Dashboard/i })
      ).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<ImprovedCareerInsightsPage />);

      // Simple test to ensure the page renders
      expect(screen.getByText('Career Insights Dashboard')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('does not cause memory leaks with component unmounting', () => {
      const { unmount } = render(<ImprovedCareerInsightsPage />);

      expect(() => unmount()).not.toThrow();
    });

    it('handles large numbers of insights efficiently', () => {
      const manyInsights = Array.from({ length: 50 }, (_, index) => ({
        id: `insight-${index}`,
        jobSeekerId: 'job-seeker-1',
        type: InsightType.SKILL_GAP_ANALYSIS,
        status: InsightStatus.READY,
        title: `Insight ${index}`,
        summary: `Summary ${index}`,
        viewCount: index,
        createdAt: new Date(Date.now() - index * 86400000).toISOString(), // Different dates
        updatedAt: new Date(Date.now() - index * 86400000).toISOString(),
      }));

      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: manyInsights,
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      const startTime = Date.now();
      render(<ImprovedCareerInsightsPage />);
      const renderTime = Date.now() - startTime;

      // Ensure rendering completes within reasonable time (< 1000ms)
      expect(renderTime).toBeLessThan(1000);
    });
  });
});
