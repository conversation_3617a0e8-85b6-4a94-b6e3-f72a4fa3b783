import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { useRouter } from 'next/router';
import '@testing-library/jest-dom';

import CompanyOnboardingPage from '@/pages/company-onboarding';
import { useAuthStore } from '@/stores/authStore';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { useCompanyStore } from '@/stores/companyStore';
import { apiClient } from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';

// Mock modules
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/stores/authStore', () => ({
  useAuthStore: jest.fn(),
}));

jest.mock('@/stores/onboardingStore', () => ({
  useOnboardingStore: jest.fn(),
}));

jest.mock('@/stores/companyStore', () => ({
  useCompanyStore: jest.fn(),
}));

jest.mock('@/lib/apiHelper', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
  },
}));

jest.mock('@/hooks/useAuthSync', () => ({
  useAuthSync: jest.fn(),
}));

jest.mock('@/lib/apiHelper.utils', () => ({
  setOnboardingContext: jest.fn(),
  clearOnboardingContext: jest.fn(),
}));

jest.mock('@/components/Toaster', () => ({
  showToast: jest.fn(),
}));

jest.mock('@/components/CompanySetup/CompanySetupSlider', () => ({
  CompanySetupSlider: ({ onSubmit, isOpen, initialData }: any) => (
    <div data-testid="company-setup-slider">
      {isOpen && (
        <button
          data-testid="complete-setup"
          onClick={async () => {
            await onSubmit({
              ...initialData,
              companyName: 'Test Company',
              companyWebsite: 'https://test.com',
              contactEmail: '<EMAIL>',
              industry: 'Technology',
              size: '11-50 employees',
            });
          }}
        >
          Complete Setup
        </button>
      )}
    </div>
  ),
}));

describe('Company Onboarding Prevention Tests', () => {
  const mockRouter = {
    query: {},
    replace: jest.fn(),
    push: jest.fn(),
    pathname: '/company-onboarding',
  };

  const mockAuthStore = {
    isAuthenticated: true,
    isLoading: false,
    isInitialized: true,
    session: {
      user: {
        sub: 'test-employer-123',
        email: '<EMAIL>',
      },
    },
  };

  const mockOnboardingStore = {
    markCompanyOnboardingComplete: jest.fn(),
    setUserRole: jest.fn(),
    getCompanyNeedsOnboarding: jest.fn(),
    hasCheckedOnboarding: true,
  };

  const mockCompanyStore = {
    company: null,
    fetchCompany: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useAuthStore as jest.Mock).mockReturnValue(mockAuthStore);
    (useOnboardingStore as jest.Mock).mockReturnValue(mockOnboardingStore);
    (useCompanyStore as jest.Mock).mockReturnValue(mockCompanyStore);

    // Reset cookies
    document.cookie = '';

    // Mock localStorage
    const localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
  });

  describe('Cookie-based Prevention', () => {
    test('should redirect to dashboard immediately if onboarding cookie exists', async () => {
      // Set the onboarding complete cookie before rendering
      document.cookie = `onboardingComplete_${mockAuthStore.session.user.sub}=true; path=/`;

      render(<CompanyOnboardingPage />);

      // Should redirect immediately
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard');
      });

      // The redirect should have been called
      expect(mockRouter.replace).toHaveBeenCalledTimes(1);
    });

    test('should set onboarding cookie when completing setup', async () => {
      const postSpy = jest.fn().mockResolvedValue({ id: 'new-company-id' });
      (apiClient.post as jest.Mock) = postSpy;

      render(<CompanyOnboardingPage />);

      // Wait for the slider to appear
      await waitFor(() => {
        expect(screen.getByTestId('company-setup-slider')).toBeInTheDocument();
      });

      // Complete the setup
      const completeButton = screen.getByTestId('complete-setup');
      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Wait for API call to complete
      await waitFor(() => {
        expect(postSpy).toHaveBeenCalled();
      });

      // Verify the redirect happened (which indicates success)
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard');
      });

      // Verify the onboarding complete was marked in the store
      expect(mockOnboardingStore.markCompanyOnboardingComplete).toHaveBeenCalled();
    });
  });

  describe('Backend Field Setting', () => {
    test('should send onboardingRequired: false when completing setup', async () => {
      const postSpy = jest.fn().mockResolvedValue({ id: 'new-company-id' });
      (apiClient.post as jest.Mock) = postSpy;

      render(<CompanyOnboardingPage />);

      // Complete the setup
      const completeButton = await screen.findByTestId('complete-setup');
      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Verify the API was called with onboardingRequired: false
      expect(postSpy).toHaveBeenCalledWith(
        '/companies',
        expect.objectContaining({
          onboardingRequired: false,
          onboardingComplete: true,
        })
      );
    });

    test('should update existing company with onboardingRequired: false', async () => {
      const putSpy = jest.fn().mockResolvedValue({ id: 'existing-company-id' });
      (apiClient.put as jest.Mock) = putSpy;

      // Mock the stores to have existing company
      const mockCompanyWithId = {
        ...mockCompanyStore,
        company: {
          id: 'existing-company-id',
          companyName: 'Existing Company',
          onboardingRequired: true,
        },
        fetchCompany: jest.fn().mockResolvedValue({
          id: 'existing-company-id',
          companyName: 'Existing Company',
          onboardingRequired: true,
        }),
      };
      (useCompanyStore as jest.Mock).mockReturnValue(mockCompanyWithId);

      const mockOnboardingWithCompany = {
        ...mockOnboardingStore,
        company: {
          id: 'existing-company-id',
          companyName: 'Existing Company',
          onboardingRequired: true,
        },
        getCompanyNeedsOnboarding: jest.fn().mockReturnValue(true),
      };
      (useOnboardingStore as jest.Mock).mockReturnValue(mockOnboardingWithCompany);

      render(<CompanyOnboardingPage />);

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByTestId('company-setup-slider')).toBeInTheDocument();
      });

      // Complete the setup
      const completeButton = screen.getByTestId('complete-setup');
      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Wait for API call
      await waitFor(() => {
        expect(putSpy).toHaveBeenCalled();
      });

      // Verify the API was called with onboardingRequired: false
      expect(putSpy).toHaveBeenCalledWith(
        '/companies/existing-company-id',
        expect.objectContaining({
          onboardingRequired: false,
          onboardingComplete: true,
        })
      );
    });
  });

  describe('Cache Clearing', () => {
    test('should clear company cache after completing onboarding', async () => {
      const postSpy = jest.fn().mockResolvedValue({ id: 'new-company-id' });
      (apiClient.post as jest.Mock) = postSpy;

      render(<CompanyOnboardingPage />);

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByTestId('company-setup-slider')).toBeInTheDocument();
      });

      // Complete the setup
      const completeButton = screen.getByTestId('complete-setup');
      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Wait for API call to complete
      await waitFor(() => {
        expect(postSpy).toHaveBeenCalled();
      });

      // Verify cache was cleared
      await waitFor(() => {
        expect(localStorage.removeItem).toHaveBeenCalledWith('api_cache_/companies/client');
        expect(localStorage.removeItem).toHaveBeenCalledWith('recent_fetch_/companies/client');
        expect(localStorage.removeItem).toHaveBeenCalledWith('pendingCompanyData');
      });
    });
  });

  describe('Flow Prevention', () => {
    test('should not show onboarding if company.onboardingRequired is false', async () => {
      // Mock company with onboarding not required
      mockCompanyStore.fetchCompany.mockResolvedValue({
        id: '1',
        companyName: 'Completed Company',
        onboardingRequired: false,
      });

      mockOnboardingStore.getCompanyNeedsOnboarding.mockReturnValue(false);

      render(<CompanyOnboardingPage />);

      // Should redirect to dashboard
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard');
      });

      // Should not show the setup slider
      expect(screen.queryByTestId('company-setup-slider')).not.toBeInTheDocument();
    });

    test('should handle multiple rapid renders without showing onboarding multiple times', async () => {
      // Set cookie to indicate completion
      document.cookie = `onboardingComplete_${mockAuthStore.session.user.sub}=true; path=/`;

      const { rerender } = render(<CompanyOnboardingPage />);

      // Rerender multiple times rapidly
      rerender(<CompanyOnboardingPage />);
      rerender(<CompanyOnboardingPage />);
      rerender(<CompanyOnboardingPage />);

      // Should only redirect once
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledTimes(1);
        expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard');
      });
    });
  });

  describe('Error Handling', () => {
    test('should not set cookie or redirect on API error', async () => {
      (apiClient.post as jest.Mock).mockRejectedValue(new Error('API Error'));

      // Ensure the onboarding store indicates that onboarding is needed
      const mockOnboardingNeedsSetup = {
        ...mockOnboardingStore,
        getCompanyNeedsOnboarding: jest.fn().mockReturnValue(true),
        hasCheckedOnboarding: true,
        company: null,
        fetchCompany: jest.fn().mockResolvedValue(null),
      };
      (useOnboardingStore as jest.Mock).mockReturnValue(mockOnboardingNeedsSetup);

      // Ensure company store also returns null
      const mockCompanyWithNoData = {
        ...mockCompanyStore,
        company: null,
        fetchCompany: jest.fn().mockResolvedValue(null),
      };
      (useCompanyStore as jest.Mock).mockReturnValue(mockCompanyWithNoData);

      render(<CompanyOnboardingPage />);

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByTestId('company-setup-slider')).toBeInTheDocument();
      });

      // Complete the setup
      const completeButton = screen.getByTestId('complete-setup');
      await act(async () => {
        fireEvent.click(completeButton);
      });

      // Wait for the error to be handled
      await waitFor(() => {
        expect(apiClient.post).toHaveBeenCalled();
      });

      // Give some time for error handling
      await new Promise(resolve => setTimeout(resolve, 100));

      // Should not redirect on error
      expect(mockRouter.replace).not.toHaveBeenCalled();

      // Should not mark onboarding as complete on error
      expect(mockOnboardingStore.markCompanyOnboardingComplete).not.toHaveBeenCalled();
    });
  });
});
