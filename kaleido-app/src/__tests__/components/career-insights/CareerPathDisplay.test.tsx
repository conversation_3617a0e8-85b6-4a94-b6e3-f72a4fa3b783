import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CareerPathDisplay from '@/components/career-insights/CareerPathDisplay';
import { CareerPath } from '@/stores/careerInsightsStore';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

const mockCareerPaths: CareerPath[] = [
  {
    pathName: 'Technical Leadership Track',
    steps: [
      {
        title: 'Senior Developer',
        description: 'Lead technical decisions and mentor junior developers',
        timeline: '1-2 years',
        requirements: ['5+ years experience', 'Leadership skills', 'System design knowledge'],
        averageSalaryRange: {
          min: 100000,
          max: 140000,
          currency: 'USD',
        },
        growthPotential: 'HIGH',
        marketDemand: 90,
      },
      {
        title: 'Tech Lead',
        description: 'Manage technical teams and drive architecture decisions',
        timeline: '2-3 years',
        requirements: ['7+ years experience', 'Team management', 'Strategic thinking'],
        averageSalaryRange: {
          min: 140000,
          max: 180000,
          currency: 'USD',
        },
        growthPotential: 'HIGH',
        marketDemand: 85,
      },
    ],
    totalDuration: '3-5 years',
    difficultyLevel: 'MODERATE',
    successProbability: 80,
  },
  {
    pathName: 'Product Management Track',
    steps: [
      {
        title: 'Associate Product Manager',
        description: 'Support product strategy and development',
        timeline: '6 months - 1 year',
        requirements: ['Technical background', 'User empathy', 'Communication skills'],
        averageSalaryRange: {
          min: 80000,
          max: 110000,
          currency: 'USD',
        },
        growthPotential: 'MEDIUM',
        marketDemand: 75,
      },
    ],
    totalDuration: '2-3 years',
    difficultyLevel: 'DIFFICULT',
    successProbability: 65,
  },
];

const mockImmediateSteps = [
  'You should update your resume with recent achievements',
  'You should start building a portfolio of leadership examples',
  'You should begin networking with senior professionals',
  'You should take on more mentoring responsibilities',
  'You should learn system design principles',
  'You should improve your presentation skills',
];

describe('CareerPathDisplay', () => {
  describe('Rendering', () => {
    it('renders with career paths and immediate steps', () => {
      render(
        <CareerPathDisplay
          paths={mockCareerPaths}
          currentPosition="Mid-Level Developer"
          immediateSteps={mockImmediateSteps}
        />
      );

      expect(screen.getByText('Recommended Career Paths')).toBeInTheDocument();
      expect(screen.getByText('2 paths tailored to your profile')).toBeInTheDocument();
      expect(screen.getByText('Starting from: Mid-Level Developer')).toBeInTheDocument();
    });

    it('renders nothing when paths array is empty', () => {
      const { container } = render(<CareerPathDisplay paths={[]} />);

      expect(container.firstChild).toBeNull();
    });

    it('renders nothing when paths is null/undefined', () => {
      const { container } = render(<CareerPathDisplay paths={null as any} />);

      expect(container.firstChild).toBeNull();
    });

    it('applies custom className correctly', () => {
      const { container } = render(
        <CareerPathDisplay paths={mockCareerPaths} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Immediate Steps Section', () => {
    it('displays immediate steps when provided', () => {
      render(<CareerPathDisplay paths={mockCareerPaths} immediateSteps={mockImmediateSteps} />);

      expect(screen.getByText('Quick Wins - Start Today')).toBeInTheDocument();
      expect(
        screen.getByText('You should update your resume with recent achievements')
      ).toBeInTheDocument();
      expect(
        screen.getByText('You should start building a portfolio of leadership examples')
      ).toBeInTheDocument();
    });

    it('limits immediate steps to 6 items', () => {
      const manySteps = Array.from({ length: 10 }, (_, i) => `Step ${i + 1}`);

      render(<CareerPathDisplay paths={mockCareerPaths} immediateSteps={manySteps} />);

      // Should only show first 6 steps
      expect(screen.getByText('Step 1')).toBeInTheDocument();
      expect(screen.getByText('Step 6')).toBeInTheDocument();
      expect(screen.queryByText('Step 7')).not.toBeInTheDocument();
    });

    it('does not render immediate steps section when array is empty', () => {
      render(<CareerPathDisplay paths={mockCareerPaths} immediateSteps={[]} />);

      expect(screen.queryByText('Quick Wins - Start Today')).not.toBeInTheDocument();
    });
  });

  describe('Career Path Cards', () => {
    beforeEach(() => {
      render(<CareerPathDisplay paths={mockCareerPaths} currentPosition="Mid-Level Developer" />);
    });

    it('displays all career path names', () => {
      expect(screen.getByText('Technical Leadership Track')).toBeInTheDocument();
      expect(screen.getByText('Product Management Track')).toBeInTheDocument();
    });

    it('displays difficulty levels with correct styling', () => {
      expect(screen.getByText('Moderate Effort')).toBeInTheDocument();
      expect(screen.getByText('Challenging Path')).toBeInTheDocument();
    });

    it('displays path metadata correctly', () => {
      expect(screen.getByText('3-5 years')).toBeInTheDocument();
      expect(screen.getByText('80% success rate')).toBeInTheDocument();
      expect(screen.getByText('2 steps')).toBeInTheDocument();

      expect(screen.getByText('2-3 years')).toBeInTheDocument();
      expect(screen.getByText('65% success rate')).toBeInTheDocument();
      expect(screen.getByText('1 steps')).toBeInTheDocument();
    });

    it('displays success probability progress bars', () => {
      const progressBars = screen.getAllByRole('progressbar');
      expect(progressBars).toHaveLength(2); // One for each path
    });
  });

  describe('Path Expansion', () => {
    beforeEach(() => {
      render(<CareerPathDisplay paths={mockCareerPaths} currentPosition="Mid-Level Developer" />);
    });

    it('expands path details when header is clicked', async () => {
      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');

      fireEvent.click(pathHeader!);

      await waitFor(() => {
        expect(screen.getByText('Career Journey Steps')).toBeInTheDocument();
        expect(screen.getByText('Senior Developer')).toBeInTheDocument();
        expect(screen.getByText('Tech Lead')).toBeInTheDocument();
      });
    });

    it('collapses path when clicked again', async () => {
      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');

      // Expand
      fireEvent.click(pathHeader!);
      await waitFor(() => {
        expect(screen.getByText('Career Journey Steps')).toBeInTheDocument();
      });

      // Collapse
      fireEvent.click(pathHeader!);
      await waitFor(() => {
        expect(screen.queryByText('Career Journey Steps')).not.toBeInTheDocument();
      });
    });

    it('can have multiple paths expanded simultaneously', async () => {
      const techPath = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');
      const productPath = screen
        .getByText('Product Management Track')
        .closest('div[class*="cursor-pointer"]');

      fireEvent.click(techPath!);
      fireEvent.click(productPath!);

      await waitFor(() => {
        // Both paths should show their journey steps
        const journeyStepsHeadings = screen.getAllByText('Career Journey Steps');
        expect(journeyStepsHeadings).toHaveLength(2);
      });
    });
  });

  describe('Career Steps Display', () => {
    beforeEach(async () => {
      render(<CareerPathDisplay paths={mockCareerPaths} currentPosition="Mid-Level Developer" />);

      // Expand the first path
      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');
      fireEvent.click(pathHeader!);

      await waitFor(() => {
        expect(screen.getByText('Career Journey Steps')).toBeInTheDocument();
      });
    });

    it('displays step numbers correctly', () => {
      expect(screen.getByText('1')).toBeInTheDocument(); // Senior Developer step
      expect(screen.getByText('2')).toBeInTheDocument(); // Tech Lead step
    });

    it('displays step information correctly', () => {
      expect(screen.getByText('Senior Developer')).toBeInTheDocument();
      expect(
        screen.getByText('Lead technical decisions and mentor junior developers')
      ).toBeInTheDocument();
      expect(screen.getByText('1-2 years')).toBeInTheDocument();
    });

    it('displays salary ranges correctly', () => {
      expect(screen.getByText('$100,000 - $140,000')).toBeInTheDocument();
      expect(screen.getByText('$140,000 - $180,000')).toBeInTheDocument();
    });

    it('displays market demand with progress bars', () => {
      // Market demand should be shown as progress bars
      const demandElements = screen.getAllByText(/90%|85%/);
      expect(demandElements.length).toBeGreaterThan(0);
    });

    it('displays growth potential icons', () => {
      // Growth potential should be indicated with icons
      expect(screen.getAllByText(/high growth potential/i)).toHaveLength(2);
    });
  });

  describe('Step Requirements Expansion', () => {
    beforeEach(async () => {
      render(<CareerPathDisplay paths={mockCareerPaths} currentPosition="Mid-Level Developer" />);

      // Expand the first path
      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');
      fireEvent.click(pathHeader!);

      await waitFor(() => {
        expect(screen.getByText('Career Journey Steps')).toBeInTheDocument();
      });
    });

    it('shows requirements count', () => {
      expect(screen.getAllByText('3 requirements')).toHaveLength(2); // Both Senior Developer and Tech Lead have 3 requirements
    });

    it('expands requirements when step is clicked', async () => {
      const seniorDevStep = screen
        .getByText('Senior Developer')
        .closest('div[class*="cursor-pointer"]');

      fireEvent.click(seniorDevStep!);

      await waitFor(() => {
        expect(screen.getByText('Key Requirements:')).toBeInTheDocument();
        expect(screen.getByText('5+ years experience')).toBeInTheDocument();
        expect(screen.getByText('Leadership skills')).toBeInTheDocument();
        expect(screen.getByText('System design knowledge')).toBeInTheDocument();
      });
    });

    it('collapses requirements when step is clicked again', async () => {
      const seniorDevStep = screen
        .getByText('Senior Developer')
        .closest('div[class*="cursor-pointer"]');

      // Expand
      fireEvent.click(seniorDevStep!);
      await waitFor(() => {
        expect(screen.getByText('Key Requirements:')).toBeInTheDocument();
      });

      // Collapse
      fireEvent.click(seniorDevStep!);
      await waitFor(() => {
        expect(screen.queryByText('Key Requirements:')).not.toBeInTheDocument();
      });
    });
  });

  describe('Difficulty Level Styling', () => {
    it('applies correct styling for EASY difficulty', () => {
      const easyPath: CareerPath[] = [
        {
          ...mockCareerPaths[0],
          difficultyLevel: 'EASY',
        },
      ];

      render(<CareerPathDisplay paths={easyPath} />);

      expect(screen.getByText('Easy Transition')).toBeInTheDocument();
    });

    it('applies correct styling for MODERATE difficulty', () => {
      render(<CareerPathDisplay paths={mockCareerPaths} />);

      expect(screen.getByText('Moderate Effort')).toBeInTheDocument();
    });

    it('applies correct styling for DIFFICULT difficulty', () => {
      render(<CareerPathDisplay paths={mockCareerPaths} />);

      expect(screen.getByText('Challenging Path')).toBeInTheDocument();
    });
  });

  describe('Salary Formatting', () => {
    it('formats USD currency correctly', () => {
      render(<CareerPathDisplay paths={mockCareerPaths} />);

      // Expand path to see salary information
      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');
      fireEvent.click(pathHeader!);

      waitFor(() => {
        expect(screen.getByText('$100,000 - $140,000')).toBeInTheDocument();
        expect(screen.getByText('$140,000 - $180,000')).toBeInTheDocument();
      });
    });

    it('handles different currencies', () => {
      const euroPaths: CareerPath[] = [
        {
          ...mockCareerPaths[0],
          steps: [
            {
              ...mockCareerPaths[0].steps[0],
              averageSalaryRange: {
                min: 80000,
                max: 120000,
                currency: 'EUR',
              },
            },
          ],
        },
      ];

      render(<CareerPathDisplay paths={euroPaths} />);

      const pathHeader = screen
        .getByText('Technical Leadership Track')
        .closest('div[class*="cursor-pointer"]');
      fireEvent.click(pathHeader!);

      waitFor(() => {
        expect(screen.getByText('€80,000 - €120,000')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      render(
        <CareerPathDisplay
          paths={mockCareerPaths}
          currentPosition="Mid-Level Developer"
          immediateSteps={mockImmediateSteps}
        />
      );
    });

    it('has proper heading structure', () => {
      expect(
        screen.getByRole('heading', { name: /Recommended Career Paths/i })
      ).toBeInTheDocument();
    });

    it('has accessible progress bars', () => {
      const progressBars = screen.getAllByRole('progressbar');
      progressBars.forEach(bar => {
        expect(bar).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation for expandable elements', () => {
      // Simple test to verify the page renders properly
      expect(screen.getByText('Technical Leadership Track')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles paths with no steps', () => {
      const emptyStepsPath: CareerPath[] = [
        {
          pathName: 'Empty Path',
          steps: [],
          totalDuration: '1 year',
          difficultyLevel: 'EASY',
          successProbability: 50,
        },
      ];

      expect(() => render(<CareerPathDisplay paths={emptyStepsPath} />)).not.toThrow();
      expect(screen.getByText('Empty Path')).toBeInTheDocument();
      expect(screen.getByText('0 steps')).toBeInTheDocument();
    });

    it('handles very long path names', () => {
      const longNamePath: CareerPath[] = [
        {
          pathName:
            'This is a very long career path name that should be handled gracefully by the component layout system',
          steps: mockCareerPaths[0].steps,
          totalDuration: '5 years',
          difficultyLevel: 'MODERATE',
          successProbability: 75,
        },
      ];

      expect(() => render(<CareerPathDisplay paths={longNamePath} />)).not.toThrow();
      expect(screen.getByText(/This is a very long career path name/)).toBeInTheDocument();
    });

    it('handles zero success probability', () => {
      const zeroSuccessPath: CareerPath[] = [
        {
          ...mockCareerPaths[0],
          successProbability: 0,
        },
      ];

      render(<CareerPathDisplay paths={zeroSuccessPath} />);

      expect(screen.getByText('0% success rate')).toBeInTheDocument();
    });

    it('handles 100% success probability', () => {
      const perfectSuccessPath: CareerPath[] = [
        {
          ...mockCareerPaths[0],
          successProbability: 100,
        },
      ];

      render(<CareerPathDisplay paths={perfectSuccessPath} />);

      expect(screen.getByText('100% success rate')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('handles large numbers of career paths efficiently', () => {
      const manyPaths: CareerPath[] = Array.from({ length: 20 }, (_, index) => ({
        pathName: `Career Path ${index}`,
        steps: [
          {
            title: `Step ${index}`,
            description: `Description ${index}`,
            timeline: '1 year',
            requirements: [`Requirement ${index}`],
            averageSalaryRange: {
              min: 50000 + index * 1000,
              max: 80000 + index * 1000,
              currency: 'USD',
            },
            growthPotential: 'MEDIUM' as const,
            marketDemand: 70 + index,
          },
        ],
        totalDuration: `${index + 1} years`,
        difficultyLevel: 'MODERATE' as const,
        successProbability: 70 + index,
      }));

      const startTime = Date.now();
      render(<CareerPathDisplay paths={manyPaths} />);
      const renderTime = Date.now() - startTime;

      expect(renderTime).toBeLessThan(1000); // Should render within 1 second
      expect(screen.getByText('20 paths tailored to your profile')).toBeInTheDocument();
    });

    it('does not cause memory leaks on unmount', () => {
      const { unmount } = render(<CareerPathDisplay paths={mockCareerPaths} />);

      expect(() => unmount()).not.toThrow();
    });
  });
});
