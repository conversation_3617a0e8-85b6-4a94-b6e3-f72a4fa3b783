import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ModernRecommendations from '@/components/career-insights/ModernRecommendations';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

const mockAiInsights = {
  strengths: [
    'Strong problem-solving abilities and analytical thinking',
    'Excellent communication skills with stakeholders',
    'Proven track record in project delivery',
    'Technical expertise in modern frameworks',
  ],
  opportunities: [
    'High demand for your skill set in the current market',
    'Remote work opportunities expanding globally',
    'Leadership roles becoming available in your industry',
    'Emerging technologies align with your background',
  ],
  challenges: [
    'Increased competition from global talent pool',
    'Need to stay updated with rapidly evolving technologies',
    'Skill gap in advanced cloud technologies',
    'Limited experience in team leadership',
  ],
  recommendations: [
    {
      priority: 'HIGH' as const,
      action: 'You should pursue AWS certification to strengthen cloud skills',
      expectedOutcome: 'Qualify for senior cloud architect positions',
      timeframe: '3 months',
    },
    {
      priority: 'HIGH' as const,
      action: 'You should develop leadership skills through mentoring',
      expectedOutcome: 'Prepare for management track opportunities',
      timeframe: '6 months',
    },
    {
      priority: 'MEDIUM' as const,
      action: 'You should build a portfolio of open source contributions',
      expectedOutcome: 'Increase visibility in the developer community',
      timeframe: '6 months',
    },
    {
      priority: 'LOW' as const,
      action: 'You should attend industry conferences and networking events',
      expectedOutcome: 'Expand professional network and stay current',
      timeframe: '12 months',
    },
  ],
  confidenceScore: 87,
};

const minimalAiInsights = {
  strengths: ['Technical skills'],
  opportunities: ['Market demand'],
  challenges: ['Competition'],
  recommendations: [
    {
      priority: 'HIGH' as const,
      action: 'You should focus on skill development',
      expectedOutcome: 'Career advancement',
      timeframe: 'Immediate',
    },
  ],
  confidenceScore: 75,
};

describe('ModernRecommendations', () => {
  describe('Component Rendering', () => {
    it('renders with complete AI insights data', () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
      expect(screen.getByText('Action Plan')).toBeInTheDocument();
    });

    it('applies custom className correctly', () => {
      const { container } = render(
        <ModernRecommendations aiInsights={mockAiInsights} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass('custom-class');
    });

    it('renders with minimal AI insights data', () => {
      render(<ModernRecommendations aiInsights={minimalAiInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
      expect(screen.getByText('Action Plan')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    beforeEach(() => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);
    });

    it('shows overview tab by default', () => {
      expect(screen.getByText('Analysis Confidence')).toBeInTheDocument();
      expect(screen.getByText('87%')).toBeInTheDocument();
      expect(screen.getByText('Your Strengths')).toBeInTheDocument();
    });

    it('switches to action plan tab when clicked', async () => {
      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
        expect(
          screen.getByText('4 strategic recommendations prioritized for maximum impact')
        ).toBeInTheDocument();
      });
    });

    it('switches back to overview tab', async () => {
      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
      });

      const overviewTab = screen.getByText('Quick Overview');
      fireEvent.click(overviewTab);

      await waitFor(() => {
        expect(screen.getByText('Analysis Confidence')).toBeInTheDocument();
      });
    });

    it('maintains visual active state for selected tab', () => {
      const overviewTab = screen.getByText('Quick Overview');
      const actionPlanTab = screen.getByText('Action Plan');

      // Initially overview should be active
      expect(overviewTab.closest('button')).toHaveClass('text-white');

      fireEvent.click(actionPlanTab);

      waitFor(() => {
        expect(actionPlanTab.closest('button')).toHaveClass('text-white');
      });
    });
  });

  describe('Overview Tab Content', () => {
    beforeEach(() => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);
    });

    it('displays confidence score correctly', () => {
      expect(screen.getByText('Analysis Confidence')).toBeInTheDocument();
      expect(screen.getByText('87%')).toBeInTheDocument();
    });

    it('displays strengths section with correct count', () => {
      expect(screen.getByText('Your Strengths')).toBeInTheDocument();
      expect(screen.getByText('4 identified')).toBeInTheDocument();
      expect(
        screen.getByText('Strong problem-solving abilities and analytical thinking')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Excellent communication skills with stakeholders')
      ).toBeInTheDocument();
      expect(screen.getByText('Proven track record in project delivery')).toBeInTheDocument();
    });

    it('limits strengths display to first 3 items', () => {
      expect(
        screen.queryByText('Technical expertise in modern frameworks')
      ).not.toBeInTheDocument();
      expect(screen.getAllByText('+1 more')).toHaveLength(3); // All three sections (strengths, opportunities, challenges) show +1 more
    });

    it('displays opportunities section with correct count', () => {
      expect(screen.getByText('Opportunities')).toBeInTheDocument();
      expect(screen.getByText('4 available')).toBeInTheDocument();
      expect(
        screen.getByText('High demand for your skill set in the current market')
      ).toBeInTheDocument();
    });

    it('displays challenges section with correct count', () => {
      expect(screen.getByText('Challenges')).toBeInTheDocument();
      expect(screen.getByText('4 to address')).toBeInTheDocument();
      expect(screen.getByText('Increased competition from global talent pool')).toBeInTheDocument();
    });

    it('displays quick stats correctly', () => {
      const totalInsights = 4 + 4 + 4; // strengths + opportunities + challenges
      expect(screen.getByText(totalInsights.toString())).toBeInTheDocument();
      expect(screen.getByText('Total Insights')).toBeInTheDocument();

      expect(screen.getByText('4')).toBeInTheDocument(); // Total action items
      expect(screen.getByText('Action Items')).toBeInTheDocument();

      expect(screen.getByText('2')).toBeInTheDocument(); // High priority items
      expect(screen.getByText('High Priority')).toBeInTheDocument();
    });
  });

  describe('Action Plan Tab Content', () => {
    beforeEach(async () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
      });
    });

    it('displays all recommendations', () => {
      expect(
        screen.getByText('You should pursue AWS certification to strengthen cloud skills')
      ).toBeInTheDocument();
      expect(
        screen.getByText('You should develop leadership skills through mentoring')
      ).toBeInTheDocument();
      expect(
        screen.getByText('You should build a portfolio of open source contributions')
      ).toBeInTheDocument();
      expect(
        screen.getByText('You should attend industry conferences and networking events')
      ).toBeInTheDocument();
    });

    it('displays priority badges correctly', () => {
      expect(screen.getAllByText('HIGH PRIORITY')).toHaveLength(2);
      expect(screen.getByText('MEDIUM PRIORITY')).toBeInTheDocument();
      expect(screen.getByText('LOW PRIORITY')).toBeInTheDocument();
    });

    it('displays timeframes for each recommendation', () => {
      expect(screen.getByText('3 months')).toBeInTheDocument();
      expect(screen.getAllByText('6 months')).toHaveLength(2); // Two recommendations have 6 months timeframe
      expect(screen.getByText('12 months')).toBeInTheDocument();
    });

    it('shows call to action button', () => {
      expect(screen.getByText('Start Your Journey')).toBeInTheDocument();
    });
  });

  describe('Recommendation Card Interaction', () => {
    beforeEach(async () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
      });
    });

    it('expands recommendation card when clicked', async () => {
      const recommendationCard = screen
        .getByText('You should pursue AWS certification to strengthen cloud skills')
        .closest('div[class*="cursor-pointer"]');

      fireEvent.click(recommendationCard!);

      await waitFor(() => {
        expect(screen.getByText('Expected Outcome')).toBeInTheDocument();
        expect(
          screen.getByText('Qualify for senior cloud architect positions')
        ).toBeInTheDocument();
      });
    });

    it('collapses recommendation card when clicked again', async () => {
      const recommendationCard = screen
        .getByText('You should pursue AWS certification to strengthen cloud skills')
        .closest('div[class*="cursor-pointer"]');

      // Expand
      fireEvent.click(recommendationCard!);
      await waitFor(() => {
        expect(screen.getByText('Expected Outcome')).toBeInTheDocument();
      });

      // Collapse
      fireEvent.click(recommendationCard!);
      await waitFor(() => {
        expect(screen.queryByText('Expected Outcome')).not.toBeInTheDocument();
      });
    });

    it('can expand multiple recommendation cards simultaneously', async () => {
      const firstCard = screen
        .getByText('You should pursue AWS certification to strengthen cloud skills')
        .closest('div[class*="cursor-pointer"]');
      const secondCard = screen
        .getByText('You should develop leadership skills through mentoring')
        .closest('div[class*="cursor-pointer"]');

      fireEvent.click(firstCard!);
      await waitFor(() => {
        expect(screen.getByText('Expected Outcome')).toBeInTheDocument();
      });

      fireEvent.click(secondCard!);
      await waitFor(() => {
        const expectedOutcomes = screen.getAllByText('Expected Outcome');
        expect(expectedOutcomes).toHaveLength(2);
      });
    });
  });

  describe('Priority Styling', () => {
    beforeEach(async () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
      });
    });

    it('applies correct styling for HIGH priority recommendations', () => {
      const highPriorityBadges = screen.getAllByText('HIGH PRIORITY');
      highPriorityBadges.forEach(badge => {
        expect(badge).toHaveClass('bg-gradient-to-r', 'from-red-500', 'to-orange-500');
      });
    });

    it('applies correct styling for MEDIUM priority recommendations', () => {
      const mediumPriorityBadge = screen.getByText('MEDIUM PRIORITY');
      expect(mediumPriorityBadge).toHaveClass(
        'bg-gradient-to-r',
        'from-amber-500',
        'to-yellow-500'
      );
    });

    it('applies correct styling for LOW priority recommendations', () => {
      const lowPriorityBadge = screen.getByText('LOW PRIORITY');
      expect(lowPriorityBadge).toHaveClass('bg-gradient-to-r', 'from-blue-500', 'to-cyan-500');
    });
  });

  describe('Timeframe Icons', () => {
    beforeEach(async () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        expect(screen.getByText('Your Personalized Action Plan')).toBeInTheDocument();
      });
    });

    it('displays appropriate timeframe information', () => {
      expect(screen.getByText('3 months')).toBeInTheDocument();
      expect(screen.getAllByText('6 months')).toHaveLength(2); // Two recommendations have 6 months timeframe
      expect(screen.getByText('12 months')).toBeInTheDocument();
    });

    it('shows timeframe icons for recommendations', () => {
      // Icons are rendered as SVG elements
      const icons = document.querySelectorAll('svg');
      expect(icons.length).toBeGreaterThan(10); // Multiple icons throughout the component
    });
  });

  describe('Edge Cases', () => {
    it('handles empty arrays gracefully', () => {
      const emptyInsights = {
        strengths: [],
        opportunities: [],
        challenges: [],
        recommendations: [],
        confidenceScore: 50,
      };

      render(<ModernRecommendations aiInsights={emptyInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('handles missing confidence score', () => {
      const insightsWithoutScore = {
        ...mockAiInsights,
        confidenceScore: undefined,
      };

      render(<ModernRecommendations aiInsights={insightsWithoutScore as any} />);

      expect(screen.queryByText('Analysis Confidence')).not.toBeInTheDocument();
    });

    it('handles very long recommendation text', () => {
      const longRecommendationInsights = {
        ...mockAiInsights,
        recommendations: [
          {
            priority: 'HIGH' as const,
            action:
              'You should pursue a very comprehensive and detailed certification program that covers multiple aspects of cloud computing including infrastructure as code, container orchestration, serverless computing, and advanced security practices',
            expectedOutcome: 'Achieve expert-level proficiency and recognition',
            timeframe: '12 months',
          },
        ],
      };

      expect(() =>
        render(<ModernRecommendations aiInsights={longRecommendationInsights} />)
      ).not.toThrow();
    });

    it('handles zero confidence score', () => {
      const zeroConfidenceInsights = {
        ...mockAiInsights,
        confidenceScore: 0,
      };

      render(<ModernRecommendations aiInsights={zeroConfidenceInsights} />);

      expect(screen.getByText('0%')).toBeInTheDocument();
    });

    it('handles 100% confidence score', () => {
      const perfectConfidenceInsights = {
        ...mockAiInsights,
        confidenceScore: 100,
      };

      render(<ModernRecommendations aiInsights={perfectConfidenceInsights} />);

      expect(screen.getByText('100%')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('renders properly on mobile devices', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
    });

    it('renders properly on tablet devices', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
    });

    it('renders properly on desktop', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });

      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(screen.getByText('Quick Overview')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);
    });

    it('has proper tab navigation structure', () => {
      const tabs = screen.getAllByRole('button');
      const tabButtons = tabs.filter(
        tab =>
          tab.textContent?.includes('Quick Overview') || tab.textContent?.includes('Action Plan')
      );

      expect(tabButtons).toHaveLength(2);
    });

    it('has proper heading structure', () => {
      expect(screen.getByText('Your Strengths')).toBeInTheDocument();
      expect(screen.getByText('Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Challenges')).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      const firstTab = screen.getByText('Quick Overview').closest('button');

      if (firstTab) {
        firstTab.focus();
        expect(document.activeElement).toBe(firstTab);
      }
    });

    it('has accessible buttons for recommendations', async () => {
      const actionPlanTab = screen.getByText('Action Plan');
      fireEvent.click(actionPlanTab);

      await waitFor(() => {
        const startJourneyButton = screen.getByText('Start Your Journey');
        expect(startJourneyButton).toBeInTheDocument();
        expect(startJourneyButton.tagName).toBe('BUTTON');
      });
    });
  });

  describe('Performance', () => {
    it('handles large numbers of recommendations efficiently', () => {
      const manyRecommendations = Array.from({ length: 50 }, (_, index) => ({
        priority: (index % 3 === 0 ? 'HIGH' : index % 2 === 0 ? 'MEDIUM' : 'LOW') as const,
        action: `You should take action number ${index + 1}`,
        expectedOutcome: `Outcome ${index + 1}`,
        timeframe: '3 months',
      }));

      const largeInsights = {
        ...mockAiInsights,
        recommendations: manyRecommendations,
      };

      const startTime = Date.now();
      render(<ModernRecommendations aiInsights={largeInsights} />);
      const renderTime = Date.now() - startTime;

      expect(renderTime).toBeLessThan(1000); // Should render within 1 second
    });

    it('does not cause memory leaks on unmount', () => {
      const { unmount } = render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Data Calculations', () => {
    it('calculates total insights correctly', () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const totalInsights =
        mockAiInsights.strengths.length +
        mockAiInsights.opportunities.length +
        mockAiInsights.challenges.length;

      expect(screen.getByText(totalInsights.toString())).toBeInTheDocument();
    });

    it('calculates high priority recommendations correctly', () => {
      render(<ModernRecommendations aiInsights={mockAiInsights} />);

      const highPriorityCount = mockAiInsights.recommendations.filter(
        r => r.priority === 'HIGH'
      ).length;
      expect(screen.getByText(highPriorityCount.toString())).toBeInTheDocument();
    });

    it('updates calculations when data changes', () => {
      const { rerender } = render(<ModernRecommendations aiInsights={mockAiInsights} />);

      expect(screen.getByText('12')).toBeInTheDocument(); // Total insights

      const updatedInsights = {
        ...mockAiInsights,
        strengths: ['One strength'],
        opportunities: ['One opportunity'],
        challenges: ['One challenge'],
      };

      rerender(<ModernRecommendations aiInsights={updatedInsights} />);

      expect(screen.getByText('3')).toBeInTheDocument(); // Updated total
    });
  });
});
