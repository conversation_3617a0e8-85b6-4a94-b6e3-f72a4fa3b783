import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import LearningResourcesDisplay from '@/components/career-insights/LearningResourcesDisplay';
import { LearningResource } from '@/stores/careerInsightsStore';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const mockResources: LearningResource[] = [
  {
    title: 'React Complete Guide',
    type: 'COURSE',
    provider: 'Udemy',
    url: 'https://udemy.com/react-course',
    estimatedDuration: '40 hours',
    cost: '$49.99',
  },
  {
    title: 'JavaScript MDN Documentation',
    type: 'TUTORIAL',
    provider: 'Mozilla',
    url: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
    estimatedDuration: 'Self-paced',
    cost: 'Free',
  },
  {
    title: 'AWS Certified Developer',
    type: 'CERTIFICATION',
    provider: 'Amazon',
    url: 'https://aws.amazon.com/certification/',
    estimatedDuration: '3 months prep',
    cost: '$150',
  },
  {
    title: 'Clean Code',
    type: 'BOOK',
    provider: 'Robert C. Martin',
    estimatedDuration: '2 weeks',
    cost: '$25.99',
  },
  {
    title: 'Useful Development Tool',
    type: 'LINK',
    provider: 'GitHub',
    url: 'https://github.com/useful-tool',
    estimatedDuration: '1 hour setup',
    cost: 'Free',
  },
];

const freeResources: LearningResource[] = [
  {
    title: 'Free React Tutorial',
    type: 'TUTORIAL',
    provider: 'FreeCodeCamp',
    url: 'https://freecodecamp.org/react',
    estimatedDuration: '10 hours',
    cost: 'Free',
  },
  {
    title: 'Open Source Book',
    type: 'BOOK',
    provider: 'Community',
    estimatedDuration: '1 week',
    cost: '$0',
  },
];

describe('LearningResourcesDisplay', () => {
  describe('Rendering', () => {
    it('renders with default title and resources', () => {
      render(<LearningResourcesDisplay resources={mockResources} />);

      expect(screen.getByText('Learning Resources')).toBeInTheDocument();
      expect(screen.getByText('5 resources')).toBeInTheDocument();
    });

    it('renders with custom title', () => {
      render(<LearningResourcesDisplay resources={mockResources} title="Custom Learning Path" />);

      expect(screen.getByText('Custom Learning Path')).toBeInTheDocument();
      expect(screen.getByText('5 resources')).toBeInTheDocument();
    });

    it('renders nothing when resources array is empty', () => {
      const { container } = render(<LearningResourcesDisplay resources={[]} />);

      expect(container.firstChild).toBeNull();
    });

    it('renders nothing when resources is null/undefined', () => {
      const { container } = render(<LearningResourcesDisplay resources={null as any} />);

      expect(container.firstChild).toBeNull();
    });

    it('applies custom className correctly', () => {
      const { container } = render(
        <LearningResourcesDisplay resources={mockResources} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Resource Cards', () => {
    beforeEach(() => {
      render(<LearningResourcesDisplay resources={mockResources} />);
    });

    it('displays all resource titles correctly', () => {
      expect(screen.getByText('React Complete Guide')).toBeInTheDocument();
      expect(screen.getByText('JavaScript MDN Documentation')).toBeInTheDocument();
      expect(screen.getByText('AWS Certified Developer')).toBeInTheDocument();
      expect(screen.getByText('Clean Code')).toBeInTheDocument();
      expect(screen.getByText('Useful Development Tool')).toBeInTheDocument();
    });

    it('displays all providers correctly', () => {
      expect(screen.getByText('Udemy')).toBeInTheDocument();
      expect(screen.getByText('Mozilla')).toBeInTheDocument();
      expect(screen.getByText('Amazon')).toBeInTheDocument();
      expect(screen.getByText('Robert C. Martin')).toBeInTheDocument();
      expect(screen.getByText('GitHub')).toBeInTheDocument();
    });

    it('displays duration and cost information', () => {
      expect(screen.getByText('40 hours')).toBeInTheDocument();
      expect(screen.getByText('$49.99')).toBeInTheDocument();
      expect(screen.getByText('Self-paced')).toBeInTheDocument();
      expect(screen.getAllByText('Free')).toHaveLength(2); // Two resources have "Free" cost
      expect(screen.getByText('3 months prep')).toBeInTheDocument();
      expect(screen.getByText('$150')).toBeInTheDocument();
    });

    it('displays resource type badges correctly', () => {
      expect(screen.getByText('COURSE')).toBeInTheDocument();
      expect(screen.getByText('TUTORIAL')).toBeInTheDocument();
      expect(screen.getByText('CERTIFICATION')).toBeInTheDocument();
      expect(screen.getByText('BOOK')).toBeInTheDocument();
      expect(screen.getByText('LINK')).toBeInTheDocument();
    });
  });

  describe('Cost Color Coding', () => {
    it('shows free resources in green', () => {
      render(<LearningResourcesDisplay resources={freeResources} />);

      const freeTexts = screen.getAllByText('Free');
      freeTexts.forEach(freeText => {
        expect(freeText).toHaveClass('text-green-400');
      });

      const zeroText = screen.getByText('$0');
      expect(zeroText).toHaveClass('text-green-400');
    });

    it('shows low-cost resources in yellow', () => {
      const lowCostResources: LearningResource[] = [
        {
          title: 'Cheap Course',
          type: 'COURSE',
          provider: 'Provider',
          estimatedDuration: '1 hour',
          cost: '$10',
        },
      ];

      render(<LearningResourcesDisplay resources={lowCostResources} />);

      const costText = screen.getByText('$10');
      expect(costText).toHaveClass('text-yellow-400');
    });

    it('shows high-cost resources in red', () => {
      const highCostResources: LearningResource[] = [
        {
          title: 'Expensive Course',
          type: 'COURSE',
          provider: 'Provider',
          estimatedDuration: '1 hour',
          cost: '$500',
        },
      ];

      render(<LearningResourcesDisplay resources={highCostResources} />);

      const costText = screen.getByText('$500');
      expect(costText).toHaveClass('text-red-400');
    });
  });

  describe('External Links', () => {
    it('opens external links when Access Resource button is clicked', () => {
      const mockOpen = jest.fn();
      Object.defineProperty(window, 'open', {
        writable: true,
        value: mockOpen,
      });

      render(<LearningResourcesDisplay resources={mockResources} />);

      const accessButtons = screen.getAllByText('Access Resource');

      // Click the first button (React Complete Guide)
      fireEvent.click(accessButtons[0]);
      expect(mockOpen).toHaveBeenCalledWith('https://udemy.com/react-course', '_blank');

      // Click the second button (JavaScript MDN Documentation)
      fireEvent.click(accessButtons[1]);
      expect(mockOpen).toHaveBeenCalledWith(
        'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
        '_blank'
      );
    });

    it('does not render Access Resource button for resources without URL', () => {
      const resourcesWithoutUrl: LearningResource[] = [
        {
          title: 'Resource Without URL',
          type: 'BOOK',
          provider: 'Publisher',
          estimatedDuration: '1 week',
          cost: '$25',
        },
      ];

      render(<LearningResourcesDisplay resources={resourcesWithoutUrl} />);

      expect(screen.queryByText('Access Resource')).not.toBeInTheDocument();
    });
  });

  describe('Resource Type Styling', () => {
    beforeEach(() => {
      render(<LearningResourcesDisplay resources={mockResources} />);
    });

    it('applies correct styling for different resource types', () => {
      // Each resource type should have its own color scheme
      // This tests that the component handles all resource types
      const resourceCards = screen
        .getAllByRole('button', { name: /Access Resource/i })
        .map(button => button.closest('[class*="bg-"]'));

      expect(resourceCards.length).toBeGreaterThan(0);
    });

    it('shows appropriate icons for different resource types', () => {
      // Icons are rendered as SVG elements
      const icons = document.querySelectorAll('svg');
      expect(icons.length).toBeGreaterThan(5); // At least one icon per resource plus UI icons
    });
  });

  describe('Summary Section', () => {
    it('displays correct free resources count', () => {
      render(<LearningResourcesDisplay resources={mockResources} />);

      expect(screen.getByText('2 Free Resources')).toBeInTheDocument(); // JavaScript MDN + Useful Development Tool
    });

    it('displays correct certifications count', () => {
      render(<LearningResourcesDisplay resources={mockResources} />);

      expect(screen.getByText('1 Certifications')).toBeInTheDocument(); // AWS Certified Developer
    });

    it('shows summary message', () => {
      render(<LearningResourcesDisplay resources={mockResources} />);

      expect(screen.getByText('Curated learning path for maximum impact')).toBeInTheDocument();
    });

    it('updates counts correctly with different resource sets', () => {
      const multiCertResources: LearningResource[] = [
        {
          title: 'AWS Cert',
          type: 'CERTIFICATION',
          provider: 'Amazon',
          estimatedDuration: '3 months',
          cost: '$150',
        },
        {
          title: 'Google Cert',
          type: 'CERTIFICATION',
          provider: 'Google',
          estimatedDuration: '2 months',
          cost: '$200',
        },
        {
          title: 'Free Tutorial',
          type: 'TUTORIAL',
          provider: 'YouTube',
          estimatedDuration: '5 hours',
          cost: 'Free',
        },
      ];

      render(<LearningResourcesDisplay resources={multiCertResources} />);

      expect(screen.getByText('1 Free Resources')).toBeInTheDocument();
      expect(screen.getByText('2 Certifications')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('renders grid layout correctly', () => {
      const { container } = render(<LearningResourcesDisplay resources={mockResources} />);

      const gridContainer = container.querySelector('.grid');
      expect(gridContainer).toBeInTheDocument();
      expect(gridContainer).toHaveClass('md:grid-cols-2');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      render(<LearningResourcesDisplay resources={mockResources} />);
    });

    it('has proper heading structure', () => {
      expect(screen.getByRole('heading', { name: /Learning Resources/i })).toBeInTheDocument();
    });

    it('has accessible buttons', () => {
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });
    });

    it('provides alternative text for icons', () => {
      // Icons should be decorative or have proper labels
      const svgElements = document.querySelectorAll('svg');
      svgElements.forEach(svg => {
        // SVGs should either have aria-hidden or proper labeling
        expect(svg).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles resources with very long titles', () => {
      const longTitleResources: LearningResource[] = [
        {
          title:
            'This is a very long resource title that might wrap to multiple lines and should be handled gracefully by the component layout',
          type: 'COURSE',
          provider: 'Provider',
          estimatedDuration: '1 hour',
          cost: '$50',
        },
      ];

      expect(() =>
        render(<LearningResourcesDisplay resources={longTitleResources} />)
      ).not.toThrow();
      expect(screen.getByText(/This is a very long resource title/)).toBeInTheDocument();
    });

    it('handles resources with special characters in cost', () => {
      const specialCostResources: LearningResource[] = [
        {
          title: 'Resource',
          type: 'COURSE',
          provider: 'Provider',
          estimatedDuration: '1 hour',
          cost: '€45.99',
        },
      ];

      render(<LearningResourcesDisplay resources={specialCostResources} />);
      expect(screen.getByText('€45.99')).toBeInTheDocument();
    });

    it('handles missing optional fields gracefully', () => {
      const incompleteResources: LearningResource[] = [
        {
          title: 'Minimal Resource',
          type: 'LINK',
          provider: 'Unknown',
          estimatedDuration: 'Unknown',
          cost: 'Unknown',
        },
      ];

      expect(() =>
        render(<LearningResourcesDisplay resources={incompleteResources} />)
      ).not.toThrow();
      expect(screen.getByText('Minimal Resource')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('handles large numbers of resources efficiently', () => {
      const manyResources: LearningResource[] = Array.from({ length: 50 }, (_, index) => ({
        title: `Resource ${index}`,
        type: 'COURSE' as const,
        provider: `Provider ${index}`,
        url: `https://example.com/resource-${index}`,
        estimatedDuration: '1 hour',
        cost: '$10',
      }));

      const startTime = Date.now();
      render(<LearningResourcesDisplay resources={manyResources} />);
      const renderTime = Date.now() - startTime;

      expect(renderTime).toBeLessThan(1000); // Should render within 1 second
      expect(screen.getByText('50 resources')).toBeInTheDocument();
    });

    it('does not cause memory leaks on unmount', () => {
      const { unmount } = render(<LearningResourcesDisplay resources={mockResources} />);

      expect(() => unmount()).not.toThrow();
    });
  });
});
