import { act, renderHook, waitFor } from '@testing-library/react';
import { useJobStore, useUnifiedJobStore } from '@/stores/unifiedJobStore';

describe('Real-time Updates Without Page Refresh', () => {
  beforeEach(() => {
    // Reset store state before each test
    useUnifiedJobStore.setState({
      jobs: [],
      jobsById: {},
      currentJob: null,
      selectedJobId: null,
      candidates: {},
      stats: {
        totalCandidates: 0,
        topTierCount: 0,
        secondTierCount: 0,
        othersCount: 0,
        unrankedCount: 0,
        shortlistedCount: 0,
        totalApplications: 0,
        viewCount: 0,
        publishedPlatformsCount: 0,
      },
      allCandidatesFlat: [],
    });
  });

  describe('TotalCandidatesSection', () => {
    it('should update candidate count in real-time when store updates', async () => {
      const { result } = renderHook(() => useJobStore());

      // Initial state
      expect(result.current.stats?.totalCandidates).toBe(0);

      // Simulate store update
      act(() => {
        useUnifiedJobStore.setState({
          currentJob: {
            id: 'job-1',
            jobTitle: 'Test Job',
            totalCandidates: 10,
            recentCandidates: [],
          } as any,
          stats: {
            totalCandidates: 10,
          } as any,
        });
      });

      // Verify update
      expect(result.current.stats?.totalCandidates).toBe(10);
      expect(result.current.currentJob?.totalCandidates).toBe(10);

      // Simulate adding more candidates
      act(() => {
        useUnifiedJobStore.setState({
          currentJob: {
            ...result.current.currentJob!,
            totalCandidates: 15,
          } as any,
          stats: {
            totalCandidates: 15,
          } as any,
        });
      });

      // Verify real-time update
      expect(result.current.stats?.totalCandidates).toBe(15);
    });
  });

  describe('ScoutOnlineTable', () => {
    it('should update candidates without page reload when scout completes', async () => {
      const { result } = renderHook(() => useJobStore());

      // Simulate initial state
      act(() => {
        useUnifiedJobStore.setState({
          currentJob: {
            id: 'job-1',
            jobTitle: 'Test Job',
            totalCandidates: 5,
          } as any,
        });
      });

      // Simulate scout completion
      await act(async () => {
        await result.current.onWorkerComplete('job-1', 'scout');
      });

      // Verify that store methods were called (in real implementation)
      // Note: This would need mocking of API calls in actual tests
    });
  });

  describe('CandidateList', () => {
    it('should update shortlisted candidates in real-time', async () => {
      const { result } = renderHook(() => useJobStore());

      // Setup initial candidates
      act(() => {
        useUnifiedJobStore.setState({
          selectedJobId: 'job-1',
          candidates: {
            'job-1': {
              topTier: [{ id: 'c1', fullName: 'John Doe', status: 'NEW', isShortlisted: false }],
              secondTier: [],
              others: [],
              unranked: [],
              shortlisted: [],
            },
          } as any,
          allCandidatesFlat: [
            { id: 'c1', fullName: 'John Doe', status: 'NEW', isShortlisted: false },
          ],
        });
      });

      // Shortlist a candidate
      act(() => {
        // Use setState to properly update and trigger re-renders
        useUnifiedJobStore.setState(state => {
          const newCandidates = { ...state.candidates };
          const jobCandidates = newCandidates['job-1'];
          if (jobCandidates) {
            const candidateIndex = jobCandidates.topTier.findIndex(c => c.id === 'c1');
            if (candidateIndex !== -1) {
              const candidate = { ...jobCandidates.topTier[candidateIndex] };
              const newTopTier = [...jobCandidates.topTier];
              newTopTier.splice(candidateIndex, 1);

              candidate.status = 'SHORTLISTED';
              candidate.isShortlisted = true;

              newCandidates['job-1'] = {
                ...jobCandidates,
                topTier: newTopTier,
                shortlisted: [...jobCandidates.shortlisted, candidate],
              };
            }
          }
          return { candidates: newCandidates };
        });
      });

      // Verify candidate moved to shortlisted group
      await waitFor(() => {
        const candidates = result.current.candidates;
        expect(candidates?.shortlisted).toHaveLength(1);
        expect(candidates?.topTier).toHaveLength(0);
        expect(candidates?.shortlisted[0].isShortlisted).toBe(true);
      });
    });
  });

  describe('FileUploader', () => {
    it('should trigger store updates after upload completion', async () => {
      const { result } = renderHook(() => useJobStore());

      // Setup initial state
      act(() => {
        useUnifiedJobStore.setState({
          currentJob: {
            id: 'job-1',
            jobTitle: 'Test Job',
            totalCandidates: 0,
          } as any,
          selectedJobId: 'job-1',
        });
      });

      // Simulate upload completion
      await act(async () => {
        await result.current.onWorkerComplete('job-1', 'upload');
      });

      // In real implementation, this would trigger fetchJobCriteria
      // which would update the candidate count
    });
  });

  describe('Store Subscriptions', () => {
    it('should notify subscribers when candidate data changes', () => {
      const mockCallback = jest.fn();

      // Subscribe to store changes
      const unsubscribe = useUnifiedJobStore.subscribe(
        state => ({
          totalCandidates: state.currentJob?.totalCandidates || state.stats?.totalCandidates || 0,
        }),
        mockCallback
      );

      // Update store
      act(() => {
        useUnifiedJobStore.setState({
          currentJob: {
            id: 'job-1',
            totalCandidates: 20,
          } as any,
        });
      });

      // Verify callback was called
      expect(mockCallback).toHaveBeenCalled();

      // Cleanup
      unsubscribe();
    });

    it('should move candidates between groups when status changes', () => {
      const { result } = renderHook(() => useJobStore());

      // Setup candidates in different groups
      act(() => {
        useUnifiedJobStore.setState({
          selectedJobId: 'job-1',
          candidates: {
            'job-1': {
              topTier: [{ id: 'c1', fullName: 'Candidate 1', status: 'NEW', isShortlisted: false }],
              secondTier: [
                { id: 'c2', fullName: 'Candidate 2', status: 'NEW', isShortlisted: false },
              ],
              others: [],
              unranked: [],
              shortlisted: [],
            },
          } as any,
        });
      });

      // Shortlist candidate from topTier
      act(() => {
        useUnifiedJobStore.setState(state => {
          const jobId = state.selectedJobId || 'job-1';
          if (state.candidates[jobId]) {
            const newCandidates = { ...state.candidates };
            const jobCandidates = { ...newCandidates[jobId] };

            // Find and remove from topTier
            const candidateIndex = jobCandidates.topTier.findIndex(c => c.id === 'c1');
            if (candidateIndex !== -1) {
              const candidate = jobCandidates.topTier[candidateIndex];
              jobCandidates.topTier = [...jobCandidates.topTier];
              jobCandidates.topTier.splice(candidateIndex, 1);

              // Add to shortlisted
              candidate.status = 'SHORTLISTED';
              candidate.isShortlisted = true;
              jobCandidates.shortlisted = [...jobCandidates.shortlisted, candidate];

              newCandidates[jobId] = jobCandidates;
              return { candidates: newCandidates };
            }
          }
          return state;
        });
      });

      // Verify movement
      const candidates = result.current.candidates;
      expect(candidates?.topTier).toHaveLength(0);
      expect(candidates?.shortlisted).toHaveLength(1);
      expect(candidates?.shortlisted[0].id).toBe('c1');
    });
  });
});
