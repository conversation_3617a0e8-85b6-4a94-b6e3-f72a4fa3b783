import React from 'react';
import { render, RenderOptions } from '@testing-library/react';

// Mock implementations for common external dependencies
export const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
};

export const mockParams = {
  id: 'test-id',
};

// Test utility functions
export const mockCareerInsightsStore = {
  insights: [],
  currentInsight: null,
  isLoading: false,
  isCreating: false,
  error: null,
  fetchInsights: jest.fn(),
  fetchInsightById: jest.fn(),
  createInsight: jest.fn(),
  clearError: jest.fn(),
  reset: jest.fn(),
};

// Mock window methods that are commonly used
export const setupWindowMocks = () => {
  Object.defineProperty(window, 'open', {
    writable: true,
    value: jest.fn(),
  });

  Object.defineProperty(navigator, 'share', {
    writable: true,
    value: jest.fn(),
  });

  Object.defineProperty(navigator, 'clipboard', {
    writable: true,
    value: {
      writeText: jest.fn(),
    },
  });

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
  }));
};

// Custom render function that includes providers if needed
const customRender = (ui: React.ReactElement, options?: Omit<RenderOptions, 'wrapper'>) => {
  return render(ui, {
    // Add any providers here if needed
    // wrapper: ({ children }) => <ThemeProvider>{children}</ThemeProvider>,
    ...options,
  });
};

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Helper functions for testing
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const createMockInsight = (overrides = {}) => ({
  id: 'mock-insight-1',
  jobSeekerId: 'job-seeker-1',
  type: 'SKILL_GAP_ANALYSIS',
  status: 'READY',
  title: 'Mock Insight',
  summary: 'Mock summary',
  viewCount: 5,
  createdAt: '2023-10-01T10:00:00Z',
  updatedAt: '2023-10-01T10:00:00Z',
  ...overrides,
});

export const createMockLearningResource = (overrides = {}) => ({
  title: 'Mock Learning Resource',
  type: 'COURSE',
  provider: 'Mock Provider',
  url: 'https://example.com',
  estimatedDuration: '1 hour',
  cost: 'Free',
  ...overrides,
});

export const createMockCareerPath = (overrides = {}) => ({
  pathName: 'Mock Career Path',
  steps: [
    {
      title: 'Mock Step',
      description: 'Mock description',
      timeline: '1 year',
      requirements: ['Mock requirement'],
      averageSalaryRange: {
        min: 50000,
        max: 80000,
        currency: 'USD',
      },
      growthPotential: 'HIGH',
      marketDemand: 85,
    },
  ],
  totalDuration: '2 years',
  difficultyLevel: 'MODERATE',
  successProbability: 75,
  ...overrides,
});

export const createMockAiInsights = (overrides = {}) => ({
  strengths: ['Mock strength'],
  opportunities: ['Mock opportunity'],
  challenges: ['Mock challenge'],
  recommendations: [
    {
      priority: 'HIGH',
      action: 'You should take mock action',
      expectedOutcome: 'Mock outcome',
      timeframe: '3 months',
    },
  ],
  confidenceScore: 85,
  ...overrides,
});

// Console error suppression for known issues in tests
export const suppressConsoleErrors = (patterns: string[]) => {
  const originalError = console.error;
  console.error = (...args: any[]) => {
    const message = args.join(' ');
    if (patterns.some(pattern => message.includes(pattern))) {
      return;
    }
    originalError.apply(console, args);
  };

  return () => {
    console.error = originalError;
  };
};

// Setup function to be called in test files
export const setupTests = () => {
  setupWindowMocks();

  // Suppress common React/Testing Library warnings
  const restoreConsole = suppressConsoleErrors([
    'Warning: ReactDOM.render is no longer supported',
    'Warning: An invalid form control',
    'Not implemented: HTMLCanvasElement.prototype.getContext',
  ]);

  return restoreConsole;
};
