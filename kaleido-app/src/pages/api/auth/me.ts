import { getAccessToken, getSession } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function me(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get the session using Auth0's getSession
    const session = await getSession(req, res);

    // Check if we have a valid session with user data
    if (!session?.user) {
      return res.status(401).json({
        user: null,
        error: 'Not authenticated',
        code: 'NO_SESSION',
        requiresLogout: true,
      });
    }

    // Attempt to reuse the access token already stored in the session first
    let accessToken: string | null = null;
    let tokenError: any = null;

    const expiresAt = session?.accessTokenExpiresAt ?? 0; // seconds since epoch
    const isTokenValid = session?.accessToken && expiresAt * 1000 > Date.now() + 60_000; // 60-s buffer

    if (isTokenValid) {
      accessToken = session.accessToken as string;
    } else {
      try {
        // The SDK will automatically refresh if the existing token is expired
        // and a refresh token is available. We do NOT force a refresh unless
        // it's necessary.
        const tokenResult = await getAccessToken(req, res);

        if (typeof tokenResult === 'string') {
          accessToken = tokenResult;
        } else if (tokenResult && typeof tokenResult === 'object' && 'accessToken' in tokenResult) {
          accessToken = tokenResult.accessToken as string;
        }
      } catch (error: any) {
        tokenError = error;
        console.error('Error getting access token:', error);

        // If we cannot refresh (e.g. missing or expired), force logout so the
        // client can redirect the user to sign in again.
        if (
          error.code === 'ERR_EXPIRED_ACCESS_TOKEN' ||
          error.message?.includes('refresh token is not available') ||
          error.message?.includes('expired')
        ) {
          // Clear the Auth0 session cookie to force a clean state
          res.setHeader('Set-Cookie', [
            'appSession=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
            'appSession.0=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
            'appSession.1=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
            'appSession.2=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
          ]);

          return res.status(401).json({
            user: null,
            error: 'Session expired. Please log in again.',
            code: 'SESSION_EXPIRED',
            requiresLogout: true,
          });
        }
      }
    }

    // Return session data even if we couldn't get a fresh access token
    // This allows the app to function with a valid session but expired token
    const responseData = {
      ...session.user, // Include all user fields
      user: session.user,
      accessToken: accessToken,
      scope: session.accessTokenScope,
      expiresAt: session.accessTokenExpiresAt,
      idToken: session.idToken,
      tokenError: tokenError ? tokenError.message : null,
    };

    // Set cache control headers
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.json(responseData);
  } catch (error: any) {
    console.error('Error fetching user data:', error);

    // Provide more specific error codes
    const errorCode = error.code || 'UNKNOWN_ERROR';
    const requiresLogout =
      errorCode === 'ERR_EXPIRED_ACCESS_TOKEN' ||
      error.message?.includes('expired') ||
      error.message?.includes('refresh');

    res.status(401).json({
      user: null,
      error: error instanceof Error ? error.message : 'Failed to fetch user data',
      code: errorCode,
      requiresLogout,
    });
  }
}
