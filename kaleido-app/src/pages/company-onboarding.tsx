import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useRouter } from 'next/router';

import { CompanySetupSlider } from '@/components/CompanySetup/CompanySetupSlider';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { showToast } from '@/components/Toaster';
import { useAuthSync } from '@/hooks/useAuthSync';
import { useAuthStore } from '@/stores/authStore';
import { apiClient as apiHelper } from '@/lib/apiHelper';
import { clearOnboardingContext, setOnboardingContext } from '@/lib/apiHelper.utils';
import { CompanySetupFormData } from '@/stores/companySetupStore';
import { useCompanyStore } from '@/stores/companyStore';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { UserRole } from '@/types/roles';

// Type alias for compatibility
type CompanySetupData = CompanySetupFormData & {
  id?: string;
  preferences?: any;
  department?: string;
  onboardingComplete?: boolean;
};

const CompanyOnboardingPage: React.FC = () => {
  const router = useRouter();

  // Use auth sync and store
  useAuthSync();
  const { isAuthenticated, isLoading: authLoading, isInitialized, session } = useAuthStore();

  // Get URL parameters for Stripe session verification
  const { session_id, plan, billing, source } = router.query;
  const {
    markCompanyOnboardingComplete,
    setUserRole,
    getCompanyNeedsOnboarding,
    hasCheckedOnboarding,
  } = useOnboardingStore();
  const { company: storeCompany, fetchCompany } = useCompanyStore();
  const [initialData, setInitialData] = useState<Partial<CompanySetupFormData>>({});
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const [needsAuthentication, setNeedsAuthentication] = useState(false);
  const [hasRedirected, setHasRedirected] = useState(false);

  // Helper function to set onboarding completion cookie and redirect
  const completeOnboardingAndRedirect = useCallback(() => {
    // Prevent multiple redirects
    if (hasRedirected) return;

    // Set onboarding completion cookie for middleware
    if (session?.user?.sub) {
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year expiry
      document.cookie = `onboardingComplete_${session.user.sub}=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; Secure`;
    }

    setHasRedirected(true);
    router.replace('/dashboard');
  }, [session?.user?.sub, router, hasRedirected]);

  // Ensure initialData always includes clientId when user is authenticated
  const finalInitialData = useMemo(() => {
    if (session?.user?.sub) {
      return {
        ...initialData,
        clientId: session.user.sub,
      };
    }
    return initialData;
  }, [initialData, session?.user?.sub]);

  // Handle Stripe session verification if coming from payment
  useEffect(() => {
    const verifyStripeSession = async () => {
      if (session_id && session?.user?.sub) {
        try {
          // Verify the session with the backend
          const response = (await apiHelper.get(
            `/payment/verify-session?session_id=${session_id}`
          )) as any;

          if (response.verified) {
            showToast({
              message: `Welcome! Your ${plan} subscription is now active.`,
              isSuccess: true,
            });
          } else {
            console.warn('Stripe session verification failed');
          }
        } catch (error) {
          console.error('Error verifying Stripe session:', error);
          showToast({
            message: 'Payment verification failed. Please contact support if you were charged.',
            isSuccess: false,
          });
        }
      }
    };

    if (session_id && session?.user?.sub) {
      verifyStripeSession();
    }
  }, [session_id, plan, session?.user?.sub]);

  // Initialize onboarding and handle authentication
  useEffect(() => {
    const initializeOnboarding = async () => {
      // Wait for auth to be initialized
      if (!isInitialized || authLoading) return;

      // If no auth, we'll handle it differently
      if (!isAuthenticated || !session?.user?.sub) {
        setIsLoading(false);
        setIsCheckingOnboarding(false);
        setNeedsAuthentication(true);
        // Set onboarding context for when authentication happens
        setOnboardingContext('company-onboarding', UserRole.EMPLOYER);
        return;
      }

      // Check if user has already completed onboarding via cookie
      const onboardingCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith(`onboardingComplete_${session.user.sub}=`));

      if (onboardingCookie) {
        // User has completed onboarding, redirect to dashboard
        setIsCheckingOnboarding(false);
        completeOnboardingAndRedirect();
        return;
      }

      try {
        setIsLoading(true);

        // Set user role
        setUserRole(UserRole.EMPLOYER);

        // Check for pending company data from interrupted onboarding
        const pendingData = localStorage.getItem('pendingCompanyData');
        if (pendingData) {
          try {
            const companyData = JSON.parse(pendingData);
            await handleCompanySetup(companyData);
            return;
          } catch (error) {
            console.error('Error processing pending company data:', error);
            localStorage.removeItem('pendingCompanyData');
          }
        }

        // Try to load existing company profile
        try {
          const response = await fetchCompany();

          if (response) {
            // Check if onboarding is required - if not, redirect to dashboard
            if (response.onboardingRequired === false || !getCompanyNeedsOnboarding()) {
              setIsCheckingOnboarding(false);
              completeOnboardingAndRedirect();
              return;
            }

            // Onboarding is needed, populate form with existing data for editing
            setInitialData({ ...(response as any), clientId: session.user.sub });
            setIsCheckingOnboarding(false);
          } else {
            // New user, needs onboarding
            setInitialData({ clientId: session.user.sub });
            setIsCheckingOnboarding(false);
          }
        } catch (error) {
          // No existing profile, start fresh
          setInitialData({ clientId: session.user.sub });
          setIsCheckingOnboarding(false);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing onboarding:', error);
        setHasError(true);
        setErrorMessage('Failed to initialize onboarding. Please try again.');
        setIsLoading(false);
        setIsCheckingOnboarding(false);
      }
    };

    initializeOnboarding();
  }, [
    isInitialized,
    authLoading,
    isAuthenticated,
    session?.user?.sub,
    fetchCompany,
    getCompanyNeedsOnboarding,
    completeOnboardingAndRedirect,
    setUserRole,
  ]);

  // Watch for changes in company data and redirect if onboarding is complete
  useEffect(() => {
    // Only redirect if we're not already loading and not currently checking
    if (
      storeCompany &&
      hasCheckedOnboarding &&
      storeCompany.onboardingRequired === false &&
      !isLoading &&
      !isCheckingOnboarding &&
      !hasRedirected
    ) {
      completeOnboardingAndRedirect();
    }
  }, [
    storeCompany?.onboardingRequired,
    hasCheckedOnboarding,
    isLoading,
    isCheckingOnboarding,
    hasRedirected,
    completeOnboardingAndRedirect,
  ]);

  // Handle company setup completion
  const handleCompanySetup = async (companyData: CompanySetupData) => {
    // If user is not authenticated, trigger authentication first
    if (!session?.user?.sub) {
      setNeedsAuthentication(true);

      // Store the form data temporarily
      localStorage.setItem('pendingCompanyData', JSON.stringify(companyData));

      showToast({
        message: 'Please login to complete your company profile.',
        type: 'info',
      });

      // Redirect to Auth0 login with return to this page
      const returnTo = '/company-onboarding';
      const encodedReturnTo = encodeURIComponent(returnTo);
      router.push(`/api/auth/login?returnTo=${encodedReturnTo}&role=${UserRole.EMPLOYER}`);
      return;
    }

    try {
      // User is authenticated, save the company profile
      const completeCompanyData = {
        ...companyData,
        onboardingComplete: true,
        onboardingRequired: false, // Explicitly set to false when completing onboarding
        clientId: session?.user?.sub || '',
      };

      // Create or update the company profile
      let response;
      if (companyData.id) {
        response = await apiHelper.put(`/companies/${companyData.id}`, completeCompanyData);
      } else {
        response = await apiHelper.post('/companies', completeCompanyData);
      }

      // Clear onboarding context
      clearOnboardingContext();

      // Clear any pending data
      localStorage.removeItem('pendingCompanyData');

      // Clear company cache to ensure fresh data is fetched
      localStorage.removeItem('api_cache_/companies/client');
      localStorage.removeItem('recent_fetch_/companies/client');

      // Mark onboarding as complete in the store
      markCompanyOnboardingComplete();

      // Set onboarding completion cookie
      if (session?.user?.sub) {
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year expiry
        document.cookie = `onboardingComplete_${session?.user?.sub}=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; Secure`;
      }

      showToast({
        message: 'Company profile completed successfully!',
        type: 'success',
      });

      // Redirect to dashboard
      router.replace('/dashboard');
    } catch (error: any) {
      console.error('Error completing company setup:', error);

      const errorMsg =
        error.response?.data?.message || error.message || 'Failed to save company data';
      showToast({
        message: errorMsg,
        type: 'error',
      });

      // Don't prevent the user from continuing - they can try again
    }
  };

  const handleClose = () => {
    // Redirect to dashboard when modal is closed
    router.replace('/dashboard');
  };

  // Handle authentication requirement during onboarding (e.g., file uploads)
  const handleAuthenticationRequired = (currentData: Partial<CompanySetupData>) => {
    // Store current form data
    localStorage.setItem('pendingCompanyData', JSON.stringify(currentData));

    showToast({
      message: 'Please login to continue with file uploads.',
      type: 'info',
    });

    // Redirect to Auth0 login with return to this page
    const returnTo = '/company-onboarding';
    const encodedReturnTo = encodeURIComponent(returnTo);
    router.push(`/api/auth/login?returnTo=${encodedReturnTo}&role=${UserRole.EMPLOYER}`);
  };

  // Handle saving progress during onboarding (only if authenticated)
  const handleSaveProgress = async (companyData: Partial<CompanySetupData>) => {
    if (!session?.user?.sub) {
      // If not authenticated, just store locally
      localStorage.setItem('pendingCompanyData', JSON.stringify(companyData));
      return;
    }

    try {
      // Save to backend if authenticated
      if (companyData.id) {
        await apiHelper.put(`/companies/${companyData.id}`, companyData);
      } else {
        const response = await apiHelper.post('/companies', {
          ...companyData,
          clientId: session?.user?.sub || '',
        });
        // Update the form with the new ID
        setInitialData(prev => ({ ...prev, id: (response as any).id }));
      }
    } catch (error) {
      console.error('Error saving progress:', error);
      // Don't show error to user for auto-save failures
    }
  };

  // Show loading while initializing or checking onboarding status
  if (isLoading || isCheckingOnboarding) {
    const loadingText = isCheckingOnboarding ? 'Checking your setup...' : 'Setting you up';
    return <ColourfulLoader text={loadingText} useModalBg={false} />;
  }

  // Show error state if something went wrong
  if (hasError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md mx-auto text-center p-6">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Setup Error</h2>
          <p className="text-gray-700 mb-6">{errorMessage}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <CompanySetupSlider
        onClose={handleClose}
        hasError={hasError}
        errorMessage={errorMessage}
        initialData={finalInitialData}
        onSubmit={async (formData: any) => {
          try {
            const preferences = formData.preferences || {};

            // Convert to CompanySetupData by adding required fields
            const companyData: CompanySetupData = {
              ...formData,
              industry: formData.industry || '',
              companyName: formData.companyName || '',
              companyWebsite: formData.companyWebsite || '',
              department: formData.department || '',
              size: formData.size || '',
              location: formData.location || '',
              contactEmail: formData.contactEmail || '',
              phoneNumber: formData.phoneNumber || '',
              logo: formData.logo || '',
              description: formData.description || '',
              onboardingComplete: true, // Mark onboarding as complete
              preferences: {
                ...preferences,
                // Ensure desiredSalary is a string if it exists
                desiredSalary: preferences.desiredSalary ? String(preferences.desiredSalary) : '',
              },
            };

            await handleCompanySetup(companyData);
          } catch (error: any) {
            console.error('Error in onSubmit handler:', error);
            throw error;
          }
        }}
        isOpen={true} // Always open since this is a dedicated page
      />
    </div>
  );
};

export default CompanyOnboardingPage;
