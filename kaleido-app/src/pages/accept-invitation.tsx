import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { showToast } from '@/components/Toaster';
import { teamService } from '@/services/team.service';
import '@/styles/accept-invitation.css';
import { useUser } from '@auth0/nextjs-auth0/client';
import { motion } from 'framer-motion';
import { Check, LogIn, Sparkles, Users, X } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useRef, useState } from 'react';

const AcceptInvitationPage: React.FC = () => {
  const router = useRouter();
  const { token } = router.query;
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const hasCheckedAuth = useRef(false);
  const hasAccepted = useRef(false);

  useEffect(() => {
    // Wait for router to be ready and token to be available
    if (!router.isReady || !token) {
      return;
    }

    // If still loading auth, wait
    if (userLoading) {
      return;
    }

    // Check auth status only once
    if (!hasCheckedAuth.current) {
      hasCheckedAuth.current = true;

      if (!user) {
        // User is not authenticated, show login prompt
        setShowLoginPrompt(true);
      } else if (!hasAccepted.current) {
        // User is authenticated, accept the invitation
        hasAccepted.current = true;
        acceptInvitation();
      }
    }
  }, [router.isReady, token, user, userLoading]);

  const handleLogin = () => {
    // Redirect to login with the current URL as returnTo
    const returnUrl = `/accept-invitation?token=${token}`;
    router.push(`/api/auth/login?returnTo=${encodeURIComponent(returnUrl)}`);
  };

  const acceptInvitation = async () => {
    if (!token || typeof token !== 'string') {
      setError('Invalid invitation link');
      return;
    }

    setLoading(true);
    try {
      await teamService.acceptInvitation(token);
      setSuccess(true);
      showToast({ message: 'Invitation accepted successfully!', isSuccess: true });

      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (error: any) {
      console.error('Failed to accept invitation:', error);
      const errorMessage =
        error.response?.data?.message || error.message || 'Failed to accept invitation';
      setError(errorMessage);
      showToast({ message: errorMessage, isSuccess: false });
    } finally {
      setLoading(false);
    }
  };

  // Shared background component
  const BackgroundWrapper = ({ children }: { children: React.ReactNode }) => (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/landing/exclusive-2.webp"
          alt="Background"
          fill
          className="object-cover"
          priority
          quality={100}
        />
        {/* Soft gradient overlay extending all the way to the top */}
        <div
          className="absolute inset-0"
          style={{
            background:
              'linear-gradient(to top, rgba(88, 28, 135, 0.7) 0%, rgba(88, 28, 135, 0.5) 15%, rgba(67, 56, 202, 0.4) 30%, rgba(67, 56, 202, 0.3) 45%, rgba(49, 46, 129, 0.2) 60%, rgba(49, 46, 129, 0.15) 75%, rgba(30, 27, 75, 0.1) 90%, rgba(30, 27, 75, 0.05) 100%)',
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        {children}
      </div>
    </div>
  );

  // Show different loading states based on what's happening
  if (userLoading) {
    return (
      <BackgroundWrapper>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
        >
          <ColorfulSmokeyOrbLoader text="Checking authentication..." />
        </motion.div>
      </BackgroundWrapper>
    );
  }

  // Show login prompt if user is not authenticated
  if (showLoginPrompt && !user) {
    return (
      <BackgroundWrapper>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full"
        >
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
              className="relative w-20 h-20"
            >
              <Image
                src="/images/logos/kaleido-logo-only.webp"
                alt="Kaleido"
                width={80}
                height={80}
                className="drop-shadow-2xl"
              />
            </motion.div>
          </div>

          {/* Card */}
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3 }}
                className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 mb-6"
              >
                <Users className="h-8 w-8 text-white" />
              </motion.div>

              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-3xl font-bold text-white mb-3"
              >
                Team Invitation
              </motion.h2>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-white/80 mb-8 text-lg"
              >
                You've been invited to join a team on Kaleido Talent. Please log in or create an
                account to accept this invitation.
              </motion.p>

              <motion.button
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                onClick={handleLogin}
                className="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-base font-medium rounded-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all transform hover:scale-105 shadow-lg"
              >
                <LogIn className="w-5 h-5 mr-2" />
                Log in to Accept Invitation
              </motion.button>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="mt-6 text-sm text-white/60"
              >
                Don't have an account? You'll be able to create one after clicking the button above.
              </motion.p>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-10 right-10 text-white/20">
            <Sparkles className="w-8 h-8 animate-pulse" />
          </div>
          <div className="absolute bottom-10 left-10 text-white/20">
            <Sparkles className="w-6 h-6 animate-pulse animation-delay-1000" />
          </div>
        </motion.div>
      </BackgroundWrapper>
    );
  }

  // Show loading while processing invitation
  if (loading) {
    return (
      <BackgroundWrapper>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
        >
          <ColorfulSmokeyOrbLoader text="Processing invitation..." />
        </motion.div>
      </BackgroundWrapper>
    );
  }

  return (
    <BackgroundWrapper>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="relative w-20 h-20"
          >
            <Image
              src="/images/logos/kaleido-logo-only.webp"
              alt="Kaleido"
              width={80}
              height={80}
              className="drop-shadow-2xl"
            />
          </motion.div>
        </div>

        {/* Card */}
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
          <div className="text-center">
            {success ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 200 }}
                  className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-green-400 to-emerald-600 mb-6"
                >
                  <Check className="h-8 w-8 text-white" />
                </motion.div>
                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-3xl font-bold text-white mb-3"
                >
                  Welcome to the Team!
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="text-white/80 mb-6 text-lg"
                >
                  You've successfully joined the team. Redirecting to your dashboard...
                </motion.p>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  className="inline-block"
                >
                  <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full" />
                </motion.div>
              </>
            ) : error ? (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-red-400 to-pink-600 mb-6"
                >
                  <X className="h-8 w-8 text-white" />
                </motion.div>
                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-3xl font-bold text-white mb-3"
                >
                  Invitation Error
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="text-white/80 mb-8 text-lg"
                >
                  {error}
                </motion.p>
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  onClick={() => router.push('/dashboard')}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-base font-medium rounded-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all transform hover:scale-105 shadow-lg"
                >
                  Go to Dashboard
                </motion.button>
              </>
            ) : (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 mb-6"
                >
                  <Users className="h-8 w-8 text-white" />
                </motion.div>
                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-3xl font-bold text-white mb-3"
                >
                  Processing Invitation
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="text-white/80 text-lg"
                >
                  Please wait while we process your invitation...
                </motion.p>
              </>
            )}
          </div>
        </div>
      </motion.div>
    </BackgroundWrapper>
  );
};

export default AcceptInvitationPage;
