import { VideoJDStatus } from '@/components/VideoJD/consts';
import { AvatarGender } from '@/types/avatar';

import { EntityTypes } from './';
import {
  CultureFitQuestions,
  ExperienceLevel,
  JobMetrics,
  JobStatus,
  JobTLDR,
  TypeOfHiring,
  TypeOfJob,
} from './Job.entities';
import { NotificationType } from './shared';

export type EntityType =
  | 'Company'
  | 'Job'
  | 'Candidate'
  | 'VideoResponse'
  | 'VideoJD'
  | 'Notification';

export interface IBaseEntity {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IJob extends IBaseEntity {
  id: string;
  companyName: string;
  slug?: string;
  companyDescription?: string;
  jobType: string;
  department: string;
  externalId?: string;
  source?: string;
  sourceType?: string;
  uploadedResumes?: string[];
  experienceLevel: ExperienceLevel;
  isGraduateRole: boolean;
  graduateRequirements?: string[];
  experience?: string;
  hiringManagerDescription?: string;
  currency?: string;
  salaryRange?: string;
  finalDraft?: string;
  generatedJDTone?: string;
  generatedJD?: string;
  socialMediaDescription?: string;
  skills?: string[];
  requirements?: string[];
  paymentPeriod?: string;
  typeOfHiring?: TypeOfHiring;
  typeOfJob?: TypeOfJob;
  status: JobStatus;
  isPublished?: boolean;
  metrics?: JobMetrics;
  company?: ICompany;
  candidates?: ICandidate[];
  videoResponses?: IVideoResponse[];
  videoJDs?: IVideoJD[];
  notifications?: INotification[];
  candidateEvaluations?: any;
  jobResponsibilities?: string[];
  cultureFitDescription?: string;
  cultureFitQuestions?: CultureFitQuestions[];
  companyValues?: string[];
  culturalFit?: string[];
  education?: string[];
  language?: string[];
  softSkills?: string[];
  location?: string[];
  benefits?: string[];
  careerGrowth?: string[];
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
  applications?: any[];
  tldr?: JobTLDR;
  candidateApplications?: any[];
  totalCandidates?: number;
  hasVideoUrl?: boolean;

  // Frontend specific fields (not in backend)
  matchedCandidatesCount?: any;
  candidatesCount?: any;
  jobTitle?: string;
  editingJobId?: string;
  title?: string;
  clientId?: string;
  companyWebsite?: string;
  payRange?: string;
  alreadyApplied?: boolean;
  layoutPreference?: string;
  applicantCount?: number;
  actionStats?: {
    videoJD: {
      status: string;
      url?: string;
      count: number;
    };
    matchAndRank: {
      totalCandidates: number;
      topTierCount: number;
      secondTierCount: number;
      pendingEvaluation: number;
      evaluationRate: number;
    };
    cultureFit: {
      completedInterviews: number;
      totalCandidates: number;
      completionRate: number;
      contactedCandidates: number;
      contactRate: number;
    };
  };
  candidateStats?: {
    totalCandidates: number;
    topTierCount: number;
    topTierPercentage: number;
    secondTierCount: number;
    secondTierPercentage: number;
    belowThresholdCount: number;
    topThreshold: number;
    secondThreshold: number;
    completedVideoInterviews: number;
    videoInterviewCompletionRate: number;
    contactedCandidates: number;
    contactRate: number;
    averageMatchScore: number;
    pendingEvaluation: number;
    evaluatedCandidates: number;
    evaluationCompletionRate: number;
  };
  displayStatus?: string;
  stats?: {
    jobTitle: string;
    totalCandidates: number;
    unevaluatedCandidates: number;
    topCandidateThreshold: number;
    secondTierCandidateThreshold: number;
    companyName: string;
    department: string;
  };
}

export interface ICandidate extends IBaseEntity {
  jobSeekerId: any;
  email: any;
  id: string;
  clientId?: string;
  jobId: string;
  externalId?: string;
  fullName: string;
  firstName: string;
  lastName: string;
  jobTitle: string;
  location: string;
  summary?: string;
  skills: string[];
  experience?: Array<{
    title: string;
    company: string;
    duration: string;
    startDate: string | null;
    endDate: string | null;
  }>;
  profileUrl: string;
  linkedinUrl?: string;
  githubUrl?: string;
  source: string;
  matchScore: number;
  status: string;
  tier: 'TOP' | 'SECOND' | 'OTHER' | 'UNRANKED';
  contacted: boolean;
  contactMethod?: string;
  notes?: string;
  criterionMatchedOn: string[];
  yourReasoningForScoring?: string;
  detailedScoreAnalysis?: any;
  hasCompletedVideoInterview: boolean;
  videoIntroEmailSent?: boolean;
  emailCorrespondence?: Array<{
    id: string;
    type: 'SENT' | 'RECEIVED';
    subject: string;
    content: string;
    from: string;
    to: string;
    timestamp: Date;
    emailType: 'interview' | 'offer' | 'status' | 'general' | 'video_intro_invitation';
    metadata?: {
      jobId?: string;
      jobTitle?: string;
      companyName?: string;
      emailType?: string;
      [key: string]: any;
    };
  }>;
  job?: EntityTypes['Job'];
  videoResponses?: any[];
  evaluation?: {
    rank: number;
    matchScore: number;
    lastEvaluatedAt: string;
    criterionMatchedOn: string[];
    detailedScoreAnalysis: {
      interviewFocusAreas: any[];
      areasOfStrength: string[];
      detailedReasoning: {
        skillsMatch: number;
        experienceRelevance: {
          industryExpertise: number;
          yearsOfRelevantExperience: number;
        };
        locationAndAvailability: number;
      };
      areasForImprovement: string[];
      overallMatchPercentage: number;
      specificCriteriaMatched: {
        skillsMatch: number;
        experienceRelevance: number;
        locationAndAvailability: number;
      };
      missingCriticalRequirements: string[];
    };
    yourReasoningForScoring: string;
  };
  jobStats?: {
    jobTitle: string;
    topCandidateThreshold: number;
    secondTierCandidateThreshold: number;
    companyName: string;
    department: string;
  };
}

export interface IVideoResponse extends IBaseEntity {
  id: string;
  candidateId: string;
  jobId: string;
  questionId: string;
  question: string;
  videoUrl: string;
  duration: number;
  recordedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  candidate?: ICandidate;
  job?: EntityTypes['Job'];
}

export interface IVideoJD extends IBaseEntity {
  liveRecordingUrl: string | boolean;
  id: string;
  clientId: string | null;
  jobId: string;
  candidateId?: string;
  gender: AvatarGender;
  languageCode: string;
  voiceId?: string;
  tone?: string;
  scriptLength?: string;
  avatarId?: string;
  voiceSpeed: number;
  voicePitch: number;
  generatedScript?: string;
  synthesiaVideoId?: string;
  status: VideoJDStatus;
  videoUrl?: string;
  job?: EntityTypes['Job'];
}

export interface ICompany {
  id?: string;
  clientId?: string;
  companyName?: string;
  companyWebsite?: string | null;
  industry?: string | null;
  description?: string | null;
  logo?: string | null;
  size?: string | null;
  location?: string | null;
  contactEmail?: string | null;
  phoneNumber?: string | null;
  layoutPreference?: string;
  cultureFitDescription?: string | null;
}

export interface INotification extends IBaseEntity {
  clientId?: string;
  job?: EntityTypes['Job'];
  type?: NotificationType;
  title: string;
  message: string;
  read: boolean;
}
