import { Column, Entity, Join<PERSON><PERSON>umn, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';

import { BaseEntity } from './BaseEntity.entities';
import { Company } from './Company.entities';
import { EntityTypes } from './EntityTypes';
import { IJob } from './interfaces';

import { Notification, VideoJD, VideoResponse } from './';

export interface CultureFitQuestions {
  id: string;
  question: string;
  duration: number;
}

export enum JobStatus {
  DRAFT = 'DRAFT',
  NEW = 'NEW',
  MATCHED = 'MATCHED',
  OPEN = 'OPEN',
  CONTACTED = 'CONTACTED',
  INTERESTED = 'INTERESTED',
  NOT_INTERESTED = 'NOT_INTERESTED',
  INTERVIEWING = 'INTERVIEWING',
  HIRED = 'HIRED',
  REJECTED = 'REJECTED',
  PUBLISHED = 'PUBLISHED',
}

export enum TypeOfHiring {
  EMPLOYMENT = 'EMPLOYMENT',
  PROJECT = 'PROJECT',
}

export enum TypeOfJob {
  PERMANENT = 'PERMANENT',
  CONTRACT = 'CONTRACT',
  PART_TIME = 'PART_TIME',
}

export enum ExperienceLevel {
  GRADUATE = 'graduate',
  JUNIOR = 'junior',
  MID = 'mid',
  SENIOR = 'senior',
  LEAD = 'lead',
}

export interface JobMetrics {
  views: number;
  applications: number;
  viewSources: Record<string, number>;
  applicationSources: Record<string, number>;
  matchScores: Record<string, number>;
}

export interface JobTLDR {
  summary: string;
  keyPoints: string[];
}

@Entity('jobs')
export class Job extends BaseEntity implements IJob {
  @Column({ type: 'varchar', nullable: true })
  clientId?: string;

  @Column({ type: 'varchar', nullable: false })
  companyName!: string;

  @Column({ type: 'varchar', nullable: true })
  title?: string;

  @Column({ type: 'varchar', nullable: false })
  jobTitle!: string;

  @Column({ type: 'int', default: 0 })
  matchedCandidatesCount: number = 0;

  @Column({ type: 'int', default: 0 })
  candidatesCount: number = 0;

  @Column({ type: 'int', default: 0 })
  totalCandidates?: number;

  @Column({ type: 'text', nullable: true })
  companyDescription?: string;

  @Column({ type: 'varchar', nullable: false })
  jobType!: string;

  @Column({ type: 'varchar', nullable: false, default: 'General' })
  department!: string;

  @Column({
    type: 'enum',
    enum: ExperienceLevel,
    default: ExperienceLevel.MID,
  })
  experienceLevel!: ExperienceLevel;

  @Column({ type: 'boolean', default: false })
  isGraduateRole!: boolean;

  // Virtual property to store uploaded resume filenames
  uploadedResumes?: string[];

  @Column({ type: 'varchar', nullable: true })
  experience?: string;

  @Column({ type: 'varchar', nullable: true })
  hiringManagerDescription?: string;

  @Column({ type: 'varchar', nullable: true })
  currency?: string;

  @Column({ type: 'varchar', nullable: true })
  salaryRange?: string;

  @Column({ type: 'text', nullable: true })
  finalDraft?: string;

  @Column({ type: 'text', nullable: true })
  cultureFitDescription?: string;

  @Column({ type: 'jsonb', nullable: true })
  cultureFitQuestions?: Array<CultureFitQuestions>;

  @Column('text', { array: true, default: [] })
  skills?: string[];

  @Column('text', { array: true, default: [] })
  jobResponsibilities?: string[];

  @Column('text', { array: true, default: [] })
  companyValues?: string[];

  @Column('text', { array: true, default: [] })
  culturalFit?: string[];

  @Column('text', { array: true, default: [] })
  education?: string[];

  @Column('text', { array: true, default: [] })
  language?: string[];

  @Column('text', { array: true, default: [] })
  softSkills?: string[];

  @Column('text', { array: true, default: [] })
  location?: string[];

  @Column('text', { array: true, default: [] })
  benefits?: string[];

  @Column('text', { array: true, default: [] })
  careerGrowth?: string[];

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.NEW,
  })
  status!: JobStatus;

  @Column({ type: 'decimal', nullable: true, precision: 5, scale: 2 })
  topCandidateThreshold?: number;

  @Column({ type: 'decimal', nullable: true, precision: 5, scale: 2 })
  secondTierCandidateThreshold?: number;

  @Column('text', { array: true, default: [] })
  requirements?: string[];

  @Column({ type: 'varchar', nullable: true })
  paymentPeriod?: string;

  @Column({
    type: 'enum',
    enum: TypeOfHiring,
    nullable: true,
  })
  typeOfHiring?: TypeOfHiring;

  @Column({
    type: 'enum',
    enum: TypeOfJob,
    nullable: true,
  })
  typeOfJob?: TypeOfJob;

  @OneToMany('VideoResponse', (videoResponse: VideoResponse) => videoResponse.job)
  videoResponses!: any;

  @OneToMany('VideoJD', (videoJD: VideoJD) => videoJD.job)
  videoJDs!: EntityTypes['VideoJD'][];

  @OneToMany('Notification', (notification: Notification) => notification.job)
  notifications!: EntityTypes['Notification'][];

  @ManyToOne('Company', (company: Company) => company.jobs)
  @JoinColumn({ name: 'clientId', referencedColumnName: 'clientId' })
  company?: EntityTypes['Company'];
}
