import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';

import { BaseEntity } from './BaseEntity.entities';

@Entity('candidates')
export class Candidate extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  clientId?: string;

  @Column({ type: 'uuid' })
  jobId!: string;

  @Column({ type: 'uuid', nullable: true })
  jobStats?: any;

  @Column({ type: 'varchar' })
  fullName!: string;

  @Column({ type: 'varchar' })
  firstName!: string;

  @Column({ type: 'varchar' })
  lastName!: string;

  @Column({ type: 'varchar' })
  jobTitle!: string;

  @Column({ type: 'varchar' })
  location!: string;

  @Column({ type: 'text', nullable: true })
  summary!: string | null;

  @Column('text', { array: true, default: [] })
  skills!: string[];

  @Column('jsonb', { nullable: true })
  experience!: Array<{
    title: string;
    company: string;
    duration: string;
    startDate: string | null;
    endDate: string | null;
  }> | null;

  @Column({ type: 'varchar' })
  profileUrl!: string;

  @Column({ type: 'varchar', nullable: true })
  linkedinUrl!: string | null;

  @Column({ type: 'varchar', nullable: true })
  githubUrl!: string | null;

  @Column({ type: 'varchar' })
  source!: string;

  @Column('jsonb', { nullable: true })
  evaluation?: any;

  @JoinColumn({ name: 'jobId' })
  job?: Promise<any>;

  @Column({ type: 'varchar', nullable: true })
  email?: string;

  @Column({ type: 'varchar', nullable: true })
  phone?: string;

  @Column({ type: 'timestamp', nullable: true })
  lastContactDate?: Date;

  @Column({ type: 'varchar', nullable: true })
  currentCompany?: string;

  @Column({ type: 'int', nullable: true })
  yearsOfExperience?: number;

  @Column({ type: 'varchar', nullable: true })
  preferredLocation?: string;

  @Column({ type: 'boolean', default: false })
  isRemoteOnly?: boolean;

  @Column({ type: 'jsonb', nullable: true })
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };

  @Column({ type: 'timestamp', nullable: true })
  availableFrom?: Date;
}
