import { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { CompanyMember, CompanyMemberPermissions } from '@/types/team.types';
import { teamService } from '@/services/team.service';

interface UseTeamPermissionsReturn {
  permissions: CompanyMemberPermissions | null;
  member: CompanyMember | null;
  loading: boolean;
  hasPermission: (permission: keyof CompanyMemberPermissions) => boolean;
  refetch: () => Promise<void>;
}

export const useTeamPermissions = (companyId?: string): UseTeamPermissionsReturn => {
  const { user } = useUser();
  const [permissions, setPermissions] = useState<CompanyMemberPermissions | null>(null);
  const [member, setMember] = useState<CompanyMember | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchPermissions = async () => {
    if (!user?.sub || !companyId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const members = await teamService.getCompanyMembers(companyId);
      const currentMember = members.find(m => m.clientId === user.sub);

      if (currentMember) {
        setMember(currentMember);
        setPermissions(currentMember.permissions);
      }
    } catch (error) {
      console.error('Failed to fetch team permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [user?.sub, companyId]);

  const hasPermission = (permission: keyof CompanyMemberPermissions): boolean => {
    if (!permissions) return false;
    return permissions[permission] === true;
  };

  return {
    permissions,
    member,
    loading,
    hasPermission,
    refetch: fetchPermissions,
  };
};
