import { useCallback } from 'react';

import { showToast } from '@/components/Toaster';
import { IJob } from '@/entities/interfaces';
import apiHelper from '@/lib/apiHelper';
import {
  useJobStateActions,
  useJobStateSelectors,
  useJobStateStore,
  jobDataArrayToIJobArray,
  jobDataToIJob,
} from '@/stores/unifiedJobStore';

interface UseJobStateReturn {
  // Data
  jobs: IJob[];
  selectedJob: IJob | null;
  isLoading: boolean;
  isSaving: boolean;

  // Actions
  fetchJobs: (force?: boolean) => Promise<IJob[]>;
  saveJob: (jobId: string, updates: Partial<IJob>) => Promise<boolean>;
  selectJob: (job: IJob | null) => void;
  refreshJob: (jobId: string) => Promise<IJob | null>;

  // Utility
  getJobById: (jobId: string) => IJob | undefined;
  isJobRecentlyUpdated: (jobId: string, thresholdMs?: number) => boolean;
}

export const useJobState = (): UseJobStateReturn => {
  const jobsData = useJobStateSelectors.useJobs();
  const selectedJobData = useJobStateSelectors.useSelectedJob();

  // Convert to IJob types
  const jobs = jobDataArrayToIJobArray(jobsData);
  const selectedJob = selectedJobData ? jobDataToIJob(selectedJobData) : null;
  const isLoading = useJobStateSelectors.useIsLoading();
  const isSaving = useJobStateSelectors.useIsSaving();

  const actions = useJobStateActions();
  const isDataStale = useJobStateStore(state => state.isDataStale);

  const fetchJobs = useCallback(
    async (force = false): Promise<IJob[]> => {
      // Check if we need to fetch data
      if (!force && jobsData.length > 0 && !isDataStale()) {
        return jobs;
      }

      try {
        actions.setIsLoading(true);

        const response = await apiHelper.get('/jobs');
        const fetchedJobs = response.data || [];

        actions.setJobs(fetchedJobs);

        return fetchedJobs;
      } catch (error) {
        console.error('Error fetching jobs:', error);
        showToast({
          message: 'Failed to fetch jobs',
          isSuccess: false,
        });
        return [];
      } finally {
        actions.setIsLoading(false);
      }
    },
    [jobs, isDataStale, actions]
  );

  const saveJob = useCallback(
    async (jobId: string, updates: Partial<IJob>): Promise<boolean> => {
      try {
        actions.setIsSaving(true);

        // Optimistically update the store
        actions.updateJob(jobId, updates);

        // Make API call
        const response = await apiHelper.patch(`/jobs/${jobId}`, updates);

        if (response.success) {
          // Mark job as updated
          actions.markJobAsUpdated(jobId);

          showToast({
            message: 'Job updated successfully',
            isSuccess: true,
          });

          return true;
        } else {
          // Revert optimistic update on failure
          const originalJob = actions.getJobById(jobId);
          if (originalJob) {
            // We need to revert the changes, but we don't have the original state
            // In a real implementation, you might want to keep a backup or refetch
            console.warn('Failed to save job, but cannot revert optimistic update');
          }

          showToast({
            message: response.message || 'Failed to update job',
            isSuccess: false,
          });

          return false;
        }
      } catch (error) {
        console.error('Error saving job:', error);

        // Revert optimistic update on error
        const originalJob = actions.getJobById(jobId);
        if (originalJob) {
          console.warn('Failed to save job, but cannot revert optimistic update');
        }

        showToast({
          message: 'Failed to save changes',
          isSuccess: false,
        });

        return false;
      } finally {
        actions.setIsSaving(false);
      }
    },
    [actions]
  );

  const selectJob = useCallback(
    (job: IJob | null) => {
      actions.setSelectedJob(job);
    },
    [actions]
  );

  const getJobById = useCallback(
    (jobId: string) => {
      return actions.getJobById(jobId);
    },
    [actions]
  );

  const isJobRecentlyUpdated = useCallback(
    (jobId: string, thresholdMs?: number) => {
      return actions.isJobRecentlyUpdated(jobId, thresholdMs);
    },
    [actions]
  );

  const refreshJob = useCallback(
    async (jobId: string): Promise<IJob | null> => {
      try {
        const response = await apiHelper.get(`/jobs/${jobId}`);

        if (response.success && response.data) {
          actions.updateJob(jobId, response.data);
          return response.data;
        }
        return null;
      } catch (error) {
        console.error('Error refreshing job:', error);
        showToast({
          message: 'Failed to refresh job',
          isSuccess: false,
        });
        return null;
      }
    },
    [actions]
  );

  return {
    // Data
    jobs,
    selectedJob,
    isLoading,
    isSaving,

    // Actions
    fetchJobs,
    saveJob,
    selectJob,
    refreshJob,

    // Utility
    getJobById,
    isJobRecentlyUpdated,
  };
};

// Specialized hooks for specific use cases
export const useJobById = (jobId: string) => {
  return useJobStateSelectors.useJobById(jobId);
};

export const useJobsByStatus = (status: string) => {
  return useJobStateSelectors.useJobsByStatus(status);
};

export const useRecentlyUpdatedJobs = (thresholdMs = 5000) => {
  return useJobStateSelectors.useRecentlyUpdatedJobs(thresholdMs);
};

// Hook for components that only need to update jobs without fetching
export const useJobUpdater = () => {
  const actions = useJobStateActions();
  const isSaving = useJobStateSelectors.useIsSaving();

  const updateJob = useCallback(
    async (jobId: string, updates: Partial<IJob>): Promise<boolean> => {
      try {
        actions.setIsSaving(true);

        // Optimistically update the store
        actions.updateJob(jobId, updates);

        // Make API call
        const response = await apiHelper.patch(`/jobs/${jobId}`, updates);

        if (response.success) {
          actions.markJobAsUpdated(jobId);

          showToast({
            message: 'Job updated successfully',
            isSuccess: true,
          });

          return true;
        } else {
          showToast({
            message: response.message || 'Failed to update job',
            isSuccess: false,
          });

          return false;
        }
      } catch (error) {
        console.error('Error updating job:', error);
        showToast({
          message: 'Failed to save changes',
          isSuccess: false,
        });

        return false;
      } finally {
        actions.setIsSaving(false);
      }
    },
    [actions]
  );

  return {
    updateJob,
    isSaving,
  };
};
