import { useCallback } from 'react';
import { useRouter } from 'next/router';

/**
 * Hook to clear all authentication data and redirect to logout
 */
export const useAuthCleanup = () => {
  const router = useRouter();

  const clearAllAuthData = useCallback(() => {
    // Clear localStorage
    if (typeof window !== 'undefined') {
      // Get all localStorage keys
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          // Remove all auth-related keys
          if (
            key.includes('auth') ||
            key.includes('user') ||
            key.includes('role') ||
            key.includes('token') ||
            key.includes('session') ||
            key.includes('@@auth0')
          ) {
            keysToRemove.push(key);
          }
        }
      }

      // Remove all identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      // Clear sessionStorage
      sessionStorage.clear();

      // Clear all cookies
      document.cookie.split(';').forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        // Clear cookie by setting expiry in the past
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
      });

      // Clear any in-memory caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
      }
    }
  }, []);

  const logoutAndCleanup = useCallback(async () => {
    // First clear all local data
    clearAllAuthData();

    // Then redirect to Auth0 logout
    // This ensures Auth0 also clears its session
    window.location.href = '/api/auth/logout';
  }, [clearAllAuthData]);

  return {
    clearAllAuthData,
    logoutAndCleanup,
  };
};
