import { useRef, useCallback } from 'react';

interface UsePreventInfiniteLoopOptions {
  maxExecutions?: number;
  timeWindow?: number;
  onLimitReached?: () => void;
}

/**
 * Hook to prevent infinite loops in functions that might be called repeatedly
 * Returns a wrapped function that tracks executions and prevents rapid repeated calls
 */
export function usePreventInfiniteLoop<T extends (...args: any[]) => any>(
  fn: T,
  options: UsePreventInfiniteLoopOptions = {}
): T {
  const {
    maxExecutions = 5,
    timeWindow = 2000, // 2 seconds
    onLimitReached,
  } = options;

  const executionTimestamps = useRef<number[]>([]);
  const isBlocked = useRef(false);

  const wrappedFn = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();

      // Clean up old timestamps
      executionTimestamps.current = executionTimestamps.current.filter(
        timestamp => now - timestamp < timeWindow
      );

      // Check if we're blocked
      if (isBlocked.current) {
        console.warn(`Function execution blocked due to rapid repeated calls`);
        return undefined;
      }

      // Check if we've hit the limit
      if (executionTimestamps.current.length >= maxExecutions) {
        console.error(
          `Infinite loop prevention: Function called ${maxExecutions} times in ${timeWindow}ms`
        );

        isBlocked.current = true;

        // Unblock after the time window
        setTimeout(() => {
          isBlocked.current = false;
          executionTimestamps.current = [];
        }, timeWindow);

        if (onLimitReached) {
          onLimitReached();
        }

        return undefined;
      }

      // Record this execution
      executionTimestamps.current.push(now);

      // Execute the original function
      return fn(...args);
    },
    [fn, maxExecutions, timeWindow, onLimitReached]
  ) as T;

  return wrappedFn;
}
