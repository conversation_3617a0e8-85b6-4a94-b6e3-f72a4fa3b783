import { useMemo } from 'react';

import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import { DEFAULT_SECOND_TIER_THRESHOLD, DEFAULT_TOP_TIER_THRESHOLD } from '@/utils/candidateTiers';

interface JobThresholds {
  topCandidateThreshold: number;
  secondTierCandidateThreshold: number;
  minThreshold: number;
}

interface UseJobThresholdsOptions {
  jobId?: string;
  fallbackJob?: any; // For cases where job data comes from props
}

/**
 * Custom hook to get job thresholds from the store or fallback sources
 * This eliminates the need for props drilling of threshold values
 */
export const useJobThresholds = (options: UseJobThresholdsOptions = {}): JobThresholds => {
  const { jobId, fallbackJob } = options;
  const { selectedJob, getJobById } = useMatchRankDetailsStore();
  const { currentJob } = useJobStore();

  const thresholds = useMemo(() => {
    let job = null;

    // Priority order for getting job data:
    // 1. Job from matchrank store by ID (if jobId provided)
    // 2. Current job from jobStore (if jobId matches)
    // 3. Currently selected job from matchrank store
    // 4. Fallback job from props
    if (jobId) {
      job = getJobById(jobId);

      // If not found in matchrank store, check jobStore
      if (!job && currentJob?.id === jobId) {
        job = currentJob;
      }
    } else if (selectedJob) {
      job = selectedJob;
    } else if (currentJob) {
      job = currentJob;
    } else if (fallbackJob) {
      job = fallbackJob;
    }

    // Extract thresholds from job data - only use defaults if no job is found
    let topThreshold: number | undefined;
    let secondThreshold: number | undefined;

    if (job) {
      // Handle different threshold formats from backend
      if (job.topCandidateThreshold !== undefined) {
        // Parse as float to handle string values
        const threshold = parseFloat(job.topCandidateThreshold.toString());

        // Fix incorrect threshold values (like 5000) - these should be percentages
        if (threshold > 100) {
          console.warn(`Invalid topCandidateThreshold value: ${threshold}. Using default 80%.`);
          topThreshold = 80; // Fallback for corrupted data
        } else {
          topThreshold = threshold > 1 ? threshold : threshold * 100;
        }
      }

      if (job.secondTierCandidateThreshold !== undefined) {
        // Parse as float to handle string values
        const threshold = parseFloat(job.secondTierCandidateThreshold.toString());

        // Fix incorrect threshold values (like 4500) - these should be percentages
        if (threshold > 100) {
          console.warn(
            `Invalid secondTierCandidateThreshold value: ${threshold}. Using default 60%.`
          );
          secondThreshold = 60; // Fallback for corrupted data
        } else {
          secondThreshold = threshold > 1 ? threshold : threshold * 100;
        }
      }
    }

    // Only use defaults if no job is found at all
    if (!job) {
      topThreshold = DEFAULT_TOP_TIER_THRESHOLD * 100; // 80%
      secondThreshold = DEFAULT_SECOND_TIER_THRESHOLD * 100; // 60%
    }

    // Calculate minimum threshold (typically 50% of second tier threshold)
    const minThreshold = secondThreshold ? Math.max(secondThreshold * 0.75, 45) : 45;

    return {
      topCandidateThreshold: topThreshold || 0,
      secondTierCandidateThreshold: secondThreshold || 0,
      minThreshold: minThreshold,
    };
  }, [jobId, selectedJob, currentJob, getJobById, fallbackJob]);

  return thresholds;
};

/**
 * Hook specifically for getting thresholds for the currently selected job
 */
export const useSelectedJobThresholds = (): JobThresholds => {
  return useJobThresholds();
};

/**
 * Hook for getting thresholds for a specific job by ID
 */
export const useJobThresholdsById = (jobId: string): JobThresholds => {
  return useJobThresholds({ jobId });
};
