import { useEffect, useMemo, useState, useRef } from 'react';

import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';

interface ProfileCompletionState {
  isModalOpen: boolean;
  missingFields: {
    firstName: boolean;
    lastName: boolean;
    email: boolean;
  };
  isLoading: boolean;
  openModal: () => void;
  closeModal: () => void;
  handleComplete: () => void;
}

export const useProfileCompletion = (): ProfileCompletionState => {
  const { user, isLoading: isUserLoading } = useUser();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();
  const [missingFields, setMissingFields] = useState({
    firstName: false,
    lastName: false,
    email: false,
  });
  const hasCheckedRef = useRef(false);
  const isCheckingRef = useRef(false);
  const lastCheckTimeRef = useRef(0);

  // Check if the user has the job-seeker role using only localStorage
  const isJobSeeker = useMemo(() => {
    if (!user?.sub) return false;

    try {
      const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
      if (cachedRoleData) {
        const { role } = JSON.parse(cachedRoleData);
        return role === UserRole.JOB_SEEKER;
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  }, [user]);

  useEffect(() => {
    const checkProfileCompletion = async () => {
      // Only proceed if user is a job-seeker and we haven't checked yet
      if (!user || isUserLoading || hasCheckedRef.current || !isJobSeeker) {
        setIsLoading(false);
        return;
      }

      // Prevent concurrent requests
      if (isCheckingRef.current) return;

      // Rate limit checks - don't check more than once per minute
      const now = Date.now();
      if (now - lastCheckTimeRef.current < 60000) {
        setIsLoading(false);
        return;
      }

      isCheckingRef.current = true;
      lastCheckTimeRef.current = now;

      setIsLoading(true);

      try {
        // First check localStorage cache for profile completion status
        const cacheKey = `profile_completion_${user.sub}`;
        const cachedData = localStorage.getItem(cacheKey);

        if (cachedData) {
          const { hasProfile, timestamp } = JSON.parse(cachedData);
          // Use cache if it's less than 5 minutes old
          if (Date.now() - timestamp < 300000) {
            hasCheckedRef.current = true;
            setIsLoading(false);
            if (hasProfile) {
              return;
            }
          }
        }

        // Check if user has a profile
        const response = await apiHelper.get('/job-seekers');

        // Cache the result
        localStorage.setItem(
          cacheKey,
          JSON.stringify({
            hasProfile: !!(response && response.id),
            timestamp: Date.now(),
          })
        );

        // If user has a complete profile, we don't need to show the modal
        if (response && response.id) {
          hasCheckedRef.current = true;
          setIsLoading(false);
          return;
        }

        // Check which fields are missing
        const missing = {
          firstName: !user.given_name && (!user.name || user.name === 'undefined undefined'),
          lastName: !user.family_name && (!user.name || user.name === 'undefined undefined'),
          email: !user.email,
        };

        setMissingFields(missing);

        // If any fields are missing, show the modal
        if (missing.firstName || missing.lastName || missing.email) {
          setIsModalOpen(true);
        }

        hasCheckedRef.current = true;
      } catch (error) {
        console.error('Error checking profile completion:', error);
        // If there's an error, mark as checked to prevent retries
        hasCheckedRef.current = true;

        // If it's a circuit breaker error, cache that we shouldn't check again
        if (error?.message?.includes('Circuit breaker')) {
          const cacheKey = `profile_completion_${user.sub}`;
          localStorage.setItem(
            cacheKey,
            JSON.stringify({
              hasProfile: true, // Assume profile exists to prevent further checks
              timestamp: Date.now(),
            })
          );
        }
      } finally {
        setIsLoading(false);
        isCheckingRef.current = false;
      }
    };

    checkProfileCompletion();
  }, [user, isUserLoading, isJobSeeker]); // Simplified dependencies

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleComplete = () => {
    setIsModalOpen(false);
    // Refresh data to show updated user data
    invalidateJobsCache();
    refreshJobs(true);

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }
  };

  // Return default state if not a job-seeker
  if (!isJobSeeker) {
    return {
      isModalOpen: false,
      missingFields: {
        firstName: false,
        lastName: false,
        email: false,
      },
      isLoading: false,
      openModal: () => {}, // No-op function
      closeModal: () => {}, // No-op function
      handleComplete: () => {}, // No-op function
    };
  }

  return {
    isModalOpen,
    missingFields,
    isLoading: isLoading || isUserLoading,
    openModal,
    closeModal,
    handleComplete,
  };
};
