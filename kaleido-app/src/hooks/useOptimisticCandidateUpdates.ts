import { useCallback } from 'react';
import { useJobStore } from '@/stores/unifiedJobStore';
import { showToast } from '@/components/Toaster';

export const useOptimisticCandidateUpdates = () => {
  const { updateCandidateStatusOptimistic, silentDataSync } = useJobStore();

  const updateCandidateStatus = useCallback(
    async (jobId: string, candidateId: string, newStatus: string) => {
      try {
        // Update UI immediately with optimistic update
        updateCandidateStatusOptimistic(jobId, candidateId, newStatus);

        // Show immediate feedback
        showToast({
          message: `Candidate status updated to ${newStatus}`,
          isSuccess: true,
        });

        // Sync data silently in background after a short delay
        setTimeout(() => {
          silentDataSync(jobId).catch(console.error);
        }, 1000);

        return true;
      } catch (error) {
        console.error('Error updating candidate status:', error);
        showToast({
          message: 'Failed to update candidate status',
          isSuccess: false,
        });
        return false;
      }
    },
    [updateCandidateStatusOptimistic, silentDataSync]
  );

  const shortlistCandidate = useCallback(
    (jobId: string, candidateId: string) => {
      return updateCandidateStatus(jobId, candidateId, 'SHORTLISTED');
    },
    [updateCandidateStatus]
  );

  const markAsInterviewing = useCallback(
    (jobId: string, candidateId: string) => {
      return updateCandidateStatus(jobId, candidateId, 'INTERVIEWING');
    },
    [updateCandidateStatus]
  );

  const markAsRejected = useCallback(
    (jobId: string, candidateId: string) => {
      return updateCandidateStatus(jobId, candidateId, 'REJECTED');
    },
    [updateCandidateStatus]
  );

  const markAsHired = useCallback(
    (jobId: string, candidateId: string) => {
      return updateCandidateStatus(jobId, candidateId, 'HIRED');
    },
    [updateCandidateStatus]
  );

  return {
    updateCandidateStatus,
    shortlistCandidate,
    markAsInterviewing,
    markAsRejected,
    markAsHired,
  };
};
