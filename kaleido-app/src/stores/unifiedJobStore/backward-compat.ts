// ============================================================================
// BACKWARD COMPATIBILITY UTILITIES
// ============================================================================
// This file provides type-safe backward compatibility for the unified store

import type { UnifiedJobStoreState, JobData } from './types';
import type { IJob } from '@/entities/interfaces';

/**
 * Convert JobData to IJob for backward compatibility
 */
export function jobDataToIJob(jobData: JobData): IJob {
  return {
    ...jobData,
    // Ensure required IJob fields are present
    matchedCandidatesCount: jobData.matchedCandidatesCount ?? 0,
    candidatesCount: jobData.candidatesCount ?? 0,
    jobTitle: jobData.jobTitle,
    // Handle location type difference
    location: Array.isArray(jobData.location)
      ? jobData.location
      : jobData.location
        ? [jobData.location]
        : [],
    // Convert cultureFitQuestions to the expected format
    cultureFitQuestions: Array.isArray(jobData.cultureFitQuestions)
      ? jobData.cultureFitQuestions.filter(q => typeof q === 'object' && 'id' in q)
      : [],
  } as unknown as IJob;
}

/**
 * Convert JobData array to IJob array
 */
export function jobDataArrayToIJobArray(jobDataArray: JobData[]): IJob[] {
  return jobDataArray.map(jobDataToIJob);
}

/**
 * Type guard to check if value is JobData
 */
export function isJobData(value: any): value is JobData {
  return value && typeof value === 'object' && 'jobTitle' in value;
}

/**
 * Type guard to check if value is JobData array
 */
export function isJobDataArray(value: any): value is JobData[] {
  return Array.isArray(value) && value.every(isJobData);
}

/**
 * Automatically convert JobData to IJob when needed
 */
export function autoConvertJob<T>(value: T): T {
  if (isJobData(value)) {
    return jobDataToIJob(value) as any;
  }
  if (isJobDataArray(value)) {
    return jobDataArrayToIJobArray(value) as any;
  }
  return value;
}
