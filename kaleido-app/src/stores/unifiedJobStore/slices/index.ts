// ============================================================================
// UNIFIED JOB STORE SLICES - Index
// ============================================================================
// This file combines all the store slices into a unified interface

export { createJobOperationsSlice } from './job-operations';
export type { JobOperationsSlice } from './job-operations';

export { createJobStateOperationsSlice } from './job-state-operations';
export type { JobStateOperationsSlice } from './job-state-operations';

export { createUploadJobsOperationsSlice } from './upload-jobs-operations';
export type { UploadJobsOperationsSlice } from './upload-jobs-operations';

export { createJobWizardOperationsSlice } from './job-wizard-operations';
export type { JobWizardOperationsSlice } from './job-wizard-operations';

export { createJobsStoreOperationsSlice } from './jobs-store-operations';
export type { JobsStoreOperationsSlice } from './jobs-store-operations';

// Import the slice types
import type { JobOperationsSlice } from './job-operations';
import type { JobStateOperationsSlice } from './job-state-operations';
import type { UploadJobsOperationsSlice } from './upload-jobs-operations';
import type { JobWizardOperationsSlice } from './job-wizard-operations';
import type { JobsStoreOperationsSlice } from './jobs-store-operations';

// Combined slice type for the unified store
export type UnifiedJobStoreSlices = JobOperationsSlice &
  JobStateOperationsSlice &
  UploadJobsOperationsSlice &
  JobWizardOperationsSlice &
  JobsStoreOperationsSlice;
