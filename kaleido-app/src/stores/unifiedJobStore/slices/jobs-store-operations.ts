// ============================================================================
// JOBS STORE OPERATIONS SLICE - Additional jobsStore functionality
// ============================================================================
// This slice provides additional jobsStore methods for backward compatibility

import apiHelper from '@/lib/apiHelper';
import { clearUserCaches } from '../cache-utils';
import { IJob } from '@/entities/interfaces';
import { stepValidations, steps } from '@/contexts/jobs/constants';

export interface JobsStoreOperationsSlice {
  // ========== Cache Management ==========
  invalidateCompanyCache: () => void;
  invalidateJobsCache: () => void;

  // ========== Job Refresh ==========
  refreshJobs: (forceRefresh?: boolean) => Promise<IJob[]>;

  // ========== Job Management (Special Methods) ==========
  updateJob: (jobId: string, jobData: Partial<IJob>) => Promise<any>;

  updateJobMatchRankCriteria: (
    jobId: string,
    criteriaData: {
      topCandidateThreshold?: number;
      secondTierCandidateThreshold?: number;
      requirements?: string[];
    }
  ) => Promise<any>;

  updateJobCultureFit: (
    jobId: string,
    cultureFitData: {
      cultureFitQuestions?: any[];
      cultureFitDescription?: string;
    }
  ) => Promise<any>;

  // ========== Jobs By Status ==========
  fetchJobsByStatus: (
    page: number,
    status: string,
    includeStats?: boolean,
    forceRefresh?: boolean
  ) => Promise<void>;

  setJobsByStatus: (data: {
    jobsByStatus: { [key: string]: { jobs: IJob[]; count: number } };
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  }) => void;

  // ========== Pending Request Management ==========
  addPendingRequest: (key: string, request: Promise<any>) => void;
  removePendingRequest: (key: string) => void;
  hasPendingRequest: (key: string) => boolean;
  waitForPendingRequest: <T>(key: string) => Promise<T | null>;

  // ========== URL Management ==========
  clearUrlParameters: (router: any, searchParams: any) => void;
}

export const createJobsStoreOperationsSlice = (set: any, get: any): JobsStoreOperationsSlice => ({
  // ========== Cache Management ==========
  invalidateCompanyCache: () => {
    set({ lastCompanyFetch: null });
    clearUserCaches();
  },

  invalidateJobsCache: () => {
    // Reset job refresh state in Zustand
    set({
      lastJobsFetch: null,
      refreshingJobs: false,
    });

    // Clear all API helper's caches for jobs endpoints
    apiHelper.clearJobsCache();

    // Specifically clear any by-status caches
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage);
      const byStatusCacheKeys = keys.filter(key => key.startsWith('api_cache_/jobs/by-status'));
      byStatusCacheKeys.forEach(key => {
        localStorage.removeItem(key);
      });
    }
  },

  // ========== Job Refresh ==========
  refreshJobs: async (forceRefresh = false) => {
    const now = Date.now();
    const lastRequest = get().lastRefreshRequest;
    const debounceMs = get().refreshDebounceMs;

    // If we're already refreshing jobs and not forcing, return current jobs
    if (get().refreshingJobs && !forceRefresh) {
      return get().jobs;
    }

    // Check if we're within debounce period and not forcing
    if (!forceRefresh && now - lastRequest < debounceMs) {
      return get().jobs;
    }

    // Check for any active file uploads
    const hasActiveUploads = Object.values(get().workerJobs || {}).some(
      (job: any) => job.status === 'queued' || job.status === 'active'
    );

    if (hasActiveUploads && !forceRefresh) {
      return get().jobs;
    }

    try {
      set({ refreshingJobs: true, lastRefreshRequest: now });

      const response = await apiHelper.get('/jobs');

      // Handle both direct array response and {success, data} format
      const jobsData = Array.isArray(response) ? response : response?.data || get().jobs;

      if (jobsData && Array.isArray(jobsData)) {
        set({
          jobs: jobsData,
          jobsById: jobsData.reduce((acc: any, job: any) => {
            acc[job.id] = job;
            return acc;
          }, {}),
          lastJobsFetch: now,
        });
        return jobsData;
      }

      return get().jobs;
    } catch (error) {
      console.error('Error refreshing jobs:', error);
      return get().jobs;
    } finally {
      set({ refreshingJobs: false });
    }
  },

  // ========== Job Management (Special Methods) ==========
  updateJob: async (jobId, jobData) => {
    try {
      const response = await apiHelper.patch(`/jobs/${jobId}`, jobData);

      // Update local state if successful
      // Handle both direct response and {success, data} format
      if (response && response.success !== false) {
        const state = get();
        state.updateJob(jobId, jobData);
      }

      return response;
    } catch (error) {
      console.error('Error updating job:', error);
      throw error;
    }
  },

  updateJobMatchRankCriteria: async (jobId, criteriaData) => {
    try {
      const response = await apiHelper.patch(`/jobs/${jobId}/matchrank-criteria`, criteriaData);
      return response;
    } catch (error) {
      console.error('Error updating job MatchRank criteria:', error);
      throw error;
    }
  },

  updateJobCultureFit: async (jobId, cultureFitData) => {
    try {
      const response = await apiHelper.patch(`/jobs/${jobId}/culture-fit`, cultureFitData);

      // Trigger a custom event to notify VideoIntroTab to refresh
      if (response.requiresRefresh || response.success) {
        window.dispatchEvent(new CustomEvent('cultureFitUpdated', { detail: { jobId } }));
      }

      return response;
    } catch (error) {
      console.error('Error updating job culture fit:', error);
      throw error;
    }
  },

  // ========== Jobs By Status ==========
  fetchJobsByStatus: async (
    page: number,
    status: string,
    includeStats: boolean = false,
    forceRefresh: boolean = false
  ) => {
    try {
      // Create a unique key for this request
      const requestKey = `jobs_by_status_${status}_${page}_${includeStats}`;

      // Check if we already have a pending request for this data
      if (get().hasPendingRequest(requestKey)) {
        await get().waitForPendingRequest(requestKey);
        return;
      }

      // Check if we already have this data in the store
      const state = get();
      const lastFetchTime = state.lastJobsByStatusFetch[`${status}_${page}`] || 0;
      const now = Date.now();
      const cacheTime = 60 * 1000; // 1 minute cache

      // If we have recent data, use it - now considering empty arrays as valid data
      if (
        !forceRefresh &&
        now - lastFetchTime < cacheTime &&
        state.jobsByStatus[status] &&
        state.jobsByStatus[status].jobs !== undefined &&
        state.jobsByStatusPagination.currentPage === page
      ) {
        return;
      }

      // Create the request parameters
      const params: any = {
        page,
        limit: 10,
        includeStats,
      };

      // Add status filter if not 'ALL'
      if (status && status !== 'ALL') {
        params.status = status;
      }

      // Add userRole if present
      const userRole = state.userRole;
      if (userRole) {
        params.userRole = userRole;
      }

      set({ isLoading: true });

      // Create the request
      const request = apiHelper.get('/jobs/by-status', { params });

      // Register the pending request
      get().addPendingRequest(requestKey, request);

      try {
        const response = await request;

        // Reset fetch attempt counter on success
        sessionStorage.removeItem(`fetch_attempt_${status}_${page}`);

        // Convert the simplified response to the format expected by the UI
        const jobsByStatus = { ...state.jobsByStatus }; // Keep existing data for other statuses
        const statusKey = status || 'ALL';

        // The response from the API has the structure { data: [...], pagination: {...} }
        // directly at the top level
        const jobs = response.data || [];
        const pagination = response.pagination || {};

        jobsByStatus[statusKey] = {
          jobs: jobs,
          count: pagination.totalItems || jobs.length,
          stats: response.stats, // Include stats if available
        };

        // Update the store state
        set({
          jobsByStatus,
          jobsByStatusPagination: {
            currentPage: pagination.currentPage || 1,
            totalPages: pagination.totalPages || 1,
            totalItems: pagination.totalItems || jobs.length,
            itemsPerPage: pagination.itemsPerPage || 10,
          },
          isLoading: false,
          lastJobsByStatusFetch: {
            ...state.lastJobsByStatusFetch,
            [`${status}_${page}`]: now,
          },
        });

        // Verify state was updated
        const newState = get();
      } catch (error) {
        console.error('[fetchJobsByStatus] Error in request:', error);
        const fetchAttemptKey = `fetch_attempt_${status}_${page}`;
        const fetchAttempt = parseInt(sessionStorage.getItem(fetchAttemptKey) || '0') + 1;

        if (fetchAttempt < 3) {
          sessionStorage.setItem(fetchAttemptKey, fetchAttempt.toString());
          console.warn(
            `[fetchJobsByStatus] Retrying fetch jobs by status (attempt ${fetchAttempt})...`
          );
          setTimeout(() => {
            get().fetchJobsByStatus(page, status, includeStats, forceRefresh);
          }, 1000 * fetchAttempt);
        } else {
          console.error(
            '[fetchJobsByStatus] Error fetching jobs by status after 3 attempts:',
            error
          );
          sessionStorage.removeItem(fetchAttemptKey);
          set({ isLoading: false });
        }
      } finally {
        // Always remove the pending request when done
        get().removePendingRequest(requestKey);
      }
    } catch (error) {
      console.error('[fetchJobsByStatus] Outer error:', error);
      set({ isLoading: false });
    }
  },

  setJobsByStatus: data => {
    set({
      jobsByStatus: data.jobsByStatus,
      jobsByStatusPagination: data.pagination,
    });
  },

  // ========== Pending Request Management ==========
  addPendingRequest: (key: string, request: Promise<any>) => {
    set((state: any) => ({
      pendingRequests: {
        ...state.pendingRequests,
        [key]: request,
      },
    }));
  },

  removePendingRequest: (key: string) => {
    set((state: any) => ({
      pendingRequests: Object.fromEntries(
        Object.entries(state.pendingRequests).filter(([k]) => k !== key)
      ),
    }));
  },

  hasPendingRequest: (key: string) => {
    const state = get();
    return !!state.pendingRequests[key];
  },

  waitForPendingRequest: async <T>(key: string): Promise<T | null> => {
    const state = get();
    if (state.hasPendingRequest(key)) {
      const request = state.pendingRequests[key];
      state.removePendingRequest(key);
      try {
        const result = await request;
        return result as T;
      } catch (error) {
        return null;
      }
    }
    return null;
  },

  // ========== URL Management ==========
  clearUrlParameters: (router, _searchParams) => {
    // Check if this is App Router (next/navigation) or Pages Router (next/router)
    if (router.query !== undefined) {
      // Pages Router - has query property and supports shallow routing
      router.push(window.location.pathname, undefined, { shallow: true });
    } else {
      // App Router - doesn't have query property and doesn't need shallow option
      router.push(window.location.pathname);
    }

    // Clear selected job
    set({ selectedJobId: null, selectedJob: null });
  },
});
