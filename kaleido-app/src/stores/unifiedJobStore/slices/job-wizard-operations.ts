// ============================================================================
// JOB WIZARD OPERATIONS SLICE - Job Creation Wizard (Missing jobsStore)
// ============================================================================
// This slice provides the missing jobsStore functionality for job creation wizard

import { steps } from '@/contexts/jobs/constants';
import { CompanyData } from '@/contexts/jobs/types';
import { JobStatus } from '@/entities';
import { IJob } from '@/entities/interfaces';
import {
  clearJobFromLocalStorage,
  getDefaultJob,
  loadJobFromLocalStorage,
  saveJobToLocalStorage,
} from '../helpers';

export interface JobWizardOperationsSlice {
  // ========== Job Creation Wizard (The missing jobsStore) ==========
  setJob: (job: IJob | null) => void;
  setIsHydrated: (hydrated: boolean) => void;
  setUserRole: (role: string | null) => void;
  setCompany: (company: CompanyData | null) => void;
  setActiveStep: (step: number) => void;
  clearJobData: () => void;
  updateURLAndStorage: (step: number) => void;
  setFullJobDescription: (data: IJob) => void;
  loadDraftData: () => boolean;

  // ========== Job Creation Process ==========
  addJobFromDescription: (data: IJob) => Promise<IJob | null>;
  updateJobDescription: (key: string, value: any) => void;
  fetchJobById: (jobId: string) => Promise<any>;

  // ========== Wizard Navigation ==========
  setFadeIn: (fadeIn: boolean) => void;
  handleNext: () => void;
  handleBack: () => void;
  handleSkip: () => void;
  handleFinalDraftComplete: () => void;
  handleEdit: () => void;
  handlePreview: () => void;

  // ========== Validation ==========
  isStepValid: (stepIndex: number) => boolean;
  validateStep: (stepIndex: number) => boolean;

  // ========== Company Management ==========
  fetchCompanyData: (clientId: string, forceRefresh?: boolean) => Promise<void>;
  updateCompanyData: (data: Partial<CompanyData>) => Promise<void>;

  // ========== AI Features ==========
  scoutCandidates: (job: IJob) => Promise<{ jobId: string } | null>;
  generateResponsibilities: (jobData: any) => Promise<any>;

  // ========== Utility Methods ==========
  clearUrlParameters: (router: any, searchParams: any) => void;
}

export const createJobWizardOperationsSlice = (set: any, get: any): JobWizardOperationsSlice => ({
  // ========== Job Creation Wizard (The missing jobsStore) ==========
  setJob: (job: IJob | null) => {
    set({ job: job || getDefaultJob() });
  },

  setIsHydrated: (hydrated: boolean) => {
    set({ isHydrated: hydrated });
  },

  setUserRole: (role: string | null) => {
    set({ userRole: role });
  },

  setCompany: (company: CompanyData | null) => {
    set({ company });
  },

  setActiveStep: (step: number) => {
    set({ activeStep: step, fadeIn: true });
    if (typeof window !== 'undefined') {
      localStorage.setItem('activeStep', step.toString());
    }
  },

  clearJobData: () => {
    const initialFormData = {
      jobTitle: '',
      jobType: '',
      jobDescription: '',
      department: '',
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
      requirements: '',
      status: JobStatus.DRAFT,
      location: '',
      employmentType: 'FULL_TIME',
      workMode: 'HYBRID',
      benefits: [],
    };

    set({
      job: getDefaultJob(),
      formData: initialFormData,
      originalFormData: null,
      hasUnsavedChanges: false,
      activeStep: 0,
      isFinalStep: false,
      isPreview: false,
      validationErrors: {},
    });
    clearJobFromLocalStorage();
  },

  updateURLAndStorage: (step: number) => {
    const stepName = steps[step]?.name?.toLowerCase().replace(/\s+/g, '-') || '';
    if (typeof window !== 'undefined') {
      localStorage.setItem('activeStep', step.toString());
      // Update URL without page reload
      const url = new URL(window.location.href);
      url.searchParams.set('step', stepName);
      window.history.replaceState({}, '', url.toString());
    }
  },

  setFullJobDescription: (data: IJob) => {
    set({ job: data, hasUnsavedChanges: true });
    saveJobToLocalStorage(data);
  },

  loadDraftData: () => {
    const savedJob = loadJobFromLocalStorage();
    if (savedJob) {
      set({ job: savedJob, hasUnsavedChanges: false });
      return true;
    }
    return false;
  },

  // ========== Job Creation Process ==========
  addJobFromDescription: async (data: IJob) => {
    try {
      set({ isLoading: true });

      // Import apiHelper dynamically
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const response = await apiHelper.post<IJob>('/jobs', data);

      // Clear job from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('job');
        localStorage.setItem('job', JSON.stringify(response));
      }

      // Clear all API helper's caches for jobs
      apiHelper.clearJobsCache();

      // Update store state
      set((state: any) => ({
        job: response,
        jobs: Array.isArray(state.jobs) ? [...state.jobs, response] : [response],
        jobsById: {
          ...state.jobsById,
          [response.id]: response,
        },
        isLoading: false,
        lastJobsFetch: Date.now(),
      }));

      return response;
    } catch (error) {
      console.error('Error adding job from description:', error);
      set({ isLoading: false });
      return null;
    }
  },

  updateJobDescription: (key: string, value: any) => {
    set((state: any) => ({
      job: { ...state.job, [key]: value },
      hasUnsavedChanges: true,
    }));

    const currentJob = get().job;
    saveJobToLocalStorage({ ...currentJob, [key]: value });
  },

  fetchJobById: async (jobId: string) => {
    const state = get();

    // Check if we already have this job in selectedJob with the same ID and it has data
    if (state.selectedJob?.id === jobId && Object.keys(state.selectedJob).length > 1) {
      return state.selectedJob;
    }

    // Check if there's already a pending request for this job
    const pendingRequestKey = `fetchJob_${jobId}`;
    if (state.hasPendingRequest && state.hasPendingRequest(pendingRequestKey)) {
      return await state.waitForPendingRequest(pendingRequestKey);
    }

    try {
      // Make the API call directly
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const response = await apiHelper.get(`/jobs/${jobId}`);

      // The response structure from your API is { job: {...}, candidates: [...], ... }
      if (response && response.job) {
        const jobData = response.job;

        // Update the job in the store
        set((state: any) => ({
          job: jobData,
          selectedJob: jobData,
          selectedJobId: jobId,
          currentJob: jobData,
          jobsById: {
            ...state.jobsById,
            [jobId]: jobData,
          },
          jobs: state.jobs.map((j: any) => (j.id === jobId ? jobData : j)),
        }));

        return jobData;
      } else if (response && response.id) {
        // Handle case where response is the job directly

        set((state: any) => ({
          job: response,
          selectedJob: response,
          selectedJobId: jobId,
          currentJob: response,
          jobsById: {
            ...state.jobsById,
            [jobId]: response,
          },
          jobs: state.jobs.map((j: any) => (j.id === jobId ? response : j)),
        }));

        return response;
      }

      return null;
    } catch (error) {
      console.error('[fetchJobById - wizard] Error fetching job:', error);
      return null;
    }
  },

  // ========== Wizard Navigation ==========
  setFadeIn: (fadeIn: boolean) => {
    set({ fadeIn });
  },

  handleNext: () => {
    const state = get();

    if (state.isPreview) {
      set({ isPreview: false });
      return;
    }

    // Save the current job data to localStorage before moving to next step
    if (state.job) {
      saveJobToLocalStorage(state.job);
    }

    if (state.activeStep < state.totalSteps - 1) {
      const nextStep = state.activeStep + 1;
      get().setActiveStep(nextStep);
      get().updateURLAndStorage(nextStep);
    } else {
      set({ isFinalStep: true });
    }
  },

  handleBack: () => {
    const state = get();

    if (state.isPreview) {
      set({ isPreview: false });
      return;
    }

    // Save the current job data to localStorage before moving to previous step
    if (state.job) {
      saveJobToLocalStorage(state.job);
    }

    if (state.activeStep > 0) {
      const prevStep = state.activeStep - 1;
      get().setActiveStep(prevStep);
      get().updateURLAndStorage(prevStep);
    }
  },

  handleSkip: () => {
    const state = get();

    // Save the current job data to localStorage before skipping
    if (state.job) {
      saveJobToLocalStorage(state.job);
    }

    if (state.activeStep < state.totalSteps - 1) {
      const nextStep = state.activeStep + 1;
      get().setActiveStep(nextStep);
      get().updateURLAndStorage(nextStep);
    }
  },

  handleFinalDraftComplete: () => {
    set({ isFinalStep: true });
  },

  handleEdit: () => {
    set({ isPreview: false });
  },

  handlePreview: () => {
    set({ isPreview: true });
  },

  // ========== Validation ==========
  isStepValid: (stepIndex: number) => {
    const state = get();
    const job = state.job;

    if (!job) return false;

    // This would implement step validation logic
    // For now, return true as placeholder
    return true;
  },

  validateStep: (stepIndex: number) => {
    return get().isStepValid(stepIndex);
  },

  // ========== Company Management ==========
  fetchCompanyData: async (clientId: string, forceRefresh = false) => {
    const state = get();
    const cacheTimeout = 5 * 60 * 1000; // 5 minutes

    if (
      !forceRefresh &&
      state.lastCompanyFetch &&
      Date.now() - state.lastCompanyFetch < cacheTimeout
    ) {
      return;
    }

    try {
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const response = await apiHelper.get(`/companies/${clientId}`);

      if (response) {
        set({
          company: response,
          lastCompanyFetch: Date.now(),
        });
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
    }
  },

  updateCompanyData: async (data: Partial<CompanyData>) => {
    const state = get();

    if (!state.company) {
      throw new Error('No company data to update');
    }

    try {
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const response = await apiHelper.put(`/companies/${state.company.id}`, data);

      if (response) {
        set({
          company: { ...state.company, ...response },
          lastCompanyFetch: Date.now(),
        });
      }
    } catch (error) {
      console.error('Error updating company data:', error);
      throw error;
    }
  },

  // ========== AI Features ==========
  scoutCandidates: async (job: IJob) => {
    try {
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const response = await apiHelper.post<{ jobId: string }>('/jobs/scout-candidates', { job });

      // Clear all API helper's caches for jobs
      apiHelper.clearJobsCache();

      return response;
    } catch (error) {
      console.error('Error scouting candidates:', error);
      return null;
    }
  },

  generateResponsibilities: async (jobData: any) => {
    try {
      // Create a unique request signature based on the input parameters
      const requestSignature = JSON.stringify(jobData);
      const requestKey = `generate_responsibilities_${requestSignature}`;
      const state = get();

      // Check if there's already a pending request for this same data
      if (state.pendingRequests && state.pendingRequests[requestKey]) {
        return state.pendingRequests[requestKey];
      }

      // Create the API request promise
      const apiHelper = (await import('@/lib/apiHelper')).default;
      const requestPromise = apiHelper.post('/jobs/generate-responsibilities', jobData);

      // Store the pending request if we have the pendingRequests state
      if (state.pendingRequests !== undefined) {
        set((state: any) => ({
          pendingRequests: {
            ...state.pendingRequests,
            [requestKey]: requestPromise,
          },
        }));
      }

      try {
        const response = await requestPromise;

        // Remove the pending request
        if (state.pendingRequests !== undefined) {
          set((state: any) => {
            const { [requestKey]: _, ...remainingRequests } = state.pendingRequests;
            return {
              pendingRequests: remainingRequests,
            };
          });
        }

        return response;
      } catch (error) {
        // Remove the pending request on error
        if (state.pendingRequests !== undefined) {
          set((state: any) => {
            const { [requestKey]: _, ...remainingRequests } = state.pendingRequests;
            return {
              pendingRequests: remainingRequests,
            };
          });
        }
        throw error;
      }
    } catch (error) {
      console.error('Error generating responsibilities:', error);
      return null;
    }
  },

  // ========== Utility Methods ==========
  clearUrlParameters: (router: any, searchParams: any) => {
    if (router.query !== undefined) {
      router.push(window.location.pathname, undefined, { shallow: true });
    } else {
      router.push(window.location.pathname);
    }

    set({
      selectedJobId: null,
      currentJob: null,
      selectedJob: null,
    });
  },
});
