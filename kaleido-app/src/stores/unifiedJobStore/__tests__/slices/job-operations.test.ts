import { create } from 'zustand';
import { createJobOperationsSlice, JobOperationsSlice } from '../../slices/job-operations';
import apiHelper from '@/lib/apiHelper';
import type { UnifiedJobStoreState } from '../../types';

// Mock apiHelper
jest.mock('@/lib/apiHelper', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock fetchJobDetails utility
jest.mock('@/utils/jobDetailsUtils', () => ({
  fetchJobDetails: jest.fn(),
}));

// Create a test store with only the job operations slice
const createTestStore = (initialState?: Partial<UnifiedJobStoreState>) => {
  return create<UnifiedJobStoreState & JobOperationsSlice>()((set, get) => ({
    // Initial state
    jobs: [],
    jobsById: {},
    candidates: {},
    allCandidatesFlat: [],
    workerJobs: {},
    currentJobDetails: null,
    stats: {
      totalCandidates: 0,
      topTierCount: 0,
      secondTierCount: 0,
      othersCount: 0,
      unrankedCount: 0,
      shortlistedCount: 0,
      totalApplications: 0,
      viewCount: 0,
      publishedPlatformsCount: 0,
    },
    matchRankCost: null,
    selectedJobId: null,
    currentJob: null,
    selectedJob: null,
    isLoading: false,
    isSaving: false,
    isPublishing: false,
    isUnpublishing: false,
    isProcessing: false,
    error: null,
    formData: {
      jobTitle: '',
      jobType: '',
      jobDescription: '',
      department: '',
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
      requirements: '',
      status: 'DRAFT',
      location: '',
      employmentType: 'FULL_TIME',
      workMode: 'HYBRID',
      benefits: [],
    },
    originalFormData: null,
    hasUnsavedChanges: false,
    validationErrors: {},
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    filters: {},
    lastUpdated: 0,
    jobUpdateTimestamps: {},
    cache: {},
    lastFetchTime: {},
    cacheTimeout: 5 * 60 * 1000,
    isResumeUploadActive: false,
    activeJobs: [],
    optimisticUpdates: {},
    silentUpdateInProgress: false,
    pendingRequests: {},
    job: {},
    activeStep: 0,
    totalSteps: 0,
    isFinalStep: false,
    isPreview: false,
    fadeIn: true,
    steps: [],
    availablePlatforms: [],
    selectedPlatforms: [],
    company: null,
    lastCompanyFetch: null,
    isHydrated: false,
    userRole: null,
    jobsByStatus: {},
    jobsByStatusPagination: {
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      itemsPerPage: 10,
    },
    lastJobsByStatusFetch: {},
    refreshingJobs: false,
    lastRefreshRequest: 0,
    refreshDebounceMs: 1000,
    ...initialState,
    // Add the slice
    ...createJobOperationsSlice(set, get),
  }));
};

describe('JobOperationsSlice', () => {
  let mockGet: jest.Mock;
  let mockPost: jest.Mock;
  let mockPut: jest.Mock;
  let mockDelete: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockGet = apiHelper.get as jest.Mock;
    mockPost = apiHelper.post as jest.Mock;
    mockPut = apiHelper.put as jest.Mock;
    mockDelete = apiHelper.delete as jest.Mock;
  });

  describe('fetchJobs', () => {
    it('should fetch jobs successfully', async () => {
      const mockJobs = [
        { id: '1', jobTitle: 'Test Job 1' },
        { id: '2', jobTitle: 'Test Job 2' },
      ];
      mockGet.mockResolvedValueOnce(mockJobs);

      const store = createTestStore();
      const result = await store.getState().fetchJobs();

      expect(mockGet).toHaveBeenCalledWith('/jobs');
      expect(result).toEqual(mockJobs);
      expect(store.getState().jobs).toEqual(mockJobs);
      expect(store.getState().isLoading).toBe(false);
      expect(store.getState().lastFetchTime.jobs).toBeDefined();
    });

    it('should handle fetchJobs error', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation();
      const error = new Error('Network error');
      mockGet.mockRejectedValueOnce(error);

      const store = createTestStore();
      await expect(store.getState().fetchJobs()).rejects.toThrow('Network error');

      expect(store.getState().isLoading).toBe(false);
      expect(store.getState().error).toBe('Network error');

      consoleError.mockRestore();
    });
  });

  describe('fetchJob', () => {
    it('should fetch a single job successfully', async () => {
      const mockJob = { id: '1', jobTitle: 'Test Job' };
      mockGet.mockResolvedValueOnce({ job: mockJob });

      const store = createTestStore();
      const result = await store.getState().fetchJob('1');

      expect(mockGet).toHaveBeenCalledWith('/jobs/1');
      expect(result).toEqual(mockJob);
      expect(store.getState().jobsById['1']).toEqual(mockJob);
    });

    it('should handle nested job response', async () => {
      const mockJob = { id: '1', jobTitle: 'Test Job' };
      mockGet.mockResolvedValueOnce(mockJob);

      const store = createTestStore();
      const result = await store.getState().fetchJob('1');

      expect(result).toEqual(mockJob);
      expect(store.getState().jobsById['1']).toEqual(mockJob);
    });
  });

  describe('createJob', () => {
    it('should create a job successfully', async () => {
      const newJob = { jobTitle: 'New Job', department: 'Tech' };
      const createdJob = { id: '3', ...newJob };
      mockPost.mockResolvedValueOnce(createdJob);

      const store = createTestStore({ jobs: [] });
      const result = await store.getState().createJob(newJob);

      expect(mockPost).toHaveBeenCalledWith('/jobs', newJob);
      expect(result).toEqual(createdJob);
      expect(store.getState().jobs).toContainEqual(createdJob);
      expect(store.getState().currentJob).toEqual(createdJob);
      expect(store.getState().selectedJobId).toBe('3');
      expect(store.getState().isSaving).toBe(false);
    });

    it('should handle createJob error', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation();
      const error = new Error('Creation failed');
      mockPost.mockRejectedValueOnce(error);

      const store = createTestStore();
      await expect(store.getState().createJob({})).rejects.toThrow('Creation failed');

      expect(store.getState().isSaving).toBe(false);
      expect(store.getState().error).toBe('Creation failed');

      consoleError.mockRestore();
    });
  });

  describe('updateJobAsync', () => {
    it('should update a job successfully', async () => {
      const existingJob = { id: '1', jobTitle: 'Old Title' };
      const updates = { jobTitle: 'New Title' };
      const updatedJob = { ...existingJob, ...updates };
      mockPut.mockResolvedValueOnce(updatedJob);

      const store = createTestStore({
        jobs: [existingJob],
        jobsById: { '1': existingJob },
        currentJob: existingJob,
      });

      const result = await store.getState().updateJobAsync('1', updates);

      expect(mockPut).toHaveBeenCalledWith('/jobs/1', updates);
      expect(result).toEqual(updatedJob);
      expect(store.getState().currentJob).toEqual(updatedJob);
      expect(store.getState().jobs[0]).toEqual(updatedJob);
      expect(store.getState().hasUnsavedChanges).toBe(false);
    });

    it('should handle updateJobAsync error', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation();
      const error = new Error('Update failed');
      mockPut.mockRejectedValueOnce(error);

      const store = createTestStore();
      await expect(store.getState().updateJobAsync('1', {})).rejects.toThrow('Update failed');

      expect(store.getState().isSaving).toBe(false);
      expect(store.getState().error).toBe('Update failed');

      consoleError.mockRestore();
    });
  });

  describe('deleteJob', () => {
    it('should delete a job successfully', async () => {
      mockDelete.mockResolvedValueOnce({});

      const store = createTestStore({
        jobs: [
          { id: '1', jobTitle: 'Job 1' },
          { id: '2', jobTitle: 'Job 2' },
        ],
        jobsById: {
          '1': { id: '1', jobTitle: 'Job 1' },
          '2': { id: '2', jobTitle: 'Job 2' },
        },
        currentJob: { id: '1', jobTitle: 'Job 1' },
        selectedJobId: '1',
      });

      await store.getState().deleteJob('1');

      expect(mockDelete).toHaveBeenCalledWith('/jobs/1');
      expect(store.getState().jobs).toHaveLength(1);
      expect(store.getState().jobs[0].id).toBe('2');
      expect(store.getState().jobsById['1']).toBeUndefined();
      expect(store.getState().currentJob).toBeNull();
      expect(store.getState().selectedJobId).toBeNull();
    });
  });

  describe('Job Selection', () => {
    it('should select a job by ID', () => {
      const job = { id: '1', jobTitle: 'Test Job' };
      const store = createTestStore({
        jobsById: { '1': job },
      });

      store.getState().setSelectedJob('1');

      expect(store.getState().selectedJobId).toBe('1');
      expect(store.getState().currentJob).toEqual(job);
      expect(store.getState().selectedJob).toEqual(job);
    });

    it('should find job in jobs array if not in jobsById', () => {
      const job = { id: '1', jobTitle: 'Test Job' };
      const store = createTestStore({
        jobs: [job],
        jobsById: {},
      });

      store.getState().setSelectedJob('1');

      expect(store.getState().selectedJob).toEqual(job);
    });

    it('should find job in jobsByStatus if not in other places', () => {
      const job = { id: '1', jobTitle: 'Test Job' };
      const store = createTestStore({
        jobsByStatus: {
          ALL: { jobs: [job] },
        },
      });

      store.getState().setSelectedJob('1');

      expect(store.getState().selectedJob).toEqual(job);
    });

    it('should clear selection when null is passed', () => {
      const store = createTestStore({
        selectedJobId: '1',
        currentJob: { id: '1' },
        selectedJob: { id: '1' },
      });

      store.getState().setSelectedJob(null);

      expect(store.getState().selectedJobId).toBeNull();
      expect(store.getState().currentJob).toBeNull();
      expect(store.getState().selectedJob).toBeNull();
    });
  });

  describe('Form Management', () => {
    it('should update form data', () => {
      const store = createTestStore();
      const updates = { jobTitle: 'Updated Title', department: 'HR' };

      store.getState().updateFormData(updates);

      expect(store.getState().formData.jobTitle).toBe('Updated Title');
      expect(store.getState().formData.department).toBe('HR');
      expect(store.getState().hasUnsavedChanges).toBe(true);
    });

    it('should reset form to initial state', () => {
      const store = createTestStore({
        formData: { jobTitle: 'Test', jobType: 'Full-time' },
        hasUnsavedChanges: true,
        validationErrors: { jobTitle: 'Required' },
      });

      store.getState().resetForm();

      expect(store.getState().formData.jobTitle).toBe('');
      expect(store.getState().formData.jobType).toBe('');
      expect(store.getState().hasUnsavedChanges).toBe(false);
      expect(store.getState().validationErrors).toEqual({});
    });

    it('should save form data', async () => {
      const formData = { jobTitle: 'Test Job' };
      mockPut.mockResolvedValueOnce({ id: '1', ...formData });

      const store = createTestStore({
        selectedJobId: '1',
        formData,
      });

      await store.getState().saveForm();

      expect(mockPut).toHaveBeenCalledWith('/jobs/1', formData);
    });
  });

  describe('Publishing', () => {
    it('should publish a job', async () => {
      const platforms = ['jobboard', 'linkedin'];
      const publishResponse = [
        { platform: 'jobboard', isPublished: true },
        { platform: 'linkedin', isPublished: true },
      ];
      mockPut.mockResolvedValueOnce(publishResponse);

      const store = createTestStore({
        currentJob: { id: '1', isPublished: false },
        jobs: [{ id: '1', isPublished: false }],
        jobsById: { '1': { id: '1', isPublished: false } },
      });

      const result = await store.getState().publishJob('1', platforms);

      expect(mockPut).toHaveBeenCalledWith('/jobs/1/publish', { platforms });
      expect(result).toEqual(publishResponse);
      expect(store.getState().isPublishing).toBe(false);

      // Check optimistic updates were applied
      const state = store.getState();
      expect(state.currentJob?.isPublished).toBe(true);
      expect(state.jobs[0].isPublished).toBe(true);
    });

    it('should unpublish a job', async () => {
      mockPut.mockResolvedValueOnce([{ platform: 'jobboard', isPublished: false }]);

      const store = createTestStore({
        currentJob: { id: '1', isPublished: true },
      });

      await store.getState().unpublishJob('1');

      expect(mockPut).toHaveBeenCalledWith('/jobs/1/publish', { platforms: ['jobboard'] });
      expect(store.getState().isUnpublishing).toBe(false);
      expect(store.getState().currentJob?.isPublished).toBe(false);
    });

    it('should toggle platform selection', () => {
      const store = createTestStore({
        selectedPlatforms: ['jobboard'],
      });

      // Add platform
      store.getState().togglePlatformSelection('linkedin');
      expect(store.getState().selectedPlatforms).toEqual(['jobboard', 'linkedin']);

      // Remove platform
      store.getState().togglePlatformSelection('jobboard');
      expect(store.getState().selectedPlatforms).toEqual(['linkedin']);
    });
  });

  describe('Candidates Management', () => {
    it('should fetch candidates successfully', async () => {
      const mockResponse = {
        job: { id: '1', jobTitle: 'Test Job' },
        candidates: [{ id: 'c1', name: 'Candidate 1' }],
        stats: { totalCandidates: 1 },
        pagination: { totalPages: 1 },
      };
      mockGet.mockResolvedValueOnce(mockResponse);

      const store = createTestStore();
      await store.getState().fetchCandidates('1', 1, { status: 'active' });

      expect(mockGet).toHaveBeenCalledWith('/jobs/1/candidates?page=1&limit=10&status=active');
      expect(store.getState().candidates['1']).toEqual(mockResponse.candidates);
      expect(store.getState().stats).toEqual(mockResponse.stats);
      expect(store.getState().filters).toEqual({ status: 'active' });
    });

    it('should update candidate status optimistically', async () => {
      const mockResponse = { success: true };
      mockPut.mockResolvedValueOnce(mockResponse);
      mockGet.mockResolvedValueOnce({
        candidates: [],
        stats: {},
        pagination: {},
      });

      const store = createTestStore();
      await store.getState().updateCandidateStatus('1', 'c1', 'SHORTLISTED');

      expect(store.getState().optimisticUpdates['1_c1']).toBeUndefined(); // Should be removed after success
      expect(mockPut).toHaveBeenCalledWith('/candidates/c1/status', { status: 'SHORTLISTED' });
    });

    it('should revert optimistic update on error', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation();
      mockPut.mockRejectedValueOnce(new Error('Update failed'));

      const store = createTestStore();

      await expect(
        store.getState().updateCandidateStatus('1', 'c1', 'SHORTLISTED')
      ).rejects.toThrow('Update failed');

      expect(store.getState().optimisticUpdates['1_c1']).toBeUndefined();

      consoleError.mockRestore();
    });
  });

  describe('Validation', () => {
    it('should check for pending changes', () => {
      const store = createTestStore({ hasUnsavedChanges: true });
      expect(store.getState().hasPendingChanges()).toBe(true);

      store.getState().setHasUnsavedChanges(false);
      expect(store.getState().hasPendingChanges()).toBe(false);
    });

    it('should check for threshold changes', () => {
      const store = createTestStore({
        formData: { topCandidateThreshold: 80, secondTierCandidateThreshold: 60 },
        originalFormData: { topCandidateThreshold: 70, secondTierCandidateThreshold: 60 },
      });

      expect(store.getState().hasThresholdChanges()).toBe(true);

      store.getState().updateFormData({ topCandidateThreshold: 70 });
      expect(store.getState().hasThresholdChanges()).toBe(false);
    });
  });

  describe('UI State Management', () => {
    it('should set loading state', () => {
      const store = createTestStore();

      store.getState().setLoading(true);
      expect(store.getState().isLoading).toBe(true);

      store.getState().setLoading(false);
      expect(store.getState().isLoading).toBe(false);
    });

    it('should set error state', () => {
      const store = createTestStore();

      store.getState().setError('Test error');
      expect(store.getState().error).toBe('Test error');

      store.getState().setError(null);
      expect(store.getState().error).toBeNull();
    });
  });

  describe('reset', () => {
    it('should reset store to initial state', () => {
      const store = createTestStore({
        jobs: [{ id: '1' }],
        currentJob: { id: '1' },
        selectedJobId: '1',
        isLoading: true,
        error: 'Some error',
      });

      store.getState().reset();

      const state = store.getState();
      expect(state.jobs).toEqual([]);
      expect(state.currentJob).toBeNull();
      expect(state.selectedJobId).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
  });
});
