#!/bin/bash

# Unified Job Store Migration - Import Update Script
# This script automatically updates import statements to use the unified store

echo "🚀 Unified Job Store Migration - Updating Imports"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Create backup directory
backup_dir="./backup_before_migration_$(date +%Y%m%d_%H%M%S)"
echo "${BLUE}📁 Creating backup in: $backup_dir${NC}"
mkdir -p "$backup_dir"

# Function to backup files
backup_files() {
    local search_pattern="$1"
    local store_name="$2"
    
    echo "${YELLOW}🔍 Finding $store_name files to backup...${NC}"
    find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "$search_pattern" 2>/dev/null | while read file; do
        if [ -f "$file" ]; then
            backup_path="$backup_dir/$(dirname "$file")"
            mkdir -p "$backup_path"
            cp "$file" "$backup_dir/$file"
            echo "   📄 Backed up: $file"
        fi
    done
}

# Function to update imports
update_imports() {
    local old_import="$1"
    local new_import="$2"
    local store_name="$3"
    
    echo "${YELLOW}🔄 Updating $store_name imports...${NC}"
    
    # Count files before update
    files_before=$(find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "$old_import" 2>/dev/null | wc -l)
    
    # Perform the replacement
    find src/ -name '*.ts' -o -name '*.tsx' | xargs sed -i '' "s|$old_import|$new_import|g" 2>/dev/null
    
    # Count files after update  
    files_after=$(find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "$old_import" 2>/dev/null | wc -l)
    
    updated_count=$((files_before - files_after))
    echo "   ${GREEN}✅ Updated $updated_count files${NC}"
    
    if [ $files_after -gt 0 ]; then
        echo "   ${RED}⚠️  Warning: $files_after files still have old imports${NC}"
    fi
}

# Confirm before proceeding
echo ""
echo "${RED}⚠️  WARNING: This will modify your source files!${NC}"
echo "A backup will be created at: $backup_dir"
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "${YELLOW}❌ Migration cancelled${NC}"
    exit 1
fi

echo ""
echo "${BLUE}📦 Starting migration...${NC}"
echo ""

# Backup all files that will be modified
backup_files "from '@/stores/jobStore'" "jobStore"
backup_files "from '@/stores/jobStateStore'" "jobStateStore"  
backup_files "from '@/stores/uploadJobsStore'" "uploadJobsStore"

echo ""
echo "${BLUE}🔄 Updating import statements...${NC}"
echo ""

# Update imports
update_imports "from '@/stores/jobStore'" "from '@/stores/unifiedJobStore'" "jobStore"
update_imports "from '@/stores/jobStateStore'" "from '@/stores/unifiedJobStore'" "jobStateStore"
update_imports "from '@/stores/uploadJobsStore'" "from '@/stores/unifiedJobStore'" "uploadJobsStore"

echo ""
echo "${BLUE}🔍 Verifying migration...${NC}"

# Check for remaining old imports
remaining_jobstore=$(find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "from '@/stores/jobStore'" 2>/dev/null | wc -l)
remaining_jobstate=$(find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "from '@/stores/jobStateStore'" 2>/dev/null | wc -l)
remaining_upload=$(find src/ -name '*.ts' -o -name '*.tsx' | xargs grep -l "from '@/stores/uploadJobsStore'" 2>/dev/null | wc -l)

total_remaining=$((remaining_jobstore + remaining_jobstate + remaining_upload))

if [ $total_remaining -eq 0 ]; then
    echo "${GREEN}✅ All imports successfully updated!${NC}"
else
    echo "${RED}⚠️  Warning: $total_remaining files still have old imports${NC}"
    echo "   jobStore: $remaining_jobstore files"
    echo "   jobStateStore: $remaining_jobstate files"  
    echo "   uploadJobsStore: $remaining_upload files"
fi

echo ""
echo "${BLUE}🧪 Running TypeScript check...${NC}"

# Run TypeScript check
if pnpm check-types > /dev/null 2>&1; then
    echo "${GREEN}✅ TypeScript compilation successful!${NC}"
else
    echo "${RED}❌ TypeScript compilation failed${NC}"
    echo "Run 'pnpm check-types' to see detailed errors"
fi

echo ""
echo "${BLUE}📋 Post-migration checklist:${NC}"
echo "1. ${YELLOW}Run tests:${NC} pnpm test"
echo "2. ${YELLOW}Check functionality:${NC} Test core job flows"
echo "3. ${YELLOW}Verify uploads:${NC} Check file upload and progress"
echo "4. ${YELLOW}Check console:${NC} Look for runtime errors"
echo "5. ${YELLOW}Test localStorage:${NC} Verify job persistence"

echo ""
echo "${BLUE}🔄 Rollback instructions:${NC}"
echo "If you need to rollback, restore from backup:"
echo "cp -r $backup_dir/src/* src/"

echo ""
if [ $total_remaining -eq 0 ]; then
    echo "${GREEN}🎉 Migration completed successfully!${NC}"
else
    echo "${YELLOW}⚠️  Migration completed with warnings${NC}"
    echo "Some files may need manual review"
fi

echo ""
echo "See MIGRATION_GUIDE.md and MIGRATION_ANALYSIS.md for detailed information."