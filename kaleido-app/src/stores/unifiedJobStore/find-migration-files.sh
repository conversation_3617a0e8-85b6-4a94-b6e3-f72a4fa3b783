#!/bin/bash

# Unified Job Store Migration - File Finder Script
# This script helps identify all files that need import updates

echo "🔍 Unified Job Store Migration - Finding Files to Update"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counter for total files
total_files=0

echo ""
echo "${BLUE}📁 Searching for files that import job stores...${NC}"
echo ""

# Find jobStore imports
echo "${YELLOW}1. Files importing jobStore:${NC}"
jobstore_files=$(grep -r "from '@/stores/jobStore'" src/ --include="*.ts" --include="*.tsx" -l 2>/dev/null || true)
if [ -n "$jobstore_files" ]; then
    echo "$jobstore_files" | while read file; do
        echo "   📄 $file"
        ((total_files++))
    done
    jobstore_count=$(echo "$jobstore_files" | wc -l)
    echo "   ${GREEN}Found: $jobstore_count files${NC}"
else
    echo "   ${GREEN}No files found${NC}"
    jobstore_count=0
fi

echo ""

# Find jobStateStore imports
echo "${YELLOW}2. Files importing jobStateStore:${NC}"
jobstatestore_files=$(grep -r "from '@/stores/jobStateStore'" src/ --include="*.ts" --include="*.tsx" -l 2>/dev/null || true)
if [ -n "$jobstatestore_files" ]; then
    echo "$jobstatestore_files" | while read file; do
        echo "   📄 $file"
        ((total_files++))
    done
    jobstatestore_count=$(echo "$jobstatestore_files" | wc -l)
    echo "   ${GREEN}Found: $jobstatestore_count files${NC}"
else
    echo "   ${GREEN}No files found${NC}"
    jobstatestore_count=0
fi

echo ""

# Find uploadJobsStore imports
echo "${YELLOW}3. Files importing uploadJobsStore:${NC}"
uploadstore_files=$(grep -r "from '@/stores/uploadJobsStore'" src/ --include="*.ts" --include="*.tsx" -l 2>/dev/null || true)
if [ -n "$uploadstore_files" ]; then
    echo "$uploadstore_files" | while read file; do
        echo "   📄 $file"
        ((total_files++))
    done
    uploadstore_count=$(echo "$uploadstore_files" | wc -l)
    echo "   ${GREEN}Found: $uploadstore_count files${NC}"
else
    echo "   ${GREEN}No files found${NC}"
    uploadstore_count=0
fi

echo ""

# Find potential jobsStore or similar imports (the missing 4th store)
echo "${YELLOW}4. Files with potential jobsStore references:${NC}"
jobsstore_files=$(grep -r -E "(jobsStore|useJobsStore)" src/ --include="*.ts" --include="*.tsx" -l 2>/dev/null || true)
if [ -n "$jobsstore_files" ]; then
    echo "$jobsstore_files" | while read file; do
        echo "   📄 $file"
        ((total_files++))
    done
    jobsstore_count=$(echo "$jobsstore_files" | wc -l)
    echo "   ${GREEN}Found: $jobsstore_count files${NC}"
else
    echo "   ${GREEN}No files found${NC}"
    jobsstore_count=0
fi

echo ""
echo "=================================================="

# Calculate totals
total_jobstore=$(echo "$jobstore_files" | wc -l 2>/dev/null || echo 0)
total_jobstatestore=$(echo "$jobstatestore_files" | wc -l 2>/dev/null || echo 0)
total_uploadstore=$(echo "$uploadstore_files" | wc -l 2>/dev/null || echo 0)
total_jobsstore=$(echo "$jobsstore_files" | wc -l 2>/dev/null || echo 0)

# Handle empty results
[ -z "$jobstore_files" ] && total_jobstore=0
[ -z "$jobstatestore_files" ] && total_jobstatestore=0
[ -z "$uploadstore_files" ] && total_uploadstore=0
[ -z "$jobsstore_files" ] && total_jobsstore=0

total_unique=$(cat <(echo "$jobstore_files") <(echo "$jobstatestore_files") <(echo "$uploadstore_files") <(echo "$jobsstore_files") | sort -u | grep -v '^$' | wc -l 2>/dev/null || echo 0)

echo "${BLUE}📊 Summary:${NC}"
echo "   jobStore imports:        $total_jobstore files"
echo "   jobStateStore imports:   $total_jobstatestore files"  
echo "   uploadJobsStore imports: $total_uploadstore files"
echo "   jobsStore references:    $total_jobsstore files"
echo "   ${GREEN}Total unique files:      $total_unique files${NC}"

echo ""
echo "${BLUE}🔧 Next Steps:${NC}"
echo "1. Update imports in the files listed above"
echo "2. Change import paths to '@/stores/unifiedJobStore'"
echo "3. Run 'pnpm check-types' to verify"
echo "4. Test functionality with existing components"

echo ""
echo "${BLUE}💡 Quick Replace Commands:${NC}"
echo ""
echo "${YELLOW}Replace jobStore imports:${NC}"
echo "find src/ -name '*.ts' -o -name '*.tsx' | xargs sed -i '' \"s|from '@/stores/jobStore'|from '@/stores/unifiedJobStore'|g\""
echo ""
echo "${YELLOW}Replace jobStateStore imports:${NC}"  
echo "find src/ -name '*.ts' -o -name '*.tsx' | xargs sed -i '' \"s|from '@/stores/jobStateStore'|from '@/stores/unifiedJobStore'|g\""
echo ""
echo "${YELLOW}Replace uploadJobsStore imports:${NC}"
echo "find src/ -name '*.ts' -o -name '*.tsx' | xargs sed -i '' \"s|from '@/stores/uploadJobsStore'|from '@/stores/unifiedJobStore'|g\""

echo ""
echo "${GREEN}✅ Migration script complete!${NC}"
echo "See MIGRATION_GUIDE.md for detailed instructions."