import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface OnboardingProgress {
  overall: {
    completed: boolean;
    percentage: number;
    completedAt: string | null;
  };
  basicInfo: {
    completed: boolean;
    percentage: number;
    completedAt: string | null;
    requiredFields: string[];
    completedFields: string[];
  };
  professionalInfo: {
    completed: boolean;
    percentage: number;
    completedAt: string | null;
    requiredFields: string[];
    completedFields: string[];
  };
  preferences: {
    completed: boolean;
    percentage: number;
    completedAt: string | null;
    requiredFields: string[];
    completedFields: string[];
  };
  additionalInfo: {
    completed: boolean;
    percentage: number;
    completedAt: string | null;
    requiredFields: string[];
    completedFields: string[];
  };
  lastUpdated: string;
}

interface JobSeekerProfile {
  id: string;
  userId: string;
  clientId: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  hasCompletedOnboarding: boolean;
  onboardingProgress: OnboardingProgress;
  // Add other profile fields as needed
  [key: string]: any;
}

interface CompanyProfile {
  id: string;
  clientId: string;
  companyName: string;
  companyWebsite: string;
  industry: string;
  size: string;
  location: string;
  contactEmail: string;
  phoneNumber: string;
  logo: string;
  description: string;
  onboardingRequired: boolean;
  // Add other company fields as needed
  [key: string]: any;
}

interface OnboardingState {
  // State
  profile: JobSeekerProfile | null;
  company: CompanyProfile | null;
  isLoading: boolean;
  error: string | null;
  shouldShowSetupSlider: boolean;
  hasCheckedOnboarding: boolean;
  userRole: UserRole | null;

  // Actions
  fetchProfile: () => Promise<void>;
  fetchCompany: () => Promise<void>;
  setProfile: (profile: JobSeekerProfile | null) => void;
  setCompany: (company: CompanyProfile | null) => void;
  setShouldShowSetupSlider: (show: boolean) => void;
  updateOnboardingProgress: (progress: OnboardingProgress) => void;
  markOnboardingComplete: () => void;
  markCompanyOnboardingComplete: () => void;
  setUserRole: (role: UserRole) => void;
  reset: () => void;

  // Computed getters
  getOverallProgress: () => number;
  getNeedsOnboarding: () => boolean;
  getCompanyNeedsOnboarding: () => boolean;
  getMissingRequiredFields: () => string[];
  shouldRedirectToOnboarding: () => string | null;
}

export const useOnboardingStore = create<OnboardingState>()(
  devtools(
    (set, get) => ({
      // Initial state
      profile: null,
      company: null,
      isLoading: false,
      error: null,
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: false,
      userRole: null,

      // Actions
      fetchProfile: async () => {
        const state = get();
        if (state.isLoading) return; // Prevent concurrent requests

        // Only fetch profile if user is a job seeker or graduate
        const userRole = state.userRole;
        if (userRole && userRole !== UserRole.JOB_SEEKER && userRole !== UserRole.GRADUATE) {
          set({
            profile: null,
            hasCheckedOnboarding: true,
            shouldShowSetupSlider: false,
            isLoading: false,
          });
          return;
        }

        set({ isLoading: true, error: null });

        try {
          const response = await apiHelper.get('/job-seekers');

          if (response) {
            set({
              profile: response,
              hasCheckedOnboarding: true,
              // Automatically determine if setup slider should show
              shouldShowSetupSlider: !response.hasCompletedOnboarding,
            });
          } else {
            // No profile found - new user needs onboarding
            set({
              profile: null,
              hasCheckedOnboarding: true,
              shouldShowSetupSlider: true,
            });
          }
        } catch (error: any) {
          console.error('Error fetching job seeker profile:', error);

          // If 404 or similar, treat as new user
          if (error?.response?.status === 404) {
            // Check if it's because user is not a job seeker
            const errorData = error?.response?.data;
            if (errorData?.userRole) {
            }

            set({
              profile: null,
              hasCheckedOnboarding: true,
              shouldShowSetupSlider: false, // Don't show setup slider - users should be redirected to dedicated onboarding page
              error: null, // Don't treat 404 as an error for new users
            });
          } else {
            set({
              error: error.message || 'Failed to fetch profile',
              hasCheckedOnboarding: true,
              shouldShowSetupSlider: false, // Don't show setup slider - users should be redirected to dedicated onboarding page
            });
          }
        } finally {
          set({ isLoading: false });
        }
      },

      fetchCompany: async () => {
        const state = get();
        if (state.isLoading) return; // Prevent concurrent requests

        set({ isLoading: true, error: null });

        try {
          const response = await apiHelper.get('/companies/client');

          if (response) {
            set({
              company: response,
              hasCheckedOnboarding: true,
            });
          } else {
            // No company found - new employer needs onboarding
            set({
              company: null,
              hasCheckedOnboarding: true,
            });
          }
        } catch (error: any) {
          console.error('Error fetching company profile:', error);

          // If 404 or similar, treat as new employer
          if (error?.response?.status === 404) {
            set({
              company: null,
              hasCheckedOnboarding: true,
              error: null, // Don't treat 404 as an error for new employers
            });
          } else {
            set({
              error: error.message || 'Failed to fetch company profile',
              hasCheckedOnboarding: true,
            });
          }
        } finally {
          set({ isLoading: false });
        }
      },

      setProfile: profile => {
        set({
          profile,
          shouldShowSetupSlider: profile ? !profile.hasCompletedOnboarding : true,
        });
      },

      setCompany: company => {
        set({
          company,
        });
      },

      setShouldShowSetupSlider: show => {
        set({ shouldShowSetupSlider: show });
      },

      updateOnboardingProgress: progress => {
        const state = get();
        if (state.profile) {
          set({
            profile: {
              ...state.profile,
              onboardingProgress: progress,
            },
          });
        }
      },

      markOnboardingComplete: () => {
        const state = get();
        if (state.profile) {
          set({
            profile: {
              ...state.profile,
              hasCompletedOnboarding: true,
              onboardingProgress: {
                ...state.profile.onboardingProgress,
                overall: {
                  ...(state.profile.onboardingProgress?.overall || {}),
                  completed: true,
                  percentage: 100,
                  completedAt: new Date().toISOString(),
                },
              },
            },
            shouldShowSetupSlider: false,
          });
        }
      },

      markCompanyOnboardingComplete: () => {
        const state = get();
        if (state.company) {
          set({
            company: {
              ...state.company,
              onboardingRequired: false,
            },
          });
        }
      },

      setUserRole: (role: UserRole) => {
        set({ userRole: role });
      },

      reset: () => {
        set({
          profile: null,
          company: null,
          isLoading: false,
          error: null,
          shouldShowSetupSlider: false,
          hasCheckedOnboarding: false,
          userRole: null,
        });
      },

      // Computed getters
      getOverallProgress: () => {
        const state = get();
        return state.profile?.onboardingProgress?.overall?.percentage || 0;
      },

      getNeedsOnboarding: () => {
        const state = get();
        return !state.profile?.hasCompletedOnboarding;
      },

      getCompanyNeedsOnboarding: () => {
        const state = get();

        if (!state.company) {
          return true;
        }

        // Use the backend's authoritative onboardingRequired field
        // The backend determines if onboarding is complete based on its own validation logic
        return state.company.onboardingRequired === true;
      },

      getMissingRequiredFields: () => {
        const state = get();
        if (!state.profile?.onboardingProgress) return [];

        const progress = state.profile.onboardingProgress;
        const missingFields: string[] = [];

        // Check each section for missing required fields
        Object.entries(progress).forEach(([sectionKey, section]) => {
          if (
            sectionKey !== 'overall' &&
            sectionKey !== 'lastUpdated' &&
            section &&
            typeof section === 'object'
          ) {
            const sectionData = section as any;
            if (sectionData.requiredFields && sectionData.completedFields) {
              const missing = sectionData.requiredFields.filter(
                (field: string) => !sectionData.completedFields.includes(field)
              );
              missingFields.push(...missing);
            }
          }
        });

        return missingFields;
      },

      shouldRedirectToOnboarding: () => {
        const state = get();

        // If we haven't checked onboarding yet, don't redirect
        if (!state.hasCheckedOnboarding) return null;

        // Check based on user role
        if (state.userRole === UserRole.EMPLOYER) {
          return state.getCompanyNeedsOnboarding() ? '/company-onboarding' : null;
        } else if (state.userRole === UserRole.JOB_SEEKER || state.userRole === UserRole.GRADUATE) {
          return state.getNeedsOnboarding() ? '/jobseeker-onboarding' : null;
        }

        return null;
      },
    }),
    {
      name: 'onboarding-store',
    }
  )
);

export default useOnboardingStore;
