import { create } from 'zustand';

import { VideoJDStatus } from '@/components/VideoJD/consts';
import apiHelper from '@/lib/apiHelper';

import { useJobStateStore } from './unifiedJobStore';

interface VideoJDState {
  // State
  generatingVideoJDs: Record<
    string,
    {
      synthesiaVideoId: string;
      status: VideoJDStatus;
      jobId: string;
      lastChecked: number;
    }
  >;
  pollingIntervals: Record<string, NodeJS.Timeout>;
  isPolling: boolean;

  // Actions
  trackVideoJD: (jobId: string, synthesiaVideoId: string, status: VideoJDStatus) => void;
  updateVideoJDStatus: (synthesiaVideoId: string, status: VideoJDStatus) => void;
  stopTrackingVideoJD: (synthesiaVideoId: string) => void;
  clearAllTracking: () => void;

  // Polling
  startPolling: () => void;
  stopPolling: () => void;
  checkVideoJDStatus: (synthesiaVideoId: string) => Promise<VideoJDStatus>;
}

export const useVideoJDStore = create<VideoJDState>((set, get) => ({
  // Initial state
  generatingVideoJDs: {},
  pollingIntervals: {},
  isPolling: false,

  // Track a videoJD that's being generated
  trackVideoJD: (jobId, synthesiaVideoId, status) => {
    // Only track if it's in a generating or pending state
    if (status !== VideoJDStatus.GENERATING && status !== VideoJDStatus.PENDING) {
      return;
    }

    // Check if we're already tracking this videoJD with the same status
    const currentState = get();
    const existingVideoJD = currentState.generatingVideoJDs[synthesiaVideoId];

    // If we're already tracking this videoJD with the same status, don't update state
    if (existingVideoJD && existingVideoJD.status === status) {
      return;
    }

    // Check if we have a cached completed status
    const statusCacheKey = `video_jd_status_${synthesiaVideoId}`;
    const cachedStatus = localStorage.getItem(statusCacheKey);

    // If we have a cached COMPLETED or FAILED status, don't track
    if (cachedStatus === 'COMPLETED' || cachedStatus === 'FAILED') {
      return;
    }

    set(state => ({
      generatingVideoJDs: {
        ...state.generatingVideoJDs,
        [synthesiaVideoId]: {
          synthesiaVideoId,
          status,
          jobId,
          lastChecked: Date.now(),
        },
      },
    }));

    // Start polling if not already polling
    if (!currentState.isPolling) {
      get().startPolling();
    }
  },

  // Update the status of a tracked videoJD
  updateVideoJDStatus: (synthesiaVideoId, status) => {
    // Get current state first
    const currentState = get();

    // Check if we have a cached completed or failed status
    const statusCacheKey = `video_jd_status_${synthesiaVideoId}`;
    const cachedStatus = localStorage.getItem(statusCacheKey);

    // If we have a cached COMPLETED or FAILED status and we're trying to set to GENERATING or PENDING,
    // ignore the update to prevent loops
    if (
      (cachedStatus === 'COMPLETED' || cachedStatus === 'FAILED') &&
      (status === VideoJDStatus.GENERATING || status === VideoJDStatus.PENDING)
    ) {
      return;
    }

    // For COMPLETED or FAILED status, we want to update even if we're not tracking it
    if (status === VideoJDStatus.COMPLETED || status === VideoJDStatus.FAILED) {
      // Cache the status
      localStorage.setItem(statusCacheKey, status);

      // If we're not tracking this videoJD but it's completed/failed, just update the job state store
      if (!currentState.generatingVideoJDs[synthesiaVideoId]) {
        // Trigger a refresh of jobs in the job state store
        useJobStateStore.getState().markJobAsUpdated(synthesiaVideoId);
        return;
      }
    } else {
      // For other statuses, if we're not tracking this videoJD, ignore
      if (!currentState.generatingVideoJDs[synthesiaVideoId]) {
        return;
      }
    }

    // If the status hasn't changed, don't update to prevent loops
    const currentVideoJD = currentState.generatingVideoJDs[synthesiaVideoId];
    if (currentVideoJD && currentVideoJD.status === status) {
      return;
    }

    // For terminal statuses (COMPLETED or FAILED), stop tracking instead of updating
    if (status === VideoJDStatus.COMPLETED || status === VideoJDStatus.FAILED) {
      // Update the job state store to notify other components about the update
      const currentVideoJD = currentState.generatingVideoJDs[synthesiaVideoId];
      if (currentVideoJD) {
        useJobStateStore.getState().markJobAsUpdated(currentVideoJD.jobId);
      }

      // Stop tracking this videoJD
      get().stopTrackingVideoJD(synthesiaVideoId);

      return;
    }

    // For non-terminal statuses, update the status
    set(state => ({
      generatingVideoJDs: {
        ...state.generatingVideoJDs,
        [synthesiaVideoId]: {
          ...state.generatingVideoJDs[synthesiaVideoId],
          status,
          lastChecked: Date.now(),
        },
      },
    }));
  },

  // Stop tracking a specific videoJD
  stopTrackingVideoJD: synthesiaVideoId => {
    set(state => {
      const { [synthesiaVideoId]: _, ...rest } = state.generatingVideoJDs;
      return {
        generatingVideoJDs: rest,
      };
    });

    // If no more generating videoJDs, stop polling
    if (Object.keys(get().generatingVideoJDs).length === 0) {
      get().stopPolling();
    }
  },

  // Clear all tracking
  clearAllTracking: () => {
    get().stopPolling();
    set({
      generatingVideoJDs: {},
    });
  },

  // Start polling for videoJD status
  startPolling: () => {
    // Get current state
    const currentState = get();

    // Don't start if already polling
    if (currentState.isPolling) {
      return;
    }

    const POLL_INTERVAL = 60000; // 60 seconds (1 minute) - reduced frequency to minimize API calls

    const intervalId = setInterval(async () => {
      // Get fresh state each time in the interval
      const state = get();
      const { generatingVideoJDs } = state;

      // If no videoJDs to track, stop polling
      if (Object.keys(generatingVideoJDs).length === 0) {
        state.stopPolling();
        return;
      }

      // Check each generating videoJD
      for (const [synthesiaVideoId, videoJD] of Object.entries(generatingVideoJDs)) {
        try {
          // First check if we have a cached completed status
          const statusCacheKey = `video_jd_status_${synthesiaVideoId}`;
          const cachedStatus = localStorage.getItem(statusCacheKey);

          if (cachedStatus === 'COMPLETED' || cachedStatus === 'FAILED') {
            // Stop tracking instead of updating status to prevent loops
            state.stopTrackingVideoJD(synthesiaVideoId);
            continue;
          }

          // Only check if it's been at least 60 seconds since last check
          if (Date.now() - videoJD.lastChecked < 60000) {
            continue;
          }

          // Add bypassCircuitBreaker flag to ensure the request goes through
          const status = await state.checkVideoJDStatus(synthesiaVideoId);

          // If status is COMPLETED or FAILED, cache it and stop tracking
          if (status === VideoJDStatus.COMPLETED || status === VideoJDStatus.FAILED) {
            localStorage.setItem(statusCacheKey, status);

            // Update the job state store to notify other components about the update
            const videoJD = state.generatingVideoJDs[synthesiaVideoId];
            if (videoJD) {
              useJobStateStore.getState().markJobAsUpdated(videoJD.jobId);
            }

            // Stop tracking this videoJD
            state.stopTrackingVideoJD(synthesiaVideoId);
          }
          // If status is still GENERATING or PENDING but changed, update it
          else if (status !== videoJD.status) {
            // Just update the lastChecked timestamp and status
            set(currentState => ({
              generatingVideoJDs: {
                ...currentState.generatingVideoJDs,
                [synthesiaVideoId]: {
                  ...currentState.generatingVideoJDs[synthesiaVideoId],
                  status,
                  lastChecked: Date.now(),
                },
              },
            }));
          } else {
            // Just update the lastChecked timestamp
            set(currentState => ({
              generatingVideoJDs: {
                ...currentState.generatingVideoJDs,
                [synthesiaVideoId]: {
                  ...currentState.generatingVideoJDs[synthesiaVideoId],
                  lastChecked: Date.now(),
                },
              },
            }));
          }
        } catch (error) {
          console.error(`Error checking videoJD status for ${synthesiaVideoId}:`, error);
        }
      }
    }, POLL_INTERVAL);

    // Store the interval ID and update polling state
    set({
      isPolling: true,
      pollingIntervals: {
        ...currentState.pollingIntervals,
        main: intervalId,
      },
    });
  },

  // Stop polling
  stopPolling: () => {
    const { pollingIntervals } = get();

    // Clear all intervals
    Object.values(pollingIntervals).forEach(intervalId => {
      clearInterval(intervalId);
    });

    // Reset polling state
    set({
      isPolling: false,
      pollingIntervals: {},
    });
  },

  // Check the status of a videoJD
  checkVideoJDStatus: async synthesiaVideoId => {
    try {
      // First check if we have a cached completed status
      const statusCacheKey = `video_jd_status_${synthesiaVideoId}`;
      const cachedStatus = localStorage.getItem(statusCacheKey);

      if (cachedStatus === 'COMPLETED') {
        return VideoJDStatus.COMPLETED;
      } else if (cachedStatus === 'FAILED') {
        return VideoJDStatus.FAILED;
      }

      // Make the API call with bypassCircuitBreaker to ensure it goes through
      const response = await apiHelper.get(`/video-jd/status/${synthesiaVideoId}`, {
        bypassCircuitBreaker: true,
      });

      // Map the response status to our VideoJDStatus enum
      let status: VideoJDStatus;
      if (response.status === 'COMPLETED' || response.status === VideoJDStatus.COMPLETED) {
        status = VideoJDStatus.COMPLETED;

        // Cache the completed status
        localStorage.setItem(statusCacheKey, 'COMPLETED');

        // When a videoJD is completed, update the job state store to refresh the job data
        const state = get();
        const videoJD = state.generatingVideoJDs[synthesiaVideoId];
        if (videoJD) {
          useJobStateStore.getState().markJobAsUpdated(videoJD.jobId);
          state.stopTrackingVideoJD(synthesiaVideoId);
        }
      } else if (response.status === 'FAILED' || response.status === VideoJDStatus.FAILED) {
        status = VideoJDStatus.FAILED;

        // Cache the failed status
        localStorage.setItem(statusCacheKey, 'FAILED');

        // Remove from tracking instead of updating status to prevent loops
        const state = get();
        if (state.generatingVideoJDs[synthesiaVideoId]) {
          state.stopTrackingVideoJD(synthesiaVideoId);
        }
      } else {
        status = VideoJDStatus.GENERATING;
      }

      return status;
    } catch (error) {
      console.error(`Error checking videoJD status for ${synthesiaVideoId}:`, error);
      return VideoJDStatus.GENERATING; // Assume still generating on error
    }
  },
}));
