import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { apiClient } from '@/lib/apiHelper';

// Define the company data interface
export interface CompanyData {
  id?: string;
  clientId?: string;
  companyName?: string;
  companyWebsite?: string;
  companyDescription?: string;
  industry?: string;
  size?: string;
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
  phoneNumber?: string;
  department?: string;
  location?: string;
  description?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  logo?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  onboardingRequired?: boolean;
  layoutPreference?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  heroImage?: string;
  footerImage?: string;
  featuredImages?: string[];
  customCss?: string;
  atsProvider?: string;
  atsApiKey?: string;
  atsSubdomain?: string;
  atsCompanyId?: string;
  atsAuthType?: string;
  atsConfig?: Record<string, any>;
  isDemoMode?: boolean;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

interface CompanyStore {
  // State
  company: CompanyData | null;
  isLoading: boolean;
  error: Error | null;
  lastFetchTime: number | null;
  pendingRequests: Map<string, Promise<CompanyData | null>>;

  // Actions
  fetchCompany: (forceRefresh?: boolean, userSub?: string) => Promise<CompanyData | null>;
  updateCompany: (data: Partial<CompanyData>) => Promise<CompanyData | null>;
  clearCompany: () => void;
  setCompany: (company: CompanyData | null) => void;

  // Internal helpers
  _setLoading: (loading: boolean) => void;
  _setError: (error: Error | null) => void;
  _setPendingRequest: (key: string, promise: Promise<CompanyData | null>) => void;
  _removePendingRequest: (key: string) => void;
}

// Create the store with persistence
export const useCompanyStore = create<CompanyStore>()(
  persist(
    (set, get) => ({
      // Initial state
      company: null,
      isLoading: false,
      error: null,
      lastFetchTime: null,
      pendingRequests: new Map(),

      // Actions
      fetchCompany: async (forceRefresh = false, userSub?: string) => {
        const state = get();

        // Check if we have a pending request for this operation
        const requestKey = `fetch_${userSub || 'current'}`;
        if (state.pendingRequests.has(requestKey)) {
          return await state.pendingRequests.get(requestKey)!;
        }

        // Check if we have recent data and don't need to refresh
        if (
          !forceRefresh &&
          state.company &&
          state.lastFetchTime &&
          Date.now() - state.lastFetchTime < 30000 // 30 seconds cache
        ) {
          return state.company;
        }

        // Create the fetch promise
        const fetchPromise = (async (): Promise<CompanyData | null> => {
          try {
            state._setLoading(true);
            state._setError(null);

            // Use the API client with appropriate options
            const options = forceRefresh ? { bypassCache: true } : {};
            const data = await apiClient.get<CompanyData>('/companies/client', options);

            // Update state
            set({
              company: data,
              lastFetchTime: Date.now(),
              isLoading: false,
              error: null,
            });

            // Remove the automatic update to onboarding store to prevent circular dependencies
            // The onboarding store should manage its own state based on user actions

            return data;
          } catch (err) {
            console.error('[CompanyStore] Error fetching company data:', err);
            const error = err instanceof Error ? err : new Error('Failed to fetch company data');

            state._setError(error);
            state._setLoading(false);

            return null;
          } finally {
            // Remove the pending request
            state._removePendingRequest(requestKey);
          }
        })();

        // Store the pending request
        state._setPendingRequest(requestKey, fetchPromise);

        return await fetchPromise;
      },

      updateCompany: async (data: Partial<CompanyData>) => {
        const state = get();

        try {
          state._setLoading(true);
          state._setError(null);

          // Merge with existing company data
          const updatedData = {
            ...(state.company || {}),
            ...data,
          };

          // Update company data in the backend
          await apiClient.patch('/companies', updatedData);

          // Clear caches to ensure fresh data
          if (typeof window !== 'undefined') {
            localStorage.removeItem('api_cache_/companies/client');
            localStorage.removeItem('recent_fetch_/companies/client');
          }

          // Fetch fresh data
          const freshData = await apiClient.get<CompanyData>('/companies/client', {
            bypassCache: true,
          });

          // Update state
          set({
            company: freshData,
            lastFetchTime: Date.now(),
            isLoading: false,
            error: null,
          });

          return freshData;
        } catch (err) {
          console.error('[CompanyStore] Error updating company data:', err);
          const error = err instanceof Error ? err : new Error('Failed to update company data');

          state._setError(error);
          state._setLoading(false);

          return null;
        }
      },

      clearCompany: () => {
        set({
          company: null,
          isLoading: false,
          error: null,
          lastFetchTime: null,
          pendingRequests: new Map(),
        });
      },

      setCompany: (company: CompanyData | null) => {
        set({
          company,
          lastFetchTime: company ? Date.now() : null,
        });
      },

      // Internal helpers
      _setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      _setError: (error: Error | null) => {
        set({ error });
      },

      _setPendingRequest: (key: string, promise: Promise<CompanyData | null>) => {
        const state = get();
        const newPendingRequests = new Map(state.pendingRequests);
        newPendingRequests.set(key, promise);
        set({ pendingRequests: newPendingRequests });
      },

      _removePendingRequest: (key: string) => {
        const state = get();
        const newPendingRequests = new Map(state.pendingRequests);
        newPendingRequests.delete(key);
        set({ pendingRequests: newPendingRequests });
      },
    }),
    {
      name: 'company-store',
      partialize: state => ({
        company: state.company,
        lastFetchTime: state.lastFetchTime,
      }),
      // Only persist for 1 hour
      version: 1,
    }
  )
);

// Helper hook for easier usage
export const useCompany = () => {
  const store = useCompanyStore();

  return {
    company: store.company,
    isLoading: store.isLoading,
    error: store.error,
    fetchCompany: store.fetchCompany,
    updateCompany: store.updateCompany,
    clearCompany: store.clearCompany,
    setCompany: store.setCompany,
  };
};

export default useCompanyStore;
