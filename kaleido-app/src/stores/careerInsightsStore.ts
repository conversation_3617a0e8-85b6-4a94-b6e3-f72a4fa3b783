import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import apiHelper from '@/lib/apiHelper';
import { InsightType, InsightStatus } from '@/lib/career-insights/config';

// Learning resource interface for online resources
export interface LearningResource {
  title: string;
  type: 'COURSE' | 'TUTORIAL' | 'CERTIFICATION' | 'BOOK' | 'LINK';
  provider: string;
  url?: string;
  estimatedDuration: string;
  cost: string;
}

// Enhanced skill gap interface
export interface SkillGap {
  skillName: string;
  currentLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  targetLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  learningResources: LearningResource[];
  timeToAchieve: string;
  marketDemand: number;
}

// Career path step interface
export interface CareerStep {
  title: string;
  description: string;
  timeline: string;
  requirements: string[];
  averageSalaryRange: {
    min: number;
    max: number;
    currency: string;
  };
  growthPotential: 'HIGH' | 'MEDIUM' | 'LOW';
  marketDemand: number;
}

// Career path interface
export interface CareerPath {
  pathName: string;
  steps: CareerStep[];
  totalDuration: string;
  difficultyLevel: 'EASY' | 'MODERATE' | 'DIFFICULT';
  successProbability: number;
}

export interface CareerInsight {
  id: string;
  jobSeekerId: string;
  type: InsightType;
  status: InsightStatus;
  title: string;
  summary: string;
  detailedAnalysis?: string;
  queueJobId?: string;

  // Enhanced type-specific data with proper structure
  skillGapAnalysis?: {
    currentSkills: string[];
    targetRole: string;
    skillGaps: SkillGap[];
    estimatedTimeToClose: string;
    overallReadinessScore: number;
  };

  careerPathRecommendation?: {
    currentPosition: string;
    recommendedPaths: CareerPath[];
    immediateNextSteps: string[];
  };

  marketTrendAnalysis?: {
    industryOverview: string;
    emergingTrends: Array<{
      trend: string;
      impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
      relevanceScore: number;
      description: string;
      dataSource: string;
    }>;
    decliningAreas: Array<{
      trend: string;
      impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
      relevanceScore: number;
      description: string;
      dataSource: string;
    }>;
    opportunities: Array<{
      title: string;
      description: string;
      readinessScore: number;
      requiredPreparation: string[];
    }>;
  };

  roleTransitionGuidance?: {
    fromRole: string;
    toRole: string;
    transitionDifficulty: 'EASY' | 'MODERATE' | 'DIFFICULT';
    transferableSkills: string[];
    newSkillsRequired: SkillGap[];
    transitionPlan: Array<{
      phase: string;
      duration: string;
      actions: string[];
      milestones: string[];
    }>;
    successStories?: Array<{
      summary: string;
      keyFactors: string[];
    }>;
  };

  compensationBenchmark?: {
    currentRole: string;
    location: string;
    experienceLevel: string;
    compensationData: {
      currentMarketRate: {
        min: number;
        max: number;
        median: number;
        currency: string;
      };
      percentile: number;
      factors: Array<{
        factor: string;
        impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
        weight: number;
      }>;
      geographicVariation?: any;
    };
    negotiationTips: string[];
    benefitsComparison?: {
      standard: string[];
      premium: string[];
    };
  };

  // Enhanced AI insights with more structured data
  aiInsights?: {
    strengths: string[];
    opportunities: string[];
    challenges: string[];
    recommendations: Array<{
      priority: 'HIGH' | 'MEDIUM' | 'LOW';
      action: string;
      expectedOutcome: string;
      timeframe: string;
    }>;
    confidenceScore: number;
  };

  viewCount: number;
  lastViewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  validUntil?: Date;
}

export interface CreateCareerInsightRequest {
  type: InsightType;
  targetRole?: string;
  currentRole?: string;
  industry?: string;
  location?: string;
  experienceLevel?: string;
}

interface CareerInsightsState {
  insights: CareerInsight[];
  currentInsight: CareerInsight | null;
  isLoading: boolean;
  isCreating: boolean;
  error: string | null;

  // Actions
  fetchInsights: () => Promise<void>;
  fetchInsightById: (id: string) => Promise<void>;
  createInsight: (data: CreateCareerInsightRequest) => Promise<CareerInsight>;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  insights: [],
  currentInsight: null,
  isLoading: false,
  isCreating: false,
  error: null,
};

export const useCareerInsightsStore = create<CareerInsightsState>()(
  devtools(
    set => ({
      ...initialState,

      fetchInsights: async () => {
        set({ isLoading: true, error: null });
        try {
          const insights = await apiHelper.get<CareerInsight[]>('/career-insights');
          set({ insights, isLoading: false });
        } catch (error) {
          console.error('Error fetching career insights:', error);
          set({
            error: 'Failed to fetch career insights',
            isLoading: false,
          });
        }
      },

      fetchInsightById: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          const insight = await apiHelper.get<CareerInsight>(`/career-insights/${id}`);
          set({ currentInsight: insight, isLoading: false });
        } catch (error) {
          console.error('Error fetching career insight:', error);
          set({
            error: 'Failed to fetch career insight',
            isLoading: false,
          });
        }
      },

      createInsight: async (data: CreateCareerInsightRequest) => {
        set({ isCreating: true, error: null });
        try {
          const newInsight = await apiHelper.post<CareerInsight>('/career-insights', data);

          // Add the new insight to the list
          set(state => ({
            insights: [newInsight, ...state.insights],
            isCreating: false,
          }));

          return newInsight;
        } catch (error) {
          console.error('Error creating career insight:', error);
          set({
            error: 'Failed to create career insight',
            isCreating: false,
          });
          throw error;
        }
      },

      clearError: () => set({ error: null }),

      reset: () => set(initialState),
    }),
    {
      name: 'career-insights-store',
    }
  )
);
