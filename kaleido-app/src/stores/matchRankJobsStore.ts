import { create } from 'zustand';

import { dispatchAppEvent, EVENTS } from '@/constants/events';
import { matchRankJobsService } from '@/services/matchRankJobsService';

import { useJobStateStore } from './unifiedJobStore';

export interface MatchRankJob {
  jobId: string;
  status:
    | 'queued'
    | 'active'
    | 'completed'
    | 'failed'
    | 'cancelled'
    | 'completed_with_errors'
    | 'error';
  progress: number;
  message?: string;
  error?: string;
  result?: any;
  createdAt: string;
  // Additional properties for tracking and UI state
  errorCount?: number;
  notifiedCompletion?: boolean;
  metadata?: any;
}

interface MatchRankJobsState {
  // State
  jobs: Record<string, MatchRankJob>;
  activeJobs: string[]; // Array of jobIds

  // Actions
  addJob: (job: Omit<MatchRankJob, 'createdAt' | 'progress'>) => void;
  updateJobStatus: (jobId: string, status: MatchRankJob['status'], result?: any) => void;
  updateJobProgress: (jobId: string, progress: number) => void;
  removeJob: (jobId: string) => void;
  clearCompletedJobs: () => void;
}

// Helper function to persist state to localStorage
const persistState = (jobs: Record<string, MatchRankJob>, activeJobs: string[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('matchRankJobs', JSON.stringify(jobs));
    localStorage.setItem('activeMatchRankJobs', JSON.stringify(activeJobs));
  }
};

// Helper function to get initial state from localStorage
const getInitialState = () => {
  if (typeof window === 'undefined') {
    return { jobs: {}, activeJobs: [] };
  }

  try {
    const jobs = JSON.parse(localStorage.getItem('matchRankJobs') || '{}');
    const activeJobs = JSON.parse(localStorage.getItem('activeMatchRankJobs') || '[]');

    // Clean up any jobs with numeric IDs (legacy jobs)
    const cleanedJobs: Record<string, MatchRankJob> = {};
    const cleanedActiveJobs: string[] = [];

    Object.entries(jobs).forEach(([key, job]: [string, any]) => {
      // Check if the key is a numeric ID (like "134") instead of a UUID
      if (/^\d+$/.test(key)) {
        console.warn(`Removing legacy match-rank job with numeric ID: ${key}`);
        // Don't include this job in the cleaned jobs
      } else {
        cleanedJobs[key] = job;
      }
    });

    // Clean active jobs array
    activeJobs.forEach((jobId: string) => {
      if (!/^\d+$/.test(jobId)) {
        cleanedActiveJobs.push(jobId);
      }
    });

    // Update localStorage with cleaned data
    if (Object.keys(cleanedJobs).length !== Object.keys(jobs).length) {
      localStorage.setItem('matchRankJobs', JSON.stringify(cleanedJobs));
      localStorage.setItem('activeMatchRankJobs', JSON.stringify(cleanedActiveJobs));
    }

    return { jobs: cleanedJobs, activeJobs: cleanedActiveJobs };
  } catch (error) {
    console.error('Error parsing match rank jobs from localStorage:', error);
    return { jobs: {}, activeJobs: [] };
  }
};

export const useMatchRankJobsStore = create<MatchRankJobsState>((set, get) => {
  // Initialize the match rank jobs service global callback
  if (typeof window !== 'undefined') {
    matchRankJobsService.setGlobalCallback((jobId, statusUpdate) => {
      // Update job progress if available
      if (statusUpdate.progress !== undefined) {
        get().updateJobProgress(jobId, statusUpdate.progress);
      }

      // Update status if it's a terminal status
      if (
        statusUpdate.status === 'completed' ||
        statusUpdate.status === 'failed' ||
        statusUpdate.status === 'cancelled' ||
        statusUpdate.status === 'error'
      ) {
        // Create an updated job object with error information if available
        const updatedJob = {
          status: statusUpdate.status,
          ...(statusUpdate.error && { error: statusUpdate.error }),
          ...(statusUpdate.message && { message: statusUpdate.message }),
          ...(statusUpdate.result && { result: statusUpdate.result }),
        };

        get().updateJobStatus(jobId, statusUpdate.status as MatchRankJob['status'], updatedJob);
      }
    });

    // Restart tracking for any active jobs after page refresh
    const initialState = getInitialState();
    if (initialState.activeJobs.length > 0) {
      // Restart polling for each active job
      initialState.activeJobs.forEach((jobId: string) => {
        if (initialState.jobs[jobId]) {
          // Only track jobs that are not completed
          const status = initialState.jobs[jobId].status;
          if (status === 'queued' || status === 'active') {
            matchRankJobsService.trackJob(jobId, {
              callback: result => {
                if (
                  result.status === 'completed' ||
                  result.status === 'failed' ||
                  result.status === 'error'
                ) {
                  // Create an updated job object with error information if available
                  const updatedJob = {
                    status: result.status,
                    ...(result.error && { error: result.error }),
                    ...(result.message && { message: result.message }),
                    ...(result.result && { result: result.result }),
                  };

                  get().updateJobStatus(jobId, result.status as MatchRankJob['status'], updatedJob);
                }
              },
              maxRetries: 5, // Set max retries to 5
            });
          }
        }
      });
    }
  }

  return {
    // Initial state from localStorage
    ...getInitialState(),

    // Actions
    addJob: job => {
      const newJob: MatchRankJob = {
        ...job,
        progress: 0,
        createdAt: new Date().toISOString(),
      };

      set(state => {
        // Check if job already exists
        const jobExists = state.jobs[job.jobId] !== undefined;

        // Check if job is already in activeJobs
        const isActive = state.activeJobs.includes(job.jobId);

        const updatedJobs = { ...state.jobs, [job.jobId]: newJob };

        // Only add to activeJobs if not already there
        const updatedActiveJobs = isActive ? state.activeJobs : [...state.activeJobs, job.jobId];

        // Persist to localStorage
        persistState(updatedJobs, updatedActiveJobs);

        // Update the job state store with the new job
        // Note: We don't need to update jobStateStore here as it manages IJob entities,
        // while matchRankJobsStore manages MatchRankJob entities which are different

        return {
          jobs: updatedJobs,
          activeJobs: updatedActiveJobs,
        };
      });

      // Register job with our service for tracking
      matchRankJobsService.trackJob(job.jobId, {
        callback: result => {
          if (
            result.status === 'completed' ||
            result.status === 'failed' ||
            result.status === 'error'
          ) {
            // Create an updated job object with error information if available
            const updatedJob = {
              status: result.status,
              ...(result.error && { error: result.error }),
              ...(result.message && { message: result.message }),
              ...(result.result && { result: result.result }),
            };

            get().updateJobStatus(job.jobId, result.status as MatchRankJob['status'], updatedJob);
          }
        },
        maxRetries: 5, // Set max retries to 5
      });
    },

    updateJobStatus: (jobId, status, result) => {
      set(state => {
        // Check if the job exists
        if (!state.jobs[jobId]) return state;

        // Create a new jobs object with the updated job
        const updatedJobs = { ...state.jobs };
        updatedJobs[jobId] = {
          ...updatedJobs[jobId],
          status,
          // Merge any additional properties from result
          ...(result && typeof result === 'object' ? result : { result }),
        };

        // If the job is now complete or failed, remove it from active jobs
        let updatedActiveJobs = [...state.activeJobs];
        if (status === 'completed' || status === 'failed' || status === 'cancelled') {
          updatedActiveJobs = updatedActiveJobs.filter(id => id !== jobId);
        }

        // Dispatch events using our centralized system
        dispatchAppEvent(EVENTS.MATCH_RANK.STATUS_CHANGED, {
          type: 'matchRank',
          action: 'statusChanged',
          jobId,
          status,
          job: updatedJobs[jobId],
        });

        // Only dispatch failed events, not completed events
        // The MatchRankStatusManager handles its own completion modal
        if (status === 'failed' || status === 'error') {
          dispatchAppEvent(EVENTS.MATCH_RANK.JOB_FAILED, {
            type: 'matchRank',
            action: 'failed',
            jobId,
            job: updatedJobs[jobId],
          });
        }

        // Also dispatch the general jobs updated event
        dispatchAppEvent(EVENTS.JOBS.UPDATED, {
          type: 'matchRank',
          action: 'statusChanged',
          jobId,
          status,
          job: updatedJobs[jobId],
        });

        // Persist to localStorage
        persistState(updatedJobs, updatedActiveJobs);

        return { jobs: updatedJobs, activeJobs: updatedActiveJobs };
      });
    },

    updateJobProgress: (jobId, progress) => {
      set(state => {
        // Check if the job exists
        if (!state.jobs[jobId]) return state;

        // Create a new jobs object with the updated job
        const updatedJobs = { ...state.jobs };
        updatedJobs[jobId] = {
          ...updatedJobs[jobId],
          progress,
        };

        // Dispatch an event for the progress update
        dispatchAppEvent(EVENTS.MATCH_RANK.JOB_UPDATED, {
          type: 'matchRank',
          action: 'updated',
          jobId,
          progress,
          job: updatedJobs[jobId],
        });

        // Persist to localStorage
        persistState(updatedJobs, state.activeJobs);

        return { jobs: updatedJobs };
      });
    },

    removeJob: jobId => {
      set(state => {
        // Create new objects without the specified job
        const { [jobId]: removedJob, ...remainingJobs } = state.jobs;
        const activeJobs = state.activeJobs.filter(id => id !== jobId);

        // Clean up tracking
        matchRankJobsService.untrackJob(jobId);

        // Dispatch an event for the job removal
        dispatchAppEvent(EVENTS.JOBS.UPDATED, {
          type: 'matchRank',
          action: 'removed',
          jobId,
        });

        // Persist to localStorage
        persistState(remainingJobs, activeJobs);

        return { jobs: remainingJobs, activeJobs };
      });
    },

    clearCompletedJobs: () => {
      set(state => {
        const updatedJobs = { ...state.jobs };
        const removedJobIds: string[] = [];

        // Remove all completed, failed, or cancelled jobs
        Object.keys(updatedJobs).forEach(jobId => {
          const status = updatedJobs[jobId].status;
          if (status === 'completed' || status === 'failed' || status === 'cancelled') {
            delete updatedJobs[jobId];
            removedJobIds.push(jobId);
          }
        });

        // Dispatch events for each removed job
        removedJobIds.forEach(jobId => {
          dispatchAppEvent(EVENTS.JOBS.UPDATED, {
            type: 'matchRank',
            action: 'removed',
            jobId,
          });
        });

        // Persist to localStorage
        persistState(updatedJobs, state.activeJobs);

        return { jobs: updatedJobs };
      });
    },
  };
});
