export enum ComparisonType {
  QUICK_OVERVIEW = 'quick_overview',
  SKILLS_DEEP_DIVE = 'skills_deep_dive',
  LEADERSHIP_COMPARISON = 'leadership_comparison',
  CULTURAL_TEAM_FIT = 'cultural_team_fit',
  COST_BENEFIT = 'cost_benefit',
  RISK_ASSESSMENT = 'risk_assessment',
  CUSTOM = 'custom',
}

export interface ComparisonOption {
  id: string;
  name: string;
  description: string;
  icon?: string;
  estimatedTime: string;
}

export interface CreateComparisonDto {
  jobId: string;
  candidateIds: string[];
  comparisonType: ComparisonType;
  userPrompt?: string;
  criteria?: string[];
  weights?: Record<string, number>;
}

export interface ScenarioComparisonDto {
  jobId: string;
  candidateIds: string[];
  scenario: string;
}

export interface ComparisonResult {
  id: string;
  status: 'pending' | 'completed' | 'failed';
  comparisonTitle: string;
  comparisonResults?: {
    executiveSummary?: string;
    candidateAnalysis?: Record<string, any>;
    recommendations?: any;
    tradeOffs?: any[];
    headToHeadComparisons?: any[];
    criticalConsiderations?: string[];
  };
  visualData?: any;
}

export const COMPARISON_OPTIONS: ComparisonOption[] = [
  {
    id: ComparisonType.QUICK_OVERVIEW,
    name: 'Quick Overview',
    description: 'Get a rapid side-by-side comparison of key metrics',
    icon: '⚡',
    estimatedTime: '30 seconds',
  },
  {
    id: ComparisonType.SKILLS_DEEP_DIVE,
    name: 'Skills & Technical Fit',
    description: 'Detailed analysis of technical capabilities and skill gaps',
    icon: '🔧',
    estimatedTime: '2 minutes',
  },
  {
    id: ComparisonType.LEADERSHIP_COMPARISON,
    name: 'Leadership & Management',
    description: 'Compare leadership experience and team management capabilities',
    icon: '👥',
    estimatedTime: '2 minutes',
  },
  {
    id: ComparisonType.CULTURAL_TEAM_FIT,
    name: 'Cultural & Team Fit',
    description: 'Assess how each candidate would integrate with the existing team',
    icon: '🤝',
    estimatedTime: '1.5 minutes',
  },
  {
    id: ComparisonType.COST_BENEFIT,
    name: 'Cost-Benefit Analysis',
    description: 'Compare compensation expectations vs. value delivered',
    icon: '💰',
    estimatedTime: '2 minutes',
  },
  {
    id: ComparisonType.RISK_ASSESSMENT,
    name: 'Risk Assessment',
    description: 'Identify and compare potential risks for each candidate',
    icon: '⚠️',
    estimatedTime: '1.5 minutes',
  },
];