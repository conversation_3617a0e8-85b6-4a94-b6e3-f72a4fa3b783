import { JobStatus } from '@/entities/Job.entities';

/**
 * Base interface for all job types
 * This contains the common properties between Job and IJob
 */
export interface BaseJob {
  id: string;
  jobTitle?: string;
  jobType?: string;
  department?: string;
  status?: JobStatus | string;
  companyName?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;

  // Common optional properties
  title?: string; // I<PERSON>ob uses 'title', Job uses 'jobTitle'
  industry?: string;
  experience?: string;
  experienceLevel?: string;
  salaryRange?: string | { min: number; max: number; currency: string };
  currency?: string;
  paymentPeriod?: string;
  location?: string | string[];
  skills?: string[];
  requirements?: string[];
  responsibilities?: string[];
  jobResponsibilities?: string[];
  hiringManagerDescription?: string;

  // Publishing properties
  isPublished?: boolean;
  publishedPlatforms?: string[];
  publishedAt?: string | Date;

  // Company properties
  clientId?: string;
  companyWebsite?: string;
  companyDescription?: string;

  // Additional properties
  jobDescription?: string;
  description?: string; // Some interfaces use 'description' instead of 'jobDescription'
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;

  // Candidate-related properties
  candidates?: any[];
  candidatesCount?: number | any;
  totalCandidates?: number;
  matchedCandidatesCount?: number | any;
  candidateEvaluations?: any;

  // Culture fit properties
  cultureFitQuestions?: string[] | { id: string; question: string; duration: number }[];
  cultureFitDescription?: string;

  // ATS properties
  isAtsJob?: boolean;

  // Other properties that might exist
  [key: string]: any;
}
