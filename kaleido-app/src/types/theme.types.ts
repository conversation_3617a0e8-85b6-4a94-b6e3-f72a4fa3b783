// Shared color theme interface for resume components
export interface ColorTheme {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  accent: string;
  border: string;
  icon: string;
  text: string;
  bg: string;
  pillBg: string;
  pillText: string;
  isDark: boolean;
  gradient?: string;
}

// Default color themes - carefully curated for accessibility and visual appeal
export const colorThemes: ColorTheme[] = [
  // Clean & Professional
  {
    id: 'clean',
    name: 'Clean',
    primary: 'bg-gray-50',
    secondary: 'bg-gray-50',
    accent: 'text-gray-700',
    border: 'border-gray-300',
    icon: 'text-gray-600',
    text: 'text-gray-800',
    bg: 'bg-white',
    pillBg: 'bg-gray-200',
    pillText: 'text-gray-800',
    isDark: false,
  },

  // Modern Purple (Default)
  {
    id: 'modern-purple',
    name: 'Modern Purple',
    primary: 'bg-gradient-to-br from-purple-600 to-indigo-600',
    secondary: 'bg-purple-50',
    accent: 'text-purple-600',
    border: 'border-purple-200',
    icon: 'text-purple-500',
    text: 'text-gray-800',
    bg: 'bg-purple-50',
    pillBg: 'bg-purple-100',
    pillText: 'text-purple-800',
    isDark: false,
  },

  // Ocean Blue
  {
    id: 'ocean-blue',
    name: 'Ocean Blue',
    primary: 'bg-gradient-to-br from-blue-600 to-cyan-500',
    secondary: 'bg-blue-50',
    accent: 'text-blue-600',
    border: 'border-blue-200',
    icon: 'text-blue-500',
    text: 'text-gray-800',
    bg: 'bg-blue-50',
    pillBg: 'bg-blue-100',
    pillText: 'text-blue-800',
    isDark: false,
  },

  // Forest Green
  {
    id: 'forest-green',
    name: 'Forest Green',
    primary: 'bg-gradient-to-br from-emerald-600 to-green-600',
    secondary: 'bg-emerald-50',
    accent: 'text-emerald-600',
    border: 'border-emerald-200',
    icon: 'text-emerald-500',
    text: 'text-gray-800',
    bg: 'bg-emerald-50',
    pillBg: 'bg-emerald-100',
    pillText: 'text-emerald-800',
    isDark: false,
  },

  // Warm Amber
  {
    id: 'warm-amber',
    name: 'Warm Amber',
    primary: 'bg-gradient-to-br from-amber-500 to-orange-500',
    secondary: 'bg-amber-50',
    accent: 'text-amber-600',
    border: 'border-amber-200',
    icon: 'text-amber-600',
    text: 'text-gray-800',
    bg: 'bg-amber-50',
    pillBg: 'bg-amber-100',
    pillText: 'text-amber-800',
    isDark: false,
  },

  // Professional Dark
  {
    id: 'professional-dark',
    name: 'Professional Dark',
    primary: 'bg-gradient-to-br from-slate-700 to-gray-800',
    secondary: 'bg-slate-800',
    accent: 'text-blue-400',
    border: 'border-slate-500',
    icon: 'text-blue-400',
    text: 'text-white',
    bg: 'bg-slate-900',
    pillBg: 'bg-slate-700',
    pillText: 'text-white',
    isDark: true,
  },

  // High Contrast
  {
    id: 'high-contrast',
    name: 'High Contrast',
    primary: 'bg-gray-900',
    secondary: 'bg-white',
    accent: 'text-blue-700',
    border: 'border-gray-400',
    icon: 'text-blue-700',
    text: 'text-black',
    bg: 'bg-white',
    pillBg: 'bg-blue-700',
    pillText: 'text-white',
    isDark: false,
  },

  // Elegant Rose
  {
    id: 'elegant-rose',
    name: 'Elegant Rose',
    primary: 'bg-gradient-to-br from-rose-500 to-pink-500',
    secondary: 'bg-rose-50',
    accent: 'text-rose-600',
    border: 'border-rose-200',
    icon: 'text-rose-500',
    text: 'text-gray-800',
    bg: 'bg-rose-50',
    pillBg: 'bg-rose-100',
    pillText: 'text-rose-800',
    isDark: false,
  },
];

// Default theme (Modern Purple)
export const defaultTheme = colorThemes[1];
