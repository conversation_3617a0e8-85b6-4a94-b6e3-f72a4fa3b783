export enum CompanyMemberRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

export enum CompanyMemberStatus {
  ACTIVE = 'active',
  INVITED = 'invited',
  SUSPENDED = 'suspended',
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

export interface CompanyMemberPermissions {
  canManageJobs?: boolean;
  canViewCandidates?: boolean;
  canManageCandidates?: boolean;
  canManageTeam?: boolean;
  canManageBilling?: boolean;
  canViewAnalytics?: boolean;
  canManageCompanySettings?: boolean;
}

export interface CompanyMember {
  id: string;
  companyId: string;
  clientId: string;
  email: string;
  role: CompanyMemberRole;
  permissions: CompanyMemberPermissions;
  status: CompanyMemberStatus;
  invitedBy?: string;
  invitedAt: Date;
  joinedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompanyInvitation {
  id: string;
  companyId: string;
  email: string;
  invitedByClientId: string;
  invitedByName?: string;
  role: CompanyMemberRole;
  permissions?: CompanyMemberPermissions;
  status: InvitationStatus;
  message?: string;
  createdAt: Date;
  expiresAt: Date;
}

export interface InviteTeamMemberDto {
  email: string;
  role: CompanyMemberRole;
  permissions?: CompanyMemberPermissions;
  message?: string;
}

export interface UpdateTeamMemberDto {
  role?: CompanyMemberRole;
  permissions?: CompanyMemberPermissions;
  status?: CompanyMemberStatus;
}

export interface CompanyTeamSettings {
  allowedEmailDomains: string[];
  autoJoinEnabled: boolean;
  defaultAutoJoinRole: CompanyMemberRole;
}

// Helper to get default permissions for a role
export const getDefaultPermissions = (role: CompanyMemberRole): CompanyMemberPermissions => {
  switch (role) {
    case CompanyMemberRole.OWNER:
      return {
        canManageJobs: true,
        canViewCandidates: true,
        canManageCandidates: true,
        canManageTeam: true,
        canManageBilling: true,
        canViewAnalytics: true,
        canManageCompanySettings: true,
      };
    case CompanyMemberRole.ADMIN:
      return {
        canManageJobs: true,
        canViewCandidates: true,
        canManageCandidates: true,
        canManageTeam: true,
        canManageBilling: false,
        canViewAnalytics: true,
        canManageCompanySettings: true,
      };
    case CompanyMemberRole.MEMBER:
      return {
        canManageJobs: true,
        canViewCandidates: true,
        canManageCandidates: true,
        canManageTeam: false,
        canManageBilling: false,
        canViewAnalytics: true,
        canManageCompanySettings: false,
      };
    case CompanyMemberRole.VIEWER:
      return {
        canManageJobs: false,
        canViewCandidates: true,
        canManageCandidates: false,
        canManageTeam: false,
        canManageBilling: false,
        canViewAnalytics: true,
        canManageCompanySettings: false,
      };
  }
};
