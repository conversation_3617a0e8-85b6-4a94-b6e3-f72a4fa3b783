// ============================================================================
// COMMON JOB INTERFACE
// ============================================================================
// This interface represents the common structure between JobData and IJob
// Both types can be used wherever CommonJob is expected

/**
 * Common job interface that both JobData and IJob implement
 * This allows for type compatibility without explicit conversions
 */
export interface CommonJob {
  id: string;
  jobTitle: string;

  // Required fields from IJob
  matchedCandidatesCount: any;
  candidatesCount: any;

  // Make all other fields optional to ensure compatibility
  jobType?: string;
  title?: string;
  department?: string;
  jobDescription?: string;
  description?: string;
  clientId?: string;
  companyName?: string;
  companyWebsite?: string;
  companyDescription?: string;
  status?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;

  // totalCandidates is optional
  totalCandidates?: number;

  // Thresholds
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;

  // Arrays that might be different types
  requirements?: string[] | string;
  skills?: string[];
  responsibilities?: string[];
  jobResponsibilities?: string[];
  benefits?: string[];

  // Location and employment
  location?: string | string[];
  employmentType?: string;
  workMode?: string;

  // Salary and payment
  salaryRange?: string | { min: number; max: number; currency: string };
  currency?: string;
  paymentPeriod?: string;

  // Experience
  experience?: string;
  experienceLevel?: string;

  // Culture fit
  cultureFitQuestions?: string[] | Array<{ id: string; question: string; duration: number }>;
  cultureFitDescription?: string;

  // Publishing
  isPublished?: boolean;
  publishedAt?: string | Date;
  publishedPlatforms?: string[];

  // Other fields
  industry?: string;
  hiringManagerDescription?: string;
  viewCount?: number;
  applicationCount?: number;
  candidates?: any[];
  candidateEvaluations?: any;

  // Allow any additional properties
  [key: string]: any;
}

/**
 * Type guard to check if an object is a CommonJob
 */
export function isCommonJob(obj: any): obj is CommonJob {
  return obj && typeof obj === 'object' && 'id' in obj && 'jobTitle' in obj;
}

/**
 * Helper to ensure an object conforms to CommonJob
 */
export function toCommonJob(obj: any): CommonJob {
  if (!obj || typeof obj !== 'object') {
    throw new Error('Invalid job object');
  }

  return {
    id: obj.id || '',
    jobTitle: obj.jobTitle || obj.title || '',
    ...obj,
  };
}
