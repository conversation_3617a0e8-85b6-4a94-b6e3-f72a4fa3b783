// Global type definitions

interface Window {
  gtag?: (
    command: 'config' | 'event' | 'js' | 'set',
    targetId: string,
    config?: Gtag.ControlParams | Gtag.EventParams | Gtag.ConfigParams | Date
  ) => void;
}

declare namespace Gtag {
  interface ConfigParams {
    page_location?: string;
    page_path?: string;
    page_title?: string;
    send_page_view?: boolean;
    [key: string]: any;
  }

  interface EventParams {
    event_category?: string;
    event_label?: string;
    value?: number;
    [key: string]: any;
  }

  interface ControlParams {
    groups?: string | string[];
    send_to?: string | string[];
    event_callback?: () => void;
    event_timeout?: number;
  }
}
