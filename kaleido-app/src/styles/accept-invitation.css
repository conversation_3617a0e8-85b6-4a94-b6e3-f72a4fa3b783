/* Animation delay utility class */
.animation-delay-1000 {
  animation-delay: 1s;
}

/* Additional glassmorphism enhancements if needed */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Custom radial gradient */
.bg-radial-gradient {
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(67, 56, 202, 0.2) 50%,
    rgba(109, 40, 217, 0.4) 100%
  );
}
