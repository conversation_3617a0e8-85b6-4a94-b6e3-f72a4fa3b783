'use client';

import React, { useState } from 'react';

import { motion } from 'framer-motion';
import { Crown, Gift, Loader2, Package, Rocket, Shield, Star } from 'lucide-react';

import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import apiHelper from '@/lib/apiHelper';
import { getCurrentUserSubscriptionPlan, getPlanPrice, getPricingData } from '@/lib/pricingUtils';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { SubscriptionPlan } from '@/types/subscription';

import { PLAN_FEATURES, PlanFeature, SubscriptionPlansProps } from './subscription-plans.types';
import styles from './SubscriptionPlans.module.css';

const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({ currentPlan }) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  const [isUpgrading, setIsUpgrading] = useState(false);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Get pricing data from locales
  const pricingData = getPricingData();

  // Get current user's subscription plan from localStorage for more accurate detection
  const userSubscriptionPlan = getCurrentUserSubscriptionPlan();

  // Use the more accurate plan from localStorage if available, otherwise fallback to currentPlan prop
  const actualCurrentPlan = userSubscriptionPlan || currentPlan;

  const handleUpgrade = async (plan: SubscriptionPlan) => {
    if (plan === actualCurrentPlan) {
      showToast({
        message: 'You are already on this plan',
        isSuccess: false,
      });
      return;
    }

    setIsUpgrading(true);
    try {
      // Call the API to upgrade the subscription
      await apiHelper.post('/subscription/upgrade', {
        plan,
        billingCycle,
      });

      showToast({
        message: `Successfully upgraded to ${plan} plan`,
        isSuccess: true,
      });

      // Refresh data after a short delay
      setTimeout(() => {
        invalidateJobsCache();
        refreshJobs(true);

        // Clear any cached data
        if (typeof localStorage !== 'undefined') {
          const keys = Object.keys(localStorage);
          const cacheKeys = keys.filter(
            key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
          );
          cacheKeys.forEach(key => localStorage.removeItem(key));
        }
      }, 2000);
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      showToast({
        message: 'Failed to upgrade subscription. Please try again later.',
        isSuccess: false,
      });
    } finally {
      setIsUpgrading(false);
    }
  };

  // Get plan price using the new pricing model
  const getPriceForPlan = (plan: SubscriptionPlan) => {
    return getPlanPrice(plan, billingCycle === 'annual' ? 'yearly' : 'monthly');
  };

  return (
    <div className="relative w-full max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`rounded-3xl overflow-hidden bg-gradient-to-br from-pink-900/40 to-purple-900/40 backdrop-blur-xl border border-pink-700/30 shadow-[0_0_50px_rgba(219,39,119,0.15)] ${styles.patternBg}`}
      >
        <div className="px-6 py-12 sm:px-12 text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Package className="h-8 w-8 text-pink-500" />
            <h1 className="text-4xl font-bold text-white">{pricingData.title}</h1>
          </div>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">{pricingData.subtitle}</p>
          <p className="text-sm text-white/60 max-w-2xl mx-auto mt-2">{pricingData.description}</p>

          <div className="mt-8 mb-12">
            <Tabs
              defaultValue="monthly"
              value={billingCycle}
              onValueChange={(value: string) => setBillingCycle(value as 'monthly' | 'annual')}
              className="mx-auto w-fit"
            >
              <TabsList className="bg-pink-950/50 border border-pink-900/50">
                <TabsTrigger
                  value="monthly"
                  className="data-[state=active]:bg-pink-700 data-[state=active]:text-white"
                >
                  {pricingData.billingToggle.monthly}
                </TabsTrigger>
                <TabsTrigger
                  value="annual"
                  className="data-[state=active]:bg-pink-700 data-[state=active]:text-white"
                >
                  {pricingData.billingToggle.yearly}{' '}
                  <span className="text-pink-300 ml-1 text-xs">
                    {pricingData.billingToggle.saveText}
                  </span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className={`grid grid-cols-1 md:grid-cols-4 gap-8 ${styles.planGrid}`}>
            {/* Free Plan */}
            <PlanCard
              name={pricingData.plans[SubscriptionPlan.FREE].name}
              plan={SubscriptionPlan.FREE}
              price={getPriceForPlan(SubscriptionPlan.FREE)}
              features={PLAN_FEATURES}
              isCurrent={actualCurrentPlan === SubscriptionPlan.FREE}
              onUpgrade={() => handleUpgrade(SubscriptionPlan.FREE)}
              isLoading={isUpgrading}
              billingCycle={billingCycle}
              icon={Gift}
            />

            {/* Starter Plan */}
            <PlanCard
              name={pricingData.plans[SubscriptionPlan.STARTER].name}
              plan={SubscriptionPlan.STARTER}
              price={getPriceForPlan(SubscriptionPlan.STARTER)}
              features={PLAN_FEATURES}
              isCurrent={actualCurrentPlan === SubscriptionPlan.STARTER}
              onUpgrade={() => handleUpgrade(SubscriptionPlan.STARTER)}
              isLoading={isUpgrading}
              billingCycle={billingCycle}
              icon={Star}
            />

            {/* Professional Plan */}
            <PlanCard
              name={pricingData.plans[SubscriptionPlan.PROFESSIONAL].name}
              plan={SubscriptionPlan.PROFESSIONAL}
              price={getPriceForPlan(SubscriptionPlan.PROFESSIONAL)}
              features={PLAN_FEATURES}
              isCurrent={actualCurrentPlan === SubscriptionPlan.PROFESSIONAL}
              onUpgrade={() => handleUpgrade(SubscriptionPlan.PROFESSIONAL)}
              isLoading={isUpgrading}
              billingCycle={billingCycle}
              isHighlighted={true}
              icon={Rocket}
            />

            {/* Enterprise Plan */}
            <PlanCard
              name={pricingData.plans[SubscriptionPlan.ENTERPRISE].name}
              plan={SubscriptionPlan.ENTERPRISE}
              price={getPriceForPlan(SubscriptionPlan.ENTERPRISE)}
              features={PLAN_FEATURES}
              isCurrent={actualCurrentPlan === SubscriptionPlan.ENTERPRISE}
              onUpgrade={() => handleUpgrade(SubscriptionPlan.ENTERPRISE)}
              isLoading={isUpgrading}
              billingCycle={billingCycle}
              icon={Crown}
            />
          </div>

          <div className="mt-12 text-center text-white/70">
            <p className="flex items-center justify-center gap-2">
              <Shield className="h-4 w-4 text-pink-400" />
              {pricingData.securePayments}
            </p>
          </div>

          {/* Credits System Information */}
          <div className="mt-8 bg-pink-950/20 rounded-xl p-6 border border-pink-900/30">
            <h3 className="text-lg font-semibold text-white mb-2">
              {pricingData.creditsSystem.title}
            </h3>
            <p className="text-sm text-white/70 mb-4">{pricingData.creditsSystem.description}</p>
            <ul className="text-sm text-white/60 space-y-1">
              {pricingData.creditsSystem.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <div className="w-1 h-1 bg-pink-400 rounded-full"></div>
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          <div className="mt-8">
            <Button
              size="lg"
              className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-6 text-lg rounded-full"
              onClick={() => {
                let nextPlan = SubscriptionPlan.STARTER;
                if (currentPlan === SubscriptionPlan.FREE) {
                  nextPlan = SubscriptionPlan.STARTER;
                } else if (currentPlan === SubscriptionPlan.STARTER) {
                  nextPlan = SubscriptionPlan.PROFESSIONAL;
                } else if (currentPlan === SubscriptionPlan.PROFESSIONAL) {
                  nextPlan = SubscriptionPlan.ENTERPRISE;
                }
                handleUpgrade(nextPlan);
              }}
              disabled={isUpgrading || currentPlan === SubscriptionPlan.ENTERPRISE}
            >
              {isUpgrading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Processing...
                </>
              ) : (
                <>Upgrade now</>
              )}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

interface PlanCardProps {
  name: string;
  plan: SubscriptionPlan;
  price: number | string;
  features: PlanFeature[];
  isCurrent: boolean;
  onUpgrade: () => void;
  isLoading: boolean;
  billingCycle: 'monthly' | 'annual';
  isHighlighted?: boolean;
  icon?: React.ElementType;
}

const PlanCard: React.FC<PlanCardProps> = ({
  name,
  plan,
  price,
  features,
  isCurrent,
  onUpgrade,
  isLoading,
  billingCycle,
  isHighlighted = false,
  icon: Icon,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        delay:
          plan === SubscriptionPlan.STARTER
            ? 0.1
            : plan === SubscriptionPlan.PROFESSIONAL
              ? 0.2
              : 0.3,
      }}
      className={`rounded-xl overflow-hidden ${styles.planCard} ${
        isHighlighted
          ? `bg-gradient-to-b from-pink-900/40 to-purple-900/40 border-2 border-pink-500/50 ${styles.highlightedPlan} ${styles.glowEffect}`
          : 'bg-pink-950/30 border border-pink-900/30'
      }`}
    >
      <div className="p-6 relative">
        {/* Current Plan Badge */}
        {isCurrent && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg border border-green-400/30">
              ✓ Current Plan
            </div>
          </div>
        )}

        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {Icon && <Icon className="h-5 w-5 text-pink-400" />}
            <h3 className="text-xl font-semibold text-white">{name}</h3>
          </div>
          {/* {isCurrent && (
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          )} */}
        </div>

        <div className={`mt-4 flex items-baseline ${styles.priceTag}`}>
          <span className="text-5xl font-extrabold text-white">
            {typeof price === 'number' ? `$${price}` : price}
          </span>
          {typeof price === 'number' && (
            <span className="ml-2 text-sm text-white/70">
              /Per user
              <br />
              /Per {billingCycle === 'monthly' ? 'month' : 'month, billed annually'}
            </span>
          )}
        </div>

        <div className="mt-6">
          <Button
            className={`w-full py-6 ${
              isCurrent
                ? 'bg-green-600/50 text-white cursor-default border border-green-500/30'
                : isHighlighted
                  ? 'bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white'
                  : 'bg-pink-700/50 hover:bg-pink-700/70 text-white'
            }`}
            onClick={onUpgrade}
            disabled={isCurrent || isLoading}
          >
            {isCurrent ? '✓ Current Plan' : 'Select Plan'}
          </Button>
        </div>

        <ul className={`mt-6 space-y-3 ${styles.featureList}`}>
          {features.map((feature, index) => {
            // Use getValue function if available, otherwise use static value
            let value;
            if (feature.getValue && feature.name === 'Monthly Credits') {
              value = feature.getValue(plan, billingCycle);
            } else {
              value = feature[plan.toLowerCase() as keyof typeof feature];
            }

            const isIncluded = value !== false;
            const Icon = feature.icon;

            return (
              <li
                key={index}
                className="flex items-start rounded-lg p-1 hover:bg-white/5 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">
                  {isIncluded ? (
                    <Icon className="h-5 w-5 text-pink-400" />
                  ) : (
                    <span className="block h-5 w-5"></span>
                  )}
                </div>
                <div className="ml-3 text-sm text-white/80">
                  <span>{feature.name}</span>
                  {isIncluded && typeof value !== 'boolean' && (
                    <span className="ml-1 text-white font-medium">
                      : {value === 'Unlimited' ? 'Unlimited' : `${value}`}
                    </span>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </motion.div>
  );
};

export default SubscriptionPlans;
