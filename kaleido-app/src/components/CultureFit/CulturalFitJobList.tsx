import { useEffect, useState } from 'react';

import { Briefcase, Building2, FileText, PenLine, Users } from 'lucide-react';

import { IJob } from '@/entities/interfaces';
import { CultureFitQuestions } from '@/entities/Job.entities';
import { useJobStateStore } from '@/stores/unifiedJobStore';

import GenericTable from '../Layouts/GenericTable';

interface CulturalFitJobListProps {
  jobs: IJob[];
  gridClass?: string;
  onJobClick?: (job: IJob) => void;
}

const CulturalFitJobList = ({ jobs = [], gridClass, onJobClick }: CulturalFitJobListProps) => {
  const [tableData, setTableData] = useState<IJob[]>(jobs);

  // Update local state when props change
  useEffect(() => {
    setTableData(jobs);
  }, [jobs]);

  // Subscribe to job state changes using Zustand store
  useEffect(() => {
    // Subscribe to job state changes
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        // This component receives jobs as props, so we don't need to update local state
        // The parent component will re-render with updated jobs
      }
    );

    // Clean up subscription
    return () => {
      unsubscribe();
    };
  }, []);

  const handleRowClick = async (job: IJob) => {
    if (onJobClick) {
      onJobClick(job);
    }
  };

  const formatCandidateCount = (count: number) => {
    if (count >= 10) return '10+';
    return count.toString();
  };

  const handleUpdate = async (data: {
    id: string;
    cultureFitDescription: string;
    cultureFitQuestions: CultureFitQuestions[];
  }): Promise<boolean> => {
    try {
      // Implement the logic to update the job details here
      // For example, you might make an API call to update the job in the database
      // Simulate a successful update
      return true;
    } catch (error) {
      console.error('Failed to update job:', error);
      return false;
    }
  };

  const columns = [
    {
      key: 'jobType',
      label: 'Job Title',
      icon: Briefcase,
    },
    {
      key: 'companyName',
      label: 'Company',
      icon: Building2,
    },
    {
      key: 'candidates',
      label: 'Candidates',
      icon: Users,
      render: (candidates: any[]) => (
        <span className="inline-flex px-3 py-1 rounded-full text-xs font-medium bg-blue-400/20 text-blue-400">
          {formatCandidateCount(candidates?.length || 0)}
        </span>
      ),
    },
    {
      key: 'cultureFitQuestions',
      label: 'Questions',
      icon: FileText,
      render: (questions: any[]) => (
        <span className="text-sm text-gray-300">{questions?.length || 0} Questions</span>
      ),
    },
    {
      key: 'cultureFitDescription',
      label: 'Culture Description',
      icon: PenLine,
      render: (description: string) => (
        <span
          className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
            description ? 'bg-green-400/20 text-green-400' : 'bg-gray-400/20 text-gray-400'
          }`}
        >
          <PenLine className="w-3 h-3" />
          {description ? 'Defined' : 'Not Set'}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (status: string) => (
        <span className="inline-flex px-3 py-1 rounded-full text-xs font-medium bg-blue-400/20 text-blue-400">
          {status || 'Draft'}
        </span>
      ),
    },
  ];

  return <GenericTable data={tableData} columns={columns} onRowClick={handleRowClick} />;
};

export default CulturalFitJobList;
