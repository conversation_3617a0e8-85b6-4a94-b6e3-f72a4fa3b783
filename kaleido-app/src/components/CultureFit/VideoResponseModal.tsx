import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { Calendar, CheckCircle, ChevronLeft, List, Play, Timer, Video, X } from 'lucide-react';

import { ICandidate, IVideoResponse } from '@/entities/interfaces';

interface CultureFitQuestion {
  id: string;
  question: string;
  duration: number;
}

interface VideoResponseModalProps {
  candidate: ICandidate;
  onClose: () => void;
}

const VideoResponseModal: React.FC<VideoResponseModalProps> = ({ candidate, onClose }) => {
  // Get unique questions and their latest responses
  const uniqueResponses =
    candidate.videoResponses?.reduce(
      (acc, response: IVideoResponse) => {
        if (
          !acc[response.question] ||
          new Date(response.recordedAt).getTime() >
            new Date(acc[response.question].recordedAt).getTime()
        ) {
          acc[response.question] = response;
        }
        return acc;
      },
      {} as Record<string, IVideoResponse>
    ) || {};

  // Convert to array and sort by recordedAt
  const candidateResponses: IVideoResponse[] = Object.values(uniqueResponses);
  candidateResponses.sort(
    (a: IVideoResponse, b: IVideoResponse) =>
      new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime()
  );

  const [selectedResponse, setSelectedResponse] = useState<IVideoResponse | null>(
    candidateResponses.length > 0 ? candidateResponses[0] : null
  );
  const [cultureFitQuestions, setCultureFitQuestions] = useState<CultureFitQuestion[]>([]);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);

  // Initialize with first question if no responses
  React.useEffect(() => {
    if (
      cultureFitQuestions.length > 0 &&
      candidateResponses.length === 0 &&
      !selectedResponse &&
      !selectedQuestionId
    ) {
      setSelectedQuestionId(cultureFitQuestions[0].id);
    }
  }, [cultureFitQuestions, candidateResponses, selectedResponse, selectedQuestionId]);

  const [isMobile, setIsMobile] = useState(false);
  const [showQuestionsList, setShowQuestionsList] = useState(false);

  // Format duration to minutes:seconds
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get relative time
  const getRelativeTime = (date: Date | string) => {
    const dateObj = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  // Format date with full date and time
  const formatDate = (date: Date | string) => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Listen for mobile changes
  useEffect(() => {
    // Mobile detection
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkMobile();

    // Listen for resize
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return createPortal(
    <div
      className="fixed inset-0 z-[10001] bg-white dark:bg-black"
      style={{ backgroundColor: `var(--background-rgb)` }}
    >
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 dark:bg-gray-900/60 backdrop-blur-md" />

      <AnimatePresence mode="wait">
        <motion.div
          className={`fixed ${
            isMobile ? 'inset-0' : 'inset-4'
          } bg-white/90 dark:bg-gray-800/50 backdrop-blur-xl ${
            isMobile ? 'rounded-none' : 'rounded-2xl'
          } overflow-hidden border border-gray-300/20 dark:border-white/10 shadow-2xl`}
          style={{
            backgroundColor: `var(--card-bg)`,
            borderColor: `var(--card-border)`,
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          {/* Header */}
          <div
            className="absolute top-0 left-0 right-0 p-6 border-b border-gray-300/20 dark:border-white/10 backdrop-blur-md"
            style={{ borderColor: `var(--card-border)` }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {isMobile && showQuestionsList && (
                  <button
                    type="button"
                    onClick={() => setShowQuestionsList(false)}
                    className="p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
                    aria-label="Back to video"
                  >
                    <ChevronLeft
                      className="w-6 h-6 text-gray-600 dark:text-gray-400"
                      style={{ color: `var(--input-placeholder)` }}
                    />
                  </button>
                )}
                <Video className="w-6 h-6 text-muted-foreground" />
                <div>
                  <h2
                    className="text-xl font-semibold text-gray-800 dark:text-white"
                    style={{ color: `var(--foreground-color)` }}
                  >
                    {isMobile && showQuestionsList
                      ? 'Select Question'
                      : 'Video Introduction Responses'}
                  </h2>
                  <p
                    className="text-gray-500 dark:text-gray-400"
                    style={{ color: `var(--input-placeholder)` }}
                  >
                    {candidate.fullName}
                  </p>
                </div>
              </div>
              <button
                type="button"
                onClick={onClose}
                className="p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
                aria-label="Close video responses"
              >
                <X
                  className="w-6 h-6 text-gray-600 dark:text-gray-400"
                  style={{ color: `var(--input-placeholder)` }}
                />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className={`w-full max-w-7xl mx-auto ${isMobile ? 'pt-24' : 'pt-36'} px-6`}>
            <div
              className={`${isMobile ? 'flex flex-col h-full' : 'grid grid-cols-1 lg:grid-cols-5 gap-8'}`}
            >
              {/* Questions Sidebar */}
              <div
                className={`${isMobile ? (showQuestionsList ? 'w-full h-full' : 'hidden') : 'col-span-1 lg:col-span-2'}`}
              >
                <div className={`${isMobile ? 'p-6' : 'sticky top-0'}`}>
                  {/* Questions List */}
                  <div className="space-y-2">
                    <AnimatePresence>
                      {/* Show all culture fit questions with their responses */}
                      {(cultureFitQuestions.length > 0
                        ? cultureFitQuestions
                        : candidateResponses
                      ).map((item: CultureFitQuestion | IVideoResponse, index: number) => {
                        // Determine if this is a culture fit question or response
                        const isCultureFitQuestion = 'duration' in item && !('videoUrl' in item);
                        const question = isCultureFitQuestion ? (item as CultureFitQuestion) : null;
                        const response = isCultureFitQuestion
                          ? candidateResponses.find(
                              r => r.question === (item as CultureFitQuestion).question
                            )
                          : (item as IVideoResponse);

                        const isSelected = response
                          ? selectedResponse?.id === response.id
                          : selectedQuestionId === question?.id;

                        return (
                          <motion.button
                            key={question?.id || response?.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            onClick={() => {
                              if (response) {
                                setSelectedResponse(response);
                                setSelectedQuestionId(null);
                              } else {
                                setSelectedResponse(null);
                                setSelectedQuestionId(question?.id || null);
                              }
                              if (isMobile) {
                                setShowQuestionsList(false);
                              }
                            }}
                            className={`w-full text-left transition-all duration-300 group relative ${
                              isSelected ? 'scale-[1.02]' : 'hover:scale-[1.01]'
                            }`}
                          >
                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                            <div
                              className={`relative flex gap-4 p-4 rounded-2xl border transition-all duration-300 ${
                                isSelected ? '' : 'hover:bg-white/5 border-transparent'
                              }`}
                            >
                              {/* Glassmorphic gradient background for selected item */}
                              {isSelected && (
                                <>
                                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-purple-600/20 blur-xl" />
                                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
                                  <div className="absolute inset-0 rounded-2xl backdrop-blur-sm" />
                                  <div className="absolute inset-0 rounded-2xl border border-purple-400/30" />
                                </>
                              )}

                              {/* Question Number */}
                              <div className="relative flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center font-bold transition-all duration-300 z-10">
                                {isSelected ? (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg shadow-purple-500/30" />
                                    <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/20 to-transparent" />
                                    <span className="relative text-white">
                                      {(index + 1).toString().padStart(2, '0')}
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-muted/50" />
                                    <span className="relative text-muted-foreground group-hover:text-foreground">
                                      {(index + 1).toString().padStart(2, '0')}
                                    </span>
                                  </>
                                )}
                              </div>

                              {/* Question Content */}
                              <div className="flex-1 min-w-0 relative z-10">
                                <p
                                  className={`font-medium line-clamp-2 mb-2 transition-colors duration-300 ${
                                    isSelected
                                      ? 'text-foreground'
                                      : 'text-muted-foreground group-hover:text-foreground'
                                  }`}
                                >
                                  {question?.question || response?.question}
                                </p>

                                <div className="flex items-center gap-3 text-xs">
                                  {response ? (
                                    <>
                                      <div
                                        className={`flex items-center gap-1 transition-colors duration-300 ${
                                          isSelected
                                            ? 'text-purple-400'
                                            : 'text-muted-foreground/70'
                                        }`}
                                      >
                                        <Timer className="w-3 h-3" />
                                        <span>{formatDuration(response.duration)}</span>
                                      </div>
                                      <span className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
                                      <span
                                        className={`transition-colors duration-300 ${
                                          isSelected ? 'text-pink-400' : 'text-muted-foreground/70'
                                        }`}
                                      >
                                        {getRelativeTime(response.recordedAt)}
                                      </span>
                                    </>
                                  ) : (
                                    <span className="text-muted-foreground/70 italic">
                                      Candidate has not responded yet
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* Play Icon or Status Icon */}
                              <div className="relative flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 z-10">
                                {response ? (
                                  isSelected ? (
                                    <>
                                      {/* Glassmorphic play button */}
                                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-500/30 to-pink-500/30 backdrop-blur-sm" />
                                      <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/10 to-transparent" />
                                      <div className="absolute inset-0 rounded-full border border-purple-400/20" />
                                      <Play
                                        className="relative w-4 h-4 text-purple-300"
                                        fill="currentColor"
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <div className="absolute inset-0 rounded-full bg-muted/30 group-hover:bg-gradient-to-br group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-300" />
                                      <Play
                                        className="relative w-4 h-4 text-muted-foreground group-hover:text-purple-400 transition-colors duration-300"
                                        fill="currentColor"
                                      />
                                    </>
                                  )
                                ) : (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-muted/20" />
                                    <Timer className="relative w-4 h-4 text-muted-foreground/50" />
                                  </>
                                )}
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                </div>
              </div>

              {/* Video Player */}
              <div
                className={`${isMobile ? (showQuestionsList ? 'hidden' : 'flex-1') : 'col-span-1 lg:col-span-3'} ${isMobile ? 'p-6' : 'p-0'}`}
              >
                <AnimatePresence mode="wait">
                  {selectedResponse ? (
                    <motion.div
                      key={selectedResponse.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                      className="h-full"
                    >
                      {/* Mobile Questions Toggle Button */}
                      {isMobile && (
                        <button
                          type="button"
                          onClick={() => setShowQuestionsList(true)}
                          className="w-full p-3 bg-gray-100/50 hover:bg-gray-200/50 dark:bg-white/5 dark:hover:bg-white/10 rounded-lg transition-colors flex items-center justify-center gap-2 mb-6"
                          aria-label="View all questions"
                        >
                          <List className="w-5 h-5" />
                          <span>
                            View All Questions (
                            {cultureFitQuestions.length > 0
                              ? cultureFitQuestions.length
                              : candidateResponses.length}
                            )
                          </span>
                        </button>
                      )}

                      <div className="h-full flex flex-col justify-center">
                        {/* Video Player with Enhanced Shadow */}
                        <div className="relative w-full max-w-4xl mx-auto rounded-2xl overflow-hidden bg-black shadow-2xl ring-1 ring-white/10">
                          <div className={`${isMobile ? 'aspect-[9/16]' : 'aspect-video'}`}>
                            <video
                              key={selectedResponse.id}
                              src={selectedResponse.videoUrl}
                              controls
                              controlsList="nodownload"
                              className={`w-full h-full ${isMobile ? 'object-cover' : 'object-contain'}`}
                              playsInline
                            />
                          </div>

                          {/* Floating Info Overlay */}
                          <div className="absolute top-6 left-6 right-6 flex justify-between items-start pointer-events-none">
                            {/* Question Number Badge */}
                            <div className="bg-black/60 backdrop-blur-md px-4 py-2 rounded-full flex items-center gap-2">
                              <span className="text-white/70 text-sm">Question</span>
                              <span className="text-white font-bold">
                                {candidateResponses.findIndex(r => r.id === selectedResponse.id) +
                                  1}
                              </span>
                              <span className="text-white/70 text-sm">
                                of {candidateResponses.length}
                              </span>
                            </div>

                            {/* Duration Badge */}
                            <div className="bg-black/60 backdrop-blur-md px-3 py-2 rounded-full text-white text-sm flex items-center gap-2">
                              <Timer className="w-4 h-4" />
                              <span className="font-medium">
                                {formatDuration(selectedResponse.duration)}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Elegant Info Bar */}
                        <div className="mt-6 flex items-center justify-between px-2 max-w-4xl mx-auto w-full">
                          {/* Status */}
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Status</p>
                              <p className="text-sm font-medium text-green-600 dark:text-green-400">
                                Completed
                              </p>
                            </div>
                          </div>

                          {/* Recording Date */}
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                              <Calendar className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Recorded</p>
                              <p className="text-[8pt] font-sm">
                                {formatDate(selectedResponse.recordedAt)}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ) : selectedQuestionId ? (
                    // Show the selected question with no response
                    <motion.div
                      key={selectedQuestionId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                      className="h-full"
                    >
                      <div className="h-full flex justify-center items-center">
                        <div className="relative p-8 rounded-2xl overflow-hidden border border-white/10 backdrop-blur-sm">
                          <motion.div
                            animate={{
                              rotate: [0, 360],
                              scale: [1, 1.1, 1],
                            }}
                            transition={{
                              duration: 15,
                              repeat: Infinity,
                              ease: 'linear',
                            }}
                            className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 blur-2xl"
                          />
                          <motion.div
                            animate={{
                              x: [-40, 40, -40],
                              y: [-20, 20, -20],
                            }}
                            transition={{
                              duration: 8,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                            className="absolute inset-0 bg-gradient-to-tr from-white/5 via-white/10 to-transparent blur-xl"
                          />

                          <div className="relative p-6">
                            <motion.div
                              initial={{ opacity: 0, scale: 0.95 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{
                                duration: 0.3,
                                ease: 'easeOut',
                              }}
                              className="flex justify-center mb-6 relative"
                            >
                              <motion.div
                                animate={{
                                  opacity: [0.4, 0.8, 0.4],
                                  scale: [0.95, 1.05, 0.95],
                                }}
                                transition={{
                                  duration: 4,
                                  repeat: Infinity,
                                  ease: 'easeInOut',
                                }}
                                className="absolute inset-0 bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-pink-500/30 rounded-full blur-xl"
                              />
                              <div className="relative">
                                <motion.div
                                  animate={{
                                    opacity: [0.5, 1, 0.5],
                                  }}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: 'easeInOut',
                                  }}
                                  className="absolute inset-0 bg-gradient-to-tr from-indigo-500 to-purple-500 rounded-full blur-md"
                                />
                                <Timer className="w-16 h-16 text-white relative z-10" />
                              </div>
                            </motion.div>

                            <motion.div
                              initial={{ opacity: 0, y: 5 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.1 }}
                              className="text-center relative z-10"
                            >
                              <h3 className="text-xl font-semibold text-white mb-3">
                                Awaiting Response
                              </h3>
                              <p className="text-white/60 max-w-md mx-auto leading-relaxed text-sm">
                                The candidate has not responded to this question yet. You'll be able
                                to view their video response once they submit it.
                              </p>
                              <div className="mt-4 text-white/40 text-xs">
                                Expected duration:{' '}
                                {
                                  cultureFitQuestions.find(q => q.id === selectedQuestionId)
                                    ?.duration
                                }{' '}
                                min
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ) : null}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>,
    document.body
  );
};
export default VideoResponseModal;
