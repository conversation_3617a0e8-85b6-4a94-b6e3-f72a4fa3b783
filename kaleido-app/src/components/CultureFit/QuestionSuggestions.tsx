import React, { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Award,
  Brain,
  Briefcase,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Clock,
  Coffee,
  Compass,
  Flame,
  Gem,
  Heart,
  Leaf,
  Lightbulb,
  MessageCircle,
  Palette,
  Puzzle,
  Smile,
  Sparkles,
  Star,
  Target,
  Users,
  Zap,
} from 'lucide-react';

// Define the list of quality cultural fit questions with associated icons
const CULTURAL_FIT_QUESTIONS = [
  {
    id: 1,
    question:
      'Describe a situation where you had to adapt to a significant change at work. How did you handle it?',
    icon: <Compass size={16} />,
  },
  {
    id: 2,
    question: 'What type of team environment brings out your best work?',
    icon: <Users size={16} />,
  },
  {
    id: 3,
    question: 'How do you handle constructive criticism?',
    icon: <MessageCircle size={16} />,
  },
  { id: 4, question: 'Describe your ideal company culture.', icon: <Heart size={16} /> },
  {
    id: 5,
    question: 'What motivates you to perform at your highest level?',
    icon: <Target size={16} />,
  },
  {
    id: 6,
    question: 'How do you prioritize tasks when facing multiple deadlines?',
    icon: <Clock size={16} />,
  },
  {
    id: 7,
    question: 'Tell us about a time you went above and beyond for a project or client.',
    icon: <Star size={16} />,
  },
  {
    id: 8,
    question: 'How do you approach learning new skills or technologies?',
    icon: <Brain size={16} />,
  },
  {
    id: 9,
    question: 'Describe a challenging workplace situation and how you resolved it.',
    icon: <Puzzle size={16} />,
  },
  {
    id: 10,
    question: 'What values are most important to you in a workplace?',
    icon: <Gem size={16} />,
  },
  {
    id: 11,
    question: 'How do you contribute to a positive team atmosphere?',
    icon: <Smile size={16} />,
  },
  { id: 12, question: 'Describe your approach to work-life balance.', icon: <Coffee size={16} /> },
  { id: 13, question: 'How do you handle high-pressure situations?', icon: <Flame size={16} /> },
  {
    id: 14,
    question: 'What leadership qualities do you value most?',
    icon: <Briefcase size={16} />,
  },
  {
    id: 15,
    question: 'How do you stay motivated during repetitive tasks?',
    icon: <Zap size={16} />,
  },
  {
    id: 16,
    question:
      'Describe a time when you had to collaborate with someone with a different working style.',
    icon: <Palette size={16} />,
  },
  {
    id: 17,
    question: 'What role do you typically take in a team setting?',
    icon: <Users size={16} />,
  },
  {
    id: 18,
    question: 'How do you approach continuous improvement in your work?',
    icon: <Sparkles size={16} />,
  },
  {
    id: 19,
    question: 'What does success look like to you in this role?',
    icon: <Award size={16} />,
  },
  {
    id: 20,
    question: 'How do you incorporate sustainability or social responsibility in your work?',
    icon: <Leaf size={16} />,
  },
];

interface QuestionSuggestionsProps {
  onSelectQuestion: (question: string) => void;
  size?: 'small' | 'medium' | 'large';
}

const QuestionSuggestions: React.FC<QuestionSuggestionsProps> = ({
  onSelectQuestion,
  size = 'small',
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isExpanded, setIsExpanded] = useState(true);
  const questionsPerPage = size === 'large' ? 6 : size === 'medium' ? 5 : 4;
  const totalPages = Math.ceil(CULTURAL_FIT_QUESTIONS.length / questionsPerPage);

  // Auto-rotate pages every 5 seconds when expanded
  useEffect(() => {
    if (!isExpanded) return;

    const interval = setInterval(() => {
      setCurrentPage(prev => (prev === totalPages ? 1 : prev + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, [totalPages, isExpanded]);

  // Get current page questions
  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = CULTURAL_FIT_QUESTIONS.slice(indexOfFirstQuestion, indexOfLastQuestion);

  // Handle page navigation
  const goToNextPage = () => {
    setCurrentPage(prev => (prev === totalPages ? 1 : prev + 1));
  };

  const goToPrevPage = () => {
    setCurrentPage(prev => (prev === 1 ? totalPages : prev - 1));
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const isLarge = size === 'large';
  const isMedium = size === 'medium';

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`overflow-hidden ${isLarge || isMedium ? 'mb-0' : 'mb-4'}`}
    >
      <motion.div
        className={`${isMedium ? 'p-0' : isLarge ? 'bg-transparent rounded-xl p-4 mb-2' : 'rounded-xl p-4 mb-2 bg-purple-950/5'}`}
      >
        {!isMedium && (
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Lightbulb
                size={isLarge ? 24 : 16}
                className={`mr-2 ${isLarge ? 'text-purple-400' : ''}`}
              />
              <h3 className={`font-medium ${isLarge ? 'text-xl text-white' : 'text-xs'}`}>
                {isLarge ? 'Choose Your Perfect Question' : 'Suggested Questions'}
              </h3>
            </div>

            <div className="flex items-center space-x-2">
              {isExpanded && (
                <>
                  <motion.button
                    onClick={goToPrevPage}
                    className={`p-2 rounded-full text-white ${isLarge ? 'bg-gray-700/50 hover:bg-gray-600/50' : ''}`}
                    style={!isLarge ? { backgroundColor: 'var(--button-secondary-bg)' } : {}}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronLeft size={isLarge ? 20 : 14} />
                  </motion.button>
                  <span
                    className={`${isLarge ? 'text-white text-lg' : 'text-xs'}`}
                    style={!isLarge ? { color: 'var(--foreground-color)' } : {}}
                  >
                    {currentPage} / {totalPages}
                  </span>
                  <motion.button
                    onClick={goToNextPage}
                    className={`p-2 rounded-full text-white ${isLarge ? 'bg-gray-700/50 hover:bg-gray-600/50' : ''}`}
                    style={!isLarge ? { backgroundColor: 'var(--button-secondary-bg)' } : {}}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronRight size={isLarge ? 20 : 14} />
                  </motion.button>
                </>
              )}
              {!isLarge && (
                <motion.button
                  onClick={toggleExpanded}
                  className="p-1 rounded-full text-white ml-1"
                  style={{ backgroundColor: 'var(--button-secondary-bg)' }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                </motion.button>
              )}
            </div>
          </div>
        )}

        {isMedium && (
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs text-gray-400">
              {currentPage} / {totalPages}
            </span>
            <div className="flex items-center space-x-1">
              <motion.button
                onClick={goToPrevPage}
                className="p-1 rounded bg-gray-700/50 hover:bg-gray-600/50 text-white"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeft size={14} />
              </motion.button>
              <motion.button
                onClick={goToNextPage}
                className="p-1 rounded bg-gray-700/50 hover:bg-gray-600/50 text-white"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronRight size={14} />
              </motion.button>
            </div>
          </div>
        )}

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              className={`${isMedium ? 'relative' : 'mt-3 relative'}`}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`${isMedium ? '' : isLarge ? 'grid gap-3 grid-cols-1 lg:grid-cols-2' : 'grid grid-cols-1 gap-2'}`}
                >
                  {currentQuestions.map((item, idx) => (
                    <React.Fragment key={item.id}>
                      <motion.button
                        onClick={() => onSelectQuestion(item.question)}
                        className={`flex items-start gap-3 transition-all duration-200 text-left group w-full ${
                          isLarge
                            ? 'p-6 bg-gradient-to-br from-gray-800/50 to-gray-700/30 border border-gray-600/30 hover:border-purple-500/50 hover:bg-gradient-to-br hover:from-purple-900/20 hover:to-pink-900/20 text-base rounded-lg'
                            : isMedium
                              ? 'py-4 px-4 text-sm hover:bg-white/5 rounded-lg'
                              : 'py-1 px-2 text-[10px] rounded'
                        }`}
                        whileHover={
                          isLarge
                            ? { scale: 1.02, y: -2 }
                            : isMedium
                              ? { x: 2 }
                              : { backgroundColor: 'var(--button-secondary-bg)' }
                        }
                        whileTap={{ scale: 0.98 }}
                      >
                        <div
                          className={`flex-shrink-0 ${isLarge ? 'mt-1 text-purple-400' : isMedium ? 'mt-0.5 text-purple-400/80 group-hover:text-purple-400' : 'mt-0.5 text-pink-400 group-hover:text-pink-400'}`}
                        >
                          {React.cloneElement(item.icon, {
                            size: isLarge ? 20 : isMedium ? 16 : 16,
                          })}
                        </div>
                        <span
                          className={`${isLarge ? 'leading-relaxed text-gray-200 group-hover:text-white' : isMedium ? 'leading-snug text-gray-200 group-hover:text-white' : 'leading-tight'}`}
                        >
                          {item.question}
                        </span>
                      </motion.button>
                      {isMedium && idx < currentQuestions.length - 1 && (
                        <div className="border-b border-white/10 -mx-6" />
                      )}
                    </React.Fragment>
                  ))}
                </motion.div>
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default QuestionSuggestions;
