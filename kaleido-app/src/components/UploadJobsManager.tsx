import { AnimatePresence, motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Eye,
  Loader2,
  RefreshCw,
  X,
  XCircle,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import { useJobsStore, useJobStore, useUploadJobsStore } from '@/stores/unifiedJobStore';
import { useRouter } from 'next/navigation';
import ReactDOM from 'react-dom';

// Define UploadJob type locally since we can't import it
interface UploadJob {
  jobId: string;
  status: 'queued' | 'active' | 'completed' | 'failed' | 'cancelled' | 'completed_with_errors';
  progress: number;
  totalFiles: number;
  processedFiles?: number;
  result?: any;
  title?: string;
  showNotification?: boolean;
  failedCount?: number;
}

interface UploadJobsManagerProps {
  onComplete?: (jobId: string) => void;
}

// The actual component content
const UploadJobsManagerContent: React.FC<UploadJobsManagerProps> = ({ onComplete }) => {
  const { jobs, activeJobs, removeJob } = useUploadJobsStore();
  const jobsStore = useJobsStore();
  const [isMinimized, setIsMinimized] = React.useState(false);

  const [expandedResponses, setExpandedResponses] = useState<Record<string, boolean>>({});
  const [completedJobStats, setCompletedJobStats] = React.useState<{
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    duplicateFiles: number;
    jobId: string;
    hasErrors: boolean;
    errorDetails: any[];
  } | null>(null);
  const router = useRouter();
  // Track which jobs have already been refreshed after completion
  const [refreshedJobIds, setRefreshedJobIds] = useState<Set<string>>(new Set());
  // Add state to track if the manager should be shown
  const [isVisible, setIsVisible] = useState(true);
  // Track completed jobs to keep them visible even after they're removed from activeJobs
  const [completedJobIds, setCompletedJobIds] = useState<Set<string>>(new Set());
  // Track if we've had any jobs at all to prevent closing
  const [hadJobs, setHadJobs] = useState(false);

  // Track all job IDs we want to display (active + completed)
  const displayJobIds = useRef<Set<string>>(new Set());

  // Initialize display job IDs from persisted jobs on mount
  useEffect(() => {
    // Check immediately if we have any existing jobs from persistent storage
    if (Object.keys(jobs).length > 0) {
      setHadJobs(true);

      // Add all current active jobs to our display set
      activeJobs.forEach(id => displayJobIds.current.add(id));

      // Check for any recently completed jobs to show
      Object.values(jobs).forEach(job => {
        if (['completed', 'completed_with_errors', 'failed', 'cancelled'].includes(job.status)) {
          // Check if this job was recently completed (within the last hour)
          const jobTime = new Date(job.createdAt).getTime();
          const now = Date.now();
          const oneHourMs = 60 * 60 * 1000;

          if (now - jobTime < oneHourMs) {
            displayJobIds.current.add(job.jobId);

            // Add to completed jobs set if not active
            if (!activeJobs.includes(job.jobId)) {
              setCompletedJobIds(prev => {
                const updated = new Set(prev);
                updated.add(job.jobId);
                return updated;
              });
            }
          }
        }
      });
    }
  }, []);

  // Update our display job IDs whenever active jobs or completed jobs change
  useEffect(() => {
    // Add current active jobs
    activeJobs.forEach(id => displayJobIds.current.add(id));
    // Add any completed jobs we're tracking
    completedJobIds.forEach(id => displayJobIds.current.add(id));

    // Check if we've ever had jobs
    if (activeJobs.length > 0 || completedJobIds.size > 0) {
      setHadJobs(true);
    }
  }, [activeJobs, completedJobIds]);

  // Get list of all jobs to display (active + completed)
  const allDisplayJobs = Object.values(jobs).filter(
    job => displayJobIds.current.has(job.jobId) || activeJobs.includes(job.jobId)
  );

  // Check if all jobs are complete
  const allJobsComplete =
    allDisplayJobs.length > 0 &&
    allDisplayJobs.every(job =>
      ['completed', 'completed_with_errors', 'failed', 'cancelled'].includes(job.status)
    );

  // Check if any jobs are in progress
  const anyJobsInProgress = allDisplayJobs.some(
    job => job.status === 'queued' || job.status === 'active'
  );

  // Track state changes of jobs to detect completion
  useEffect(() => {
    Object.values(jobs).forEach(job => {
      // If a job changed from active/queued to a terminal state, add to completed jobs
      if (
        ['completed', 'completed_with_errors', 'failed', 'cancelled'].includes(job.status) &&
        !completedJobIds.has(job.jobId)
      ) {
        setCompletedJobIds(prev => {
          const updated = new Set(prev);
          updated.add(job.jobId);
          return updated;
        });
      }
    });
  }, [jobs, completedJobIds]);

  const toggleResponseView = (jobId: string) => {
    setExpandedResponses(prev => ({
      ...prev,
      [jobId]: !prev[jobId],
    }));
  };

  const handleDismissJob = (jobId: string) => {
    // Remove job from our UI lists
    displayJobIds.current.delete(jobId);
    setCompletedJobIds(prev => {
      const updated = new Set(prev);
      updated.delete(jobId);
      return updated;
    });

    // Remove job from store
    removeJob(jobId);

    // Add to dismissed jobs list in localStorage to prevent reappearing on refresh
    try {
      const dismissedJobs = JSON.parse(localStorage.getItem('dismissedUploadJobs') || '[]');
      if (!dismissedJobs.includes(jobId)) {
        dismissedJobs.push(jobId);
        localStorage.setItem('dismissedUploadJobs', JSON.stringify(dismissedJobs));

        // Record dismissal timestamp
        const dismissedJobsData = JSON.parse(
          localStorage.getItem('dismissedUploadJobsData') || '{}'
        );
        dismissedJobsData[jobId] = Date.now();
        localStorage.setItem('dismissedUploadJobsData', JSON.stringify(dismissedJobsData));
      }
    } catch (e) {
      console.error('Error removing job from localStorage', e);
    }
  };

  // Add a handler to close the manager
  const handleCloseManager = () => {
    setIsVisible(false);
    // Add all current jobs to dismissed list
    clearAllCompletedJobs();
  };

  const handleViewResults = (jobId: string) => {
    // Mark the job as dismissed so it doesn't reappear
    try {
      const dismissedJobs = JSON.parse(localStorage.getItem('dismissedUploadJobs') || '[]');
      if (!dismissedJobs.includes(jobId)) {
        dismissedJobs.push(jobId);
        localStorage.setItem('dismissedUploadJobs', JSON.stringify(dismissedJobs));

        // Record dismissal timestamp
        const dismissedJobsData = JSON.parse(
          localStorage.getItem('dismissedUploadJobsData') || '{}'
        );
        dismissedJobsData[jobId] = Date.now();
        localStorage.setItem('dismissedUploadJobsData', JSON.stringify(dismissedJobsData));
      }
    } catch (error) {
      console.error('Error updating dismissed jobs list:', error);
    }

    // Use job.result.id if available, otherwise fallback to jobId
    const job: any = Object.values(jobs).find(j => j.jobId === jobId);
    const resultJobId = job?.data?.jobId || jobId;

    // Navigate to job descriptions with filter parameters using new URL format
    router.push(`/jobs/${resultJobId}/candidates`);
  };

  // Clear all completed jobs and update state
  const clearAllCompletedJobs = () => {
    try {
      const dismissedJobs = JSON.parse(localStorage.getItem('dismissedUploadJobs') || '[]');
      const dismissedJobsData = JSON.parse(localStorage.getItem('dismissedUploadJobsData') || '{}');

      // Add all current job IDs to dismissed list with timestamps
      allDisplayJobs.forEach(job => {
        if (!dismissedJobs.includes(job.jobId)) {
          dismissedJobs.push(job.jobId);
          dismissedJobsData[job.jobId] = Date.now();
        }
      });

      localStorage.setItem('dismissedUploadJobs', JSON.stringify(dismissedJobs));
      localStorage.setItem('dismissedUploadJobsData', JSON.stringify(dismissedJobsData));
    } catch (e) {
      console.error('Error updating dismissed jobs in localStorage', e);
    }

    // Clear all jobs from the store
    allDisplayJobs.forEach(job => {
      removeJob(job.jobId);
    });

    // Clear our tracking state
    displayJobIds.current.clear();
    setCompletedJobIds(new Set());
  };

  // Refresh job statuses
  useEffect(() => {
    // Only refresh job data when a job moves from active/queued to completed state
    const newlyCompletedJobs = allDisplayJobs.filter(
      job =>
        (job.status === 'completed' || job.status === 'completed_with_errors') &&
        // Only refresh for jobs we haven't refreshed yet
        !refreshedJobIds.has(job.jobId)
    );

    if (newlyCompletedJobs.length > 0) {
      // Update refreshed job IDs set
      const jobIdsToRefresh = newlyCompletedJobs.map(job => job.jobId);
      setRefreshedJobIds(prev => {
        const updated = new Set(prev);
        jobIdsToRefresh.forEach(id => updated.add(id));
        return updated;
      });

      // Show the latest completed job's stats
      const latestJob = newlyCompletedJobs[newlyCompletedJobs.length - 1];

      // Wait a moment before showing the completion information
      setTimeout(() => {
        // Provide completion information
        const errorDetails = latestJob.result?.errors || [];
        const duplicateInfo = latestJob.result?.data?.duplicateInfo || [];
        const hasErrors = errorDetails.length > 0;

        setCompletedJobStats({
          totalFiles: latestJob.totalFiles || 0,
          successfulFiles: (latestJob.totalFiles || 0) - errorDetails.length - duplicateInfo.length,
          failedFiles: errorDetails.length,
          duplicateFiles: duplicateInfo.length,
          jobId: latestJob.jobId,
          hasErrors,
          errorDetails,
        });

        // Get the actual job ID from the result
        const actualJobId = latestJob.result?.jobId || latestJob.data?.jobId || latestJob.jobId;

        // Call the onComplete callback if provided
        onComplete?.(actualJobId);

        // Always refresh job criteria when upload completes
        // This ensures recentCandidates and other data is updated
        const jobStore = useJobStore.getState();

        // Log completion without page refresh
        setTimeout(() => {
          if (hasErrors) {
          } else {
          }
        }, 2000); // Give user 2 seconds to see the completion modal
      }, 1000);
    }
  }, [allDisplayJobs, jobsStore, refreshedJobIds]);

  // Clean up old dismissed jobs
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Clean up old dismissed jobs (keep for 7 days maximum)
      try {
        const dismissedJobsData = JSON.parse(
          localStorage.getItem('dismissedUploadJobsData') || '{}'
        );
        const dismissedJobs = JSON.parse(localStorage.getItem('dismissedUploadJobs') || '[]');
        const now = Date.now();
        const oneWeekMs = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
        let hasChanges = false;

        // Filter out jobs older than 7 days
        const updatedJobs = dismissedJobs.filter(jobId => {
          const timestamp = dismissedJobsData[jobId];
          // Keep job if no timestamp or less than 7 days old
          return !timestamp || now - timestamp < oneWeekMs;
        });

        // Update the list if jobs were removed
        if (updatedJobs.length !== dismissedJobs.length) {
          localStorage.setItem('dismissedUploadJobs', JSON.stringify(updatedJobs));
          hasChanges = true;
        }

        // Update timestamps for existing jobs without timestamps
        dismissedJobs.forEach(jobId => {
          if (!dismissedJobsData[jobId]) {
            dismissedJobsData[jobId] = now;
            hasChanges = true;
          }
        });

        if (hasChanges) {
          localStorage.setItem('dismissedUploadJobsData', JSON.stringify(dismissedJobsData));
        }
      } catch (error) {
        console.error('Error cleaning up dismissed jobs:', error);
      }
    }
  }, []); // Run only once on mount

  // Don't show anything if there are no active or completed jobs or if manually closed
  if ((!hadJobs && allDisplayJobs.length === 0) || !isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 20, opacity: 0 }}
        className="fixed bottom-4 left-4 z-[9999]"
      >
        <div className="bg-white/10 backdrop-blur-xl rounded-lg shadow-lg w-[380px] border border-purple-500/20 shadow-purple-500/10">
          <div className="p-3 border-b border-purple-500/20 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {anyJobsInProgress ? (
                <RefreshCw className="h-4 w-4 text-purple-400 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4 text-purple-400" />
              )}
              <h3 className="text-lg font-semibold text-white">
                {anyJobsInProgress ? 'Upload Manager' : 'Upload Complete'}
              </h3>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white/70 hover:text-white transition-all p-1 rounded-full hover:bg-purple-500/10"
              >
                {isMinimized ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>
              {/* Close button - always visible */}
              <button
                onClick={handleCloseManager}
                className="text-white/70 hover:text-white transition-all p-1 rounded-full hover:bg-purple-500/10"
                title="Close manager"
                aria-label="Close upload manager"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          <AnimatePresence>
            {!isMinimized && (
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                className="overflow-hidden"
              >
                {/* Show different status messages based on job status */}
                {anyJobsInProgress ? (
                  /* Continue Working message during active uploads */
                  <div className="bg-purple-800/20 border-b border-purple-500/20 p-3 text-sm text-white">
                    <p className="flex items-center gap-1.5">
                      <CheckCircle className="h-4 w-4 text-purple-400" />
                      <span>
                        <strong>You can continue working</strong> - we'll handle the upload in the
                        background.
                      </span>
                    </p>
                    <p className="text-xs text-white/80 ml-6 mt-1">
                      We'll notify you when your files are processed. You can minimize this panel if
                      needed.
                    </p>
                  </div>
                ) : allJobsComplete ? (
                  /* Completion message when all uploads are done */
                  <div className="bg-purple-950/30 border-b border-purple-800/20 p-3 text-sm text-white">
                    <p className="flex items-center gap-1.5">
                      <CheckCircle className="h-4 w-4 text-purple-400" />
                      <span>
                        <strong>All uploads are complete!</strong> You can now rank your candidates.
                      </span>
                    </p>
                    <p className="text-xs text-white/80 ml-6 mt-1">
                      Your files have been processed successfully. You can close this panel or use
                      the View Results button.
                    </p>
                    {/* Add a view results button for the most recent job if any are completed */}
                    {/* {allDisplayJobs.some(job => job.status === 'completed') && (
                      <button
                        onClick={() => {
                          const completedJob = allDisplayJobs.find(
                            job => job.status === 'completed'
                          );
                          if (completedJob) handleViewResults(completedJob.jobId);
                        }}
                        className="ml-6 mt-2 px-3 py-1 bg-purple-950/60 hover:bg-purple-800 rounded-md text-xs flex items-center gap-1"
                      >
                        <Eye size={12} />
                        View Results
                      </button>
                    )} */}
                  </div>
                ) : null}

                <div className="p-4 space-y-4 max-h-[400px] overflow-y-auto">
                  <AnimatePresence>
                    {allDisplayJobs.map(job => (
                      <motion.div
                        key={job.jobId}
                        className="space-y-2 bg-black/10 p-3 rounded-md border border-purple-500/10 mb-4 last:mb-0"
                        initial={{ opacity: 1, height: 'auto', scale: 1 }}
                        exit={{ opacity: 0, height: 0, scale: 0.95, marginBottom: 0 }}
                        transition={{ duration: 0.3, ease: 'easeInOut' }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 ">
                            {job.status === 'completed' && !job.failedCount ? (
                              <>
                                <CheckCircle className="text-purple-500" size={18} />
                                <span className="text-sm font-medium text-white">
                                  Completed ({job.processedFiles || job.totalFiles}/{job.totalFiles}
                                  )
                                  {job.result?.data?.duplicateInfo?.length > 0 &&
                                    ` - ${job.result.data.duplicateInfo.length} duplicates`}
                                </span>
                              </>
                            ) : job.status === 'completed_with_errors' ||
                              (job.status === 'completed' &&
                                job.failedCount &&
                                job.failedCount > 0) ? (
                              <>
                                <AlertCircle className="text-orange-500" size={18} />
                                <span className="text-sm font-medium text-white">
                                  Completed with {job.failedCount} errors
                                  {job.result?.data?.duplicateInfo?.length > 0 &&
                                    `, ${job.result.data.duplicateInfo.length} duplicates`}
                                </span>
                              </>
                            ) : job.status === 'failed' ? (
                              <>
                                <XCircle className="text-red-500" size={18} />
                                <span className="text-sm font-medium text-white">
                                  Upload failed
                                </span>
                              </>
                            ) : job.status === 'cancelled' ? (
                              <>
                                <XCircle className="text-orange-500" size={18} />
                                <span className="text-sm font-medium text-white">
                                  Upload cancelled
                                </span>
                              </>
                            ) : (
                              <>
                                <Loader2 className="animate-spin text-purple-500" size={18} />
                                <span className="text-sm font-medium text-white">
                                  Processing {job.processedFiles || 0} of {job.totalFiles} files
                                </span>
                              </>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            {job.status === 'completed' && (
                              <button
                                onClick={() => handleViewResults(job.jobId)}
                                className="text-purple-400 hover:text-purple-300 transition-all p-1 rounded-full hover:bg-purple-500/10"
                                title="View results"
                                aria-label="View results"
                              >
                                <Eye size={16} />
                              </button>
                            )}
                            {job.status !== 'active' && job.status !== 'queued' && (
                              <button
                                onClick={() => toggleResponseView(job.jobId)}
                                className="text-white/70 hover:text-white transition-all p-1 rounded-full hover:bg-purple-500/10"
                                title={
                                  expandedResponses[job.jobId] ? 'Hide details' : 'Show details'
                                }
                                aria-label={
                                  expandedResponses[job.jobId] ? 'Hide details' : 'Show details'
                                }
                              >
                                {expandedResponses[job.jobId] ? (
                                  <ChevronUp size={16} />
                                ) : (
                                  <ChevronDown size={16} />
                                )}
                              </button>
                            )}
                            {(job.status === 'completed' ||
                              job.status === 'failed' ||
                              job.status === 'cancelled' ||
                              job.status === 'completed_with_errors') && (
                              <button
                                onClick={() => handleDismissJob(job.jobId)}
                                className="text-white/70 hover:text-white transition-all p-1 rounded-full hover:bg-purple-500/10"
                                title="Remove from list"
                                aria-label="Remove upload from list"
                              >
                                <X size={16} />
                              </button>
                            )}
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="overflow-hidden h-2 text-xs flex rounded-full bg-black/20">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${job.progress}%` }}
                              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                                job.status === 'failed'
                                  ? 'bg-red-500'
                                  : job.status === 'cancelled'
                                    ? 'bg-orange-500'
                                    : job.status === 'completed_with_errors'
                                      ? 'bg-yellow-500'
                                      : job.status === 'completed'
                                        ? 'bg-purple-500'
                                        : 'bg-purple-400'
                              }`}
                            />
                          </div>
                        </div>
                        {job.message && !expandedResponses[job.jobId] && (
                          <p className="text-xs text-white/80 mt-1">{job.message}</p>
                        )}

                        {/* API Response Section (expandable) */}
                        <AnimatePresence>
                          {expandedResponses[job.jobId] && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: 'auto', opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              className="overflow-hidden"
                            >
                              <div className="bg-black/20 p-3 rounded-md mt-2 border border-purple-500/10">
                                <h4 className="text-xs font-medium text-white/80 mb-2">Summary:</h4>

                                <div className="space-y-2">
                                  {/* Processed Files */}
                                  {job.result?.data?.files && job.result.data.files.length > 0 && (
                                    <div className="flex items-center gap-2 text-xs text-white/80 p-2 bg-black/10 rounded-md">
                                      <CheckCircle className="text-purple-500 h-4 w-4 flex-shrink-0" />
                                      <div className="flex flex-col">
                                        <span className="truncate">
                                          {job.result.data.files[0].originalname || 'File 1'}
                                        </span>
                                        {job.result.data.files.length > 1 && (
                                          <span className="text-xs text-white/60">
                                            +{job.result.data.files.length - 1} more files
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  {/* Failed Uploads */}
                                  {job.result?.data?.failedUploads &&
                                    job.result.data.failedUploads.length > 0 && (
                                      <div className="flex items-center gap-2 text-xs text-white/80 p-2 bg-black/10 rounded-md">
                                        <XCircle className="text-red-500 h-4 w-4 flex-shrink-0" />
                                        <div className="flex flex-col">
                                          <span className="truncate">
                                            {job.result.data.failedUploads[0].originalname ||
                                              'Failed file 1'}
                                          </span>
                                          {job.result.data.failedUploads.length > 1 && (
                                            <span className="text-xs text-white/60">
                                              +{job.result.data.failedUploads.length - 1} more
                                              failed files
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                  {/* Duplicates */}
                                  {job.result?.data?.duplicateInfo &&
                                    job.result.data.duplicateInfo.length > 0 && (
                                      <div className="flex items-center gap-2 text-xs text-white/80 p-2 bg-black/10 rounded-md">
                                        <AlertCircle className="text-blue-500 h-4 w-4 flex-shrink-0" />
                                        <div className="flex flex-col">
                                          <span className="truncate">
                                            {job.result.data.duplicateInfo[0].filename ||
                                              job.result.data.duplicateInfo[0].originalname ||
                                              'Duplicate file 1'}
                                          </span>
                                          <span className="text-xs text-blue-400">
                                            {job.result.data.duplicateInfo[0].message ||
                                              'Resume already exists for this client and job combination'}
                                          </span>
                                          {job.result.data.duplicateInfo.length > 1 && (
                                            <span className="text-xs text-white/60">
                                              +{job.result.data.duplicateInfo.length - 1} more
                                              duplicates
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                  {/* Empty state */}
                                  {(!job.result?.data?.files ||
                                    job.result.data.files.length === 0) &&
                                    (!job.result?.data?.failedUploads ||
                                      job.result.data.failedUploads.length === 0) &&
                                    (!job.result?.data?.duplicateInfo ||
                                      job.result.data.duplicateInfo.length === 0) && (
                                      <div className="text-xs text-white/80 p-2 text-center">
                                        <p>No detailed file information available</p>
                                      </div>
                                    )}
                                </div>

                                {/* Summary stats section */}
                                {job.result?.data && (
                                  <div className="mt-3 pt-3 border-t border-purple-500/10 grid grid-cols-3 gap-2">
                                    <div className="text-center">
                                      <div className="bg-purple-500/20 p-2 rounded-full inline-flex items-center justify-center mb-1">
                                        <CheckCircle className="text-purple-500 h-4 w-4" />
                                      </div>
                                      <p className="text-xs text-white/80">
                                        {job.result.data.files?.length || 0} Processed
                                      </p>
                                    </div>
                                    <div className="text-center">
                                      <div className="bg-red-500/20 p-2 rounded-full inline-flex items-center justify-center mb-1">
                                        <XCircle className="text-red-500 h-4 w-4" />
                                      </div>
                                      <p className="text-xs text-white/80">
                                        {job.result.data.failedUploads?.length || 0} Failed
                                      </p>
                                    </div>
                                    <div className="text-center">
                                      <div className="bg-blue-500/20 p-2 rounded-full inline-flex items-center justify-center mb-1">
                                        <AlertCircle className="text-blue-500 h-4 w-4" />
                                      </div>
                                      <p className="text-xs text-white/80">
                                        {job.result.data.duplicateInfo?.length || 0} Duplicates
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

// Portal wrapper component that renders the content in a portal
export const UploadJobsManager: React.FC<UploadJobsManagerProps> = props => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Only render in browser environment and after component has mounted
  if (typeof window === 'undefined' || !mounted) return null;

  // Get or create portal container
  let portalContainer = document.getElementById('upload-jobs-portal');
  if (!portalContainer) {
    portalContainer = document.createElement('div');
    portalContainer.id = 'upload-jobs-portal';
    document.body.appendChild(portalContainer);
  }

  // Render content in portal with high z-index
  return ReactDOM.createPortal(<UploadJobsManagerContent {...props} />, portalContainer);
};
