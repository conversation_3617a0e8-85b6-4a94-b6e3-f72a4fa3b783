import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

import { IJob } from '@/entities/interfaces';

interface CareerCardProps {
  career: IJob;
}

const CareerCard = ({ career }: CareerCardProps) => {
  return (
    <div className="bg-white rounded-lg p-6 relative flex flex-col h-full hover:bg-gray-50 transition-colors shadow-sm">
      {/* Company Info */}
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900 mb-1">{career.jobType}</h2>
        <p className="text-gray-700">{career.companyName}</p>
      </div>

      {/* Career Details */}
      <div className="space-y-4 flex-grow">
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">Department</h3>
          <p className="text-gray-600">{career.department}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">Location</h3>
          <p className="text-gray-600">{career.location.join(', ')}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">Experience Level</h3>
          <p className="text-gray-600">{career.experience}</p>
        </div>
      </div>

      {/* View Details Button */}
      <div className="mt-6 flex justify-end">
        <Link
          href={`?careerId=${career.id}`}
          className="inline-flex items-center gap-2 text-black hover:text-black-700 transition-colors"
        >
          <span>View Career Details</span>
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
};

export default CareerCard;
