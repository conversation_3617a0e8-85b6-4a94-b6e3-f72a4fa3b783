'use client';

import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import CareerCard from './CareerCard';
import CareerDetails from './CareerDetails';
import FloatingPagination from '../Layouts/FloatingPagination';
import { Skeleton } from '../Ranked/Skeleton';

import { useJobs } from '@/contexts/jobs/JobsContext';
import { jobDataArrayToIJobArray, jobDataToIJob } from '@/stores/unifiedJobStore';
import { IJob } from '@/entities/interfaces';

const CareersList = () => {
  const searchParams = useSearchParams();
  const careerId = searchParams?.get('careerId');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const { jobs: jobsData, isLoading, error } = useJobs();

  // Convert JobData to IJob
  const jobs = jobDataArrayToIJobArray(jobsData);

  const totalPages = Math.ceil(jobs.length / itemsPerPage);
  const paginatedCareers = jobs.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  if (isLoading) {
    return (
      <div className="w-full px-4 py-8">
        <div className="mt-10 mb-10">
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="space-y-6">
          <Skeleton />
          <Skeleton />
          <Skeleton />
        </div>
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  if (careerId) {
    const selectedCareer = jobs.find(c => c.id === careerId);
    if (!selectedCareer) return null;

    return (
      <>
        <div className="w-full px-4 py-8 flex">
          <div className="flex-grow">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedCareers.map(career => (
                <CareerCard key={career.id} career={career} />
              ))}
            </div>
            <FloatingPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
          <CareerDetails career={selectedCareer} />
        </div>
      </>
    );
  }

  return (
    <div className="w-full px-4 py-8 pb-24">
      <div className="mt-10 mb-10">
        <h1 className="text-3xl font-bold text-gray-900">Our Client Jobs</h1>
        <p className="text-gray-600 mt-2">Find your perfect job</p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
        {paginatedCareers.map(career => (
          <CareerCard key={career.id} career={career} />
        ))}
      </div>

      <FloatingPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default CareersList;
