import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SkillsSection from '../SkillsSection';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
  },
}));

describe('SkillsSection', () => {
  const mockSkills = [
    'JavaScript',
    'TypeScript',
    'React',
    'Node.js',
    'Python',
    'AWS',
    'Docker',
    'GraphQL',
  ];

  const mockTheme = {
    id: 'default',
    name: 'Default',
    primary: 'bg-purple-900',
    secondary: 'bg-gray-800',
    accent: 'text-purple-400',
    border: 'border-purple-200',
    icon: 'text-purple-600',
    pillBg: 'bg-purple-100',
    pillText: 'text-purple-700',
    isDark: false,
  };

  it('renders skills section with title', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} />);

    expect(screen.getByText('Skills')).toBeInTheDocument();
  });

  it('displays all provided skills', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} />);

    mockSkills.forEach(skill => {
      expect(screen.getByText(skill)).toBeInTheDocument();
    });
  });

  it('shows skills count indicator', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} />);

    expect(screen.getByText(`${mockSkills.length} skills`)).toBeInTheDocument();
  });

  it('applies correct theme colors for light theme', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} isDarkTheme={false} />);

    const skillElements = screen.getAllByText(/JavaScript|TypeScript|React/);
    expect(skillElements.length).toBeGreaterThan(0);

    // Check that theme classes are applied to the main container
    const mainContainer = screen.getByText('Skills').closest('.h-auto');
    expect(mainContainer).toHaveClass('shadow-lg');
  });

  it('applies correct theme colors for dark theme', () => {
    const darkTheme = { ...mockTheme, isDark: true };
    render(<SkillsSection skills={mockSkills} theme={darkTheme} isDarkTheme={true} />);

    const skillElements = screen.getAllByText(/JavaScript|TypeScript|React/);
    expect(skillElements.length).toBeGreaterThan(0);
  });

  it('handles empty skills array', () => {
    render(<SkillsSection skills={[]} theme={mockTheme} />);

    expect(screen.getByText('No skills available')).toBeInTheDocument();
  });

  it('handles skills as objects with skill property', () => {
    const objectSkills = [
      { skill: 'JavaScript' },
      { skill: 'TypeScript' },
      { skill: 'React' },
    ] as any;

    render(<SkillsSection skills={objectSkills} theme={mockTheme} />);

    expect(screen.getByText('JavaScript')).toBeInTheDocument();
    expect(screen.getByText('TypeScript')).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument();
  });

  it('applies scrollable container for many skills', () => {
    const manySkills = Array.from({ length: 20 }, (_, i) => `Skill ${i + 1}`);
    render(<SkillsSection skills={manySkills} theme={mockTheme} />);

    // Check that the scroll container has overflow properties
    const scrollContainer = document.querySelector('.overflow-y-auto');
    expect(scrollContainer).toBeInTheDocument();
    expect(scrollContainer).toHaveClass('overflow-y-auto');
    expect(scrollContainer).toHaveClass('max-h-[240px]');
  });

  it('shows Code icon instead of Star icon', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} />);

    // Check that the container has the skills title with Code icon
    const title = screen.getByText('Skills');
    expect(title).toBeInTheDocument();
  });

  it('applies glassmorphism effects to skill pills', () => {
    render(<SkillsSection skills={['JavaScript']} theme={mockTheme} />);

    const skillPill = screen.getByText('JavaScript').closest('div');
    expect(skillPill).toHaveClass('backdrop-blur-sm');
    expect(skillPill).toHaveClass('border');
    expect(skillPill).toHaveClass('border-white/20');
  });

  it('applies hover effects to skill pills', () => {
    render(<SkillsSection skills={['JavaScript']} theme={mockTheme} />);

    const skillPill = screen.getByText('JavaScript').closest('div');
    expect(skillPill).toHaveClass('hover:border-white/40');
    expect(skillPill).toHaveClass('hover:bg-white/20');
    expect(skillPill).toHaveClass('transition-all');
  });

  it('uses fallback styling when theme is not provided', () => {
    render(<SkillsSection skills={mockSkills} />);

    expect(screen.getByText('Skills')).toBeInTheDocument();
    mockSkills.forEach(skill => {
      expect(screen.getByText(skill)).toBeInTheDocument();
    });
  });

  it('applies correct padding and spacing', () => {
    render(<SkillsSection skills={mockSkills} theme={mockTheme} />);

    // Check that the skills container has proper gap
    const skillsContainer = document.querySelector('.flex.flex-wrap');
    expect(skillsContainer).toBeInTheDocument();
    expect(skillsContainer).toHaveClass('flex-wrap');
    expect(skillsContainer).toHaveClass('gap-2.5');
  });
});
