import React, { useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { Pause, Play } from 'lucide-react';
import Image from 'next/image';

import { ProfileImageCard } from '@/components/shared/ProfileImageCard';
import { StandardizedProfile } from '@/shared/types/profile.types';
import { UserRole } from '@/types/roles';

// Theme interface to match the structure in ResumeGenerator
interface ColorTheme {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  accent: string;
  border: string;
  icon: string;
  gradient?: string;
  text?: string;
  bg?: string;
  pillBg?: string;
  pillText?: string;
  isDark?: boolean;
}

interface ProfileImageSectionProps {
  profile: StandardizedProfile;
  theme?: ColorTheme;
  showProfileImage: boolean;
  onClose?: () => void;
  isProfileVerified: boolean;
  isIdentityVerified: boolean;
}

const ProfileImageSection: React.FC<ProfileImageSectionProps> = ({
  profile,
  theme,
  showProfileImage,
  onClose,
  isProfileVerified,
  isIdentityVerified,
}) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  return (
    <motion.div
      className={`h-[40%] ${
        theme?.secondary ? theme.secondary : 'bg-gradient-to-br from-gray-900 to-black'
      } flex items-center justify-center relative`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {isVideoPlaying && profile.videoIntroUrl ? (
        <div className="w-full h-full relative">
          <video
            ref={videoRef}
            src={profile.videoIntroUrl}
            className="w-full h-full object-cover"
            autoPlay
            controls={false}
            onEnded={() => setIsVideoPlaying(false)}
          />
          <button
            type="button"
            onClick={() => setIsVideoPlaying(false)}
            className="absolute bottom-4 right-4 bg-black/40 hover:bg-black/60 rounded-full p-3 backdrop-blur-sm transition-colors"
            aria-label="Pause video"
          >
            <Pause className="w-5 h-5 text-white" />
          </button>
        </div>
      ) : (
        <div className="w-full h-full relative">
          {showProfileImage && profile.myProfileImage ? (
            <>
              <Image
                src={profile.myProfileImage}
                alt={`${profile.firstName} ${profile.lastName}`}
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
              {/* Verification badge */}
              {(isProfileVerified || isIdentityVerified) && (
                <motion.div
                  className={`absolute top-4 left-4 ${
                    theme?.pillBg || 'bg-purple-600'
                  } rounded-full p-2 shadow-lg`}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.5, type: 'spring', stiffness: 200 }}
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </motion.div>
              )}
              {/* Video intro button */}
              {profile.videoIntroUrl && (
                <button
                  type="button"
                  onClick={() => {
                    setIsVideoPlaying(true);
                    if (videoRef.current) {
                      videoRef.current.currentTime = 0;
                      videoRef.current.play();
                    }
                  }}
                  className="absolute bottom-4 right-4 bg-black/40 hover:bg-black/60 rounded-full p-3 backdrop-blur-sm transition-colors"
                  aria-label="Play video introduction"
                >
                  <Play className="w-5 h-5 text-white" />
                </button>
              )}
            </>
          ) : !showProfileImage ? null : (
            <div className="w-full h-full">
              <ProfileImageCard
                userId={profile.userId || ''}
                userType={UserRole.JOB_SEEKER}
                currentImageUrl={profile.myProfileImage}
                userName={`${profile.firstName} ${profile.lastName}`}
                onUploadComplete={() => {}}
              />
              {profile.videoIntroUrl && (
                <button
                  type="button"
                  onClick={() => {
                    setIsVideoPlaying(true);
                    if (videoRef.current) {
                      videoRef.current.currentTime = 0;
                      videoRef.current.play();
                    }
                  }}
                  className="absolute bottom-4 right-4 bg-black/40 hover:bg-black/60 rounded-full p-3 backdrop-blur-sm transition-colors"
                  aria-label="Play video introduction"
                >
                  <Play className="w-5 h-5 text-white" />
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ProfileImageSection;
