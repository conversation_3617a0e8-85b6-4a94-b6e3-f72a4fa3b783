import React from 'react';

import { motion } from 'framer-motion';
import { Star, Code } from 'lucide-react';

// Theme interface to match the structure in ResumeGenerator
interface ColorTheme {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  accent: string;
  border: string;
  icon: string;
  gradient?: string;
  text?: string;
  bg?: string;
  pillBg?: string;
  pillText?: string;
  isDark?: boolean;
}

interface SkillsSectionProps {
  skills: string[];
  theme?: ColorTheme;
  isDarkTheme?: boolean;
}

const SkillsSection: React.FC<SkillsSectionProps> = ({ skills, theme, isDarkTheme = false }) => {
  // Smart theme detection
  const isThemeDark = isDarkTheme || theme?.isDark || false;
  const isLightBackground = theme?.id === 'clean' || theme?.id === 'high-contrast';

  // If no skills, show a message
  if (!skills || skills.length === 0) {
    return (
      <div
        className={`h-auto py-6 ${
          theme?.primary
            ? theme.primary
            : 'bg-gradient-to-br from-purple-900 via-purple-950 to-slate-900'
        } shadow-lg`}
      >
        <div className={`text-center ${isLightBackground ? 'text-gray-600' : 'text-white/70'}`}>
          No skills available
        </div>
      </div>
    );
  }

  // Smart text color based on background - always use white for skills section headers
  const textColor = isLightBackground ? 'text-gray-800' : 'text-white';
  const iconColor = theme?.icon || (isLightBackground ? 'text-gray-600' : 'text-blue-400');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const skillVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      className={`h-auto py-6 ${
        theme?.primary
          ? theme.primary
          : 'bg-gradient-to-br from-purple-900 via-purple-950 to-slate-900'
      } shadow-lg`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="grid grid-cols-8 h-full">
        <div className="col-span-1 flex items-center justify-center p-4 border-r border-white/10">
          <motion.h2
            className="text-lg font-bold flex items-center text-white"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Code className={`w-5 h-5 ${iconColor} mr-2`} />
            Skills
          </motion.h2>
        </div>

        <div className="col-span-7 relative">
          <div className="p-6 overflow-y-auto max-h-[240px] scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
            <div className="flex flex-wrap gap-2.5">
              {skills.map((skill, index) => {
                const skillText =
                  typeof skill === 'string' ? skill : (skill as { skill: string }).skill;

                return (
                  <motion.div
                    key={index}
                    className="group relative"
                    variants={skillVariants}
                    whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
                  >
                    <div
                      className={`
                      ${theme?.pillBg || 'bg-white/10'} 
                      ${theme?.pillText || 'text-white'} 
                      px-3 py-1.5 rounded-full text-sm font-medium
                      backdrop-blur-sm border border-white/20
                      hover:border-white/40 hover:bg-white/20
                      transition-all duration-300 ease-in-out
                      shadow-sm hover:shadow-md
                    `}
                    >
                      <span className="relative z-10">{skillText}</span>
                      {/* Subtle glow effect on hover */}
                      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400/20 to-blue-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Skills count indicator */}
          {skills.length > 0 && (
            <div className="absolute bottom-2 right-2 bg-black/20 backdrop-blur-sm rounded-full px-2 py-1 text-xs text-white/70">
              {skills.length} skills
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default SkillsSection;
