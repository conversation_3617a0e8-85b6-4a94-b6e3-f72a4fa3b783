import { motion } from 'framer-motion';
import React from 'react';

interface StepProgressProps {
  currentStep: number;
  totalSteps: number;
  overallCompletion?: number;
  steps?: string[];
}

export const StepProgress: React.FC<StepProgressProps> = ({
  currentStep,
  totalSteps,
  overallCompletion,
  steps,
}) => {
  // Calculate progress based on either onboardingProgress or step count
  const progressPercentage =
    overallCompletion !== undefined ? overallCompletion : (currentStep / totalSteps) * 100;

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
      className="relative flex flex-col items-center"
    >
      <div className="relative">
        <div className="relative w-16 h-16">
          {/* Background circle */}
          <svg className="absolute inset-0 w-full h-full -rotate-90">
            <circle
              cx="32"
              cy="32"
              r="28"
              stroke="rgba(255,255,255,0.2)"
              strokeWidth="3"
              fill="none"
            />
          </svg>

          {/* Progress circle */}
          <svg className="absolute inset-0 w-full h-full -rotate-90">
            <circle
              cx="32"
              cy="32"
              r="28"
              stroke="url(#progressGradient)"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 28}`}
              strokeDashoffset={`${2 * Math.PI * 28 * (1 - progressPercentage / 100)}`}
              className="transition-all duration-500 ease-out drop-shadow-lg"
            />
            <defs>
              <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#9333ea" />
                <stop offset="100%" stopColor="#ec4899" />
              </linearGradient>
            </defs>
          </svg>

          {/* Percentage text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-white font-bold text-sm drop-shadow-lg">
              {Math.round(progressPercentage)}%
            </span>
          </div>
        </div>

        {/* Step indicator below */}
        <div className="mt-3 text-center -ml-5">
          <p className="text-sm text-white font-semibold backdrop-blur-sm bg-black/25 rounded-full px-3 py-1 drop-shadow">
            Step {currentStep} of {totalSteps}
          </p>
        </div>
      </div>
    </motion.div>
  );
};
