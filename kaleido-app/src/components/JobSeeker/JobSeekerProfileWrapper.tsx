import { FileText, User, X } from 'lucide-react';
import React, { useState } from 'react';

import JobSeekerFullProfile from './JobSeekerFullProfile';
import JobSeekerProfileStandard from './JobSeekerProfileStandard';

import { JobSeekerProfile } from '@/types/jobSeeker';

interface JobSeekerProfileWrapperProps {
  profile: JobSeekerProfile;
  onClose?: () => void;
}

const JobSeekerProfileWrapper: React.FC<JobSeekerProfileWrapperProps> = ({ profile, onClose }) => {
  const [viewMode, setViewMode] = useState<'default' | 'standard'>('default');

  return (
    <div className="relative w-full h-full">
      {/* View selector - adjusted position to avoid conflict with download/share buttons */}
      <div className="absolute top-20 right-4 z-20 flex space-x-2">
        <button
          type="button"
          onClick={() => setViewMode('default')}
          className={`p-2 rounded-full ${
            viewMode === 'default'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          } transition-colors shadow-md`}
          aria-label="Default view"
          title="Interactive profile view"
        >
          <User size={20} />
        </button>
        <button
          type="button"
          onClick={() => setViewMode('standard')}
          className={`p-2 rounded-full ${
            viewMode === 'standard'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          } transition-colors shadow-md`}
          aria-label="Resume view"
          title="Standard resume view"
        >
          <FileText size={20} />
        </button>
      </div>

      {/* Render the appropriate profile component based on viewMode */}
      <div className="w-full h-full bg-transparent">
        {viewMode === 'default' ? (
          <JobSeekerFullProfile profile={profile} onClose={onClose} />
        ) : (
          <JobSeekerProfileStandard profile={profile} onClose={onClose} />
        )}
      </div>
    </div>
  );
};

export default JobSeekerProfileWrapper;
