import { CURRENCIES, INDUSTRIES, JO<PERSON>_TYPES, LOCATION_OPTIONS } from '@/constants/jobOptions';
import { Banknote, Briefcase, Clock, DollarSign, Globe, HomeIcon, MapPin } from 'lucide-react';
import React from 'react';

import { EditableSection } from '@/components/common/EditableSection';
import { FormField } from '@/components/common/FormField';
import StyledMultiSelect from '@/components/common/styledInputs/StyledMultiSelect';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import { BaseStepProps } from '../types';
import StyledInput from '@/components/common/styledInputs/StyledInput';
import { formatNumberWithK } from '@/utils/formatters';

const REMOTE_PREFERENCES = ['remote', 'hybrid', 'onsite', 'flexible'];
const SALARY_PERIODS = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'bi-weekly', label: 'Bi-Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
];

export const PreferencesStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  // Initialize preferences if needed
  React.useEffect(() => {
    // If desiredSalary is a string, parse it to initialize the desiredSalary
    if (
      formData.preferences?.desiredSalary &&
      typeof formData.preferences.desiredSalary === 'string' &&
      !formData.preferences.desiredSalary
    ) {
      try {
        // Safe to use split since we've confirmed it's a string
        const salaryString = formData.preferences.desiredSalary as string;
        const salaryParts = salaryString.split(' ');

        if (salaryParts.length >= 4) {
          const currency = salaryParts[0];
          const rangeParts = salaryParts[1].split('-');
          const min = parseInt(rangeParts[0]);
          const max = parseInt(rangeParts[1]);
          const periodStr = salaryParts[3]; // "yearly", "monthly", etc.

          // Validate that the period is one of the allowed values
          const period =
            periodStr === 'daily' ||
            periodStr === 'weekly' ||
            periodStr === 'bi-weekly' ||
            periodStr === 'monthly' ||
            periodStr === 'yearly'
              ? (periodStr as 'daily' | 'weekly' | 'bi-weekly' | 'monthly' | 'yearly')
              : 'yearly';

          if (!isNaN(min) && !isNaN(max)) {
            // Set both the object format for type safety and compatibility
            const desiredSalaryObject = {
              min,
              max,
              currency,
              period,
            };

            // Update the form with proper object format
            const updatedPreferences = {
              ...formData.preferences,
              desiredSalary: desiredSalaryObject,
            };

            const update = { preferences: updatedPreferences };
            onUpdate(update);
          }
        }
      } catch (error) {
        console.error('Error parsing salary string:', error);
      }
    }
  }, []);

  const handlePreferenceChange = (field: string, value: any) => {
    const updatedValue = value;

    // Special handling for desiredSalary
    if (field === 'desiredSalary') {
      // If min and max are provided, create proper object format
      if (value.min && value.max) {
        const currency = value.currency || 'USD';

        // Validate period is one of the allowed values
        const periodValue = value.period || 'yearly';
        const period =
          periodValue === 'daily' ||
          periodValue === 'weekly' ||
          periodValue === 'bi-weekly' ||
          periodValue === 'monthly' ||
          periodValue === 'yearly'
            ? (periodValue as 'daily' | 'weekly' | 'bi-weekly' | 'monthly' | 'yearly')
            : 'yearly';

        // Create proper object format for desiredSalary
        const salaryObject = {
          min: value.min,
          max: value.max,
          currency,
          period,
        };

        // Create a type-safe version of the value for desiredSalary
        // const typeSafeValue = {
        //   ...value,
        //   period,
        // };

        // Store both the proper object format and the UI display object
        const updatedPreferences = {
          ...formData.preferences,
          desiredSalary: salaryObject, // Keep the object for UI display with type-safe period
        };

        const update = { preferences: updatedPreferences };
        onUpdate(update);
        return;
      }
    }

    // Special handler for salary object fields - update both desiredSalary and desiredSalary
    if (field === 'desiredSalary') {
      // Validate period is one of the allowed values
      const periodValue = value.period || 'yearly';
      const period =
        periodValue === 'daily' ||
        periodValue === 'weekly' ||
        periodValue === 'bi-weekly' ||
        periodValue === 'monthly' ||
        periodValue === 'yearly'
          ? (periodValue as 'daily' | 'weekly' | 'bi-weekly' | 'monthly' | 'yearly')
          : 'yearly';

      // Create a type-safe version of the value for desiredSalary
      const typeSafeValue = {
        ...value,
        period,
      };

      const updatedPreferences = {
        ...formData.preferences,
        desiredSalary: typeSafeValue,
      };

      // Also update desiredSalary if we have enough data
      if (value.min && value.max) {
        const currency = value.currency || 'USD';

        updatedPreferences.desiredSalary = {
          min: value.min,
          max: value.max,
          currency,
          period,
        };
      }

      const update = { preferences: updatedPreferences };
      onUpdate(update);
      return;
    }

    const updatedPreferences = {
      ...formData.preferences,
      [field]: updatedValue,
    };

    const update = { preferences: updatedPreferences };
    onUpdate(update);
  };

  const ViewMode = (
    <div className="space-y-4">
      {/* Salary Range */}
      <div className="flex items-center gap-2">
        <Banknote className="w-4 h-4 text-gray-500" />
        <div>
          <span className="text-sm font-medium text-gray-700">Salary Range</span>
          <div className="text-sm text-gray-900">
            {formData.preferences?.desiredSalary ? (
              typeof formData.preferences.desiredSalary === 'string' ? (
                // Display the string format
                <span>{formData.preferences.desiredSalary}</span>
              ) : formData.preferences.desiredSalary?.min &&
                formData.preferences.desiredSalary?.max ? (
                // Display using the object format
                <div className="flex flex-col">
                  <div className="font-medium text-gray-900">
                    {formData.preferences.desiredSalary.currency || 'USD'}{' '}
                    {formatNumberWithK(Number(formData.preferences.desiredSalary.min))} -{' '}
                    {formatNumberWithK(Number(formData.preferences.desiredSalary.max))}
                  </div>
                  {formData.preferences.desiredSalary.period && (
                    <div className="text-sm text-gray-600 mt-1">
                      per{' '}
                      {(() => {
                        // Map period to grammatically correct singular, lowercase form
                        const periodMap: Record<string, string> = {
                          daily: 'day',
                          weekly: 'week',
                          'bi-weekly': '2 weeks',
                          monthly: 'month',
                          yearly: 'year',
                        };
                        return (
                          periodMap[formData.preferences?.desiredSalary?.period] ||
                          formData.preferences.desiredSalary.period
                        );
                      })()}
                    </div>
                  )}
                </div>
              ) : (
                <span className="text-gray-500">-</span>
              )
            ) : (
              <span className="text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>

      {/* Remote Preferences */}
      <div className="flex gap-2">
        <HomeIcon className="w-4 h-4 text-gray-500 mt-1 flex-shrink-0" />
        <div>
          <span className="text-sm font-medium text-gray-700">Remote Work</span>
          <div className="flex flex-wrap gap-2 mt-1">
            {formData.preferences?.remotePreference ? (
              <span className="px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs">
                {formData.preferences.remotePreference}
              </span>
            ) : (
              <span className="text-sm text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>

      {/* Job Types */}
      <div className="flex gap-2">
        <Briefcase className="w-4 h-4 text-gray-500 mt-1 flex-shrink-0" />
        <div>
          <span className="text-sm font-medium text-gray-700">Job Types</span>
          <div className="flex flex-wrap gap-2 mt-1">
            {formData.preferences?.jobTypes?.length > 0 ? (
              formData.preferences.jobTypes.map((type, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs"
                >
                  {type}
                </span>
              ))
            ) : (
              <span className="text-sm text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>

      {/* Locations */}
      <div className="flex gap-2">
        <MapPin className="w-4 h-4 text-gray-500 mt-1 flex-shrink-0" />
        <div>
          <span className="text-sm font-medium text-gray-700">Preferred Locations</span>
          <div className="flex flex-wrap gap-2 mt-1">
            {formData.preferences?.locations?.length > 0 ? (
              formData.preferences.locations.map((location, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-50 text-purple-700 rounded-full text-xs"
                >
                  {location}
                </span>
              ))
            ) : (
              <span className="text-sm text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>

      {/* Industries */}
      <div className="flex gap-2">
        <Globe className="w-4 h-4 text-gray-500 mt-1 flex-shrink-0" />
        <div>
          <span className="text-sm font-medium text-gray-700">Industries</span>
          <div className="flex flex-wrap gap-2 mt-1">
            {formData.preferences?.industries?.length > 0 ? (
              formData.preferences.industries.map((industry, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-orange-50 text-orange-700 rounded-full text-xs"
                >
                  {industry}
                </span>
              ))
            ) : (
              <span className="text-sm text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const EditMode = (
    <div className="space-y-6 text-gray-900">
      {/* Salary Range */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StyledInput
            label="Minimum Salary"
            value={formData.preferences?.desiredSalary?.min || ''}
            onChange={e => {
              const currentObj = formData.preferences?.desiredSalary || {};
              handlePreferenceChange('desiredSalary', {
                ...currentObj,
                min: e.target.value,
              });
            }}
            placeholder="e.g. 50000"
            icon={<Banknote className="w-4 h-4" />}
            required={true}
          />

          <StyledInput
            label="Maximum Salary"
            value={formData.preferences?.desiredSalary?.max || ''}
            onChange={e => {
              const currentObj = formData.preferences?.desiredSalary || {};
              handlePreferenceChange('desiredSalary', {
                ...currentObj,
                max: e.target.value,
              });
            }}
            placeholder="e.g. 80000"
            icon={<Banknote className="w-4 h-4" />}
            required={true}
          />
        </div>
      </div>

      {/* Currency and Period in their own row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StyledSelect
          label="Currency"
          options={CURRENCIES.map(curr => ({
            value: curr,
            label: curr,
          }))}
          value={formData.preferences?.desiredSalary?.currency}
          onChange={value => {
            const currentObj = formData.preferences?.desiredSalary || {};
            handlePreferenceChange('desiredSalary', {
              ...currentObj,
              currency: value,
            });
          }}
          icon={<DollarSign className="w-4 h-4" />}
          variant="light"
          className="text-sm"
          showListOnEmpty={true}
          required={true}
        />
        <StyledSelect
          label="Period"
          options={SALARY_PERIODS}
          value={formData.preferences?.desiredSalary?.period}
          onChange={value => {
            const currentObj = formData.preferences?.desiredSalary || {};
            handlePreferenceChange('desiredSalary', {
              ...currentObj,
              period: value,
            });
          }}
          icon={<Clock className="w-4 h-4" />}
          variant="light"
          className="text-sm"
          showListOnEmpty={true}
          required={true}
        />
      </div>

      {/* Remote Preference */}
      <StyledSelect
        label="Remote Preference"
        options={REMOTE_PREFERENCES.map(pref => ({
          value: pref,
          label: pref.charAt(0).toUpperCase() + pref.slice(1),
        }))}
        value={formData.preferences?.remotePreference || ''}
        onChange={value => handlePreferenceChange('remotePreference', value)}
        icon={<HomeIcon className="w-4 h-4" />}
        variant="light"
        className="text-sm"
        showListOnEmpty={true}
        required={true}
      />

      {/* Job Types */}
      <StyledMultiSelect
        label="Job Types"
        options={JOB_TYPES}
        values={formData.preferences?.jobTypes || []}
        onChange={values => handlePreferenceChange('jobTypes', values)}
        placeholder="Select job types"
        icon={<Briefcase className="w-4 h-4" />}
        variant="light"
        required={true}
      />

      {/* Preferred Locations */}
      <StyledMultiSelect
        label="Preferred Locations"
        options={LOCATION_OPTIONS}
        values={formData.preferences?.locations || []}
        onChange={values => handlePreferenceChange('locations', values)}
        placeholder="Select preferred locations"
        icon={<MapPin className="w-4 h-4" />}
        variant="light"
        required={true}
      />

      {/* Industries */}
      <StyledMultiSelect
        label="Industries"
        options={INDUSTRIES}
        values={formData.preferences?.industries || []}
        onChange={values => handlePreferenceChange('industries', values)}
        placeholder="Select industries"
        icon={<Globe className="w-4 h-4" />}
        variant="light"
        allowCustomValues={false}
        required={false}
      />
    </div>
  );

  const isEmpty =
    !formData.preferences ||
    !formData.preferences.remotePreference ||
    !formData.preferences.jobTypes?.length ||
    !formData.preferences.locations?.length ||
    !formData.preferences.desiredSalary?.currency ||
    !formData.preferences.desiredSalary?.period ||
    !formData.preferences.desiredSalary?.min ||
    !formData.preferences.desiredSalary?.max;

  return (
    <div className="flex flex-col w-full gap-6">
      <EditableSection
        viewMode={ViewMode}
        editMode={EditMode}
        className="md:w-full mx-auto"
        isEmpty={isEmpty}
      />
    </div>
  );
};
