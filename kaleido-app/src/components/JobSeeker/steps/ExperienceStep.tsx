import { Briefcase, Building, Calendar, ChevronDown, MapPin, Plus, X } from 'lucide-react';
import React, { useState } from 'react';

import { ArrayFieldPagination } from '@/components/common/ArrayFieldPagination';
import { FormField } from '@/components/common/FormField';
import { Button } from '@/components/ui/button';
import { CandidateExperience } from '@/shared/types';
import { BaseStepProps } from '../types';

const emptyExperience: CandidateExperience = {
  company: '',
  title: '',
  location: '',
  startDate: new Date(),
  endDate: null,
  current: false,
  description: '',
  skills: [],
  achievements: undefined,
};

export const ExperienceStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [currentExperience, setCurrentExperience] = useState<CandidateExperience>(emptyExperience);
  const [newSkill, setNewSkill] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<number>>(new Set());
  const itemsPerPage = 3;
  const MAX_DESCRIPTION_LENGTH = 150;

  const handleAddExperience = () => {
    const updatedExperience = [...(formData.experience || [])];
    if (editingIndex !== null) {
      updatedExperience[editingIndex] = currentExperience;
    } else {
      updatedExperience.push(currentExperience);
    }

    const update = { experience: updatedExperience };
    onUpdate(update);
    setEditingIndex(null);
    setCurrentExperience(emptyExperience);
  };

  const handleRemoveExperience = (index: number) => {
    const updatedExperience = (formData.experience || []).filter((_, i) => i !== index);
    const update = { experience: updatedExperience };
    onUpdate(update);
  };

  const handleEditExperience = (index: number) => {
    setEditingIndex(index);
    // Ensure location is set to a string value for Experience type
    const experience = formData.experience?.[index];
    if (experience) {
      const expToEdit = {
        ...experience,
        location: experience.location || '',
      };
      setCurrentExperience(expToEdit as CandidateExperience);
    }
  };

  const handleExperienceChange = (field: keyof CandidateExperience, value: any) => {
    setCurrentExperience(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddSkill = () => {
    if (!newSkill.trim()) return;

    setCurrentExperience(prev => ({
      ...prev,
      skills: [...(prev.skills || []), newSkill.trim()],
    }));
    setNewSkill('');
  };

  const handleRemoveSkill = (index: number) => {
    setCurrentExperience(prev => ({
      ...prev,
      skills: (prev.skills || []).filter((_, i) => i !== index),
    }));
  };

  const toggleDescription = (index: number) => {
    setExpandedDescriptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const totalPages = Math.ceil((formData.experience?.length || 0) / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const visibleExperience = formData.experience?.slice(startIndex, startIndex + itemsPerPage) || [];

  return (
    <div className="space-y-6 md:w-full mx-auto">
      {editingIndex !== null || !formData.experience || formData.experience.length === 0 ? (
        <div className="space-y-6 p-6 bg-white/50 rounded-xl border border-gray-200 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Company"
              value={currentExperience.company}
              onChange={e => handleExperienceChange('company', e.target.value)}
              placeholder="Enter company name"
              icon={<Building className="w-4 h-4 text-gray-500" />}
              containerClassName="w-full"
            />
            <FormField
              label="Job Title"
              value={currentExperience.title}
              onChange={e => handleExperienceChange('title', e.target.value)}
              placeholder="Enter your job title"
              icon={<Briefcase className="w-4 h-4 text-gray-500" />}
              containerClassName="w-full"
            />
          </div>

          <FormField
            label="Location"
            value={currentExperience.location}
            onChange={e => handleExperienceChange('location', e.target.value)}
            placeholder="City, Country"
            icon={<MapPin className="w-4 h-4 text-gray-500" />}
            containerClassName="w-full"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Start Date"
              type="date"
              value={
                currentExperience.startDate
                  ? new Date(currentExperience.startDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={e => handleExperienceChange('startDate', new Date(e.target.value))}
              icon={<Calendar className="w-4 h-4 text-gray-500" />}
              containerClassName="w-full"
            />
            <FormField
              label="End Date"
              type="date"
              value={
                currentExperience.endDate
                  ? new Date(currentExperience.endDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={e =>
                handleExperienceChange('endDate', e.target.value ? new Date(e.target.value) : null)
              }
              disabled={currentExperience.current}
              icon={<Calendar className="w-4 h-4 text-gray-500" />}
              containerClassName="w-full"
            />
          </div>

          <FormField
            label="Description"
            value={currentExperience.description}
            onChange={e => handleExperienceChange('description', e.target.value)}
            placeholder="Describe your responsibilities and achievements..."
            multiline
            rows={4}
            containerClassName="w-full"
            className="px-3 py-2"
          />

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FormField
                label="Skills Used"
                value={newSkill}
                onChange={e => setNewSkill(e.target.value)}
                placeholder="Add skills used in this role"
                onKeyDown={e => e.key === 'Enter' && handleAddSkill()}
                containerClassName="flex-1"
                className="px-3 py-2"
              />
              <Button
                onClick={handleAddSkill}
                disabled={!newSkill.trim()}
                className="h-12 flex items-center justify-center px-6 rounded-md mt-6"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Skill
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {currentExperience?.skills?.map((skill, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 rounded-full group"
                >
                  <span className="text-sm">{skill}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(index)}
                    className="p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-blue-100"
                    aria-label="Remove skill"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => {
                setEditingIndex(null);
                setCurrentExperience(emptyExperience);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAddExperience}>
              {editingIndex !== null ? 'Save Changes' : 'Add Experience'}
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {visibleExperience.map((exp, index) => (
            <div
              key={startIndex + index}
              className="p-6 rounded-lg bg-white/50 border border-gray-200 shadow-sm relative group hover:shadow-md transition-shadow duration-200"
            >
              <div className="absolute right-3 top-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  type="button"
                  onClick={() => handleEditExperience(startIndex + index)}
                  className="p-1.5 rounded-full bg-blue-50 text-blue-500 hover:bg-blue-100"
                  aria-label="Edit experience"
                >
                  <Briefcase className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => handleRemoveExperience(startIndex + index)}
                  className="p-1.5 rounded-full bg-red-50 text-red-500 hover:bg-red-100"
                  aria-label="Remove experience"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{exp.title}</h4>
                  <p className="text-base font-medium text-purple-600">{exp.company}</p>
                </div>

                <div className="flex items-center gap-3 text-sm text-purple-800/60">
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4" />
                    <span>{exp.location}</span>
                  </div>
                  <span className="text-gray-300">•</span>
                  <div className="flex items-center gap-1.5">
                    <Calendar className="w-4 h-4 text-primary-400" />
                    <span>
                      {exp.startDate
                        ? new Date(exp.startDate).toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric',
                          })
                        : 'N/A'}{' '}
                      -{' '}
                      {exp.endDate
                        ? new Date(exp.endDate).toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric',
                          })
                        : 'Present'}
                    </span>
                  </div>
                </div>

                {exp.description && (
                  <div className="relative">
                    <p
                      className="text-sm text-gray-600 whitespace-pre-wrap cursor-pointer hover:text-gray-800 transition-colors duration-200"
                      onClick={() => toggleDescription(startIndex + index)}
                    >
                      {expandedDescriptions.has(startIndex + index)
                        ? exp.description
                        : exp.description.length > MAX_DESCRIPTION_LENGTH
                          ? `${exp.description.slice(0, MAX_DESCRIPTION_LENGTH)}...`
                          : exp.description}
                    </p>
                    {exp.description.length > MAX_DESCRIPTION_LENGTH && (
                      <button
                        type="button"
                        onClick={() => toggleDescription(startIndex + index)}
                        className="flex items-center gap-1 mt-1 text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors duration-200"
                      >
                        {expandedDescriptions.has(startIndex + index) ? 'Show less' : 'Show more'}
                        <ChevronDown
                          className={`w-4 h-4 transition-transform duration-200 ${
                            expandedDescriptions.has(startIndex + index)
                              ? 'transform rotate-180'
                              : ''
                          }`}
                        />
                      </button>
                    )}
                  </div>
                )}

                {exp.skills?.length > 0 && (
                  <div className="flex flex-wrap gap-2 pt-2">
                    {exp.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          <ArrayFieldPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />

          <Button
            onClick={() => setEditingIndex(null)}
            variant="outline"
            className="w-full flex items-center justify-center gap-2 py-6 border-dashed"
          >
            <Plus className="w-4 h-4" />
            Add Experience
          </Button>
        </div>
      )}
    </div>
  );
};
