import { Calendar, Clock, Plane } from 'lucide-react';
import React from 'react';

import { BaseStepProps } from '../types';

import { EditableSection } from '@/components/common/EditableSection';
import { FormField } from '@/components/common/FormField';
import { ExtendedWorkAvailability } from '@/components/JobSeeker/types';

interface Option {
  value: string;
  label: string;
}

const availabilityOptions: Option[] = [
  { value: 'IMMEDIATE', label: 'Immediately Available' },
  { value: 'TWO_WEEKS', label: '2 Weeks Notice' },
  { value: 'ONE_MONTH', label: '1 Month Notice' },
  { value: 'CUSTOM', label: 'Custom Notice Period' },
];

const tooltips = {
  availability: `When can you start working with a new employer?`,
  notice: `How much notice do you need to give your current employer?`,
  relocation: `Are you willing to relocate for the right opportunity?`,
};

const getAvailabilityLabel = (immediatelyAvailable: boolean, noticePeriod?: number) => {
  if (immediatelyAvailable) return 'Immediately Available';
  if (noticePeriod && noticePeriod > 0) return `${noticePeriod} weeks notice`;
  return 'Not specified';
};

export const WorkAvailabilityStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  const handleChange = (field: keyof ExtendedWorkAvailability, value: any) => {
    const data = {
      workAvailability: {
        ...formData.workAvailability,
        [field]: value,
      },
    };
    onUpdate(data);
  };

  const handleNoticePeriodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow empty string or valid numbers
    if (value === '') {
      handleChange('noticePeriod', null);
    } else {
      const numValue = parseInt(value, 10);
      if (!isNaN(numValue) && numValue >= 0) {
        handleChange('noticePeriod', numValue);
      }
    }
  };

  const ViewMode = (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4 text-gray-500" />
        <div>
          <span className="text-sm font-medium text-gray-700">Availability</span>
          <div className="text-sm text-gray-900">
            {getAvailabilityLabel(
              formData.workAvailability?.immediatelyAvailable || false,
              formData.workAvailability?.noticePeriod
            )}
          </div>
        </div>
      </div>

      {!formData.workAvailability?.immediatelyAvailable &&
        formData.workAvailability?.noticePeriod && (
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <div>
              <span className="text-sm font-medium text-gray-700">Notice Period</span>
              <div className="text-sm text-gray-600">
                {formData.workAvailability.noticePeriod} weeks
              </div>
            </div>
          </div>
        )}

      <div className="flex items-center gap-2">
        <Plane className="w-4 h-4 text-gray-500" />
        <div>
          <span className="text-sm font-medium text-gray-700">Relocation</span>
          <div className="text-sm text-gray-600">
            {formData.workAvailability?.willingToRelocate !== undefined ? (
              formData.workAvailability.willingToRelocate ? (
                'Willing to relocate'
              ) : (
                'Not willing to relocate'
              )
            ) : (
              <span className="text-gray-500">-</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const EditMode = (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <label htmlFor="immediatelyAvailable" className="text-sm font-medium text-gray-900">
            Immediately Available
          </label>
          <p className="text-sm text-gray-500">Are you available to start work immediately?</p>
        </div>
        <input
          type="checkbox"
          id="immediatelyAvailable"
          name="immediatelyAvailable"
          aria-label="Immediately Available"
          checked={formData.workAvailability?.immediatelyAvailable || false}
          onChange={e => handleChange('immediatelyAvailable', e.target.checked)}
          className="w-5 h-5 rounded border-2 border-gray-300 focus:ring-pink-500 focus:border-pink-500 bg-transparent appearance-none checked:bg-pink-600 checked:border-pink-600"
        />
      </div>

      {!formData.workAvailability?.immediatelyAvailable && (
        <FormField
          label="Notice Period (in weeks)"
          tooltip={tooltips.notice}
          type="number"
          value={formData.workAvailability?.noticePeriod ?? ''}
          onChange={handleNoticePeriodChange}
          placeholder="e.g. 2"
          icon={<Calendar className="w-4 h-4" />}
        />
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="text-gray-500">
            <Plane className="w-4 h-4" />
          </div>
          <label htmlFor="willingToRelocate" className="text-sm text-gray-700">
            I am willing to relocate for the right opportunity
          </label>
        </div>
        <input
          type="checkbox"
          id="willingToRelocate"
          checked={formData.workAvailability?.willingToRelocate || false}
          onChange={e => handleChange('willingToRelocate', e.target.checked)}
          className="w-5 h-5 rounded border-2 border-gray-300 focus:ring-pink-500 focus:border-pink-500 bg-transparent appearance-none checked:bg-pink-600 checked:border-pink-600"
        />
      </div>
    </div>
  );

  const isEmpty =
    !formData.workAvailability ||
    (!formData.workAvailability.immediatelyAvailable &&
      !formData.workAvailability.noticePeriod &&
      formData.workAvailability.willingToRelocate === undefined);

  return (
    <EditableSection
      viewMode={ViewMode}
      editMode={EditMode}
      className="md:w-full mx-auto"
      isEmpty={isEmpty}
    />
  );
};
