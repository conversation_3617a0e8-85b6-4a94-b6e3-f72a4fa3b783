import React, { useRef, useState } from 'react';

import { motion } from 'framer-motion';
import {
  BadgeCheck,
  Briefcase,
  Building,
  Building2,
  Calendar,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Clock,
  Clock4,
  Github,
  Globe,
  GraduationCap,
  Heart,
  Languages,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Play,
  Square,
  Star,
  User,
  X,
  Code,
  Palette,
  Megaphone,
  Target,
  Users,
  Lightbulb,
  Settings,
  Database,
  Layout,
  PenTool,
  Smartphone,
  Monitor,
  Camera,
  Headphones,
  BookOpen,
  TrendingUp,
  BarChart3,
  MessageSquare,
  Zap,
  Shield,
  Award,
  Timer,
} from 'lucide-react';
import { FaCertificate } from 'react-icons/fa';

import { StandardizedProfile } from '@/shared/types/profile.types';
import { ColorTheme, defaultTheme } from '@/types/theme.types';
import { safeFormatDate } from '@/utils/dateUtils';

import {
  NameSection,
  ProfileImageSection,
  SkillsSection,
  SummarySection,
} from './components/ProfileStandard';

type Experience = StandardizedProfile['experience'][number];
type Education = StandardizedProfile['education'][number];

// Extended work availability type to match UI requirements
interface ExtendedWorkAvailability {
  noticePeriod?: string;
  willingToRelocate?: boolean;
  immediatelyAvailable?: boolean;
  immediateStart?: boolean;
  preferredWorkType?: string;
}

interface JobSeekerProfileStandardProps {
  profile: Omit<StandardizedProfile, 'workAvailability'> & {
    workAvailability?: ExtendedWorkAvailability;
  };
  onClose?: () => void;
  theme?: ColorTheme;
  showProfileImage?: boolean;
}

const JobSeekerProfileStandard: React.FC<JobSeekerProfileStandardProps> = ({
  profile,
  onClose,
  theme = defaultTheme,
  showProfileImage = true,
}) => {
  const [showFullSummary, setShowFullSummary] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [currentSkillsPage, setCurrentSkillsPage] = React.useState(1);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const itemsPerPage = 3;
  const skillsPerPage = 12;

  // Check if profile is verified (has completed onboarding)
  const isProfileVerified =
    profile.hasCompletedOnboarding || profile.onboardingProgress?.overall?.completed === true;

  // Theme-aware text colors
  const isThemeDark = theme?.isDark || false;
  const mainTextColor = 'text-white';
  const secondaryTextColor = isThemeDark ? 'text-gray-200' : 'text-gray-600';

  // Helper function to get proper theme colors
  const getThemeAccentColor = (asBackground = false) => {
    if (asBackground) {
      return theme.accent.replace('text-', 'bg-');
    }
    return theme.accent;
  };

  const getThemeIconColor = () => {
    return theme.icon;
  };

  // Check if identity is verified
  const isIdentityVerified =
    profile.verifications?.identity?.isVerified || profile.verifications?.video?.isVerified;

  const paginatedExperience = (profile.experience || []).slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const paginatedSkills = (profile.skills || []).slice(
    (currentSkillsPage - 1) * skillsPerPage,
    currentSkillsPage * skillsPerPage
  );

  const totalPages = Math.ceil((profile.experience?.length || 0) / itemsPerPage);
  const totalSkillPages = Math.ceil((profile.skills?.length || 0) / skillsPerPage);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 },
    },
  };

  const skillsContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.2,
      },
    },
    exit: { opacity: 0 },
  };

  const skillItemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 },
    },
    exit: {
      y: -20,
      opacity: 0,
      transition: { duration: 0.2 },
    },
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.4,
  };

  const formatSocialUrl = (url?: string) => {
    if (!url) return '';
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return `https://${url}`;
  };

  const formatDateShort = (dateStr: string) => {
    const date = new Date(dateStr);
    const month = date.toLocaleString('default', { month: 'short' });
    const year = date.getFullYear();
    return { month, year };
  };

  const formatDateRange = (startDate: string, endDate: string | null) => {
    const start = formatDateShort(startDate);
    if (!endDate) return `${start.month} ${start.year} - Present`;

    const end = formatDateShort(endDate);
    if (start.year === end.year) {
      return `${start.month} - ${end.month} ${end.year}`;
    }
    return `${start.month} ${start.year} - ${end.month} ${end.year}`;
  };

  const formatDuration = (duration: string | number): string => {
    // If duration is a number, assume it's in months
    if (typeof duration === 'number') {
      const years = Math.floor(duration / 12);
      const months = duration % 12;

      if (years === 0) {
        return `${months} ${months === 1 ? 'month' : 'months'}`;
      } else if (months === 0) {
        return `${years} ${years === 1 ? 'year' : 'years'}`;
      } else {
        return `${years} ${years === 1 ? 'year' : 'years'}, ${months} ${months === 1 ? 'month' : 'months'}`;
      }
    }

    // If duration is a string, check if it's in months format
    const monthsMatch = duration?.match(/^(\d+)\s*months?$/i);
    if (monthsMatch) {
      const totalMonths = parseInt(monthsMatch[1]);
      const years = Math.floor(totalMonths / 12);
      const months = totalMonths % 12;

      if (years === 0) {
        return `${months} ${months === 1 ? 'month' : 'months'}`;
      } else if (months === 0) {
        return `${years} ${years === 1 ? 'year' : 'years'}`;
      } else {
        return `${years} ${years === 1 ? 'year' : 'years'}, ${months} ${months === 1 ? 'month' : 'months'}`;
      }
    }

    return duration;
  };

  // Smart skill icon mapping function
  const getSkillIcon = (skill: string) => {
    const skillLower = skill.toLowerCase();

    // Programming & Development
    if (
      skillLower.includes('javascript') ||
      skillLower.includes('js') ||
      skillLower.includes('typescript') ||
      skillLower.includes('react') ||
      skillLower.includes('vue') ||
      skillLower.includes('angular') ||
      skillLower.includes('node') ||
      skillLower.includes('python') ||
      skillLower.includes('java') ||
      skillLower.includes('c++') ||
      skillLower.includes('programming') ||
      skillLower.includes('development') ||
      skillLower.includes('coding') ||
      skillLower.includes('software')
    ) {
      return Code;
    }

    // Design & Creative
    if (
      skillLower.includes('design') ||
      skillLower.includes('photoshop') ||
      skillLower.includes('illustrator') ||
      skillLower.includes('figma') ||
      skillLower.includes('sketch') ||
      skillLower.includes('ui') ||
      skillLower.includes('ux') ||
      skillLower.includes('graphic') ||
      skillLower.includes('creative') ||
      skillLower.includes('visual')
    ) {
      return Palette;
    }

    // Marketing & Communication
    if (
      skillLower.includes('marketing') ||
      skillLower.includes('advertising') ||
      skillLower.includes('social media') ||
      skillLower.includes('content') ||
      skillLower.includes('copywriting') ||
      skillLower.includes('email') ||
      skillLower.includes('campaign') ||
      skillLower.includes('promotion')
    ) {
      return Megaphone;
    }

    // Strategy & Business
    if (
      skillLower.includes('strategy') ||
      skillLower.includes('planning') ||
      skillLower.includes('business') ||
      skillLower.includes('consulting') ||
      skillLower.includes('analysis') ||
      skillLower.includes('management') ||
      skillLower.includes('leadership') ||
      skillLower.includes('operations')
    ) {
      return Target;
    }

    // Team & People
    if (
      skillLower.includes('team') ||
      skillLower.includes('collaboration') ||
      skillLower.includes('communication') ||
      skillLower.includes('interpersonal') ||
      skillLower.includes('leadership') ||
      skillLower.includes('mentoring') ||
      skillLower.includes('coaching') ||
      skillLower.includes('people')
    ) {
      return Users;
    }

    // Innovation & Ideas
    if (
      skillLower.includes('innovation') ||
      skillLower.includes('creative') ||
      skillLower.includes('brainstorming') ||
      skillLower.includes('ideation') ||
      skillLower.includes('problem solving') ||
      skillLower.includes('concept') ||
      skillLower.includes('invention') ||
      skillLower.includes('thinking')
    ) {
      return Lightbulb;
    }

    // Technical & Tools
    if (
      skillLower.includes('excel') ||
      skillLower.includes('office') ||
      skillLower.includes('tools') ||
      skillLower.includes('software') ||
      skillLower.includes('technical') ||
      skillLower.includes('system') ||
      skillLower.includes('configuration') ||
      skillLower.includes('setup')
    ) {
      return Settings;
    }

    // Data & Analytics
    if (
      skillLower.includes('data') ||
      skillLower.includes('analytics') ||
      skillLower.includes('statistics') ||
      skillLower.includes('reporting') ||
      skillLower.includes('database') ||
      skillLower.includes('sql') ||
      skillLower.includes('analysis') ||
      skillLower.includes('metrics')
    ) {
      return Database;
    }

    // Sales & Growth
    if (
      skillLower.includes('sales') ||
      skillLower.includes('revenue') ||
      skillLower.includes('growth') ||
      skillLower.includes('acquisition') ||
      skillLower.includes('conversion') ||
      skillLower.includes('performance') ||
      skillLower.includes('optimization') ||
      skillLower.includes('roi')
    ) {
      return TrendingUp;
    }

    // Presentation & Speaking
    if (
      skillLower.includes('presentation') ||
      skillLower.includes('public speaking') ||
      skillLower.includes('keynote') ||
      skillLower.includes('speaking') ||
      skillLower.includes('training') ||
      skillLower.includes('workshop') ||
      skillLower.includes('teaching') ||
      skillLower.includes('demonstration')
    ) {
      return Monitor;
    }

    // Writing & Content
    if (
      skillLower.includes('writing') ||
      skillLower.includes('content') ||
      skillLower.includes('blogging') ||
      skillLower.includes('documentation') ||
      skillLower.includes('copywriting') ||
      skillLower.includes('editorial') ||
      skillLower.includes('journalism') ||
      skillLower.includes('publishing')
    ) {
      return PenTool;
    }

    // Mobile & Digital
    if (
      skillLower.includes('mobile') ||
      skillLower.includes('app') ||
      skillLower.includes('ios') ||
      skillLower.includes('android') ||
      skillLower.includes('responsive') ||
      skillLower.includes('digital')
    ) {
      return Smartphone;
    }

    // Photography & Visual
    if (
      skillLower.includes('photography') ||
      skillLower.includes('photo') ||
      skillLower.includes('camera') ||
      skillLower.includes('visual') ||
      skillLower.includes('video') ||
      skillLower.includes('filming') ||
      skillLower.includes('editing') ||
      skillLower.includes('production')
    ) {
      return Camera;
    }

    // Audio & Media
    if (
      skillLower.includes('audio') ||
      skillLower.includes('sound') ||
      skillLower.includes('music') ||
      skillLower.includes('podcast') ||
      skillLower.includes('recording') ||
      skillLower.includes('mixing') ||
      skillLower.includes('media') ||
      skillLower.includes('broadcasting')
    ) {
      return Headphones;
    }

    // Learning & Education
    if (
      skillLower.includes('learning') ||
      skillLower.includes('education') ||
      skillLower.includes('training') ||
      skillLower.includes('development') ||
      skillLower.includes('curriculum') ||
      skillLower.includes('instruction') ||
      skillLower.includes('knowledge') ||
      skillLower.includes('study')
    ) {
      return BookOpen;
    }

    // Quality & Security
    if (
      skillLower.includes('quality') ||
      skillLower.includes('security') ||
      skillLower.includes('testing') ||
      skillLower.includes('assurance') ||
      skillLower.includes('compliance') ||
      skillLower.includes('audit') ||
      skillLower.includes('risk') ||
      skillLower.includes('safety')
    ) {
      return Shield;
    }

    // Achievement & Awards
    if (
      skillLower.includes('achievement') ||
      skillLower.includes('award') ||
      skillLower.includes('recognition') ||
      skillLower.includes('excellence') ||
      skillLower.includes('certification') ||
      skillLower.includes('qualification') ||
      skillLower.includes('expertise') ||
      skillLower.includes('mastery')
    ) {
      return Award;
    }

    // Agile & Methodologies
    if (
      skillLower.includes('agile') ||
      skillLower.includes('scrum') ||
      skillLower.includes('kanban') ||
      skillLower.includes('methodology') ||
      skillLower.includes('framework') ||
      skillLower.includes('process') ||
      skillLower.includes('workflow') ||
      skillLower.includes('iteration')
    ) {
      return Zap;
    }

    // Default icon for unmatched skills
    return Star;
  };

  const experienceContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
    exit: { opacity: 0 },
  };

  const experienceItemVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.4 },
    },
    exit: {
      x: 20,
      opacity: 0,
      transition: { duration: 0.2 },
    },
  };

  const truncateText = (text: string): { truncated: string; shouldTruncate: boolean } => {
    if (!text) return { truncated: '', shouldTruncate: false };

    const MIN_LENGTH_FOR_TRUNCATION = 150;

    // If text is shorter than minimum length, don't truncate
    if (text.length <= MIN_LENGTH_FOR_TRUNCATION) {
      return {
        truncated: text,
        shouldTruncate: false,
      };
    }

    // First try to split by paragraphs
    const paragraphs = text.split(/\n\n|\r\n\r\n/);
    if (paragraphs.length > 1 && text.length > MIN_LENGTH_FOR_TRUNCATION) {
      const firstParagraph = paragraphs[0];
      // If first paragraph is too short and we have more paragraphs,
      // include the second paragraph if it won't make it too long
      if (firstParagraph.length < MIN_LENGTH_FOR_TRUNCATION && paragraphs.length > 1) {
        const combinedParagraphs = paragraphs.slice(0, 2).join('\n\n');
        if (combinedParagraphs.length <= MIN_LENGTH_FOR_TRUNCATION * 1.5) {
          return {
            truncated: combinedParagraphs,
            shouldTruncate: true,
          };
        }
      }
      return {
        truncated: firstParagraph,
        shouldTruncate: true,
      };
    }

    // If no paragraphs, try to split by sentence
    const sentences = text.split(/(?<=[.!?])\s+/);
    if (sentences.length > 1 && text.length > MIN_LENGTH_FOR_TRUNCATION) {
      let truncated = sentences[0];
      // If first sentence is too short, try to include the next sentence
      if (truncated.length < MIN_LENGTH_FOR_TRUNCATION && sentences.length > 1) {
        const twoSentences = sentences.slice(0, 2).join(' ');
        if (twoSentences.length <= MIN_LENGTH_FOR_TRUNCATION * 1.5) {
          truncated = twoSentences;
        }
      }
      return {
        truncated,
        shouldTruncate: true,
      };
    }

    // If it's just one sentence or paragraph, return as is
    return {
      truncated: text,
      shouldTruncate: false,
    };
  };

  // Dynamic gradient based on theme
  const getBackgroundGradient = () => {
    if (theme.isDark) {
      return 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900';
    }

    switch (theme.id) {
      case 'modern-purple':
        return 'bg-gradient-to-br from-purple-900 via-purple-800 to-purple-900';
      case 'ocean-blue':
        return 'bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900';
      case 'forest-green':
        return 'bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-900';
      case 'warm-amber':
        return 'bg-gradient-to-br from-amber-900 via-amber-800 to-amber-900';
      case 'elegant-rose':
        return 'bg-gradient-to-br from-rose-900 via-rose-800 to-rose-900';
      case 'high-contrast':
        return 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900';
      default:
        return 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900';
    }
  };

  const getSidebarGradient = () => {
    if (theme.isDark) {
      return 'bg-gradient-to-b from-slate-600 to-slate-800';
    }

    switch (theme.id) {
      case 'modern-purple':
        return 'bg-gradient-to-b from-purple-600 to-purple-800';
      case 'ocean-blue':
        return 'bg-gradient-to-b from-blue-600 to-blue-800';
      case 'forest-green':
        return 'bg-gradient-to-b from-emerald-600 to-emerald-800';
      case 'warm-amber':
        return 'bg-gradient-to-b from-amber-600 to-amber-800';
      case 'elegant-rose':
        return 'bg-gradient-to-b from-rose-600 to-rose-800';
      case 'high-contrast':
        return 'bg-gradient-to-b from-gray-700 to-gray-900';
      default:
        return 'bg-gradient-to-b from-gray-600 to-gray-800';
    }
  };

  return (
    <motion.div
      className={`min-h-screen ${getBackgroundGradient()} flex items-center justify-center p-4`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="relative bg-white rounded-2xl shadow-2xl max-w-6xl w-full h-[90vh] overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Close Button */}
        {onClose && (
          <motion.button
            onClick={onClose}
            className="absolute top-4 right-4 z-50 p-2 rounded-full bg-white/90 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-200"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <X className="w-5 h-5 text-gray-600" />
          </motion.button>
        )}

        <div className="grid grid-cols-3 h-full">
          {/* Left Sidebar - Contact & Info */}
          <motion.div
            className={`${getSidebarGradient()} text-white p-8 overflow-y-auto`}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Profile Image & Name */}
            <motion.div className="text-center mb-8" variants={itemVariants}>
              <div className="w-32 h-32 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <User className="w-16 h-16 text-white/70" />
              </div>
              <h1 className="text-2xl font-bold mb-1">
                {profile.fullName || `${profile.firstName} ${profile.lastName}`}
              </h1>
              <p className="text-white/80 text-lg">
                {profile.experience?.[0]?.title || 'Professional'}
              </p>
            </motion.div>

            {/* Contact Information */}
            <motion.div className="mb-8" variants={itemVariants}>
              <h2 className="text-xl font-bold mb-4 border-b border-white/30 pb-2">Contact</h2>
              <div className="space-y-3">
                {profile.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-white/70" />
                    <span className="text-sm">{profile.phone}</span>
                  </div>
                )}
                {profile.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-white/70" />
                    <span className="text-sm break-all">{profile.email}</span>
                  </div>
                )}
                {profile.location && (
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-white/70" />
                    <span className="text-sm">{profile.location}</span>
                  </div>
                )}
                {profile.linkedinUrl && (
                  <div className="flex items-center gap-3">
                    <Linkedin className="w-4 h-4 text-white/70" />
                    <span className="text-sm break-all">{profile.linkedinUrl}</span>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Work Availability */}
            {profile.workAvailability && (
              <motion.div className="mb-8" variants={itemVariants}>
                <h2 className="text-xl font-bold mb-4 border-b border-white/30 pb-2">
                  Work Availability
                </h2>
                <div className="space-y-2">
                  {profile.workAvailability.immediatelyAvailable && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-sm">Available Immediately</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                    <span className="text-sm">Full-time (40h)</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Preferences */}
            {profile.preferences && (
              <motion.div className="mb-8" variants={itemVariants}>
                <h2 className="text-xl font-bold mb-4 border-b border-white/30 pb-2">
                  Preferences
                </h2>
                <div className="space-y-3">
                  {profile.preferences.remotePreference && (
                    <div>
                      <span className="text-sm font-medium">Work Type: </span>
                      <span className="text-sm capitalize">
                        {profile.preferences.remotePreference}
                      </span>
                    </div>
                  )}
                  {profile.preferences.jobTypes && profile.preferences.jobTypes.length > 0 && (
                    <div>
                      <span className="text-sm font-medium block mb-1">Job Types:</span>
                      <div className="flex flex-wrap gap-1">
                        {profile.preferences.jobTypes.map((jobType, index) => (
                          <span key={index} className="bg-white/20 text-xs px-2 py-1 rounded-full">
                            {jobType}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Main Content Area */}
          <motion.div
            className="col-span-2 p-8 overflow-y-auto bg-gray-50"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Professional Summary */}
            {profile.summary && (
              <motion.div className="mb-8" variants={itemVariants}>
                <h2 className={`text-2xl font-bold ${getThemeAccentColor()} mb-4`}>
                  Professional Summary
                </h2>
                <p className="text-gray-700 leading-relaxed">{profile.summary}</p>
              </motion.div>
            )}

            {/* Core Values */}
            {profile.myValues && profile.myValues.length > 0 && (
              <motion.div className="mb-8" variants={itemVariants}>
                <h2 className={`text-2xl font-bold ${getThemeAccentColor()} mb-4`}>Core Values</h2>
                <div className="grid grid-cols-1 gap-3">
                  {profile.myValues.map((value, index) => (
                    <motion.div
                      key={index}
                      className={`flex items-center gap-3 p-3 rounded-lg ${theme.secondary} hover:shadow-sm transition-all duration-200 group`}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div
                        className={`p-1.5 rounded-full ${getThemeAccentColor(true)} group-hover:scale-110 transition-transform duration-200`}
                      >
                        <Heart className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-gray-700 font-medium">{value}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Experience */}
            <motion.div className="mb-8" variants={itemVariants}>
              <h2 className={`text-2xl font-bold ${getThemeAccentColor()} mb-6`}>Experience</h2>
              <motion.div
                className="space-y-6"
                variants={experienceContainerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                key={currentPage}
              >
                {paginatedExperience.map((exp: Experience, index) => (
                  <motion.div
                    key={index}
                    className={`relative pl-8 border-l-2 ${theme.border}`}
                    variants={experienceItemVariants}
                  >
                    <div
                      className={`absolute -left-3 top-0 w-6 h-6 ${getThemeAccentColor(true)} rounded-full flex items-center justify-center shadow-sm`}
                    >
                      <Briefcase className="w-3 h-3 text-white" />
                    </div>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800">{exp.title}</h3>
                        <p className={`${getThemeAccentColor()} font-medium`}>{exp.company}</p>
                      </div>
                      <div className="text-right text-sm">
                        <div className={`${getThemeIconColor()} font-medium`}>
                          {formatDateRange(exp.startDate, exp.endDate)}
                        </div>
                        <div
                          className={`text-xs ${getThemeIconColor()} opacity-75 flex items-center gap-1 justify-end`}
                        >
                          <Timer className={`w-3 h-3 ${getThemeIconColor()}`} />
                          {formatDuration(exp.duration)}
                        </div>
                      </div>
                    </div>
                    {exp.description && (
                      <p className="text-gray-600 text-sm leading-relaxed">{exp.description}</p>
                    )}
                  </motion.div>
                ))}
              </motion.div>

              {/* Experience Pagination */}
              {totalPages > 1 && (
                <motion.div
                  className="flex justify-center mt-6 gap-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <motion.button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded-full hover:${theme.secondary} disabled:opacity-50 disabled:hover:bg-transparent`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronLeft className={`w-5 h-5 ${getThemeIconColor()}`} />
                  </motion.button>
                  <span className={`text-sm ${getThemeIconColor()} flex items-center px-3`}>
                    {currentPage} of {totalPages}
                  </span>
                  <motion.button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className={`p-2 rounded-full hover:${theme.secondary} disabled:opacity-50 disabled:hover:bg-transparent`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronRight className={`w-5 h-5 ${getThemeIconColor()}`} />
                  </motion.button>
                </motion.div>
              )}
            </motion.div>

            {/* Education */}
            <motion.div className="mb-8" variants={itemVariants}>
              <h2 className={`text-2xl font-bold ${getThemeAccentColor()} mb-6`}>Education</h2>
              <div className="space-y-4">
                {((profile.education as Education[]) || []).map((edu: Education, index) => (
                  <motion.div
                    key={index}
                    className={`bg-white p-4 rounded-lg shadow-sm border ${theme.border}`}
                    variants={itemVariants}
                  >
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">{edu.degree}</h3>
                    <p className={`${getThemeAccentColor()} font-medium mb-2`}>{edu.institution}</p>
                    <div className={`flex items-center gap-2 text-sm ${getThemeIconColor()}`}>
                      <Calendar className={`w-4 h-4 ${getThemeIconColor()}`} />
                      <span>
                        {safeFormatDate(edu.startDate || '')} -{' '}
                        {edu.endDate ? safeFormatDate(edu.endDate) : 'Present'}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Skills */}
            <motion.div className="mb-8" variants={itemVariants}>
              <h2 className={`text-2xl font-bold ${getThemeAccentColor()} mb-6`}>
                Skills & Expertise
              </h2>
              <motion.div
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                variants={skillsContainerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                key={currentSkillsPage}
              >
                <div className="flex flex-wrap gap-4">
                  {paginatedSkills.map((skill, index) => {
                    const IconComponent = getSkillIcon(skill);
                    return (
                      <motion.div
                        key={index}
                        className={`group flex items-center gap-3 px-4 py-2.5 rounded-lg ${theme.secondary} hover:shadow-md transition-all duration-200 border ${theme.border}`}
                        variants={skillItemVariants}
                        whileHover={{ scale: 1.05, y: -2 }}
                      >
                        <div
                          className={`p-1.5 rounded-md ${getThemeAccentColor(true)} group-hover:scale-110 transition-transform duration-200`}
                        >
                          <IconComponent className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-gray-800 font-medium text-sm whitespace-nowrap">
                          {skill}
                        </span>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>

              {/* Skills Pagination */}
              {totalSkillPages > 1 && (
                <motion.div
                  className="flex justify-center mt-4 gap-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <motion.button
                    onClick={() => setCurrentSkillsPage(prev => Math.max(1, prev - 1))}
                    disabled={currentSkillsPage === 1}
                    className={`p-2 rounded-full hover:${theme.secondary} disabled:opacity-50`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronLeft className={`w-5 h-5 ${getThemeIconColor()}`} />
                  </motion.button>
                  <span className={`text-sm ${getThemeIconColor()} flex items-center px-3`}>
                    {currentSkillsPage} of {totalSkillPages}
                  </span>
                  <motion.button
                    onClick={() =>
                      setCurrentSkillsPage(prev => Math.min(totalSkillPages, prev + 1))
                    }
                    disabled={currentSkillsPage === totalSkillPages}
                    className={`p-2 rounded-full hover:${theme.secondary} disabled:opacity-50`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ChevronRight className={`w-5 h-5 ${getThemeIconColor()}`} />
                  </motion.button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default JobSeekerProfileStandard;
