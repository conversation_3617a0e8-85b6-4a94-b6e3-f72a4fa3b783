import { render, screen } from '@testing-library/react';
import JobSeekerProfileStandard from '../JobSeekerProfileStandard';

const candidate = {
  experience: [
    { startDate: '2022-01-01', endDate: '2023-01-01', title: 'Engineer' },
    { startDate: 'invalid-date', endDate: null, title: 'Broken Date' },
  ],
  education: [
    { startDate: '2020-09-01', endDate: '2022-06-01', degree: 'BSc', field: 'CS' },
    { startDate: '', endDate: '', degree: 'MSc', field: 'Math' },
  ],
  certifications: [{ issueDate: 'not-a-date' }],
};

describe('JobSeekerProfileStandard date rendering', () => {
  it('renders valid dates and fallback for invalid dates', () => {
    render(<JobSeekerProfileStandard profile={candidate as any} />);
    // Valid date: should contain the year
    expect(screen.getAllByText(content => content.includes('2022')).length).toBeGreaterThan(0);
    // "Present" for null endDate
    expect(screen.getAllByText(/Present/).length).toBeGreaterThan(0);
    // Invalid date fallback - check for empty span instead of "-" text
    expect(screen.getAllByText(/Invalid Date NaN/).length).toBeGreaterThan(0);
  });
});
