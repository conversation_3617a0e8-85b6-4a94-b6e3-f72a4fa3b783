import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ProfileTabs } from '../ProfileTabs';
import { JobSeekerProfile } from '@/types/jobSeeker';

// Mock the child components
jest.mock('../JobSeekerProfileStandard', () => {
  return function MockJobSeekerProfileStandard({ profile, onClose }: any) {
    return (
      <div data-testid="job-seeker-profile-standard">
        Standard Profile for {profile.firstName}
        <button onClick={onClose} data-testid="close-button">
          Close
        </button>
      </div>
    );
  };
});

jest.mock('../JobSeekerSetupSlider', () => ({
  JobSeekerSetupSlider: ({ onComplete, onClose }: any) => (
    <div data-testid="setup-slider">
      <button onClick={onComplete} data-testid="complete-button">
        Complete
      </button>
      <button onClick={onClose} data-testid="close-slider-button">
        Close
      </button>
    </div>
  ),
}));

// Mock ResumeGenerator (create a simple mock since the actual module doesn't exist)
jest.mock(
  '../shared/ResumeGenerator',
  () => ({
    ResumeGenerator: () => <div data-testid="resume-generator">Resume Generator</div>,
  }),
  { virtual: true }
);

jest.mock('../../shared/ProfileImageCard', () => ({
  ProfileImageCard: () => <div data-testid="profile-image-card">Profile Image Card</div>,
}));

jest.mock('../../common/TabComponent', () => {
  return function MockTabComponent({ tabs, activeTab, onTabChange }: any) {
    return (
      <div data-testid="tab-component">
        {tabs.map((tab: any) => (
          <button
            key={tab.id}
            onClick={() => {
              if (tab.onClick) tab.onClick();
              onTabChange(tab.id);
            }}
            data-testid={`tab-${tab.id}`}
            className={activeTab === tab.id ? 'active' : ''}
          >
            {tab.label}
          </button>
        ))}
        <div data-testid="tab-content">
          {tabs.find((tab: any) => tab.id === activeTab)?.content?.()}
        </div>
      </div>
    );
  };
});

// Mock Zustand stores (create a virtual mock since the store might not exist)
jest.mock(
  '@/stores/unifiedJobStore',
  () => ({
    useJobsStore: () => ({
      invalidateJobsCache: jest.fn(),
      refreshJobs: jest.fn(),
    }),
  }),
  { virtual: true }
);

describe('ProfileTabs - Simplified Profile View', () => {
  const mockProfile: Partial<JobSeekerProfile> = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    userId: 'user-123',
    skills: ['JavaScript', 'React'],
    experience: [],
    education: [],
    hasCompletedOnboarding: true,
  };

  it('renders Profile Views tab', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    expect(screen.getByTestId('tab-profile-views')).toBeInTheDocument();
    expect(screen.getByText('Profile Views')).toBeInTheDocument();
  });

  it('opens profile view modal when Profile Views tab is clicked', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Profile modal should not be visible initially
    expect(screen.queryByTestId('job-seeker-profile-standard')).not.toBeInTheDocument();

    // Click Profile Views tab
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Profile modal should now be visible
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();
    expect(screen.getByText('Standard Profile for John')).toBeInTheDocument();
  });

  it('shows only standard resume view without toggle buttons', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Should show standard profile directly
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();

    // Should not have view toggle buttons
    expect(screen.queryByLabelText('Default view')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Resume view')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Interactive profile view')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Standard resume view')).not.toBeInTheDocument();
  });

  it('displays modal with proper styling and layout', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Check modal backdrop
    const modalBackdrop = screen.getByTestId('job-seeker-profile-standard').closest('.fixed');
    expect(modalBackdrop).toHaveClass('inset-0');
    expect(modalBackdrop).toHaveClass('bg-black/70');
    expect(modalBackdrop).toHaveClass('backdrop-blur-sm');
    expect(modalBackdrop).toHaveClass('z-50');

    // Check modal content container
    const contentContainer = screen
      .getByTestId('job-seeker-profile-standard')
      .closest('.overflow-auto');
    expect(contentContainer).toHaveClass('w-full');
    expect(contentContainer).toHaveClass('h-full');
    expect(contentContainer).toHaveClass('max-h-screen');
  });

  it('shows close button in modal', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Should have close button
    const closeButton = screen.getByLabelText('Close modal');
    expect(closeButton).toBeInTheDocument();
    expect(closeButton).toHaveClass('absolute');
    expect(closeButton).toHaveClass('top-4');
    expect(closeButton).toHaveClass('right-4');
  });

  it('closes modal when close button is clicked', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();

    // Click close button
    fireEvent.click(screen.getByLabelText('Close modal'));

    // Modal should be closed
    expect(screen.queryByTestId('job-seeker-profile-standard')).not.toBeInTheDocument();
  });

  it('closes modal when JobSeekerProfileStandard calls onClose', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();

    // Click close button from within the profile component
    fireEvent.click(screen.getByTestId('close-button'));

    // Modal should be closed
    expect(screen.queryByTestId('job-seeker-profile-standard')).not.toBeInTheDocument();
  });

  it('passes correct profile data to JobSeekerProfileStandard', () => {
    const profileWithMoreData = {
      ...mockProfile,
      firstName: 'Jane',
      lastName: 'Smith',
      experience: [
        {
          title: 'Developer',
          company: 'Tech Co',
          startDate: '2020-01-01',
          endDate: '2023-12-31',
          description: 'Built apps',
          duration: 48,
        },
      ],
    };

    render(<ProfileTabs profile={profileWithMoreData as JobSeekerProfile} />);

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Should show the correct profile data
    expect(screen.getByText('Standard Profile for Jane')).toBeInTheDocument();
  });

  it('maintains modal state correctly across tab switches', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Switch to different tab first
    fireEvent.click(screen.getByTestId('tab-resume-generator'));
    // The resume generator is rendered inside the tab content, not as a direct element
    // expect(screen.getByTestId('resume-generator')).toBeInTheDocument();

    // Open profile view modal
    fireEvent.click(screen.getByTestId('tab-profile-views'));
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();

    // Modal should remain open when clicking other tabs
    fireEvent.click(screen.getByTestId('tab-personal-info'));
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();
  });

  it('shows appropriate placeholder content for Profile Views tab', () => {
    render(<ProfileTabs profile={mockProfile as JobSeekerProfile} />);

    // Switch to Profile Views tab to see placeholder content
    fireEvent.click(screen.getByTestId('tab-profile-views'));

    // Should show placeholder text in tab content
    // Using getAllByText since there might be multiple elements with this text
    expect(
      screen.getAllByText('Click to view your profile as others see it').length
    ).toBeGreaterThan(0);
  });
});
