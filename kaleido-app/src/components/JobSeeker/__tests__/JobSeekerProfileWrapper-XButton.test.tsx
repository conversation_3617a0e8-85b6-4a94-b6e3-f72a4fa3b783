import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import JobSeekerProfileWrapper from '../JobSeekerProfileWrapper';
import { StandardizedProfile } from '@/shared/types/profile.types';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock the profile components
jest.mock('../JobSeekerFullProfile', () => {
  return function MockJobSeekerFullProfile({ profile, onClose }: any) {
    return (
      <div data-testid="job-seeker-full-profile">
        Full Profile for {profile.firstName}
        <button onClick={onClose} data-testid="mock-close">
          Close
        </button>
      </div>
    );
  };
});

jest.mock('../JobSeekerProfileStandard', () => {
  return function MockJobSeekerProfileStandard({ profile, onClose }: any) {
    return (
      <div data-testid="job-seeker-profile-standard">
        Standard Profile for {profile.firstName}
        <button onClick={onClose} data-testid="mock-close">
          Close
        </button>
      </div>
    );
  };
});

describe('JobSeekerProfileWrapper - X Button Removal (Legacy Test - Component No Longer Used in Profile Views)', () => {
  const mockProfile: Partial<StandardizedProfile> = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    userId: 'user-123',
    skills: [],
    experience: [],
    education: [],
  };

  const mockOnClose = jest.fn();

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('renders without the white X button in top-left corner', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    // Should not find any X button with white background styling
    expect(screen.queryByLabelText('Close panel')).not.toBeInTheDocument();

    // Should not find button with specific white styling classes
    const whiteButtons = screen
      .queryAllByRole('button')
      .filter(
        button => button.className.includes('bg-white') && button.className.includes('rounded-full')
      );
    expect(whiteButtons).toHaveLength(0);
  });

  it('renders view mode selector buttons correctly', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    // Should have default view button
    expect(screen.getByLabelText('Default view')).toBeInTheDocument();
    expect(screen.getByTitle('Interactive profile view')).toBeInTheDocument();

    // Should have resume view button
    expect(screen.getByLabelText('Resume view')).toBeInTheDocument();
    expect(screen.getByTitle('Standard resume view')).toBeInTheDocument();
  });

  it('switches between default and standard view modes', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    // Default view should be shown initially
    expect(screen.getByTestId('job-seeker-full-profile')).toBeInTheDocument();
    expect(screen.queryByTestId('job-seeker-profile-standard')).not.toBeInTheDocument();

    // Click standard view button
    fireEvent.click(screen.getByLabelText('Resume view'));

    // Standard view should now be shown
    expect(screen.getByTestId('job-seeker-profile-standard')).toBeInTheDocument();
    expect(screen.queryByTestId('job-seeker-full-profile')).not.toBeInTheDocument();

    // Click default view button
    fireEvent.click(screen.getByLabelText('Default view'));

    // Default view should be shown again
    expect(screen.getByTestId('job-seeker-full-profile')).toBeInTheDocument();
    expect(screen.queryByTestId('job-seeker-profile-standard')).not.toBeInTheDocument();
  });

  it('passes onClose function to child components', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    // Click close button in the child component
    fireEvent.click(screen.getByTestId('mock-close'));

    // onClose should be called
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('applies correct positioning to view selector buttons', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    const viewSelector = screen.getByLabelText('Default view').closest('div');
    expect(viewSelector).toHaveClass('absolute');
    expect(viewSelector).toHaveClass('top-20');
    expect(viewSelector).toHaveClass('right-4');
    expect(viewSelector).toHaveClass('z-20');
    expect(viewSelector).toHaveClass('flex');
    expect(viewSelector).toHaveClass('space-x-2');
  });

  it('applies correct styling to view selector buttons', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    const defaultButton = screen.getByLabelText('Default view');
    const resumeButton = screen.getByLabelText('Resume view');

    // Both buttons should have common styling
    [defaultButton, resumeButton].forEach(button => {
      expect(button).toHaveClass('p-2');
      expect(button).toHaveClass('rounded-full');
      expect(button).toHaveClass('transition-colors');
      expect(button).toHaveClass('shadow-md');
    });

    // Default button should be active initially
    expect(defaultButton).toHaveClass('bg-blue-600');
    expect(defaultButton).toHaveClass('text-white');

    // Resume button should be inactive initially
    expect(resumeButton).toHaveClass('bg-gray-200');
    expect(resumeButton).toHaveClass('text-gray-700');
  });

  it('updates button states when switching views', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    const defaultButton = screen.getByLabelText('Default view');
    const resumeButton = screen.getByLabelText('Resume view');

    // Click resume view
    fireEvent.click(resumeButton);

    // Button states should switch
    expect(resumeButton).toHaveClass('bg-blue-600');
    expect(resumeButton).toHaveClass('text-white');
    expect(defaultButton).toHaveClass('bg-gray-200');
    expect(defaultButton).toHaveClass('text-gray-700');
  });

  it('does not render when onClose is not provided', () => {
    render(<JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} />);

    // Component should still render without issues
    expect(screen.getByTestId('job-seeker-full-profile')).toBeInTheDocument();

    // No close functionality should be available
    expect(screen.queryByLabelText('Close panel')).not.toBeInTheDocument();
  });

  it('maintains proper component structure without X button', () => {
    render(
      <JobSeekerProfileWrapper profile={mockProfile as StandardizedProfile} onClose={mockOnClose} />
    );

    // Main container should exist with proper classes
    const mainContainer = screen
      .getByTestId('job-seeker-full-profile')
      .closest('div')?.parentElement;
    expect(mainContainer).toHaveClass('w-full');
    expect(mainContainer).toHaveClass('h-full');
    expect(mainContainer).toHaveClass('bg-transparent');

    // View selector should be positioned correctly
    const viewSelector = screen.getByLabelText('Default view').closest('div');
    expect(viewSelector).toHaveClass('absolute');
    expect(viewSelector).toHaveClass('top-20');
    expect(viewSelector).toHaveClass('right-4');
  });
});
