'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import {
  CompletionState,
  IJobSeekerProfile,
  JobSeekerSetupSliderProps,
  StepComponentProps,
} from './types';

import { getValidationMessage, validateSlide } from './utils/validation';

import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';
import { X } from 'lucide-react';
import Image from 'next/image';
import Confetti from 'react-confetti';
import ColourfulLoader from '../Layouts/ColourfulLoader';
import { CompletionView } from './components/CompletionView';
import { ConfirmationDialog } from './components/ConfirmationDialog';
import LinkedInProfileHandler from './components/LinkedInProfileHandler';
import { NavigationButtons } from './components/NavigationButtons';
import { StepProgress } from './components/StepProgress';
import StepUrlHandler from './components/StepUrlHandler';
import ValidationHandler from './components/ValidationHandler';
// Import our new utilities and components
import useUser from '@/hooks/useUser';
import { useJobSeekerStore } from '@/stores/jobSeekerStore';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { showToast } from '../Toaster';
import { setupSlides } from './constants/slides';
import { fetchJobSeekerProfile } from './utils/profileUtils';

export const JobSeekerSetupSliderInner: React.FC<JobSeekerSetupSliderProps> = ({
  onComplete,
  onClose,
  initialData,
  initialStep,
  validationResponse,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [completion, setCompletion] = useState<CompletionState>({
    show: false,
    animate: false,
  });
  const [showConfirmation, setShowConfirmation] = useState(false);
  // Get user data - will be null on holding pages before authentication
  const { user } = useUser();
  const { formData, updateFormData, setFormData, saveStep, isLoading, setLinkedInData } =
    useJobSeekerStore();
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Helper function to refresh data instead of full page reload
  const handleDataRefresh = () => {
    invalidateJobsCache();
    refreshJobs(true);

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }
  };

  // Initialize store with initial data
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      // Use setFormData directly with the initial data - don't merge with current formData
      // as it might be empty at this point
      setFormData(initialData as IJobSeekerProfile);
    }
  }, [initialData, setFormData]);

  // Only fetch profile if we don't have initial data
  useEffect(() => {
    if (user?.sub && (!initialData || Object.keys(initialData).length === 0)) {
      fetchJobSeekerProfile(user, updateFormData);
    }
  }, [user, initialData]);

  // ValidationHandler component now handles this logic

  // Use our extracted components for validation and LinkedIn profile handling
  useEffect(() => {
    if (validationResponse) {
      // This will be handled by ValidationHandler component
    }
  }, [validationResponse]);

  // Handle LinkedIn profile data if available
  useEffect(() => {
    if (initialData?.linkedInProfile) {
      // This will be handled by LinkedInProfileHandler component
    }
  }, [initialData]);

  // Handle URL step parameters
  useEffect(() => {
    // This will be handled by StepUrlHandler component
  }, [searchParams, initialStep]);

  const handleUpdate = (data: Partial<IJobSeekerProfile>) => {
    updateFormData(data);
  };

  const handleNext = async () => {
    const currentSlide = setupSlides[currentSlideIndex];

    // Check if current slide is valid before proceeding
    if (!isCurrentSlideValid()) {
      showToast({
        message: 'Please complete all required fields before continuing.',
        isSuccess: false,
      });
      return;
    }

    try {
      if (currentSlideIndex === setupSlides.length - 1) {
        // On the last step, skip saveStep and go directly to handleComplete
        // handleComplete will do its own saving with proper data sanitization

        // Dispatch profile completion event for success modal
        const event = new CustomEvent('profileSetupCompleted', {
          detail: {
            action: 'profileSetup',
            actionType: 'completed',
            status: 'success',
            title: 'Profile Setup Complete!',
            description:
              'Your profile has been completed successfully. You can now apply for jobs and connect with employers.',
            stats: {
              'Profile Completion': '100%',
              'Steps Completed': setupSlides.length,
            },
            closeManager: () => {
              // Refresh data when modal is closed to show updated profile state
              handleDataRefresh();
            },
          },
        });
        window.dispatchEvent(event);

        // Automatically complete the onboarding
        await handleComplete();
      } else {
        // For non-final steps, save current step data to backend
        await saveStep(currentSlide.id);

        // Small delay to ensure state propagation
        await new Promise(resolve => setTimeout(resolve, 50));
        const nextIndex = currentSlideIndex + 1;
        const nextSlide = setupSlides[nextIndex];

        // Update the URL and navigate to the next step
        const params = new URLSearchParams(searchParams.toString());
        params.set('step', nextSlide.id);

        // Set the slide index first to ensure state is ready
        setCurrentSlideIndex(nextIndex);

        // Then update the URL
        router.push(`?${params.toString()}`);
      }
    } catch (error) {
      console.error('Error saving step:', error);
      showToast({
        message: 'Failed to save progress. Please try again.',
        isSuccess: false,
      });
    }
  };

  const handleBack = () => {
    if (currentSlideIndex === 0) {
      handleCloseAttempt();
    } else {
      const prevIndex = currentSlideIndex - 1;
      const prevSlide = setupSlides[prevIndex];

      const params = new URLSearchParams(searchParams.toString());
      params.set('step', prevSlide.id);
      router.push(`?${params.toString()}`);

      setCurrentSlideIndex(prevIndex);
    }
  };

  const handleCloseAttempt = () => {
    // Refresh data when closing the slider
    handleDataRefresh();
    if (onClose) {
      onClose();
    }
  };

  const isCurrentSlideValid = () => {
    const currentSlide = setupSlides[currentSlideIndex];
    // Always return true for the portfolio step since it's optional
    if (currentSlide.id === 'portfolio') {
      return true;
    }
    return validateSlide(currentSlide.id, formData);
  };

  const handleComplete = async () => {
    try {
      const jobSeekerId = searchParams.get('jobSeekerId');

      // Deeply sanitize education entries to ensure no null fields
      const sanitizedEducation = formData.education.map(edu => ({
        ...edu,
        degree: typeof edu.degree === 'string' && edu.degree ? edu.degree : 'Not specified',
        field: typeof edu.field === 'string' && edu.field ? edu.field : 'Not specified',
        institution:
          typeof edu.institution === 'string' && edu.institution
            ? edu.institution
            : 'Not specified',
      }));

      // Sanitize preferences data
      let sanitizedPreferences = formData.preferences;
      if (sanitizedPreferences) {
        // Ensure desiredSalary is a string
        if (
          sanitizedPreferences.desiredSalary &&
          sanitizedPreferences.desiredSalary.min &&
          sanitizedPreferences.desiredSalary.max
        ) {
          const min = sanitizedPreferences.desiredSalary.min;
          const max = sanitizedPreferences.desiredSalary.max;
          const currency = sanitizedPreferences.desiredSalary.currency || 'USD';
          const period = sanitizedPreferences.desiredSalary.period || 'yearly';

          sanitizedPreferences = {
            ...sanitizedPreferences,
            desiredSalary: {
              min,
              max,
              currency,
              period,
            },
          };

          // Remove the internal desiredSalary
          delete sanitizedPreferences.desiredSalary;
        } else if (typeof sanitizedPreferences.desiredSalary === 'object') {
          // Make sure the object has all required fields
          if (sanitizedPreferences.desiredSalary?.min && sanitizedPreferences.desiredSalary?.max) {
            const min = sanitizedPreferences.desiredSalary.min;
            const max = sanitizedPreferences.desiredSalary.max;
            const currency = sanitizedPreferences.desiredSalary.currency || 'USD';
            const period = sanitizedPreferences.desiredSalary.period || 'yearly';

            sanitizedPreferences = {
              ...sanitizedPreferences,
              desiredSalary: {
                min,
                max,
                currency,
                period,
              },
            };
          } else {
            // If incomplete, remove it
            delete sanitizedPreferences.desiredSalary;
          }
        }
      }

      const updatedFormData = {
        ...formData,
        projects: formData.projects.map(project => {
          const { startDate, endDate, ...rest } = project;
          return rest;
        }),
        // Replace education with sanitized version
        education: sanitizedEducation,
        // Set sanitized preferences
        preferences: sanitizedPreferences,
        id: jobSeekerId,
        clientId: user?.sub,
        hasCompletedOnboarding: true,
        updatedAt: new Date().toISOString(),
        myValues: formData.myValues || [],
      };

      if (jobSeekerId) {
        await apiHelper.put(`/job-seekers/${jobSeekerId}`, updatedFormData);
      } else {
        await apiHelper.post('/job-seekers', updatedFormData);
      }

      // Check for pending job application and redirect back to the job
      try {
        const pendingJobId = localStorage.getItem('headstart_pending_job_application');
        const redirectUrl = localStorage.getItem('headstart_redirect_after_auth');

        if (pendingJobId && redirectUrl) {
          // Clear the stored application intent
          localStorage.removeItem('headstart_pending_job_application');
          localStorage.removeItem('headstart_redirect_after_auth');

          // Redirect back to the job page
          window.location.href = redirectUrl;
          return;
        }
      } catch (error) {
        console.error('Error checking pending job application:', error);
      }

      onComplete(updatedFormData);
    } catch (error) {
      console.error('Error completing setup:', error);
      showToast({
        message: 'Failed to save profile. Please try again.',
        isSuccess: false,
      });
    }
  };

  const completionPercentage = useMemo(() => {
    // Use onboardingProgress data if available for more accurate progress
    if (validationResponse?.onboardingProgress) {
      return validationResponse.onboardingProgress.overall.percentage;
    }
    // Fall back to slide-based percentage
    return ((currentSlideIndex + 1) / setupSlides.length) * 100;
  }, [currentSlideIndex, validationResponse]);

  // Get section completion data
  const getSectionCompletionStatus = (sectionId: string) => {
    if (!validationResponse?.onboardingProgress) return null;

    switch (sectionId) {
      case 'basics':
        return validationResponse.onboardingProgress.basicInfo;
      case 'professional':
        return validationResponse.onboardingProgress.professionalInfo;
      case 'preferences':
        return validationResponse.onboardingProgress.preferences;
      default:
        return null;
    }
  };

  // Get missing fields for current section
  const getMissingSectionFields = (sectionId: string) => {
    const sectionData = getSectionCompletionStatus(sectionId);
    if (!sectionData) return [];

    return sectionData.requiredFields.filter(field => !sectionData.completedFields.includes(field));
  };

  // StepUrlHandler component now handles this logic

  return (
    <>
      {/* Handle validation and step navigation */}
      <ValidationHandler
        validationResponse={validationResponse}
        setCurrentSlideIndex={setCurrentSlideIndex}
        formData={formData}
        updateFormData={updateFormData}
      />

      {/* Handle LinkedIn profile data if available */}
      {initialData?.linkedInProfile && (
        <LinkedInProfileHandler
          linkedInProfile={initialData.linkedInProfile}
          formData={formData}
          user={user}
          updateFormData={updateFormData}
        />
      )}

      {/* Handle URL step parameters */}
      <StepUrlHandler setCurrentSlideIndex={setCurrentSlideIndex} initialStep={initialStep} />

      <div
        className="fixed inset-0 z-50 bg-gradient-to-br from-gray-50 to-blue-50/30 backdrop-blur-3xl"
        style={
          {
            // Light theme CSS variables for better contrast on white background
            '--input-bg': '#ffffff',
            '--input-border': 'rgba(0, 0, 0, 0.1)',
            '--input-text': '#1f2937',
            '--input-placeholder': 'rgba(31, 41, 55, 0.6)',
            '--foreground-color': '#1f2937',
            '--card-bg': '#ffffff',
            '--card-border': 'rgba(0, 0, 0, 0.1)',
            '--primary': '#7c3aed',
            '--primary-bg': 'rgba(124, 58, 237, 0.1)',
            '--hover': 'rgba(0, 0, 0, 0.05)',
            '--scrollbar-thumb': 'rgba(0, 0, 0, 0.2)',
            // Dropdown-specific variables
            '--dropdown-bg': '#ffffff',
            '--dropdown-border': 'rgba(0, 0, 0, 0.1)',
            '--dropdown-text': '#1f2937',
            '--dropdown-hover-bg': 'rgba(0, 0, 0, 0.05)',
            '--dropdown-selected-bg': 'rgba(124, 58, 237, 0.1)',
          } as React.CSSProperties
        }
      >
        {completion.show && <Confetti numberOfPieces={200} recycle={false} />}

        <AnimatePresence mode="wait">
          {completion.show ? (
            <CompletionView
              image={setupSlides[setupSlides.length - 1].image}
              onComplete={handleComplete}
              onClose={onClose}
              formData={formData}
              setCompletion={setCompletion}
              onboardingProgress={validationResponse?.onboardingProgress}
            />
          ) : (
            <motion.div
              key={currentSlideIndex}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="relative h-screen"
            >
              {/* Full-width Background Image */}
              <motion.div
                initial={{ opacity: 0, scale: 1.1 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.1 }}
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: `url(${setupSlides[currentSlideIndex].image})`,
                }}
              >
                {/* Overlay for modern feel */}
                {setupSlides[currentSlideIndex].overlay && (
                  <div
                    className="absolute inset-0"
                    style={{
                      background: setupSlides[currentSlideIndex].overlay.gradient,
                      mixBlendMode: setupSlides[currentSlideIndex].overlay.blend as any,
                      opacity: setupSlides[currentSlideIndex].overlay.opacity,
                    }}
                  />
                )}
                {/* Close button */}
                <button
                  type="button"
                  onClick={handleCloseAttempt}
                  className="absolute top-8 right-8 p-4 hover:bg-white/10 rounded-full transition-colors text-white/90 hover:text-white z-20"
                  aria-label="Close setup"
                >
                  <X className="w-8 h-8" />
                </button>

                <div className="absolute top-8 right-24 z-20">
                  <StepProgress
                    currentStep={currentSlideIndex}
                    totalSteps={setupSlides.length}
                    overallCompletion={validationResponse?.onboardingProgress?.overall.percentage}
                  />
                </div>

                {/* Modern fun fact section centered in the right half of the screen */}
                <div className="hidden md:block absolute right-0 top-1/2 transform -translate-y-1/2 w-1/2 lg:w-5/12 xl:w-2/5 flex justify-center items-center z-20 px-6 lg:px-12">
                  <div className="relative max-w-lg w-full">
                    {/* Soft glow effect */}
                    <div className="absolute -inset-8 blur-3xl bg-white/20 rounded-[2.5rem]" />
                    <div className="absolute -inset-6 blur-2xl bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-[2rem]" />

                    {/* Main card with faded edges */}
                    <div className="relative p-8 lg:p-10 rounded-[1.75rem] backdrop-blur-2xl bg-white/15 border border-white/30 shadow-2xl shadow-black/20 overflow-hidden">
                      {/* Faded edge overlay */}
                      <div className="absolute inset-0 rounded-[1.75rem] bg-gradient-to-r from-transparent via-transparent to-white/5 pointer-events-none" />
                      <div className="absolute inset-0 rounded-[1.75rem] bg-gradient-to-b from-transparent via-transparent to-white/3 pointer-events-none" />
                      <div className="absolute inset-0 rounded-[1.75rem] bg-gradient-to-t from-black/5 via-transparent to-transparent pointer-events-none" />

                      <div className="relative flex items-start space-x-5">
                        <div className="flex-shrink-0">
                          {/* Enhanced icon with fully rounded design */}
                          <div className="relative">
                            {/* Icon glow effect */}
                            <div className="absolute inset-0 w-14 h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full blur-sm opacity-60" />

                            {/* Main icon container - fully rounded */}
                            <div className="relative w-14 h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-yellow-300 via-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-xl shadow-yellow-400/30 backdrop-blur-sm">
                              {/* Inner highlight */}
                              <div className="absolute inset-1 bg-gradient-to-br from-white/40 to-transparent rounded-full" />

                              {/* SVG Light bulb icon */}
                              <svg
                                className="w-7 h-7 lg:w-8 lg:h-8 text-white drop-shadow-lg relative z-10"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1zM9 21h6c.55 0 1-.45 1-1s-.45-1-1-1H9c-.55 0-1 .45-1 1s.45 1 1 1zm1-3h4c.55 0 1-.45 1-1s-.45-1-1-1h-4c-.55 0-1 .45-1 1s.45 1 1 1z" />
                              </svg>
                            </div>
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-3">
                            <h3 className="text-white text-xl lg:text-2xl font-bold drop-shadow-lg tracking-tight">
                              Did You Know?
                            </h3>
                            <div className="ml-2 w-2 h-2 bg-yellow-400 rounded-full animate-pulse shadow-sm shadow-yellow-400/50"></div>
                          </div>
                          <p className="text-white/95 text-base lg:text-lg leading-relaxed drop-shadow-md font-medium">
                            "{setupSlides[currentSlideIndex].funFact}"
                          </p>
                        </div>

                        {/* Animated logo section - absolutely positioned at far right top */}
                        <div className="absolute -top-8 -right-8 w-16 h-16 lg:w-20 lg:h-20 pointer-events-none">
                          {/* Background for the loader */}
                          <div className="absolute inset-0 rounded-full bg-black/10 backdrop-blur-sm" />

                          {/* ColourfulLoader without modal background */}
                          <div className="absolute inset-0 scale-40 lg:scale-50 right-[-20px]">
                            <ColourfulLoader text="" useModalBg={false} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Form Panel Overlay */}
              <div className="absolute inset-0 md:inset-y-0 md:left-0 md:w-[55%] lg:w-[50%] bg-white/75 backdrop-blur-lg overflow-y-auto relative flex flex-col h-full border-r border-white/20">
                {/* Logo at top left */}
                <div className="absolute top-6 left-6 z-10">
                  <Image
                    src="/images/logos/kaleido-logo-only.webp"
                    alt="Kaleido Logo"
                    width={64}
                    height={64}
                    className="h-16 w-auto"
                  />
                </div>
                <div className="flex-1 flex flex-col justify-center max-w-2xl mx-auto w-full px-6 pb-24">
                  <div className="mb-8">
                    <div className="md:hidden mb-4">
                      <div className="h-1 bg-gray-200 rounded-full">
                        <div
                          className="h-full bg-gradient-to-r from-blue-600 to-purple-600 rounded-full transition-all"
                          style={{
                            width: `${completionPercentage}%`,
                          }}
                        />
                      </div>
                    </div>

                    <div className="text-sm text-gray-500 mb-2 flex justify-between items-center">
                      <span>
                        Step {currentSlideIndex + 1} of {setupSlides.length}
                      </span>
                      {validationResponse && !validationResponse.isValid && (
                        <span className="text-red-500">
                          {validationResponse.mandatoryMissingFields.length} required fields missing
                        </span>
                      )}
                    </div>
                    <h1 className="text-2xl md:text-3xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {setupSlides[currentSlideIndex].title}
                    </h1>
                    <p className="text-gray-600">{setupSlides[currentSlideIndex].subtitle}</p>

                    {/* Show section completion status */}
                    {validationResponse?.onboardingProgress && (
                      <div className="mt-2 flex items-center">
                        <div className="w-full">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Section progress</span>
                            {getSectionCompletionStatus(setupSlides[currentSlideIndex].id)
                              ?.percentage && (
                              <span>
                                {
                                  getSectionCompletionStatus(setupSlides[currentSlideIndex].id)
                                    ?.percentage
                                }
                                %
                              </span>
                            )}
                          </div>
                          <div className="h-1 bg-gray-200 rounded-full">
                            <div
                              className="h-full bg-gradient-to-r from-blue-600 to-purple-600 rounded-full transition-all"
                              style={{
                                width: `${getSectionCompletionStatus(setupSlides[currentSlideIndex].id)?.percentage || 0}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {!isCurrentSlideValid() && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-100 rounded-md">
                        <p className="text-red-600 text-sm font-medium">
                          {getValidationMessage(setupSlides[currentSlideIndex].id, formData)}
                        </p>
                        {getMissingSectionFields(setupSlides[currentSlideIndex].id).length > 0 && (
                          <ul className="mt-1 text-xs text-red-500 list-disc list-inside">
                            {getMissingSectionFields(setupSlides[currentSlideIndex].id).map(
                              field => (
                                <li key={field}>{field}</li>
                              )
                            )}
                          </ul>
                        )}
                      </div>
                    )}
                  </div>
                  {/* Render current step component */}
                  {React.createElement(setupSlides[currentSlideIndex].component, {
                    formData,
                    onUpdate: handleUpdate,
                    onNext: handleNext,
                    ...(setupSlides[currentSlideIndex].id === 'resume'
                      ? { uploading, setUploading }
                      : {}),
                  } as StepComponentProps)}
                </div>
              </div>

              {/* Navigation Footer - positioned outside the overlay */}
              <div className="absolute bottom-0 left-0 md:w-[55%] lg:w-[50%] z-30">
                <NavigationButtons
                  onPrev={handleBack}
                  onNext={handleNext}
                  onClose={onClose}
                  onComplete={handleComplete}
                  isLoading={isLoading || uploading}
                  currentStep={currentSlideIndex + 1}
                  totalSteps={setupSlides.length}
                  isValid={isCurrentSlideValid()}
                />
              </div>

              {/* Mobile close button */}
              <button
                type="button"
                onClick={handleCloseAttempt}
                className="absolute top-4 right-4 p-2 md:hidden hover:bg-gray-100 rounded-full transition-colors text-gray-600 z-20"
                aria-label="Close setup"
              >
                <X className="w-6 h-6" />
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <ConfirmationDialog
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={() => {
          setShowConfirmation(false);
          // Refresh data when confirming close
          handleDataRefresh();
          if (onClose) {
            onClose();
          }
        }}
      />
    </>
  );
};

// Main component that uses Zustand store
export const JobSeekerSetupSlider: React.FC<JobSeekerSetupSliderProps> = props => {
  return <JobSeekerSetupSliderInner {...props} />;
};

// Authenticated version that checks user roles
export const AuthenticatedJobSeekerSetupSlider: React.FC<JobSeekerSetupSliderProps> = props => {
  const { user, isLoading } = useUser();

  // Check for user role in all possible locations
  const isEmployer = useMemo(() => {
    // If user is loading or doesn't exist, allow the component to render
    // This handles the pre-authentication case on holding pages
    if (isLoading || !user) return false;

    // Check direct role property
    if (user.role === UserRole.EMPLOYER) return true;

    // Check localStorage for cached role
    try {
      if (user.sub) {
        const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
        if (cachedRoleData) {
          const { role } = JSON.parse(cachedRoleData);
          if (role === UserRole.EMPLOYER) return true;
        }
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  }, [user, isLoading]);

  // Don't render the component if the user role is "employer"
  if (isEmployer) {
    return null;
  }

  return <JobSeekerSetupSliderInner {...props} />;
};
