import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

import StatusTimeline from '../StatusTimeline';
import { CandidateStatus } from '../../../types/candidate.types';
import { UserRole } from '../../../types/roles';

describe('StatusTimeline Component', () => {
  describe('Status Priority Logic', () => {
    test('uses currentStatus prop as authoritative source, not timeline data', () => {
      const statusTimelineData = [
        {
          newStatus: 'MATCHED',
          previousStatus: 'APPLIED',
          timestamp: '2024-01-01T10:00:00Z',
          description: 'Matched',
        },
        {
          newStatus: 'APPLIED', // This would be wrong if used as current status
          previousStatus: 'MATCHED',
          timestamp: '2024-01-02T10:00:00Z',
          description: 'Applied again',
        },
      ];

      render(
        <StatusTimeline
          currentStatus={CandidateStatus.OFFER_EXTENDED}
          isJobSeeker={true}
          statusTimelineData={statusTimelineData}
        />
      );

      // Should use OFFER_EXTENDED (from currentStatus prop), not APPLIED (from timeline)
      // Verify that the component rendered with the correct status
      expect(screen.getByText('Offer Extended')).toBeInTheDocument();
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('correctly identifies job seeker path selection', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.OFFER_EXTENDED} isJobSeeker={true} />);

      // Verify job seeker path is used by checking for APPLIED status
      expect(screen.getByText('Applied')).toBeInTheDocument();
    });

    test('uses common path for non-job seekers', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.OFFER_EXTENDED} isJobSeeker={false} />);

      // Verify common path is used by checking for NEW status instead of APPLIED
      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.queryByText('Applied')).not.toBeInTheDocument();
    });

    test('correctly identifies user role for path selection', () => {
      render(
        <StatusTimeline
          currentStatus={CandidateStatus.OFFER_EXTENDED}
          userRole={UserRole.JOB_SEEKER}
          isJobSeeker={false} // userRole takes precedence
        />
      );

      // Verify job seeker path is used when userRole is JOB_SEEKER
      expect(screen.getByText('Applied')).toBeInTheDocument();
    });
  });

  describe('Job Seeker Hiring Path', () => {
    test('includes OFFER_APPROVED in the progression', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.OFFER_APPROVED} isJobSeeker={true} />);

      // Verify OFFER_APPROVED is displayed as current status
      expect(screen.getByText('Offer Approved')).toBeInTheDocument();
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('correctly finds OFFER_EXTENDED in progression', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.OFFER_EXTENDED} isJobSeeker={true} />);

      // Verify OFFER_EXTENDED is displayed as current status
      expect(screen.getByText('Offer Extended')).toBeInTheDocument();
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('displays correct progression for complete hiring flow', () => {
      const { container } = render(
        <StatusTimeline
          currentStatus={CandidateStatus.OFFER_EXTENDED}
          isJobSeeker={true}
          size="lg"
        />
      );

      // Should show all statuses in order: Applied, Matched, Shortlisted, Interviewing, Offer Pending Approval, Offer Approved, Offer Extended, Offer Accepted, Hired
      const statusElements = container.querySelectorAll('[class*="flex-col items-center"]');
      expect(statusElements).toHaveLength(9); // 9 statuses in job seeker path

      // Check that OFFER_EXTENDED and previous statuses are highlighted
      const circles = container.querySelectorAll('[class*="rounded-full"]');

      // First 7 circles (Applied through Offer Extended) should be highlighted/active
      for (let i = 0; i <= 6; i++) {
        expect(circles[i]).toHaveClass('bg-purple-800'); // Active/past status styling
      }

      // Remaining circles should be future status styling
      for (let i = 7; i < circles.length; i++) {
        expect(circles[i]).toHaveClass('bg-gray-800'); // Future status styling
      }
    });
  });

  describe('Status Highlighting Logic', () => {
    test('highlights all statuses up to current status', () => {
      const { container } = render(
        <StatusTimeline currentStatus={CandidateStatus.SHORTLISTED} isJobSeeker={true} />
      );

      const circles = container.querySelectorAll('[class*="rounded-full"]');

      // Applied (0), Matched (1), Shortlisted (2) should be highlighted
      expect(circles[0]).toHaveClass('bg-purple-800'); // Applied - past
      expect(circles[1]).toHaveClass('bg-purple-800'); // Matched - past
      expect(circles[2]).toHaveClass('bg-purple-800'); // Shortlisted - current

      // Remaining should not be highlighted
      for (let i = 3; i < circles.length; i++) {
        expect(circles[i]).toHaveClass('bg-gray-800');
      }
    });

    test('shows current status indicator', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.INTERVIEWING} isJobSeeker={true} />);

      // Should show "Current" indicator for the active status
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('handles withdrawn status correctly', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.WITHDRAWN} isJobSeeker={true} />);

      // Should show withdrawal warning
      expect(screen.getByText('Application Withdrawn')).toBeInTheDocument();
      expect(screen.getByText(/This application has been withdrawn/)).toBeInTheDocument();
    });
  });

  describe('Status Clicking Functionality', () => {
    test('calls onStatusClick for clickable statuses', () => {
      const mockOnStatusClick = jest.fn();
      const { container } = render(
        <StatusTimeline
          currentStatus={CandidateStatus.MATCHED}
          onStatusClick={mockOnStatusClick}
          isJobSeeker={true}
        />
      );

      // Find the next status (SHORTLISTED) which should be clickable
      const circles = container.querySelectorAll('[class*="rounded-full"]');
      const shortlistedCircle = circles[2]; // Should be SHORTLISTED at index 2

      fireEvent.click(shortlistedCircle);

      expect(mockOnStatusClick).toHaveBeenCalledWith(CandidateStatus.SHORTLISTED);
    });

    test('does not call onStatusClick for past statuses', () => {
      const mockOnStatusClick = jest.fn();
      const { container } = render(
        <StatusTimeline
          currentStatus={CandidateStatus.SHORTLISTED}
          onStatusClick={mockOnStatusClick}
          isJobSeeker={true}
        />
      );

      // Click on APPLIED (past status) - should not trigger callback
      const circles = container.querySelectorAll('[class*="rounded-full"]');
      const appliedCircle = circles[0];

      fireEvent.click(appliedCircle);

      expect(mockOnStatusClick).not.toHaveBeenCalled();
    });
  });

  describe('Timeline Data Integration', () => {
    test('displays timeline data for historical context', () => {
      const statusTimelineData = [
        {
          newStatus: 'MATCHED',
          previousStatus: 'APPLIED',
          timestamp: '2024-01-01T10:00:00Z',
          description: 'Candidate was matched',
        },
        {
          newStatus: 'SHORTLISTED',
          previousStatus: 'MATCHED',
          timestamp: '2024-01-02T10:00:00Z',
          description: 'Candidate was shortlisted',
        },
      ];

      render(
        <StatusTimeline
          currentStatus={CandidateStatus.SHORTLISTED}
          isJobSeeker={true}
          statusTimelineData={statusTimelineData}
        />
      );

      // Verify the timeline displays the current status
      expect(screen.getByText('Shortlisted')).toBeInTheDocument();
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('renders metadata when renderMetadata function provided', () => {
      const statusTimelineData = [
        {
          newStatus: 'MATCHED',
          previousStatus: 'APPLIED',
          timestamp: '2024-01-01T10:00:00Z',
          description: 'Matched',
          metadata: { notes: 'Great candidate!' },
        },
      ];

      const renderMetadata = (metadata: any) => <div data-testid="metadata">{metadata.notes}</div>;

      render(
        <StatusTimeline
          currentStatus={CandidateStatus.MATCHED}
          isJobSeeker={true}
          statusTimelineData={statusTimelineData}
          renderMetadata={renderMetadata}
        />
      );

      expect(screen.getByTestId('metadata')).toHaveTextContent('Great candidate!');
    });
  });

  describe('Edge Cases', () => {
    test('handles status not found in path gracefully', () => {
      // Use a status that's not in the job seeker path
      render(<StatusTimeline currentStatus={CandidateStatus.CONTACTED} isJobSeeker={true} />);

      // CONTACTED is not in job seeker path, so timeline should still render
      // but without showing CONTACTED as a status
      expect(screen.queryByText('Contacted')).not.toBeInTheDocument();
      // Should show the first status in the path
      expect(screen.getByText('Applied')).toBeInTheDocument();
    });

    test('works without timeline data', () => {
      render(<StatusTimeline currentStatus={CandidateStatus.OFFER_EXTENDED} isJobSeeker={true} />);

      // Verify component renders without timeline data
      expect(screen.getByText('Offer Extended')).toBeInTheDocument();
      expect(screen.getByText('Current')).toBeInTheDocument();
    });

    test('works without onStatusClick handler', () => {
      const { container } = render(
        <StatusTimeline currentStatus={CandidateStatus.MATCHED} isJobSeeker={true} />
      );

      // Should render without errors
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Size Variants', () => {
    test('applies correct sizing for different size props', () => {
      const { rerender, container } = render(
        <StatusTimeline currentStatus={CandidateStatus.MATCHED} isJobSeeker={true} size="sm" />
      );

      let circles = container.querySelectorAll('[class*="w-8 h-8"]');
      expect(circles.length).toBeGreaterThan(0);

      rerender(
        <StatusTimeline currentStatus={CandidateStatus.MATCHED} isJobSeeker={true} size="lg" />
      );

      circles = container.querySelectorAll('[class*="w-12 h-12"]');
      expect(circles.length).toBeGreaterThan(0);
    });
  });
});
