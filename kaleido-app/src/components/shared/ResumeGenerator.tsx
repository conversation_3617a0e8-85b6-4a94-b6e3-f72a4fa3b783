import React, { useState } from 'react';

import { <PERSON>, EyeOff, Monitor, Palette, Save, Smartphone } from 'lucide-react';

import apiHelper from '@/lib/apiHelper';
import { StandardizedProfile } from '@/shared/types/profile.types';
import { GraduateProfile } from '@/types/graduate';
import { JobSeekerProfile } from '@/types/jobSeeker';
import { UserRole } from '@/types/roles';
import { ColorTheme, colorThemes, defaultTheme } from '@/types/theme.types';

import { GoogleWalletButton } from '../JobSeeker/GoogleWalletButton';
import JobSeekerProfileStandard from '../JobSeeker/JobSeekerProfileStandard';
import MobileTemplate from './templates/MobileTemplate';

interface ResumeGeneratorProps {
  profile: JobSeekerProfile | GraduateProfile;
  userRole: UserRole.GRADUATE | UserRole.JOB_SEEKER;
  isPublicView?: boolean;
}

// Check if a profile is a GraduateProfile
const isGraduateProfile = (
  profile: JobSeekerProfile | GraduateProfile
): profile is GraduateProfile => {
  return 'university' in profile || 'degreeProgram' in profile || 'major' in profile;
};

export function ResumeGenerator({ profile, userRole, isPublicView = false }: ResumeGeneratorProps) {
  const [selectedTheme, setSelectedTheme] = useState<ColorTheme>(() => {
    const savedThemeId = (profile as any).resumeTheme;
    if (savedThemeId) {
      for (const theme of colorThemes) {
        if (theme.id === savedThemeId) {
          return theme;
        }
      }
    }
    return defaultTheme;
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);
  const [viewMode, setViewMode] = useState<'web' | 'mobile'>('web');
  const [showProfileImage, setShowProfileImage] = useState<boolean>(true);

  const handleViewResume = () => {
    // Navigate to the profile page based on user role and view mode
    if (userRole === 'graduate') {
      window.open(`/graduate/${profile.id}/profile`, '_blank');
    } else if (userRole === UserRole.JOB_SEEKER) {
      if (viewMode === 'mobile') {
        window.open(`/job-seeker/mobile/${profile.id}/profile`, '_blank');
      } else {
        window.open(`/job-seeker/${profile.id}/profile`, '_blank');
      }
    } else {
      // Fallback to the original URL format
      window.open(`/candidate/${profile.id}/profile`, '_blank');
    }
  };

  const handleSaveResume = async () => {
    if (!profile) return;

    setIsSaving(true);
    setSaveMessage(null);

    try {
      // Get the current resume preview HTML
      const previewElement = document.querySelector('.resume-preview');
      const currentHtml = previewElement?.innerHTML || '';

      const endpoint = userRole === 'graduate' ? '/graduates/profile' : '/job-seekers/profile';

      // Prepare the data to save based on current view mode
      const saveData: any = {
        resumeTheme: selectedTheme.id, // Save the selected theme ID
      };

      if (viewMode === 'web') {
        // Save web template
        saveData.resumeTemplate = 'standard';
        saveData.resumeHtml = currentHtml;
      } else {
        // Save mobile template
        saveData.resumeMobileTemplate = 'standard';
        saveData.resumeMobileHtml = currentHtml;
      }

      await apiHelper.patch(endpoint, saveData);

      setSaveMessage({
        type: 'success',
        text: `${viewMode === 'web' ? 'Web' : 'Mobile'} resume saved successfully!`,
      });

      // Clear success message after 3 seconds
      setTimeout(() => setSaveMessage(null), 3000);
    } catch (error) {
      console.error('Error saving resume:', error);
      setSaveMessage({
        type: 'error',
        text: 'Failed to save resume. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Convert profile to StandardizedProfile format for JobSeekerProfileStandard
  const standardizedProfile: StandardizedProfile = {
    id: profile.id,
    userId: profile.userId || '',
    firstName: profile.firstName,
    lastName: profile.lastName,
    fullName: `${profile.firstName} ${profile.lastName}`,
    email: profile.email,
    phone: profile.phone,
    location: profile.location,
    myProfileImage:
      'profileImage' in profile
        ? (profile.profileImage as string)
        : (profile.myProfileImage as string),
    summary: profile.summary,
    skills: profile.skills || [],
    experience: isGraduateProfile(profile)
      ? (profile.internships || []).map(intern => ({
          title: intern.role || '',
          company: intern.company || '',
          startDate: intern.startDate ? intern.startDate.toString() : '',
          endDate: intern.endDate ? intern.endDate.toString() : '',
          duration: intern.duration || '',
          description: intern.description || '',
          achievements: intern.achievements || [],
        }))
      : profile.experience || [],
    education: isGraduateProfile(profile)
      ? [
          {
            fieldOfStudy: profile.major || '',
            institution: profile.university || '',
            degree: profile.degreeProgram || '',
            field: profile.major || '',
            startDate: '',
            endDate: profile.graduationYear || '',
            description: '',
          },
        ]
      : profile.education || [],
    achievements: profile.achievements || [],
    recommendations: profile.recommendations || [],
    resumeUrl: profile.resumeUrl,
    linkedinUrl: profile.linkedinUrl,
    githubUrl: profile.githubUrl,
    portfolioUrl: 'portfolioUrl' in profile ? profile.portfolioUrl : undefined,
    workAvailability: {
      immediatelyAvailable: false,
    },
  };

  // Render the appropriate preview based on view mode
  const renderPreview = () => {
    // Convert our simplified theme to the format expected by components
    const compatibleTheme = {
      ...selectedTheme,
      gradient: selectedTheme.isDark ? 'from-gray-800 to-gray-900' : 'from-gray-50 to-white',
    };

    if (viewMode === 'mobile') {
      // Prepare data for MobileTemplate
      const socialLinks = {
        linkedin: profile.linkedinUrl,
        github: profile.githubUrl,
        portfolio: 'portfolioUrl' in profile ? profile.portfolioUrl : undefined,
      };

      const experiences = isGraduateProfile(profile)
        ? (profile.internships || []).map(intern => ({
            role: intern.role || '',
            title: intern.role || '',
            company: intern.company || '',
            startDate: intern.startDate,
            endDate: intern.endDate,
            duration: intern.duration || '',
            description: intern.description || '',
            achievements: intern.achievements || [],
          }))
        : profile.experience || [];

      const educationDisplay = isGraduateProfile(profile)
        ? {
            institution: profile.university || '',
            degree: profile.degreeProgram || '',
            field: profile.major || '',
            timeline: profile.graduationYear || '',
            description: null,
            location: null,
          }
        : profile.education?.[0]
          ? {
              institution: profile.education[0].institution || '',
              degree: profile.education[0].degree || '',
              field: profile.education[0].field || '',
              timeline: `${profile.education[0].startDate || ''} - ${profile.education[0].endDate || ''}`,
              description: profile.education[0].description || null,
              location: null,
            }
          : null;

      return (
        <MobileTemplate
          profile={profile}
          selectedTheme={compatibleTheme}
          socialLinks={socialLinks}
          experiences={experiences}
          educationDisplay={educationDisplay}
          isGraduateProfile={isGraduateProfile}
          showProfileImage={showProfileImage}
        />
      );
    }

    // Web view - use JobSeekerProfileStandard
    return (
      <JobSeekerProfileStandard
        profile={standardizedProfile}
        theme={compatibleTheme}
        showProfileImage={showProfileImage}
      />
    );
  };

  return (
    <div className="w-full space-y-4 text-white">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 lg:gap-6">
        {/* Left Column: Compact Control Panel */}
        {!isPublicView && (
          <div className="lg:col-span-3">
            <div className="bg-gradient-to-br from-white/10 to-transparent rounded-xl p-3 border border-white/10">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Resume Controls</h3>
                {saveMessage && (
                  <div
                    className={`text-xs py-1 px-2 rounded ${saveMessage.type === 'success' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}
                  >
                    {saveMessage.text}
                  </div>
                )}
              </div>

              {/* Color Theme Selection - Simplified */}
              <div className="mb-3">
                <div className="flex items-center justify-between gap-1.5 mb-1.5">
                  <div className="flex items-center gap-1.5">
                    <Palette className="w-3 h-3 text-green-400" />
                    <h4 className="text-xs font-medium">Color Theme</h4>
                  </div>
                  <div className="text-xs text-gray-400">{selectedTheme.name}</div>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {colorThemes.map(theme => (
                    <div
                      key={theme.id}
                      title={theme.name}
                      className={`
                        aspect-square rounded-lg cursor-pointer
                        border transition-all duration-200
                        ${theme.primary}
                        ${
                          selectedTheme.id === theme.id
                            ? 'ring-2 ring-white/60 border-white/80 scale-105'
                            : 'border-white/20 hover:border-white/40 hover:scale-102'
                        }
                      `}
                      onClick={() => setSelectedTheme(theme)}
                    />
                  ))}
                </div>
              </div>

              {/* Toggle Controls Group */}
              <div className="grid grid-cols-2 gap-2 mb-3">
                {/* View Mode Toggle */}
                <div>
                  <div className="flex items-center gap-1.5 mb-1.5">
                    <Monitor className="w-3 h-3 text-blue-400" />
                    <h4 className="text-xs font-medium">View</h4>
                  </div>
                  <div className="flex gap-1 h-7">
                    <button
                      type="button"
                      onClick={() => setViewMode('web')}
                      className={`
                        flex-1 flex items-center justify-center text-xs rounded
                        transition-all duration-200
                        ${
                          viewMode === 'web'
                            ? 'bg-blue-500/30 border border-blue-500/50'
                            : 'bg-white/5 border border-white/10 hover:bg-white/10'
                        }
                      `}
                    >
                      <Monitor className="w-3 h-3 mr-1" />
                      Web
                    </button>
                    <button
                      type="button"
                      onClick={() => setViewMode('mobile')}
                      className={`
                        flex-1 flex items-center justify-center text-xs rounded
                        transition-all duration-200
                        ${
                          viewMode === 'mobile'
                            ? 'bg-blue-500/30 border border-blue-500/50'
                            : 'bg-white/5 border border-white/10 hover:bg-white/10'
                        }
                      `}
                    >
                      <Smartphone className="w-3 h-3 mr-1" />
                      Mobile
                    </button>
                  </div>
                </div>

                {/* Profile Image Toggle */}
                <div>
                  <div className="flex items-center gap-1.5 mb-1.5">
                    <Eye className="w-3 h-3 text-amber-400" />
                    <h4 className="text-xs font-medium">Photo</h4>
                  </div>
                  <button
                    type="button"
                    onClick={() => setShowProfileImage(!showProfileImage)}
                    className={`
                      w-full h-7 flex items-center justify-center text-xs rounded
                      transition-all duration-200
                      ${
                        showProfileImage
                          ? 'bg-amber-500/30 border border-amber-500/50'
                          : 'bg-white/5 border border-white/10 hover:bg-white/10'
                      }
                    `}
                  >
                    {showProfileImage ? (
                      <>
                        <EyeOff className="w-3 h-3 mr-1" />
                        Hide
                      </>
                    ) : (
                      <>
                        <Eye className="w-3 h-3 mr-1" />
                        Show
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <button
                    type="button"
                    onClick={handleViewResume}
                    className="flex items-center justify-center gap-1 py-1.5 rounded text-xs bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200"
                  >
                    <Eye className="w-3 h-3" />
                    View Profile
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveResume}
                    className="flex items-center justify-center gap-1 py-1.5 rounded text-xs bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200"
                    disabled={isSaving}
                  >
                    <Save className="w-3 h-3" />
                    {isSaving ? 'Saving...' : 'Save Resume'}
                  </button>
                </div>
                {userRole === UserRole.JOB_SEEKER && (
                  <GoogleWalletButton
                    size="sm"
                    variant="secondary"
                    className="w-full text-xs py-1.5"
                  />
                )}
              </div>
            </div>
          </div>
        )}

        {/* Right Column: Preview */}
        <div className={isPublicView ? 'col-span-1 lg:col-span-12' : 'lg:col-span-9'}>
          <div className="bg-gradient-to-br from-white/10 to-transparent rounded-xl p-3 lg:p-4 border border-white/10">
            {!isPublicView && (
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm lg:text-base font-medium">Resume Preview</h3>
                <div className="text-xs text-white/70">
                  {viewMode === 'web' ? 'Web View' : 'Mobile View'}
                </div>
              </div>
            )}
            <div
              className={`bg-white rounded-lg shadow-xl overflow-auto max-h-[80vh] resume-preview ${
                viewMode === 'mobile' ? 'max-w-sm mx-auto' : 'max-w-4xl mx-auto'
              }`}
            >
              {renderPreview()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
