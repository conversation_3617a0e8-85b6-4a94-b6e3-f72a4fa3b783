import React from 'react';

import { AlertCircle, CheckCircle } from 'lucide-react';

import { CandidateStatus } from '@/types/candidate.types';
import { UserRole } from '@/types/roles';

import { commonHiringPath, getStatusInfo, jobSeekerHiringPath } from './StatusUtils';

interface StatusTimelineProps {
  currentStatus: CandidateStatus;
  onStatusClick?: (status: CandidateStatus) => void;
  size?: 'sm' | 'md' | 'lg';
  showAllStatuses?: boolean;
  statusTimelineData?: Array<{
    newStatus: string;
    previousStatus: string;
    timestamp: string;
    description: string;
    metadata?: {
      jobId?: string;
      notes?: string;
    };
  }>;
  renderMetadata?: (metadata: any) => React.ReactNode;
  userRole?: UserRole; // Added to determine which statuses to show
  isJobSeeker?: boolean; // Alternative flag to determine if user is a job seeker
}

// Export getStatusInfo from StatusUtils to maintain backward compatibility
export { getStatusInfo } from './StatusUtils';

const StatusTimeline: React.FC<StatusTimelineProps> = ({
  currentStatus,
  onStatusClick,
  size = 'md',
  showAllStatuses = false,
  statusTimelineData = [],
  renderMetadata,
  userRole,
  isJobSeeker = false,
}) => {
  // Use the provided currentStatus as the authoritative current state
  // Timeline data is used for historical information, not to override current status
  const effectiveCurrentStatus = currentStatus;

  // Determine if we should use the job seeker path
  const useJobSeekerPath = isJobSeeker || userRole === UserRole.JOB_SEEKER;

  // Define the statuses in our timeline based on user role and showAllStatuses flag
  const allStatuses = showAllStatuses
    ? Object.values(CandidateStatus)
    : useJobSeekerPath
      ? jobSeekerHiringPath
      : commonHiringPath;

  // Find the index of the current status
  const currentStatusIndex = allStatuses.findIndex(s => s === effectiveCurrentStatus);
  const effectiveIndex = currentStatusIndex === -1 ? 0 : currentStatusIndex;

  // Determine sizes based on the size prop
  const circleSize = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  }[size];

  const iconSize = {
    sm: 12,
    md: 16,
    lg: 20,
  }[size];

  const textSize = {
    sm: 'text-[9px]',
    md: 'text-[10px]',
    lg: 'text-xs',
  }[size];

  // Function to find status timeline info for a particular status
  const findStatusTimelineInfo = (status: CandidateStatus) => {
    return statusTimelineData.find(item => item.newStatus === status);
  };

  // Check if the application is withdrawn
  const isWithdrawn = effectiveCurrentStatus === CandidateStatus.WITHDRAWN;

  return (
    <div className="relative pt-2 pb-1">
      {isWithdrawn && (
        <div className="mb-3 p-2 bg-red-900/10 border border-red-800/5 rounded-md flex items-start gap-2">
          <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
          <div className="text-xs text-red-300">
            <p className="font-medium">Application Withdrawn</p>
            <p className="mt-0.5 text-white/50">
              This application has been withdrawn. No further updates will be made to the status.
            </p>
          </div>
        </div>
      )}
      <div className="flex w-full justify-between overflow-x-auto relative">
        {/* Connecting lines between circles */}
        <div className="absolute h-0.5 top-5 left-5 right-5 bg-gray-700 z-0"></div>
        {allStatuses.map((_, index) => {
          if (index === allStatuses.length - 1) return null;

          const isPast = index < effectiveIndex;

          return (
            <div
              key={`line-${index}`}
              className={`absolute h-0.5 top-5 z-0 ${isPast ? 'bg-purple-800' : 'bg-gray-700'}`}
              style={{
                left: `calc(${index * (100 / (allStatuses.length - 1))}% + 5px)`,
                width: `calc(${100 / (allStatuses.length - 1)}% - 10px)`,
              }}
            />
          );
        })}

        {allStatuses.map((statusItem, index) => {
          const isActive = statusItem === effectiveCurrentStatus;
          const isPast = effectiveIndex > index;
          const info = getStatusInfo(statusItem);
          const isFuture = index > effectiveIndex;
          const isClickable =
            onStatusClick && !isPast && (index === effectiveIndex || index === effectiveIndex + 1);

          // Find timeline data for this status if available
          const timelineInfo = findStatusTimelineInfo(statusItem);

          return (
            <div
              key={statusItem}
              className={`flex flex-col items-center min-w-[60px] max-w-[90px] flex-1 ${isPast || isActive ? 'opacity-100' : 'opacity-70'} relative z-10`}
            >
              <div
                onClick={() => isClickable && onStatusClick(statusItem)}
                className={`flex items-center justify-center ${circleSize} rounded-full border-2
                  ${
                    isActive
                      ? 'bg-purple-800 border-purple-400'
                      : isPast
                        ? 'bg-purple-800 border-purple-700'
                        : 'bg-gray-800 border-gray-700'
                  }
                  ${isClickable ? 'cursor-pointer hover:border-purple-400 hover:scale-110 transition-all' : ''}
                  ${isPast && onStatusClick ? 'cursor-not-allowed opacity-60' : ''}
                `}
              >
                <div className="text-white">
                  {React.cloneElement(info.icon as React.ReactElement, { size: iconSize })}
                </div>
              </div>

              <span
                className={`${textSize} leading-tight font-medium mt-1 text-center break-words w-full px-0.5
                ${isActive ? 'text-white' : isPast ? 'text-gray-300' : 'text-gray-500'}`}
              >
                {info.label}
              </span>

              {isActive && (
                <div className="flex items-center mt-0.5 text-[9px] text-purple-400 font-medium">
                  <CheckCircle size={7} className="mr-0.5" />
                  <span>Current</span>
                </div>
              )}

              {/* Render metadata if provided and we have timeline info */}
              {renderMetadata && timelineInfo && timelineInfo.metadata && (
                <div className="mt-1">{renderMetadata(timelineInfo.metadata)}</div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StatusTimeline;
