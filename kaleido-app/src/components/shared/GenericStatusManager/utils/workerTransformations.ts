export interface TransformationDetail {
  id: string;
  name: string;
  value: string;
  status: string;
  type: string;
  message?: string;
  tier?: string;
  score?: number;
  location?: string;
  company?: string;
  url?: string;
}

export interface TransformationStats {
  [key: string]: number | string;
}

export interface TransformationResult {
  details: TransformationDetail[];
  stats: TransformationStats;
  description: string;
}

/**
 * Extract details from job result based on action type
 */
export const extractDetailsFromResult = (
  result: any,
  actionType: string
): TransformationDetail[] => {
  if (!result) return [];

  switch (actionType) {
    case 'upload':
      return extractUploadDetails(result);
    case 'matchRank':
      return extractMatchRankDetails(result);
    case 'scout':
      return extractScoutDetails(result);
    case 'videoJD':
    case 'videoGeneration':
    case 'videoDownload':
      return extractVideoDetails(result);
    case 'careerInsight':
      return extractCareerInsightDetails(result);
    default:
      return [];
  }
};

/**
 * Build completion stats based on job type and result
 */
export const buildCompletionStats = (
  managerId: string,
  result: any,
  details: TransformationDetail[]
): TransformationStats => {
  const stats: TransformationStats = {};

  switch (managerId) {
    case 'upload':
      return buildUploadStats(result, details);
    case 'scout':
      return buildScoutStats(result, details);
    case 'matchRank':
      return buildMatchRankStats(result, details);
    case 'videoJD':
    case 'videoGeneration':
    case 'videoDownload':
      return buildVideoStats(details);
    case 'careerInsight':
      return buildCareerInsightStats(details);
    default:
      return stats;
  }
};

/**
 * Generate completion description based on job type and result
 */
export const generateCompletionDescription = (
  managerId: string,
  result: any,
  details: TransformationDetail[]
): string => {
  switch (managerId) {
    case 'upload':
      return generateUploadDescription(result);
    case 'scout':
      return generateScoutDescription(result);
    case 'matchRank':
      return generateMatchRankDescription(result, details);
    case 'videoJD':
    case 'videoGeneration':
      return 'Video job description generated successfully.';
    case 'videoDownload':
      return 'Video downloaded successfully.';
    case 'careerInsight':
      return generateCareerInsightDescription(details);
    default:
      return 'Process completed successfully.';
  }
};

// Upload specific functions
const extractUploadDetails = (result: any): TransformationDetail[] => {
  const fileDetails: TransformationDetail[] = [];

  // Check multiple possible locations for candidates
  const candidates =
    result.result?.candidates || result.candidates || result.data?.candidates || [];
  const uploadData = result.data || result.result || result;

  // Add successful candidates
  if (candidates.length > 0) {
    candidates.forEach((candidate: any) => {
      fileDetails.push({
        id: candidate.id || `file-${Math.random().toString(36).substring(2, 9)}`,
        name:
          candidate.originalFilename ||
          candidate.filename ||
          candidate.name ||
          candidate.fullName ||
          'Unknown File',
        value: 'Processed',
        status: 'success',
        type: 'candidate',
      });
    });
  }

  // Add duplicate info
  if (uploadData.duplicateInfo?.length > 0) {
    uploadData.duplicateInfo.forEach((dup: any) => {
      fileDetails.push({
        id: `dup-${Math.random().toString(36).substring(2, 9)}`,
        name: dup.filename || 'Unknown File',
        value: 'Duplicate',
        status: 'duplicate',
        message: dup.message || 'File already exists',
        type: 'duplicate',
      });
    });
  }

  // If no details found, try to extract from other possible locations
  if (fileDetails.length === 0) {
    // Check for files array
    if (uploadData.files?.length > 0) {
      uploadData.files.forEach((file: any) => {
        fileDetails.push({
          id: file.id || `file-${Math.random().toString(36).substring(2, 9)}`,
          name: file.filename || file.originalname || file.name || 'Unknown File',
          value: file.status || 'Processed',
          status: file.status || 'success',
          type: file.type || 'file',
          message: file.message,
        });
      });
    }

    // Check for results array
    if (uploadData.results?.length > 0) {
      uploadData.results.forEach((result: any) => {
        fileDetails.push({
          id: result.id || `result-${Math.random().toString(36).substring(2, 9)}`,
          name: result.filename || result.name || 'Unknown File',
          value: result.status || 'Processed',
          status: result.status || 'success',
          type: 'candidate',
          message: result.message,
        });
      });
    }
  }

  return fileDetails;
};

const buildUploadStats = (result: any, _details: TransformationDetail[]): TransformationStats => {
  const stats: TransformationStats = {};

  // Try different locations for stats in the result
  const successCount =
    result.result?.successCount || result.successCount || result.data?.successCount || 0;
  const failedCount =
    result.result?.failedCount || result.failedCount || result.data?.failedCount || 0;
  const duplicateCount =
    result.result?.duplicateCount || result.duplicateCount || result.data?.duplicateCount || 0;
  const totalFiles =
    result.result?.totalFiles ||
    result.totalFiles ||
    result.data?.totalFiles ||
    successCount + failedCount ||
    1;

  stats['Total Files'] = totalFiles || 1;
  stats['Successful'] = successCount;
  if (failedCount > 0) stats['Failed'] = failedCount;
  if (duplicateCount > 0) stats['Duplicates'] = duplicateCount;

  return stats;
};

const generateUploadDescription = (result: any): string => {
  const successCount =
    result.result?.successCount || result.successCount || result.data?.successCount || 0;
  const failedCount =
    result.result?.failedCount || result.failedCount || result.data?.failedCount || 0;
  const duplicateCount =
    result.result?.duplicateCount || result.duplicateCount || result.data?.duplicateCount || 0;

  if (successCount === 0 && failedCount === 0) {
    return 'No files were processed.';
  }

  let desc = `Successfully processed ${successCount} candidate file${successCount !== 1 ? 's' : ''}`;
  if (failedCount > 0) {
    desc += ` (${failedCount} failed)`;
  }
  if (duplicateCount > 0) {
    desc += ` (${duplicateCount} duplicate${duplicateCount !== 1 ? 's' : ''})`;
  }
  return desc;
};

// Match Rank specific functions
const extractMatchRankDetails = (result: any): TransformationDetail[] => {
  // Check multiple possible locations for results - updated to include matchedCandidateDetails
  const matchResults =
    result.result?.matchedCandidateDetails ||
    result.matchedCandidateDetails ||
    result.result?.results ||
    result.results ||
    result.result?.matchedCandidates ||
    result.matchedCandidates ||
    [];

  if (matchResults.length > 0) {
    return matchResults.map((candidate: any) => ({
      id:
        candidate.id ||
        candidate.candidateId ||
        `candidate-${Math.random().toString(36).substring(2, 9)}`,
      name: candidate.name || candidate.candidateName || candidate.fullName || 'Unknown Candidate',
      value:
        candidate.matchScore !== undefined
          ? `${Math.round(candidate.matchScore > 1 ? candidate.matchScore : candidate.matchScore * 100)}% match`
          : candidate.tier || 'Matched',
      status: candidate.success !== false ? 'success' : 'failed',
      type: 'candidate',
      tier: candidate.tier,
      score: candidate.matchScore,
    }));
  }
  return [];
};

const buildMatchRankStats = (result: any, details: TransformationDetail[]): TransformationStats => {
  const stats: TransformationStats = {};

  // Use the correct field names from the match rank result
  const totalCandidates = result.result?.totalCandidates || result.totalCandidates || 0;
  const matchedCandidates =
    result.result?.matchedCandidates || result.matchedCandidates || details.length || 0;
  const failedCandidates = result.result?.failedCandidates || result.failedCandidates || 0;

  // Check for summary data for enhanced stats
  const summary = result.result?.summary || result.summary;

  stats['Total Candidates'] = totalCandidates;
  stats['Matched'] = matchedCandidates;

  // Add tier breakdown if available
  if (summary) {
    if (summary.topTierCount > 0) stats['Top Tier'] = summary.topTierCount;
    if (summary.secondTierCount > 0) stats['Second Tier'] = summary.secondTierCount;
    if (summary.averageMatchScore > 0) stats['Avg Match Score'] = `${summary.averageMatchScore}%`;
  }

  if (failedCandidates > 0) stats['Failed'] = failedCandidates;

  return stats;
};

const generateMatchRankDescription = (result: any, details: TransformationDetail[]): string => {
  const matchedCandidates =
    result.result?.matchedCandidates || result.matchedCandidates || details.length || 0;
  const totalCandidates = result.result?.totalCandidates || result.totalCandidates || 0;

  if (matchedCandidates === 0) {
    return 'No candidates were matched.';
  }

  let desc = `Successfully matched and ranked ${matchedCandidates} candidate${matchedCandidates !== 1 ? 's' : ''}`;
  if (totalCandidates > 0) {
    desc += ` out of ${totalCandidates} total`;
  }
  return desc;
};

// Scout specific functions
const extractScoutDetails = (result: any): TransformationDetail[] => {
  // Check multiple possible locations for results
  const scoutResults = result.result?.results || result.results || result.data?.results || [];

  if (scoutResults.length > 0) {
    return scoutResults.map((profile: any) => ({
      id:
        profile.scoutedCandidateId ||
        profile.candidateData?.id ||
        `profile-${Math.random().toString(36).substring(2, 9)}`,
      name: profile.profileName || profile.candidateData?.fullName || 'Unknown Profile',
      value: profile.candidateData?.jobTitle || profile.candidateData?.currentCompany || 'Scouted',
      status: profile.success ? 'success' : 'failed',
      type: 'profile',
      location: profile.candidateData?.location,
      company: profile.candidateData?.currentCompany,
    }));
  }
  return [];
};

const buildScoutStats = (result: any, details: TransformationDetail[]): TransformationStats => {
  const stats: TransformationStats = {};

  const successCount =
    result.result?.successCount || result.successCount || result.data?.successCount || 0;
  const failedCount =
    result.result?.failedCount || result.failedCount || result.data?.failedCount || 0;
  const duplicateCount =
    result.result?.duplicateCount || result.duplicateCount || result.data?.duplicateCount || 0;
  const totalProfiles = successCount + failedCount + duplicateCount;

  stats['Total Profiles'] = totalProfiles || details.length || 0;
  stats['Scouted'] = successCount;
  if (failedCount > 0) stats['Failed'] = failedCount;
  if (duplicateCount > 0) stats['Duplicates'] = duplicateCount;

  return stats;
};

const generateScoutDescription = (result: any): string => {
  const successCount =
    result.result?.successCount || result.successCount || result.data?.successCount || 0;
  const failedCount =
    result.result?.failedCount || result.failedCount || result.data?.failedCount || 0;
  const duplicateCount =
    result.result?.duplicateCount || result.duplicateCount || result.data?.duplicateCount || 0;

  if (successCount === 0 && failedCount === 0) {
    return 'No profiles were scouted.';
  }

  let desc = `Successfully scouted ${successCount} profile${successCount !== 1 ? 's' : ''}`;
  if (failedCount > 0) {
    desc += ` (${failedCount} failed)`;
  }
  if (duplicateCount > 0) {
    desc += ` (${duplicateCount} duplicate${duplicateCount !== 1 ? 's' : ''})`;
  }
  return desc;
};

// Video specific functions
const extractVideoDetails = (result: any): TransformationDetail[] => {
  const videoData = result.result || result;
  const videoDetails: TransformationDetail[] = [];

  if (videoData.videoUrl || videoData.downloadUrl) {
    videoDetails.push({
      id: videoData.videoId || `video-${Math.random().toString(36).substring(2, 9)}`,
      name: videoData.title || videoData.filename || 'Video',
      value: videoData.status || 'Completed',
      status: 'success',
      type: 'video',
      url: videoData.videoUrl || videoData.downloadUrl,
    });
  }

  return videoDetails;
};

const buildVideoStats = (details: TransformationDetail[]): TransformationStats => {
  const stats: TransformationStats = {};

  stats['Status'] = 'Completed';
  if (details.length > 0) {
    stats['Videos'] = details.length;
  }

  return stats;
};

// Career Insight specific functions
const extractCareerInsightDetails = (result: any): TransformationDetail[] => {
  const insightData = result.result || result;
  const insightDetails: TransformationDetail[] = [];

  if (insightData.reportId || insightData.insights) {
    insightDetails.push({
      id: insightData.reportId || `insight-${Math.random().toString(36).substring(2, 9)}`,
      name: insightData.candidateName || 'Career Insight Report',
      value: 'Generated',
      status: 'success',
      type: 'report',
    });
  }

  return insightDetails;
};

const buildCareerInsightStats = (details: TransformationDetail[]): TransformationStats => {
  const stats: TransformationStats = {};
  stats['Reports Generated'] = details.length || 1;
  return stats;
};

const generateCareerInsightDescription = (details: TransformationDetail[]): string => {
  const count = details.length || 1;
  return `Generated ${count} career insight report${count !== 1 ? 's' : ''}.`;
};
