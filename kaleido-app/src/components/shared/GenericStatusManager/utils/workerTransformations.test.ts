import {
  extractDetailsFromResult,
  buildCompletionStats,
  generateCompletionDescription,
  TransformationDetail,
} from './workerTransformations';

describe('workerTransformations - Video Generation Support', () => {
  describe('extractDetailsFromResult', () => {
    it('should extract video details for videoGeneration action', () => {
      const result = {
        videoUrl: 'https://example.com/video.mp4',
        videoId: 'video-123',
        title: 'Job Description Video',
        status: 'completed',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details).toHaveLength(1);
      expect(details[0]).toEqual({
        id: 'video-123',
        name: 'Job Description Video',
        value: 'completed',
        status: 'success',
        type: 'video',
        url: 'https://example.com/video.mp4',
      });
    });

    it('should extract video details for videoJD action', () => {
      const result = {
        videoUrl: 'https://example.com/video.mp4',
        videoId: 'video-456',
        filename: 'custom-video.mp4',
      };

      const details = extractDetailsFromResult(result, 'videoJD');

      expect(details).toHaveLength(1);
      expect(details[0]).toEqual({
        id: 'video-456',
        name: 'custom-video.mp4',
        value: 'Completed',
        status: 'success',
        type: 'video',
        url: 'https://example.com/video.mp4',
      });
    });

    it('should handle nested result structure', () => {
      const result = {
        result: {
          videoUrl: 'https://example.com/nested-video.mp4',
          videoId: 'nested-video-789',
          title: 'Nested Video',
        },
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details).toHaveLength(1);
      expect(details[0]).toEqual({
        id: 'nested-video-789',
        name: 'Nested Video',
        value: 'Completed',
        status: 'success',
        type: 'video',
        url: 'https://example.com/nested-video.mp4',
      });
    });

    it('should prefer videoUrl over downloadUrl', () => {
      const result = {
        videoUrl: 'https://example.com/video.mp4',
        downloadUrl: 'https://example.com/download.mp4',
        videoId: 'video-choice',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details[0].url).toBe('https://example.com/video.mp4');
    });

    it('should use downloadUrl when videoUrl is not available', () => {
      const result = {
        downloadUrl: 'https://example.com/download.mp4',
        videoId: 'video-download',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details[0].url).toBe('https://example.com/download.mp4');
    });

    it('should generate random ID when videoId is missing', () => {
      const result = {
        videoUrl: 'https://example.com/video.mp4',
        title: 'Video without ID',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details[0].id).toMatch(/^video-[a-z0-9]{7}$/);
      expect(details[0].name).toBe('Video without ID');
    });

    it('should use default values when optional fields are missing', () => {
      const result = {
        videoUrl: 'https://example.com/minimal-video.mp4',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details[0].name).toBe('Video');
      expect(details[0].value).toBe('Completed');
      expect(details[0].status).toBe('success');
      expect(details[0].type).toBe('video');
    });

    it('should return empty array when no video URL is provided', () => {
      const result = {
        videoId: 'video-no-url',
        title: 'Video without URL',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');

      expect(details).toHaveLength(0);
    });

    it('should handle videoDownload action', () => {
      const result = {
        downloadUrl: 'https://example.com/download.mp4',
        filename: 'download-file.mp4',
      };

      const details = extractDetailsFromResult(result, 'videoDownload');

      expect(details).toHaveLength(1);
      expect(details[0].name).toBe('download-file.mp4');
      expect(details[0].url).toBe('https://example.com/download.mp4');
    });

    it('should return empty array for non-video actions', () => {
      const result = {
        videoUrl: 'https://example.com/video.mp4',
      };

      const details = extractDetailsFromResult(result, 'upload');

      expect(details).toHaveLength(0);
    });
  });

  describe('buildCompletionStats', () => {
    it('should build video stats for videoGeneration', () => {
      const details: TransformationDetail[] = [
        {
          id: 'video-1',
          name: 'Video 1',
          value: 'Completed',
          status: 'success',
          type: 'video',
          url: 'https://example.com/video1.mp4',
        },
        {
          id: 'video-2',
          name: 'Video 2',
          value: 'Completed',
          status: 'success',
          type: 'video',
          url: 'https://example.com/video2.mp4',
        },
      ];

      const stats = buildCompletionStats('videoGeneration', {}, details);

      expect(stats).toEqual({
        Status: 'Completed',
        Videos: 2,
      });
    });

    it('should build video stats for videoJD', () => {
      const details: TransformationDetail[] = [
        {
          id: 'video-1',
          name: 'Video 1',
          value: 'Completed',
          status: 'success',
          type: 'video',
          url: 'https://example.com/video1.mp4',
        },
      ];

      const stats = buildCompletionStats('videoJD', {}, details);

      expect(stats).toEqual({
        Status: 'Completed',
        Videos: 1,
      });
    });

    it('should build video stats for videoDownload', () => {
      const details: TransformationDetail[] = [
        {
          id: 'video-1',
          name: 'Downloaded Video',
          value: 'Completed',
          status: 'success',
          type: 'video',
          url: 'https://example.com/download.mp4',
        },
      ];

      const stats = buildCompletionStats('videoDownload', {}, details);

      expect(stats).toEqual({
        Status: 'Completed',
        Videos: 1,
      });
    });

    it('should handle empty details for video actions', () => {
      const stats = buildCompletionStats('videoGeneration', {}, []);

      expect(stats).toEqual({
        Status: 'Completed',
      });
    });

    it('should return empty stats for non-video actions', () => {
      const details: TransformationDetail[] = [
        {
          id: 'video-1',
          name: 'Video 1',
          value: 'Completed',
          status: 'success',
          type: 'video',
          url: 'https://example.com/video1.mp4',
        },
      ];

      const stats = buildCompletionStats('unknown', {}, details);

      expect(stats).toEqual({});
    });
  });

  describe('generateCompletionDescription', () => {
    it('should generate description for videoGeneration', () => {
      const description = generateCompletionDescription('videoGeneration', {}, []);

      expect(description).toBe('Video job description generated successfully.');
    });

    it('should generate description for videoJD', () => {
      const description = generateCompletionDescription('videoJD', {}, []);

      expect(description).toBe('Video job description generated successfully.');
    });

    it('should generate description for videoDownload', () => {
      const description = generateCompletionDescription('videoDownload', {}, []);

      expect(description).toBe('Video downloaded successfully.');
    });

    it('should generate default description for unknown actions', () => {
      const description = generateCompletionDescription('unknown', {}, []);

      expect(description).toBe('Process completed successfully.');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete video generation workflow', () => {
      const result = {
        videoUrl: 'https://synthesia.io/videos/abc-123.mp4',
        videoId: 'synthesia-abc-123',
        title: 'Sales Manager Job Description',
        status: 'completed',
        metadata: {
          duration: 120,
          resolution: '1920x1080',
        },
      };

      // Extract details
      const details = extractDetailsFromResult(result, 'videoGeneration');
      expect(details).toHaveLength(1);
      expect(details[0].url).toBe('https://synthesia.io/videos/abc-123.mp4');

      // Build stats
      const stats = buildCompletionStats('videoGeneration', result, details);
      expect(stats.Status).toBe('Completed');
      expect(stats.Videos).toBe(1);

      // Generate description
      const description = generateCompletionDescription('videoGeneration', result, details);
      expect(description).toBe('Video job description generated successfully.');
    });

    it('should handle video generation with missing optional fields', () => {
      const result = {
        videoUrl: 'https://example.com/minimal.mp4',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');
      expect(details[0].name).toBe('Video');
      expect(details[0].value).toBe('Completed');

      const stats = buildCompletionStats('videoGeneration', result, details);
      expect(stats.Videos).toBe(1);

      const description = generateCompletionDescription('videoGeneration', result, details);
      expect(description).toBe('Video job description generated successfully.');
    });

    it('should handle failed video generation (no video URL)', () => {
      const result = {
        error: 'Video generation failed',
        status: 'failed',
      };

      const details = extractDetailsFromResult(result, 'videoGeneration');
      expect(details).toHaveLength(0);

      const stats = buildCompletionStats('videoGeneration', result, details);
      expect(stats.Status).toBe('Completed');
      expect(stats.Videos).toBeUndefined();

      const description = generateCompletionDescription('videoGeneration', result, details);
      expect(description).toBe('Video job description generated successfully.');
    });
  });
});
