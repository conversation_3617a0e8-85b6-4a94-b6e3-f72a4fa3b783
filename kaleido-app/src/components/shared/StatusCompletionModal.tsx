'use client';

import React from 'react';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Eye, RefreshCw, Trophy, X } from 'lucide-react';
import { usePathname } from 'next/navigation';

export interface StatusCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  status: 'success' | 'error' | 'warning' | 'info';
  stats?: {
    [key: string]: number | string;
  };
  details?: Array<{
    id: string;
    name?: string;
    value?: string | number;
    status?: string;
    message?: string;
    type?: string;
    url?: string;
  }>;
  customContent?: React.ReactNode; // Custom content to display instead of or in addition to details
  targetPath?: string; // Path to navigate to for viewing results
  jobId?: string; // Optional job ID for more specific navigation
  errorDetails?: any[]; // Optional error details
  closeManager?: () => void; // Optional callback to close the status manager instance
  action?: string; // The action type (e.g., 'matchRank', 'upload', 'scout')
  action_display?: string; // Explicit display name for the action (overrides action for display purposes)
  actionType?: string; // The specific action (e.g., 'completed', 'failed')
}

/**
 * A uniform modal component for displaying status completion information
 * Used by various status managers (MatchRank, Upload, Scout, etc.)
 *
 * The component will display the correct event name based on the 'action' prop:
 * - 'matchRank' -> 'Match & Rank'
 * - 'upload' -> 'Upload'
 * - 'scout' -> 'Scout'
 * - Any other value will be capitalized and displayed as is
 *
 * If no title or description is provided, they will be generated based on the action and actionType.
 */
const StatusCompletionModal: React.FC<StatusCompletionModalProps> = ({
  isOpen,
  onClose,
  title, // Will use dynamic title based on action if not provided
  description, // Will use dynamic description based on action if not provided
  status,
  stats,
  details,
  customContent,
  targetPath,
  jobId,
  errorDetails,
  closeManager,
  action,
  action_display, // Explicit display name for the action
  actionType,
}) => {
  const pathname = usePathname();

  if (!isOpen) return null;

  // Determine if we're already on the target path
  const isOnTargetPath = targetPath && pathname ? pathname.indexOf(targetPath) !== -1 : false;

  // Get the event name based on the action type
  const getEventName = () => {
    // First check for the action_display property which is specifically for display purposes
    if (action_display) {
      switch (action_display) {
        case 'upload':
          return 'Upload';
        case 'scout':
          return 'Scout';
        default:
          return action_display.charAt(0).toUpperCase() + action_display.slice(1);
      }
    }

    // Fall back to the action property if action_display is not provided
    if (!action) return 'Operation';

    switch (action) {
      case 'matchRank':
        return 'Match & Rank';
      case 'upload':
        return 'Upload';
      case 'scout':
        return 'Scout';
      default:
        return action.charAt(0).toUpperCase() + action.slice(1);
    }
  };

  // Generate a fallback title when no title is provided
  const generateFallbackTitle = () => {
    const eventName = getEventName();

    // Handle different action types
    switch (actionType) {
      case 'completed':
        return `${eventName} Complete`;
      case 'failed':
        return `${eventName} Failed`;
      case 'error':
        return `${eventName} Error`;
      case 'warning':
        return `${eventName} Warning`;
      default:
        return `${eventName} ${actionType || 'Complete'}`;
    }
  };

  // Generate a fallback description when no description is provided
  const generateFallbackDescription = () => {
    const eventName = getEventName();

    switch (actionType) {
      case 'completed':
        return `The ${eventName.toLowerCase()} operation has been completed successfully.`;
      case 'failed':
        return `The ${eventName.toLowerCase()} operation has failed.`;
      case 'error':
        return `An error occurred during the ${eventName.toLowerCase()} operation.`;
      case 'warning':
        return `The ${eventName.toLowerCase()} operation completed with warnings.`;
      default:
        return `The ${eventName.toLowerCase()} operation has finished.`;
    }
  };

  // Log the action information for debugging

  // Get status icon and color
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="text-green-500 h-10 w-10" />;
      case 'error':
        return <AlertCircle className="text-red-500 h-10 w-10" />;
      case 'warning':
        return <AlertCircle className="text-orange-500 h-10 w-10" />;
      case 'info':
        return <Trophy className="text-purple-500 h-10 w-10" />;
      default:
        return <CheckCircle className="text-green-500 h-10 w-10" />;
    }
  };

  // Get background gradient based on status
  const getHeaderGradient = () => {
    switch (status) {
      case 'success':
        return 'bg-gradient-to-r from-green-900/90 to-purple-950/80';
      case 'error':
        return 'bg-gradient-to-r from-red-900/90 to-red-700/80';
      case 'warning':
        return 'bg-gradient-to-r from-orange-900/90 to-orange-700/80';
      case 'info':
        return 'bg-gradient-to-r from-purple-900/90 to-purple-700/80';
      default:
        return 'bg-gradient-to-r from-green-900/90 to-green-700/80';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/30 backdrop-blur-md flex items-center justify-center z-[99999]"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className={`backdrop-blur-sm rounded-xl ${customContent ? 'max-w-2xl' : 'max-w-md'} w-full max-h-[90vh] border border-white/10 overflow-hidden`}
      >
        {/* Header Section with gradient background */}
        <div className={`p-6 ${getHeaderGradient()}`}>
          <div className="flex items-center">
            <div className="p-2 bg-white/20 rounded-full mr-4">{getStatusIcon()}</div>
            <div>
              <h3 className="text-xl font-semibold text-white">
                {title && title.trim() ? title : generateFallbackTitle()}
              </h3>
              <p className="text-white/80 text-sm mt-1">
                {description && description.trim() ? description : generateFallbackDescription()}
              </p>
            </div>
            <button
              type="button"
              onClick={() => {
                onClose();
                // Close the status manager if the closeManager prop is provided
                if (closeManager) {
                  closeManager();
                }
              }}
              className="ml-auto text-white/70 hover:text-white transition-colors"
              title="Close modal"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Stats Section */}
        {stats && Object.keys(stats).length > 0 && (
          <div className="p-6 border-b border-white/10">
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(stats).map(([key, value]) => (
                <div key={key} className="text-center">
                  <p className="text-white/60 text-xs uppercase tracking-wider">{key}</p>
                  <p className="text-white text-xl font-semibold mt-1">{value}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Custom Content Section */}
        {customContent && (
          <div className="p-6 border-b border-white/10 max-h-96 overflow-y-auto">
            {customContent}
          </div>
        )}

        {/* Details Section */}
        {details && details.length > 0 && (
          <div className="p-6 max-h-[300px] overflow-y-auto border-b border-white/10">
            <h4 className="text-white/80 text-sm font-medium mb-3">
              {action === 'matchRank'
                ? 'Candidate Details'
                : action === 'scout'
                  ? 'Profile Details'
                  : 'File Details'}
            </h4>
            <div className="space-y-3">
              {/* Group details by type */}
              {(() => {
                const candidates = details.filter(d => d.type === 'candidate');
                const duplicates = details.filter(d => d.type === 'duplicate');
                const others = details.filter(
                  d => !d.type || (d.type !== 'candidate' && d.type !== 'duplicate')
                );

                return (
                  <>
                    {/* Processed Candidates */}
                    {candidates.length > 0 && (
                      <div>
                        <h5 className="text-green-400 text-xs font-medium mb-2 flex items-center gap-1">
                          <CheckCircle size={12} />
                          {action === 'matchRank'
                            ? `Matched Candidates (${candidates.length})`
                            : action === 'scout'
                              ? `Scouted Profiles (${candidates.length})`
                              : `Processed Files (${candidates.length})`}
                        </h5>
                        <div className="space-y-2">
                          {candidates.map(detail => (
                            <div
                              key={detail.id}
                              className="p-3 rounded-lg border bg-green-500/10 border-green-500/30"
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-white text-sm font-medium">
                                  {detail.name}
                                </span>
                                <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-300">
                                  {detail.status || detail.value}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Duplicate Files */}
                    {duplicates.length > 0 && (
                      <div>
                        <h5 className="text-blue-400 text-xs font-medium mb-2 flex items-center gap-1">
                          <AlertCircle size={12} />
                          Duplicate Files ({duplicates.length})
                        </h5>
                        <div className="space-y-2">
                          {duplicates.map(detail => (
                            <div
                              key={detail.id}
                              className="p-3 rounded-lg border bg-blue-500/10 border-blue-500/30"
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-white text-sm font-medium">
                                  {detail.name}
                                </span>
                                <span className="text-xs px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">
                                  {detail.status || detail.value}
                                </span>
                              </div>
                              {detail.message && (
                                <div className="mt-1">
                                  <span className="text-xs text-blue-400">{detail.message}</span>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Other Files */}
                    {others.length > 0 && (
                      <div className="space-y-2">
                        {others.map(detail => (
                          <div
                            key={detail.id}
                            className="p-3 rounded-lg border bg-white/5 border-white/10"
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white text-sm font-medium">{detail.name}</span>
                              <span className="text-xs px-2 py-1 rounded-full bg-white/10 text-white/70">
                                {detail.status || detail.value}
                              </span>
                            </div>
                            {detail.message && (
                              <div className="mt-1">
                                <span className="text-xs text-white/60">{detail.message}</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                );
              })()}
            </div>
          </div>
        )}

        {/* Error Details Section */}
        {/* {errorDetails && errorDetails.length > 0 && (
          <div className="p-6 max-h-[200px] overflow-y-auto border-b border-white/10">
            <h4 className="text-white/80 text-sm font-medium mb-3">Error Details</h4>
            <div className="space-y-2">
              {errorDetails.map((error, index) => (
                <div key={`error-${index}-${error.message || JSON.stringify(error).slice(0, 20)}`} className="bg-red-500/10 p-2 rounded">
                  <span className="text-red-300 text-sm">
                    {error.message || JSON.stringify(error)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )} */}

        {/* Footer Section */}
        <div className="p-6 flex justify-center gap-4">
          {/* Show View Result button for successful completion actions */}
          {status === 'success' && actionType === 'completed' && jobId && targetPath && (
            <button
              type="button"
              onClick={() => {
                onClose();
                // Close the status manager if the closeManager prop is provided
                if (closeManager) {
                  closeManager();
                }

                // Determine the target URL based on action type
                let targetUrl = '';
                if (action === 'matchRank') {
                  // For match rank: navigate to ranked mode
                  targetUrl = `${targetPath}/${jobId}/candidates`;
                } else if (action === 'upload' || action === 'scout') {
                  // For upload and scout: navigate to edit mode
                  targetUrl = `${targetPath}/${jobId}/edit`;
                } else if (action === 'videoGeneration') {
                  // For video generation: check if we have details with video URL
                  const videoDetail = details?.find(d => d.type === 'video' || d.url);
                  if (videoDetail?.url) {
                    // Open video in new tab
                    window.open(videoDetail.url, '_blank');
                    return; // Don't navigate, just open the video
                  } else {
                    // Fallback to job page with video JD tab
                    targetUrl = `${targetPath}/${jobId}/manage?tab=video-jd`;
                  }
                } else {
                  // Fallback to basic job page
                  targetUrl = `${targetPath}`;
                }

                // Use full page refresh as required for state changes
                window.location.href = targetUrl;
              }}
              className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-700 hover:from-purple-700 hover:to-pink-800 text-white rounded-full transition-colors flex items-center gap-2 shadow-md"
            >
              <Eye size={16} />
              <span>{action === 'videoGeneration' ? 'Watch Video' : 'View Result'}</span>
            </button>
          )}

          {/* Show Refresh button for other cases (errors, warnings, or when no jobId/targetPath) */}
          {(status !== 'success' || actionType !== 'completed' || !jobId || !targetPath) && (
            <button
              type="button"
              onClick={() => {
                onClose();
                // Close the status manager if the closeManager prop is provided
                if (closeManager) {
                  closeManager();
                }
              }}
              className="px-6 py-2 bg-white/10 hover:bg-white/20 text-white rounded-full transition-colors flex items-center gap-2"
            >
              <RefreshCw size={16} />
              <span>Refresh</span>
            </button>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default StatusCompletionModal;
