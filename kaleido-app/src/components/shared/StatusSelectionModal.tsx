'use client';

import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import Image from 'next/image';

import StatusTimeline from '@/components/shared/StatusTimeline';
import { CandidateStatus } from '@/types/candidate.types';
import { useJobStore } from '@/stores/unifiedJobStore';
import { showToast } from '@/components/Toaster';
import ColorfulLoader from '@/components/Layouts/ColourfulLoader';

interface StatusSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate: {
    id: string;
    fullName: string;
    status: CandidateStatus;
  };
  onStatusClick?: (status: CandidateStatus) => void;
  jobId?: string;
  companyName?: string;
  jobTitle?: string;
  candidateEmail?: string;
}

const StatusSelectionModal: React.FC<StatusSelectionModalProps> = ({
  isOpen,
  onClose,
  candidate,
  onStatusClick,
  jobId,
  companyName = 'Your Company',
  jobTitle = 'Open Position',
  candidateEmail,
}) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(candidate.status);

  const { updateCandidateStatusWithMessage } = useJobStore();

  // Update current status when candidate prop changes
  useEffect(() => {
    setCurrentStatus(candidate.status);
  }, [candidate.status]);

  // Create portal container
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let container = document.getElementById('status-selection-portal');
    if (!container) {
      container = document.createElement('div');
      container.id = 'status-selection-portal';
      document.body.appendChild(container);
    }

    setPortalContainer(container);

    return () => {
      if (container && container.parentNode && !isOpen) {
        container.parentNode.removeChild(container);
      }
    };
  }, [isOpen]);

  const handleStatusClick = async (status: CandidateStatus) => {
    // If external handler is provided, use it
    if (onStatusClick) {
      onStatusClick(status);
      return;
    }

    // Otherwise, handle internally with optimistic update
    if (!jobId) {
      console.error('JobId is required for status update');
      return;
    }

    setIsUpdating(true);

    try {
      // Update the UI immediately for instant feedback
      setCurrentStatus(status);

      // Generate appropriate message based on status
      const message = `Dear ${candidate.fullName},

This is an update regarding your application for the ${jobTitle} position at ${companyName}.

Your application status has been updated. Please let us know if you have any questions.

Best regards,
${companyName} Team`;

      // Call the store method for optimistic update
      await updateCandidateStatusWithMessage(jobId, candidate.id, status, message, {
        candidateEmail,
      });

      showToast({
        message: `Status updated to ${status}`,
        isSuccess: true,
      });

      // Do NOT close the modal - keep it open for further updates
    } catch (error) {
      console.error('Error updating status:', error);

      // Revert the status on error
      setCurrentStatus(candidate.status);

      showToast({
        message: 'Failed to update status',
        isSuccess: false,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (!isOpen || !portalContainer) return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center overflow-hidden">
      <div
        className="absolute inset-0 backdrop-blur-xl"
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)' }}
        onClick={onClose}
      ></div>

      {/* Main Content - Centered */}
      <div className="relative z-10 flex items-center justify-center w-full p-4 sm:p-8 overflow-y-auto">
        <div
          className="flex flex-col rounded-3xl max-w-4xl w-full min-h-[600px] overflow-hidden relative"
          style={{
            background:
              'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
            borderColor: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(20px)',
          }}
        >
          {/* Close Button */}
          <button
            type="button"
            onClick={onClose}
            className="absolute top-4 right-4 z-20 p-2 rounded-full bg-black/20 hover:bg-black/40 transition-colors focus:outline-none"
            aria-label="Close"
          >
            <svg
              className="w-6 h-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Full Width Header Image with Curved Edges */}
          <div className="w-full h-40 sm:h-48 md:h-56 relative overflow-hidden">
            <Image
              src="/images/narrow/expand-6.webp"
              alt="Status Update"
              fill
              className="w-full h-full object-cover object-center"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-purple-700/30 to-transparent"></div>
          </div>

          <div className="text-center mb-8 sm:mb-12 px-8 sm:px-12 pt-8 sm:pt-10">
            <p className="text-lg sm:text-xl md:text-2xl mb-4 sm:mb-6 font-light tracking-wide text-white/90">
              Select a status to update
            </p>
            <p className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 sm:mb-12 bg-gradient-to-r from-purple-300 via-pink-300 to-purple-400 bg-clip-text text-transparent leading-tight">
              {candidate.fullName}'s application
            </p>
          </div>

          <div className="w-full px-8 sm:px-12 mb-8 sm:mb-12">
            <StatusTimeline
              currentStatus={currentStatus}
              onStatusClick={handleStatusClick}
              size="lg"
              showAllStatuses={false}
            />
          </div>

          {/* Loading overlay with ColorfulLoader */}
          {isUpdating && (
            <div className="absolute inset-0 z-30 rounded-3xl">
              <ColorfulLoader text="Updating status" useModalBg={true} />
            </div>
          )}
        </div>
      </div>
    </div>,
    portalContainer
  );
};

export default StatusSelectionModal;
