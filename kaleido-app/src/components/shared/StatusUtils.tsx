import React from 'react';

import {
  <PERSON>ertCircle,
  BadgeCheck,
  BadgeX,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  Mail,
  Star,
  ThumbsUp,
  User,
  UserX,
} from 'lucide-react';

import { CandidateStatus } from '@/types/candidate.types';

export interface StatusInfo {
  icon: React.ReactNode;
  label: string;
  className: string;
  color: string;
}

export const getStatusInfo = (status: CandidateStatus): StatusInfo => {
  const statusMap: Record<CandidateStatus, StatusInfo> = {
    [CandidateStatus.NEW]: {
      icon: <AlertCircle size={16} className="text-white" />,
      label: 'New',
      className: 'bg-gray-500/10 text-gray-300 border-gray-500/20',
      color: 'gray',
    },
    [CandidateStatus.APPLIED]: {
      icon: <User size={16} className="text-white" />,
      label: 'Applied',
      className: 'bg-teal-500/10 text-teal-300 border-teal-500/20',
      color: 'teal',
    },
    [CandidateStatus.MATCHED]: {
      icon: <Star size={16} className="text-white" />,
      label: 'Matched',
      className: 'bg-amber-500/10 text-amber-300 border-amber-500/20',
      color: 'amber',
    },
    [CandidateStatus.SHORTLISTED]: {
      icon: <ThumbsUp size={16} className="text-white" />,
      label: 'Shortlisted',
      className: 'bg-blue-500/10 text-blue-300 border-blue-500/20',
      color: 'blue',
    },
    [CandidateStatus.CONTACTED]: {
      icon: <Mail size={16} className="text-white" />,
      label: 'Contacted',
      className: 'bg-cyan-500/10 text-cyan-300 border-cyan-500/20',
      color: 'cyan',
    },
    [CandidateStatus.INTERESTED]: {
      icon: <ThumbsUp size={16} className="text-white" />,
      label: 'Interested',
      className: 'bg-teal-500/10 text-teal-300 border-teal-500/20',
      color: 'teal',
    },
    [CandidateStatus.NOT_INTERESTED]: {
      icon: <UserX size={16} className="text-white" />,
      label: 'Not Interested',
      className: 'bg-rose-500/10 text-rose-300 border-rose-500/20',
      color: 'rose',
    },
    [CandidateStatus.INTERVIEWING]: {
      icon: <Calendar size={16} className="text-white" />,
      label: 'Interviewing',
      className: 'bg-purple-900/10 text-purple-300 border-purple-500/20',
      color: 'purple',
    },
    [CandidateStatus.OFFER_PENDING_APPROVAL]: {
      icon: <Clock size={16} className="text-white" />,
      label: 'Offer Pending Approval',
      className: 'bg-yellow-500/10 text-yellow-300 border-yellow-500/20',
      color: 'yellow',
    },
    [CandidateStatus.OFFER_APPROVED]: {
      icon: <CheckCircle size={16} className="text-white" />,
      label: 'Offer Approved',
      className: 'bg-green-500/10 text-green-300 border-green-500/20',
      color: 'green',
    },
    [CandidateStatus.OFFER_REJECTED]: {
      icon: <BadgeX size={16} className="text-white" />,
      label: 'Offer Rejected',
      className: 'bg-red-500/10 text-red-300 border-red-500/20',
      color: 'red',
    },
    [CandidateStatus.OFFER_EXTENDED]: {
      icon: <Check size={16} className="text-white" />,
      label: 'Offer Extended',
      className: 'bg-indigo-500/10 text-indigo-300 border-indigo-500/20',
      color: 'indigo',
    },
    [CandidateStatus.OFFER_ACCEPTED]: {
      icon: <BadgeCheck size={16} className="text-white" />,
      label: 'Offer Accepted',
      className: 'bg-emerald-500/10 text-emerald-300 border-emerald-500/20',
      color: 'emerald',
    },
    [CandidateStatus.OFFER_DECLINED]: {
      icon: <UserX size={16} className="text-white" />,
      label: 'Offer Declined',
      className: 'bg-orange-500/10 text-orange-300 border-orange-500/20',
      color: 'orange',
    },
    [CandidateStatus.HIRE_PENDING_APPROVAL]: {
      icon: <Clock size={16} className="text-white" />,
      label: 'Hire Pending Approval',
      className: 'bg-amber-600/10 text-amber-300 border-amber-600/20',
      color: 'amber',
    },
    [CandidateStatus.HIRE_APPROVED]: {
      icon: <CheckCircle size={16} className="text-white" />,
      label: 'Hire Approved',
      className: 'bg-lime-500/10 text-lime-300 border-lime-500/20',
      color: 'lime',
    },
    [CandidateStatus.HIRED]: {
      icon: <CheckCircle size={16} className="text-white" />,
      label: 'Hired',
      className: 'bg-green-500/10 text-green-300 border-green-500/20',
      color: 'green',
    },
    [CandidateStatus.REJECTED]: {
      icon: <BadgeX size={16} className="text-white" />,
      label: 'Rejected',
      className: 'bg-red-500/10 text-red-300 border-red-500/20',
      color: 'red',
    },
    [CandidateStatus.WITHDRAWN]: {
      icon: <User size={16} className="text-white" />,
      label: 'Withdrawn',
      className: 'bg-gray-500/10 text-gray-300 border-gray-500/20',
      color: 'gray',
    },
    [CandidateStatus.CULTURAL_FIT_ANSWERED]: {
      icon: <Check size={16} className="text-white" />,
      label: 'Video Introduction Answered',
      className: 'bg-violet-500/10 text-violet-300 border-violet-500/20',
      color: 'violet',
    },
  };

  return statusMap[status] || statusMap[CandidateStatus.NEW];
};

// This represents a typical hiring flow path for employers
export const commonHiringPath = [
  CandidateStatus.NEW,
  CandidateStatus.MATCHED,
  CandidateStatus.SHORTLISTED,
  CandidateStatus.INTERVIEWING,
  CandidateStatus.OFFER_PENDING_APPROVAL,
  CandidateStatus.OFFER_APPROVED,
  CandidateStatus.OFFER_EXTENDED,
  CandidateStatus.OFFER_ACCEPTED,
  CandidateStatus.HIRED,
];

// This represents a simplified hiring flow path for job seekers
// It includes all statuses that should be visible to job seekers
export const jobSeekerHiringPath = [
  CandidateStatus.APPLIED, // Start with APPLIED status
  CandidateStatus.MATCHED,
  CandidateStatus.SHORTLISTED,
  CandidateStatus.INTERVIEWING,
  CandidateStatus.OFFER_PENDING_APPROVAL, // Added to show pending approval status
  CandidateStatus.OFFER_APPROVED, // Added to show approved offer status
  CandidateStatus.OFFER_EXTENDED,
  CandidateStatus.OFFER_ACCEPTED,
  CandidateStatus.HIRED,
];

// Helper to get available actions based on current status
export const getAvailableActions = (currentStatus: CandidateStatus) => {
  const actions = [];

  switch (currentStatus) {
    case CandidateStatus.NEW:
      actions.push(
        { status: CandidateStatus.SHORTLISTED, label: 'Shortlist Candidate' },
        { status: CandidateStatus.REJECTED, label: 'Reject' }
      );
      break;
    case CandidateStatus.MATCHED:
      actions.push(
        { status: CandidateStatus.SHORTLISTED, label: 'Shortlist Candidate' },
        { status: CandidateStatus.REJECTED, label: 'Reject' }
      );
      break;
    case CandidateStatus.SHORTLISTED:
      actions.push(
        { status: CandidateStatus.INTERVIEWING, label: 'Start Interview Process' },
        { status: CandidateStatus.REJECTED, label: 'Reject' }
      );
      break;
    case CandidateStatus.INTERVIEWING:
      actions.push(
        { status: CandidateStatus.OFFER_PENDING_APPROVAL, label: 'Create Offer' },
        { status: CandidateStatus.REJECTED, label: 'Reject' }
      );
      break;
    case CandidateStatus.OFFER_PENDING_APPROVAL:
      actions.push(
        { status: CandidateStatus.OFFER_APPROVED, label: 'Approve Offer' },
        { status: CandidateStatus.REJECTED, label: 'Reject Offer' }
      );
      break;
    case CandidateStatus.OFFER_APPROVED:
      actions.push({ status: CandidateStatus.OFFER_EXTENDED, label: 'Extend Offer' });
      break;
    case CandidateStatus.OFFER_EXTENDED:
      actions.push(
        { status: CandidateStatus.OFFER_ACCEPTED, label: 'Mark as Accepted' },
        { status: CandidateStatus.REJECTED, label: 'Mark as Declined' }
      );
      break;
    case CandidateStatus.OFFER_ACCEPTED:
      actions.push({ status: CandidateStatus.HIRED, label: 'Complete Hiring' });
      break;
  }

  return actions;
};

// Helper to convert API status to UI status
export const convertApiStatus = (apiStatus: string): CandidateStatus => {
  // Check if the apiStatus is a valid CandidateStatus enum value
  if (Object.values(CandidateStatus).includes(apiStatus as CandidateStatus)) {
    return apiStatus as CandidateStatus;
  }

  // Default mapping for any values that don't match
  switch (apiStatus) {
    case 'OFFER_SENT':
      return CandidateStatus.OFFER_EXTENDED;
    case 'QUALIFIED':
      return CandidateStatus.SHORTLISTED;
    case 'APPLIED':
      return CandidateStatus.APPLIED;
    default:
      // If we can't map it, default to APPLIED for job seekers
      console.warn(`Unknown status from API: ${apiStatus}`);
      return CandidateStatus.APPLIED;
  }
};
