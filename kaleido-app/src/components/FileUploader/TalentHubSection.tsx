import React, { useCallback, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Database, ListChecks, Plus, Search } from 'lucide-react';

import { debounce } from '@/lib/utils';
import { useJobStore } from '@/stores/unifiedJobStore';

import TalentPoolTable from './TalentPoolTable';

interface TalentHubSectionProps {
  jobId: string;
  job: any;
  selectedJob: any;
  showTalentPool: boolean;
  setShowTalentPool: (show: boolean) => void;
  isTalentPoolLoading: boolean;
  setIsTalentPoolLoading: (loading: boolean) => void;
  setUploadedCandidateCount: (count: number) => void;
  onCandidateCountChange: (count: number) => void;
}

const TalentHubSection: React.FC<TalentHubSectionProps> = ({
  jobId,
  job,
  selectedJob,
  showTalentPool,
  setShowTalentPool,
  isTalentPoolLoading,
  setIsTalentPoolLoading,
  setUploadedCandidateCount,
  onCandidateCountChange,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');

  // Get jobEditStore actions
  const { onWorkerComplete, fetchJobCriteria } = useJobStore();

  // Create a debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      // Only set the debounced search term if it's 3 or more characters
      if (value.length >= 3 || value.length === 0) {
        setDebouncedSearchTerm(value);
      }
    }, 500),
    []
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-4 px-4 border-b border-white/10 bg-gradient-to-r from-cyan-900/10 to-cyan-800/5">
        <div className="flex items-center gap-3">
          <div className="bg-cyan-500/20 p-2 rounded-full">
            <Database className="h-5 w-5 text-cyan-400" />
          </div>
          <div>
            <p className="text-sm lg:text-md font-medium text-white-force">Add from Talent Hub</p>
            <p className="text-xs text-white-force-60">
              Find candidates from your internal database
            </p>
          </div>
        </div>
        <button
          type="button"
          onClick={() => setShowTalentPool(!showTalentPool)}
          className="px-3 py-1.5 rounded-full bg-cyan-500/10 hover:bg-cyan-500/30 text-sm font-medium transition-all duration-300 flex items-center gap-2 border border-cyan-500/20 self-start sm:self-auto"
        >
          {showTalentPool ? (
            <>
              <ListChecks className="h-4 w-4" />
              <span className="hidden sm:inline">Hide Talent Hub</span>
              <span className="sm:hidden">Hide</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Show Talent Hub</span>
              <span className="sm:hidden">Show</span>
            </>
          )}
        </button>
      </div>

      <AnimatePresence>
        {showTalentPool && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-visible"
            style={{ display: 'block' }}
          >
            {/* Search field */}
            <div className="bg-purple-900/20 px-4 py-3 border-b border-white/10">
              <div className="relative max-w-md">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search className="h-4 w-4 text-white/40" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search candidates by name..."
                  className={`pl-10 ${searchTerm ? 'pr-8' : 'pr-4'} py-2 bg-purple-500/10 border border-purple-500/20 rounded-full text-sm text-white/90 placeholder-white/40 focus:outline-none focus:ring-1 focus:ring-purple-500/50 w-full`}
                />
                {searchTerm.length > 0 && (
                  <>
                    {searchTerm.length < 3 ? (
                      <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-white/40">
                        Type {3 - searchTerm.length} more character
                        {searchTerm.length === 2 ? '' : 's'}
                      </div>
                    ) : (
                      <button
                        type="button"
                        onClick={() => {
                          setSearchTerm('');
                          setDebouncedSearchTerm('');
                        }}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-white/40 hover:text-white/70 transition-colors"
                        aria-label="Clear search"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
            <TalentPoolTable
              jobId={jobId}
              jobType={job?.jobType || ''}
              searchTerm={debouncedSearchTerm}
              onCandidateAdded={async (addedCount: number) => {
                // Update jobEditStore with the new candidate count
                await onWorkerComplete(jobId, 'upload');
                // Force refresh job criteria to get latest data
                await fetchJobCriteria(jobId, true);
              }}
              isLoading={isTalentPoolLoading}
              setIsLoading={setIsTalentPoolLoading}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default TalentHubSection;
