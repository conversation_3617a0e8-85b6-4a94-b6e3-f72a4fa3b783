import { AnimatePresence, motion } from 'framer-motion';
import React, { useCallback, useEffect, useState } from 'react';
import { ExtendedUploadedFile, UploadedFile } from './types';

import { RankNowButton } from '@/components/MatchRank/components/RankNowButton';
import { useJobStore } from '@/stores/unifiedJobStore';
import { UserPlus } from 'lucide-react';
import { NewUploadsFileList } from './NewUploadsFileList';
import { PreviousUploadsFileList } from './PreviousUploadsFileList';

interface TotalCandidatesSectionProps {
  jobId: string;
  files: UploadedFile[];
  isUploading: boolean;
  activeJobs: string[];
  handleRankClick: () => Promise<void>;
  handleDelete: (fileId: string) => void;
  useJobEditStore?: boolean;
}

const TotalCandidatesSection: React.FC<TotalCandidatesSectionProps> = ({
  jobId,
  files,
  isUploading,
  activeJobs,
  handleRankClick,
  handleDelete,
  useJobEditStore = false,
}) => {
  const [showCandidates, setShowCandidates] = useState(false);

  // Get data from Zustand store
  const { currentJob, stats, fetchJobCriteria, matchRankCost } = useJobStore();

  const criteriaData = currentJob;
  const candidates = currentJob?.recentCandidates || []; // Get candidates from store
  const matchRankCostData = matchRankCost || {
    success: true,
    creditCost: 0,
    unevaluatedCandidatesCount: 0,
    isValid: true,
    message: 'No cost calculated',
    availableCredits: 0,
  };
  const totalCandidates = stats?.totalCandidates || candidates?.length;

  // Fetch job data on mount and when jobId changes, but only if we don't already have the data
  useEffect(() => {
    if (jobId && (!currentJob || currentJob.id !== jobId)) {
      fetchJobCriteria(jobId, false); // Don't force refresh if data already exists
    }
  }, [jobId, currentJob, fetchJobCriteria]); // Include fetchJobCriteria in dependencies

  // Subscribe to job store updates for real-time data sync
  useEffect(() => {
    // Subscribe to specific store updates
    const unsubscribe = useJobStore.subscribe(
      state => ({
        currentJob: state.currentJob,
        stats: state.stats,
        totalCandidates: state.currentJob?.totalCandidates || state.stats?.totalCandidates || 0,
        recentCandidates: state.currentJob?.recentCandidates || [],
        matchRankCost: state.matchRankCost,
      }),
      (current, previous) => {
        // Only update if the job ID matches and data has changed
        if (
          current.currentJob?.id === jobId &&
          (current.totalCandidates !== previous.totalCandidates ||
            current.recentCandidates?.length !== previous.recentCandidates?.length ||
            current.matchRankCost?.creditCost !== previous.matchRankCost?.creditCost)
        ) {
          // Data has been updated in the store, component will re-render automatically
        }
      },
      {
        equalityFn: Object.is,
        fireImmediately: false,
      }
    );

    return () => {
      unsubscribe();
    };
  }, [jobId]);

  // Function to check if thresholds are valid (both must be greater than 0)
  const isThresholdValid = useCallback(() => {
    // If we don't have criteria data yet, assume thresholds are valid to not block the button
    if (!criteriaData) {
      return true;
    }

    const topThreshold = criteriaData.topCandidateThreshold || 0;
    const secondTierThreshold = criteriaData.secondTierCandidateThreshold || 0;

    // Both thresholds must be greater than 0
    return topThreshold > 0 && secondTierThreshold > 0;
  }, [criteriaData]);

  // Create previouslyUploadedFiles from candidates data
  const previouslyUploadedFiles: ExtendedUploadedFile[] = candidates.map(candidate => ({
    id: candidate.id,
    name: candidate.originalFilename || `${candidate.fullName}.pdf`,
    size: 0,
    type: 'application/pdf',
    status: 'success' as const,
    isPreviouslyUploaded: true,
    progress: 100,
    candidateName: candidate.fullName,
    candidateId: candidate.id,
    isNew: false,
    isVirtualFile: false,
  }));

  return (
    <div className="mt-8 bg-gradient-to-r from-purple-900/20 to-purple-800/10  border border-purple-700/20 backdrop-blur-sm">
      {/* Main header with Total Candidates and RankNow button */}
      <div className="p-4 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          <div className="bg-pink-700/20 p-3 rounded-full">
            <UserPlus className="h-6 w-6 text-pink-700" />
          </div>
          <div>
            <p className="text-sm lg:text-md font-medium text-white">Total Candidates</p>
            <div className="flex items-center gap-2">
              <span className="text-xl lg:text-2xl font-bold text-white">
                {totalCandidates || 0}
              </span>
              <span className="text-xs text-white/60">from all sources</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="relative group">
            <RankNowButton
              jobId={jobId}
              candidateCount={totalCandidates}
              className="mx-auto"
              isThresholdValid={isThresholdValid}
              matchRankCost={matchRankCostData}
              onRankClick={async () => {
                await handleRankClick();
                // Refresh data after ranking operation
                await fetchJobCriteria(jobId, true);
              }}
              useJobEditStore={useJobEditStore}
              disabled={
                isUploading || activeJobs.length > 0 || files.some(f => f.status === 'uploading')
              }
            />

            {/* Tooltip for disabled state */}
            {(isUploading ||
              activeJobs.length > 0 ||
              files.some(f => f.status === 'uploading') ||
              !isThresholdValid() ||
              totalCandidates === 0) && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 backdrop-blur-sm text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 w-64 text-center pointer-events-none border border-white/10">
                {isUploading || activeJobs.length > 0 || files.some(f => f.status === 'uploading')
                  ? 'Please wait for all file uploads to complete before ranking candidates'
                  : !isThresholdValid()
                    ? 'Both Top and 2nd-tier thresholds must be greater than 0 to rank candidates'
                    : totalCandidates === 0
                      ? 'You need to add candidates before ranking'
                      : 'Unable to rank candidates at this time'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Show/Hide Candidates button row */}
      {totalCandidates > 0 && (
        <div className="px-4 py-3 border-b border-white/5">
          <button
            type="button"
            onClick={() => setShowCandidates(!showCandidates)}
            className="w-full px-4 py-2 text-gray-100/70 rounded-lg bg-pink-700/10 hover:bg-pink-700/20 text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 border border-purple-700/10"
          >
            <span>{showCandidates ? 'Hide Candidates' : 'Show Candidates'}</span>
            <svg
              className={`w-4 h-4 transition-transform duration-200 ${showCandidates ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Candidate Lists */}
      <AnimatePresence>
        {showCandidates && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-visible"
            style={{ display: 'block' }}
          >
            <div className="p-4">
              {/* Display previously uploaded files */}
              <PreviousUploadsFileList
                files={previouslyUploadedFiles}
                job={{ ...criteriaData, candidates }}
                uploadedCandidateCount={totalCandidates}
                onDelete={handleDelete}
              />

              {/* Display newly uploaded files */}
              <NewUploadsFileList files={files} onDelete={handleDelete} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TotalCandidatesSection;
