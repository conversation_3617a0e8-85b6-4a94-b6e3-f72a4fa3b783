// Import custom tooltip styles
import '@/styles/tooltip-fix.css';

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Award,
  Briefcase,
  Building,
  Check,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Eye,
  GraduationCap,
  Info,
  Linkedin,
  Loader2,
  MapPin,
  Play,
  Plus,
  Search,
  User,
  UserPlus,
  Users,
} from 'lucide-react';

import { CreditButton } from '@/components/common/CreditButton';
import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import apiHelper from '@/lib/apiHelper';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useScoutedCandidatesStore } from '@/stores/scoutedCandidatesStore';
import { useScoutJobsStore } from '@/stores/scoutJobsStore';
import { useJobStore } from '@/stores/unifiedJobStore';

import { CreditActionType } from '../common';
import CandidateDetailsModal from './CandidateDetailsModal';

interface ScoutOnlineTableProps {
  jobId: string;
  jobType: string;
  onScoutStarted: () => void;
  onCandidateAdded?: (addedCount: number) => void; // Updated to pass the count of added candidates
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const ScoutOnlineTable: React.FC<ScoutOnlineTableProps> = ({
  jobId,
  jobType,
  onScoutStarted,
  onCandidateAdded,
  isLoading,
  setIsLoading,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchCount, setSearchCount] = useState<number>(2);
  const [isStartingScout, setIsStartingScout] = useState(false);
  const [addingCandidate, setAddingCandidate] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(true);
  const [showSearchControls, setShowSearchControls] = useState(true);
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [isAddingSelected, setIsAddingSelected] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Use the scoutedCandidatesStore instead of local state
  const {
    fetchScoutedCandidates,
    candidatesByJobId,
    isLoading: loadingCandidatesMap,
    removeCandidateFromList,
  } = useScoutedCandidatesStore();

  // Use the scout jobs store to track scout job status
  const { addJob: addScoutJob } = useScoutJobsStore();

  // Import jobStore to trigger updates
  const { onWorkerComplete, fetchJobCriteria, currentJob } = useJobStore();

  // Reference for the dropdown trigger button
  const dropdownTriggerRef = useRef<HTMLButtonElement>(null);
  // Reference for the dropdown content
  const dropdownRef = useRef<HTMLDivElement>(null);
  // State for portal container
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  // State for dropdown position
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // Create portal container when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      let container = document.getElementById('scout-dropdown-portal');
      if (!container) {
        container = document.createElement('div');
        container.id = 'scout-dropdown-portal';
        document.body.appendChild(container);
      }
      setPortalContainer(container);

      // Clean up on unmount
      return () => {
        if (container && container.childNodes.length === 0 && document.body.contains(container)) {
          document.body.removeChild(container);
        }
      };
    }
  }, []);

  // Update dropdown position when it's opened
  useEffect(() => {
    if (isDropdownOpen && dropdownTriggerRef.current) {
      const rect = dropdownTriggerRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
      });
    }
  }, [isDropdownOpen]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        dropdownTriggerRef.current &&
        !dropdownTriggerRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  // Get loading state for this job
  const loadingCandidates = loadingCandidatesMap[jobId] || false;

  // Get candidates for this job from the store
  const scoutedCandidates = Array.isArray(candidatesByJobId[jobId]) ? candidatesByJobId[jobId] : [];

  // Pagination calculations
  const totalItems = scoutedCandidates.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedCandidates = scoutedCandidates.slice(startIndex, endIndex);
  const showPagination = totalItems > itemsPerPage;

  // Reset to first page when candidates change
  useEffect(() => {
    setCurrentPage(1);
  }, [scoutedCandidates.length]);

  // Pagination handlers
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1));
  };

  // Reset to first page when candidates change
  useEffect(() => {
    setCurrentPage(1);
  }, [scoutedCandidates.length]);

  // Fetch scouted candidates when the component mounts
  useEffect(() => {
    if (jobId) {
      fetchScoutedCandidates(jobId);
      // Don't automatically show results when component is mounted

      // Check URL parameters to see if we should show the match rank
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlJobId = urlParams.get('jobId');
        const mode = urlParams.get('mode');

        if (urlJobId === jobId && mode === 'ranked') {
          setShowResults(true);
        }
      }
    }
  }, [jobId, fetchScoutedCandidates]);

  // Simple effect to refresh scouted candidates when component mounts
  useEffect(() => {
    fetchScoutedCandidates(jobId);
  }, [jobId, fetchScoutedCandidates]);

  const startScouting = async () => {
    if (isStartingScout) return;

    setIsStartingScout(true);
    setIsLoading(true);

    try {
      const response = await apiHelper.post('/candidates/scout', {
        jobId,
        jobType,
        count: searchCount,
      });

      if (response.success) {
        showToast({
          message: 'Scouting started successfully',
          isSuccess: true,
        });

        // Add the scout job to the scout jobs store so GenericStatusManager can track it
        addScoutJob({
          jobId: response.jobId,
          status: response.status || 'queued',
          totalProfiles: response.totalProfiles || searchCount,
          jobType: 'scout',
          relatedId: jobId, // Store the original job entity ID
        });

        // Notify parent component
        onScoutStarted();

        // Fetch scouted candidates after a delay to allow backend processing
        // This ensures the table is updated after the scout job starts
        setTimeout(() => {
          fetchScoutedCandidates(jobId);
        }, 2000); // Wait 2 seconds before fetching to allow backend processing
      } else {
        showToast({
          message: response.message || 'Failed to start scouting',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error starting scout:', error);
      showToast({
        message: 'Failed to start scouting. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsStartingScout(false);
      setIsLoading(false);
    }
  };

  // Handle selection of a candidate
  const handleSelectCandidate = (candidateId: string) => {
    setSelectedCandidates(prev => {
      if (prev.includes(candidateId)) {
        return prev.filter(id => id !== candidateId);
      } else {
        return [...prev, candidateId];
      }
    });
  };

  // Function to select all candidates at once
  const selectAllCandidates = () => {
    const nonAddedCandidates = scoutedCandidates.filter(c => !c.isAdded);
    const nonAddedCandidateIds = nonAddedCandidates.map(c => c.id);

    // Check if all non-added candidates are currently selected
    const allNonAddedSelected =
      selectedCandidates.length === nonAddedCandidates.length &&
      nonAddedCandidateIds.every(id => selectedCandidates.includes(id));

    if (allNonAddedSelected) {
      // If all are selected, deselect all
      setSelectedCandidates([]);
    } else {
      // Otherwise, select all that aren't already added
      setSelectedCandidates(nonAddedCandidateIds);
    }
  };

  // Add a single candidate
  const handleAddCandidate = async (candidateId: string) => {
    try {
      setAddingCandidate(candidateId);
      const response = await apiHelper.post('/scouted-candidates/add-to-candidate', {
        scoutedCandidateId: candidateId,
      });

      if (response) {
        showToast({
          message: 'Candidate added successfully',
          isSuccess: true,
        });

        // Remove the candidate from the scouted candidates list
        removeCandidateFromList(candidateId, jobId);

        // Refresh the scouted candidates from the backend to get latest data
        fetchScoutedCandidates(jobId, true).catch((error: Error) => {
          console.error('Error refreshing scouted candidates after adding candidate:', error);
        });

        // Refresh the match rank details store to update the view without full page reload
        const { refreshAfterScouting } = useMatchRankDetailsStore.getState();
        refreshAfterScouting(jobId).catch((error: Error) => {
          console.error('Error refreshing match rank details after adding candidate:', error);
        });

        // Notify parent component that a candidate was added
        if (onCandidateAdded) {
          onCandidateAdded(1);
        }

        // Update the job store to reflect the new candidate count
        await onWorkerComplete(jobId, 'scout');
        await fetchJobCriteria(jobId, true);
      } else {
        showToast({
          message: response.message || 'Failed to add candidate',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error adding candidate:', error);
      showToast({
        message: 'Failed to add candidate',
        isSuccess: false,
      });
    } finally {
      setAddingCandidate(null);
    }
  };

  // Add all selected candidates
  const handleAddSelectedCandidates = async () => {
    if (selectedCandidates.length === 0) return;

    setIsAddingSelected(true);

    try {
      // Process candidates one by one
      for (const candidateId of selectedCandidates) {
        setAddingCandidate(candidateId);
        await apiHelper.post('/scouted-candidates/add-to-candidate', {
          scoutedCandidateId: candidateId,
        });

        // Remove each candidate from the scouted candidates list
        removeCandidateFromList(candidateId, jobId);
      }

      showToast({
        message: `${selectedCandidates.length} candidates added successfully`,
        isSuccess: true,
      });

      // Clear selection
      setSelectedCandidates([]);

      // Refresh the scouted candidates from the backend to get latest data
      fetchScoutedCandidates(jobId, true).catch((error: Error) => {
        console.error(
          'Error refreshing scouted candidates after adding selected candidates:',
          error
        );
      });

      // Refresh the match rank details store to update the view without full page reload
      const { refreshAfterScouting } = useMatchRankDetailsStore.getState();
      refreshAfterScouting(jobId).catch((error: Error) => {
        console.error(
          'Error refreshing match rank details after adding selected candidates:',
          error
        );
      });

      // Notify parent component that candidates were added
      if (onCandidateAdded) {
        onCandidateAdded(selectedCandidates.length);
      }

      // Update the job store to reflect the new candidate count
      await onWorkerComplete(jobId, 'scout');
      await fetchJobCriteria(jobId, true);
    } catch (error) {
      console.error('Error adding selected candidates:', error);
      showToast({
        message: 'Failed to add some candidates',
        isSuccess: false,
      });
    } finally {
      setIsAddingSelected(false);
      setAddingCandidate(null);
    }
  };

  // Add all candidates at once
  const handleAddAllCandidates = async () => {
    // Get all non-added candidates
    const candidatesToAdd = scoutedCandidates
      .filter(candidate => !candidate.isAdded)
      .map(candidate => candidate.id);

    if (candidatesToAdd.length === 0) return;

    setIsAddingSelected(true);

    try {
      // Process candidates one by one since the API doesn't support batch operations
      let successCount = 0;
      let failCount = 0;

      for (const candidateId of candidatesToAdd) {
        setAddingCandidate(candidateId);
        try {
          const response = await apiHelper.post('/scouted-candidates/add-to-candidate', {
            scoutedCandidateId: candidateId,
          });

          if (response) {
            // Remove the candidate from the scouted candidates list
            removeCandidateFromList(candidateId, jobId);
            successCount++;
          } else {
            failCount++;
          }
        } catch (err) {
          console.error(`Error adding candidate ${candidateId}:`, err);
          failCount++;
        }
      }

      // Show appropriate toast message based on results
      if (successCount > 0 && failCount === 0) {
        showToast({
          message: `${successCount} candidates added successfully`,
          isSuccess: true,
        });
      } else if (successCount > 0 && failCount > 0) {
        showToast({
          message: `${successCount} candidates added successfully, ${failCount} failed`,
          isSuccess: true,
        });
      } else {
        showToast({
          message: 'Failed to add candidates',
          isSuccess: false,
        });
      }

      // Clear selection
      setSelectedCandidates([]);

      // Refresh the scouted candidates from the backend to get latest data
      fetchScoutedCandidates(jobId, true).catch((error: Error) => {
        console.error('Error refreshing scouted candidates after adding all candidates:', error);
      });

      // Refresh the match rank details store to update the view without full page reload
      const { refreshAfterScouting } = useMatchRankDetailsStore.getState();
      refreshAfterScouting(jobId).catch((error: Error) => {
        console.error('Error refreshing match rank details after adding all candidates:', error);
      });

      // Notify parent component that candidates were added
      if (onCandidateAdded) {
        onCandidateAdded(successCount);
      }

      // Update the job store to reflect the new candidate count
      await onWorkerComplete(jobId, 'scout');
      await fetchJobCriteria(jobId, true);
    } catch (error) {
      console.error('Error in add all candidates process:', error);
      showToast({
        message: 'Failed to add candidates',
        isSuccess: false,
      });
    } finally {
      setIsAddingSelected(false);
      setAddingCandidate(null);
    }
  };

  // Handle opening candidate details modal
  const handleOpenCandidateModal = (candidate: any) => {
    setSelectedCandidate(candidate);
    setIsModalOpen(true);
  };

  // Handle closing candidate details modal
  const handleCloseCandidateModal = () => {
    setIsModalOpen(false);
    setSelectedCandidate(null);
  };

  const renderCandidatesTable = () => {
    if (loadingCandidates) {
      return (
        <div className="flex flex-col justify-center items-center py-8 bg-black/10 rounded-lg border border-white/5">
          <div className="bg-purple-500/20 p-3 rounded-full mb-3">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-purple-300 border-t-pink-700"></div>
          </div>
          <p className="text-white font-medium text-sm">Searching for candidates...</p>
          <p className="text-white/60 text-xs mt-1">This may take a few moments</p>
        </div>
      );
    }

    if (scoutedCandidates.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-8 bg-black/10 rounded-lg border border-white/5">
          <div className="bg-purple-500/20 p-3 rounded-full mb-3">
            <Search className="h-6 w-6 text-purple-400" />
          </div>
          <p className="text-white font-medium text-sm">No scouted candidates found</p>
          <p className="text-white/60 text-xs mt-1 text-center px-4">
            {loadingCandidates
              ? 'Searching for candidates that match this job...'
              : 'Use the Scout Online button to find candidates. If no matches were found, our system will continue searching in the background.'}
          </p>
        </div>
      );
    }

    // Filter out candidates that have already been added
    // This is just for reference in the UI

    return (
      <div className="w-full">
        {/* Action bar for selected candidates or add all button */}
        {selectedCandidates.length > 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-between items-center mb-4 p-3 bg-gradient-to-r from-pink-700/20 to-purple-700/10 rounded-lg border border-pink-700/30 backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <div className="bg-pink-700/20 p-1.5 rounded-full">
                <UserPlus className="h-4 w-4 text-pink-700" />
              </div>
              <div>
                <span className="text-white text-sm font-medium">
                  {selectedCandidates.length} candidate{selectedCandidates.length !== 1 ? 's' : ''}{' '}
                  selected
                </span>
                <p className="text-white/60 text-xs">Ready to add to this job</p>
              </div>
            </div>
            <Button
              onClick={handleAddSelectedCandidates}
              disabled={isAddingSelected || selectedCandidates.length < 1}
              className="bg-gradient-to-r from-purple-600 to-pink-700 hover:from-purple-700 hover:to-pink-800 text-white h-8 px-3 text-xs rounded-md shadow-md shadow-purple-900/20 border border-purple-500/20 transition-all duration-300"
            >
              {isAddingSelected ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent mr-1"></div>
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="w-3 h-3 mr-1" />
                  Add Selected
                </>
              )}
            </Button>
          </motion.div>
        ) : (
          // Show Add All button when there are non-added candidates
          scoutedCandidates.filter(c => !c.isAdded).length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-between items-center mb-4 p-3 bg-gradient-to-r from-pink-700/20 to-purple-700/10 rounded-lg border border-pink-700/30 backdrop-blur-sm"
            >
              <div className="flex items-center gap-2">
                <div className="bg-pink-700/20 p-1.5 rounded-full">
                  <Users className="h-4 w-4 text-pink-700" />
                </div>
                <div>
                  <span className="text-white text-sm font-medium">
                    {scoutedCandidates.filter(c => !c.isAdded).length} candidate
                    {scoutedCandidates.filter(c => !c.isAdded).length !== 1 ? 's' : ''} available
                  </span>
                  <p className="text-white/60 text-xs">Add all candidates to this job</p>
                </div>
              </div>
              <Button
                onClick={handleAddAllCandidates}
                disabled={isAddingSelected}
                className="bg-gradient-to-r from-purple-600 to-pink-700 hover:from-purple-700 hover:to-pink-800 text-white h-8 px-3 text-xs rounded-md shadow-md shadow-purple-900/20 border border-purple-500/20 transition-all duration-300"
              >
                {isAddingSelected ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent mr-1"></div>
                    Adding All...
                  </>
                ) : (
                  <>
                    <Plus className="w-3 h-3 mr-1" />
                    Add All
                  </>
                )}
              </Button>
            </motion.div>
          )
        )}

        {/* Candidate Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 p-1">
          {Array.isArray(paginatedCandidates) &&
            paginatedCandidates.map(candidate => (
              <div
                key={candidate.id}
                className={`relative rounded-lg border ${candidate.isAdded ? 'bg-green-900/10 border-green-700/30' : 'bg-black/20 border-white/10 hover:border-purple-500/30'} p-3 transition-all duration-200`}
              >
                {/* Selection checkbox */}
                {!candidate.isAdded && (
                  <div
                    className={`absolute top-3 right-3 w-5 h-5 rounded-full border flex items-center justify-center cursor-pointer transition-all duration-200 ${
                      selectedCandidates.includes(candidate.id)
                        ? 'bg-pink-700 border-pink-700'
                        : 'border-white/30 hover:border-pink-700/50'
                    }`}
                    onClick={() => handleSelectCandidate(candidate.id)}
                  >
                    {selectedCandidates.includes(candidate.id) && (
                      <Check className="w-3 h-3 text-white" />
                    )}
                  </div>
                )}

                {/* Candidate info */}
                <div className="flex items-start gap-3 mb-3">
                  <div className="bg-purple-500/20 p-2 rounded-full flex-shrink-0 mt-1">
                    <User className="h-5 w-5 text-purple-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1 mb-1">
                      <h3 className="text-sm font-medium text-white truncate">
                        {candidate.fullName || 'Unknown'}
                      </h3>
                      {candidate.linkedinUrl && (
                        <a
                          href={candidate.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-400 hover:text-purple-300"
                          title={`View ${candidate.fullName}'s LinkedIn profile`}
                        >
                          <Linkedin className="w-3 h-3" />
                        </a>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      {candidate.jobTitle && (
                        <div className="flex items-center gap-1.5">
                          <Briefcase className="w-3 h-3 text-purple-400 flex-shrink-0" />
                          <span className="text-xs text-white/80 truncate">
                            {candidate.jobTitle}
                          </span>
                        </div>
                      )}

                      {candidate.currentCompany && (
                        <div className="flex items-center gap-1.5">
                          <Building className="w-3 h-3 text-purple-400 flex-shrink-0" />
                          <span className="text-xs text-white/80 truncate">
                            {candidate.currentCompany}
                          </span>
                        </div>
                      )}

                      {candidate.location && (
                        <div className="flex items-center gap-1.5">
                          <MapPin className="w-3 h-3 text-purple-400 flex-shrink-0" />
                          <span className="text-xs text-white/80 truncate">
                            {candidate.location}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Skills */}
                {candidate.skills && candidate.skills.length > 0 && (
                  <div className="mb-3">
                    <div className="flex items-center gap-1.5 mb-1.5">
                      <GraduationCap className="w-3 h-3 text-purple-400" />
                      <span className="text-xs font-medium text-white/80">Skills</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {candidate.skills.slice(0, 3).map((skill, idx) => (
                        <span
                          key={idx}
                          className="px-1.5 py-0.5 bg-purple-500/10 border border-purple-500/20 text-purple-400 rounded text-[10px]"
                        >
                          {skill}
                        </span>
                      ))}
                      {candidate.skills.length > 3 && (
                        <span className="px-1.5 py-0.5 bg-purple-500/10 border border-purple-500/20 text-purple-400 rounded text-[10px]">
                          +{candidate.skills.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between mt-2 pt-2 border-t border-white/10">
                  <button
                    type="button"
                    onClick={() => handleOpenCandidateModal(candidate)}
                    className="px-2 py-1 rounded text-xs text-white/60 hover:text-white/90 hover:bg-white/10 transition-colors flex items-center gap-1"
                  >
                    <Eye className="w-3 h-3" />
                    <span>Details</span>
                  </button>

                  {candidate.isAdded ? (
                    <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs flex items-center gap-1">
                      <Check className="w-3 h-3" />
                      Added
                    </span>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-pink-700/30 bg-pink-700/10 hover:bg-pink-700/20 text-pink-700 hover:text-pink-600 text-xs h-7 px-2 rounded-md"
                      onClick={() => handleAddCandidate(candidate.id)}
                      disabled={addingCandidate === candidate.id}
                    >
                      {addingCandidate === candidate.id ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-pink-700 border-t-transparent"></div>
                      ) : (
                        <>
                          <Plus className="w-3 h-3 mr-1" />
                          Add to Job
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
        </div>

        {/* Pagination */}
        {showPagination && (
          <div className="flex items-center justify-between mt-3 px-2">
            <div className="text-xs text-white/60">
              Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} candidates
            </div>
            <div className="flex items-center gap-1">
              <button
                type="button"
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                aria-label="Previous page"
                className="p-1 rounded bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                <ChevronLeft className="w-3 h-3" />
              </button>

              <div className="flex items-center gap-0.5">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    type="button"
                    onClick={() => goToPage(page)}
                    className={`px-2 py-0.5 rounded text-xs transition-all duration-200 ${
                      currentPage === page
                        ? 'bg-gradient-to-r from-purple-600 to-pink-700 text-white'
                        : 'bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <button
                type="button"
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                aria-label="Next page"
                className="p-1 rounded bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                <ChevronRight className="w-3 h-3" />
              </button>
            </div>
          </div>
        )}

        {/* Help tooltip */}
        <div className="mt-4 bg-black/20 p-3 rounded-lg border border-white/10">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-pink-700" />
            <span className="text-sm font-medium text-white">About Scouted Candidates</span>
          </div>
          <p className="text-xs text-white/70 mt-2">
            Candidates scouted online are saved to your talent pool. You can add them to this job by
            clicking the "Add to Job" button. Click the "Details" button to view full candidate
            information including experience, education, and more.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gradient-to-r from-purple-950/20 to-purple-900/10 p-4 rounded-lg border border-purple-700/20 backdrop-blur-sm overflow-x-hidden max-w-full">
      <div className="flex flex-col space-y-4 overflow-x-hidden">
        {/* Header Section */}
        <div className="flex items-center justify-between border-b border-white/10 pb-3">
          <div className="flex items-center gap-3">
            <div>
              <p className="text-md font-medium text-pink-600">Scout Online Candidates</p>
              <p className="text-xs text-white/60">
                Find relevant candidates from LinkedIn that match this job
              </p>
            </div>
          </div>
          {loadingCandidates && (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin text-pink-600" />
              <span className="text-xs text-white/70">Searching...</span>
            </div>
          )}
        </div>

        {/* Scout Controls Section */}
        <div className="bg-black/20 rounded-lg border border-white/5 overflow-hidden">
          <div
            className="flex items-center justify-between p-3 cursor-pointer border-b border-white/10"
            onClick={() => setShowSearchControls(!showSearchControls)}
          >
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-pink-700" />
              <span className="text-sm font-medium text-white">Search Controls</span>
            </div>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-300 ${showSearchControls ? 'rotate-180' : ''}`}
            />
          </div>

          {showSearchControls && (
            <div className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <label className="text-sm font-medium text-white">
                  Number of candidates to find
                </label>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex-grow">
                  <div className="relative w-full">
                    <button
                      type="button"
                      ref={dropdownTriggerRef}
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      disabled={isStartingScout}
                      className="w-full bg-[#1e1e38] border-0 rounded-md px-3 py-2 text-white h-10 cursor-pointer flex items-center justify-between"
                    >
                      <span>{searchCount} candidates</span>
                      <ChevronDown
                        className={`h-5 w-5 text-white transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                      />
                    </button>

                    {isDropdownOpen &&
                      portalContainer &&
                      createPortal(
                        <motion.div
                          ref={dropdownRef}
                          initial={{ opacity: 0, y: -5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                          transition={{ duration: 0.2 }}
                          className="bg-[#1e1e38] border border-purple-700/20 rounded-md shadow-lg z-[999999] max-h-60 overflow-y-auto"
                          style={{
                            position: 'absolute',
                            top: `${dropdownPosition.top}px`,
                            left: `${dropdownPosition.left}px`,
                            width: `${dropdownTriggerRef.current?.offsetWidth}px`,
                          }}
                        >
                          {[2, 5, 10, 20, 50, 100].map(count => (
                            <div
                              key={count}
                              className={`px-3 py-2 cursor-pointer hover:bg-purple-700/20 ${
                                searchCount === count
                                  ? 'bg-purple-700/30 text-pink-600'
                                  : 'text-white'
                              }`}
                              onClick={() => {
                                setSearchCount(count);
                                setIsDropdownOpen(false);
                              }}
                            >
                              {count} candidates
                            </div>
                          ))}
                        </motion.div>,
                        portalContainer
                      )}
                  </div>
                </div>
                <CreditButton
                  actionType={CreditActionType.SCOUT}
                  count={searchCount}
                  onClick={startScouting}
                  isLoading={isStartingScout}
                  disabled={isLoading}
                  loadingText="Starting..."
                  variant="primary"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Start Scouting
                </CreditButton>
              </div>
              <p className="text-xs text-white/60 mt-3 text-center">
                The process runs in the background and may take several minutes
              </p>
            </div>
          )}
        </div>

        {/* Results Section */}
        <div className="bg-black/20 rounded-lg border border-white/5 overflow-hidden mt-4">
          <div
            className="flex items-center justify-between p-3 cursor-pointer border-b border-white/10"
            onClick={() => setShowResults(!showResults)}
          >
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-pink-700" />
              <span className="text-sm font-medium text-white">Scouted Candidates</span>
              {scoutedCandidates.length > 0 && (
                <span className="text-xs bg-pink-700/20 text-pink-700 px-1.5 py-0.5 rounded-full">
                  {scoutedCandidates.length}
                </span>
              )}
            </div>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-300 ${showResults ? 'rotate-180' : ''}`}
            />
          </div>

          {showResults && (
            <div className="overflow-x-hidden">
              <div className="w-full overflow-x-hidden">
                <table className="w-full border-collapse table-fixed">
                  <thead className="sticky top-0 z-10 bg-black/30">
                    <tr className="bg-black/20 text-white/80">
                      <th className="px-3 py-2 text-left text-xs font-medium w-[40px]">
                        <div
                          className={`w-4 h-4 rounded border flex items-center justify-center cursor-pointer ${
                            selectedCandidates.length > 0 &&
                            selectedCandidates.length ===
                              scoutedCandidates.filter(c => !c.isAdded).length
                              ? 'bg-purple-600 border-purple-600'
                              : 'border-white/30'
                          }`}
                          onClick={selectAllCandidates}
                        >
                          {selectedCandidates.length > 0 &&
                            selectedCandidates.length ===
                              scoutedCandidates.filter(c => !c.isAdded).length && (
                              <Check className="w-2 h-2 text-white" />
                            )}
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium w-[25%]">
                        <span className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          Name
                        </span>
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium w-[25%]">
                        <span className="flex items-center">
                          <Briefcase className="w-3 h-3 mr-1" />
                          Position
                        </span>
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium w-[25%]">
                        <span className="flex items-center">
                          <Award className="w-3 h-3 mr-1" />
                          Company
                        </span>
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium w-[40px]">
                        <span className="flex items-center">
                          <Info className="w-3 h-3 mr-1" />
                          Info
                        </span>
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium w-[80px]">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.isArray(paginatedCandidates) &&
                      paginatedCandidates.map(candidate => (
                        <tr
                          key={candidate.id}
                          className="border-b border-white/10 hover:bg-white/5 transition-colors"
                        >
                          <td className="px-3 py-2">
                            {candidate.isAdded ? (
                              <div className="w-4 h-4 rounded border border-white/10 flex items-center justify-center opacity-50"></div>
                            ) : (
                              <div
                                className={`w-4 h-4 rounded border flex items-center justify-center cursor-pointer ${
                                  selectedCandidates.includes(candidate.id)
                                    ? 'bg-purple-600 border-purple-600'
                                    : 'border-white/30'
                                }`}
                                onClick={() => handleSelectCandidate(candidate.id)}
                              >
                                {selectedCandidates.includes(candidate.id) && (
                                  <Check className="w-2 h-2 text-white" />
                                )}
                              </div>
                            )}
                          </td>
                          <td className="px-3 py-2 text-white text-xs">
                            <div className="flex items-center">
                              <User className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                              <span className="truncate max-w-[150px]">
                                {candidate.fullName || 'Unknown'}
                              </span>
                              {candidate.linkedinUrl && (
                                <a
                                  href={candidate.linkedinUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="ml-2 text-purple-400 hover:text-purple-300 inline-flex items-center"
                                  title={`View ${candidate.fullName}'s LinkedIn profile`}
                                  aria-label={`View ${candidate.fullName}'s LinkedIn profile`}
                                >
                                  <svg
                                    className="w-3 h-3"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    aria-hidden="true"
                                  >
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                                  </svg>
                                </a>
                              )}
                            </div>
                          </td>
                          <td className="px-3 py-2 text-white/80 text-xs">
                            <div className="flex items-center">
                              <Briefcase className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                              <span className="truncate max-w-[150px]">
                                {candidate.jobTitle || 'N/A'}
                              </span>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-white/80 text-xs">
                            <div className="flex items-center">
                              <Award className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                              <span className="truncate max-w-[150px]">
                                {candidate.currentCompany || 'N/A'}
                              </span>
                            </div>
                          </td>
                          <td className="px-3 py-2">
                            <button
                              type="button"
                              onClick={() => handleOpenCandidateModal(candidate)}
                              className="p-1 rounded-full hover:bg-purple-500/20 transition-colors"
                              aria-label="View candidate details"
                              title="View candidate details"
                            >
                              <Eye className="w-4 h-4 text-purple-400" />
                              <span className="sr-only">View candidate details</span>
                            </button>
                          </td>
                          <td className="px-3 py-2">
                            {candidate.isAdded ? (
                              <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded text-xs">
                                Added
                              </span>
                            ) : (
                              <Button
                                size="sm"
                                variant="outline"
                                className="border-white/10 hover:bg-purple-500/20 hover:text-purple-400 text-xs h-7 px-2"
                                onClick={() => handleAddCandidate(candidate.id)}
                                disabled={addingCandidate === candidate.id}
                              >
                                {addingCandidate === candidate.id ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent"></div>
                                ) : (
                                  <>
                                    <Plus className="w-3 h-3 mr-1" />
                                    Add
                                  </>
                                )}
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                  {/* Table Footer with Add All button */}
                  {scoutedCandidates.filter(c => !c.isAdded).length > 0 && (
                    <tfoot className="bg-black/30 border-t border-white/10">
                      <tr>
                        <td colSpan={6} className="px-3 py-3">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className="bg-pink-700/20 p-1.5 rounded-full">
                                <Users className="h-4 w-4 text-pink-700" />
                              </div>
                              <div>
                                <span className="text-white text-sm font-medium">
                                  {selectedCandidates.length > 0
                                    ? `${selectedCandidates.length} candidate${selectedCandidates.length !== 1 ? 's' : ''} selected`
                                    : `${scoutedCandidates.filter(c => !c.isAdded).length} candidate${scoutedCandidates.filter(c => !c.isAdded).length !== 1 ? 's' : ''} available`}
                                </span>
                              </div>
                            </div>
                            <Button
                              onClick={
                                selectedCandidates.length > 0
                                  ? handleAddSelectedCandidates
                                  : handleAddAllCandidates
                              }
                              disabled={isAddingSelected}
                              className="bg-gradient-to-r from-purple-600 to-pink-700 hover:from-purple-700 hover:to-pink-800 text-white h-8 px-3 text-xs rounded-md shadow-md shadow-purple-900/20 border border-purple-500/20 transition-all duration-300"
                            >
                              {isAddingSelected ? (
                                <>
                                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent mr-1"></div>
                                  {selectedCandidates.length > 0
                                    ? 'Adding Selected...'
                                    : 'Adding All...'}
                                </>
                              ) : (
                                <>
                                  <Plus className="w-3 h-3 mr-1" />
                                  {selectedCandidates.length > 0
                                    ? 'Add Selected'
                                    : 'Add All Candidates'}
                                </>
                              )}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    </tfoot>
                  )}
                </table>
              </div>

              {/* Table Pagination */}
              {showPagination && (
                <div className="flex items-center justify-between p-3 border-t border-white/10">
                  <div className="text-xs text-white/60">
                    Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems}{' '}
                    candidates
                  </div>
                  <div className="flex items-center gap-1">
                    <button
                      type="button"
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                      aria-label="Previous page"
                      className="p-1 rounded bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <ChevronLeft className="w-3 h-3" />
                    </button>

                    <div className="flex items-center gap-0.5">
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          type="button"
                          onClick={() => goToPage(page)}
                          className={`px-2 py-0.5 rounded text-xs transition-all duration-200 ${
                            currentPage === page
                              ? 'bg-gradient-to-r from-purple-600 to-pink-700 text-white'
                              : 'bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </div>

                    <button
                      type="button"
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                      aria-label="Next page"
                      className="p-1 rounded bg-black/20 border border-white/10 text-white/60 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <ChevronRight className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Candidate Details Modal */}
      <CandidateDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseCandidateModal}
        candidate={selectedCandidate}
      />
    </div>
  );
};

export default ScoutOnlineTable;
