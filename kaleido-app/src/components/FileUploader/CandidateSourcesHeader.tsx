import { Database, FileText, Layers, Linkedin } from 'lucide-react';

import { useJobStore } from '@/stores/unifiedJobStore';
import Image from 'next/image';
import React from 'react';

interface CandidateSourcesHeaderProps {
  jobId: string;
  uploadedCandidateCount?: number; // Keep as fallback
}

const CandidateSourcesHeader: React.FC<CandidateSourcesHeaderProps> = ({
  jobId,
  uploadedCandidateCount,
}) => {
  // Get data from Zustand store
  const { currentJob, stats } = useJobStore();

  const totalCandidates = stats?.totalCandidates || 0;

  // No need to fetch data here - the parent component (edit page) already fetches criteria data
  // which contains all the same information plus more detail

  // Use store data if available, otherwise fallback to prop
  const displayCount = totalCandidates || uploadedCandidateCount || 0;

  // Define candidate sources data array
  const candidateSources = [
    {
      icon: Database,
      title: 'Talent Hub',
      description: 'Internal DB',
      iconColor: 'text-cyan-300',
      titleColor: 'text-cyan-50',
      descriptionColor: 'text-cyan-100/70',
    },
    {
      icon: Linkedin,
      title: 'Scout Online',
      description: 'Find talent',
      iconColor: 'text-pink-300',
      titleColor: 'text-pink-50',
      descriptionColor: 'text-pink-100/70',
    },
    {
      icon: FileText,
      title: 'Upload',
      description: 'PDF/DOC',
      iconColor: 'text-green-400',
      titleColor: 'text-green-50',
      descriptionColor: 'text-green-100/70',
    },
  ];
  return (
    <div className="mb-6 p-4 md:p-6 relative overflow-hidden rounded-lg border border-purple-700/30 backdrop-blur-sm min-h-[160px] md:min-h-[180px]">
      {/* Background image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/narrow/expand-1.webp"
          alt="Candidate Sources Background"
          fill
          style={{ objectFit: 'cover', objectPosition: 'center' }}
          className="opacity-60"
          priority
        />
        {/* Lighter gradient overlay to preserve image visibility */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent from-10% via-fuchsia-900/15 via-60% to-fuchsia-950/50 to-100%" />
        {/* Reduced horizontal gradient for better image visibility */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-950/20 to-fuchsia-900/20 mix-blend-overlay" />
      </div>

      {/* Header layout with proper spacing */}
      <div className="relative z-10 flex flex-col gap-4 md:gap-5 h-full justify-between">
        {/* Title and count in a compact row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Layers className="h-6 w-6 md:h-7 md:w-7 text-pink-300 drop-shadow-sm" />
            <h2 className="text-white text-xl md:text-2xl font-bold drop-shadow-sm">
              Candidate Sources
            </h2>
            <span className="bg-pink-500/40 text-white text-sm md:text-base px-3 md:px-4 py-1 md:py-1.5 rounded-full font-medium border border-pink-400/40 shadow-lg backdrop-blur-sm">
              {displayCount}
            </span>
          </div>
        </div>
        {/* Single card with modern line separators */}
        <div className="bg-white/10 p-3 md:p-4 rounded-lg border border-white/20 backdrop-blur-md shadow-lg">
          <div className="flex flex-col gap-2">
            <p className="text-xs text-white/70 font-normal tracking-wide">Available sources</p>
            <div className="flex items-center justify-between">
              {candidateSources.map((source, index) => {
                const IconComponent = source.icon;
                const isLast = index === candidateSources.length - 1;

                return (
                  <React.Fragment key={index}>
                    <div className="flex items-center gap-2 md:gap-3 flex-1 relative">
                      <IconComponent
                        className={`h-5 w-5 md:h-6 md:w-6 ${source.iconColor} flex-shrink-0 drop-shadow-sm`}
                      />
                      <div className="flex flex-col min-w-0">
                        <span
                          className={`text-sm md:text-base font-semibold ${source.titleColor} drop-shadow-sm truncate`}
                        >
                          {source.title}
                        </span>
                        <span className={`text-xs md:text-sm ${source.descriptionColor} truncate`}>
                          {source.description}
                        </span>
                      </div>
                      {/* Large transparent number */}
                      <span className="text-3xl md:text-4xl font-bold text-white/10 absolute right-0 top-1/2 -translate-y-1/2 pointer-events-none">
                        {index + 1}
                      </span>
                    </div>

                    {/* Modern vertical separator */}
                    {!isLast && (
                      <div className="h-12 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent mx-2 md:mx-3" />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateSourcesHeader;
