import React, { useState } from 'react';

import { motion } from 'framer-motion';
import { Download, Linkedin, Smartphone } from 'lucide-react';
import { FaFacebook, FaInstagram } from 'react-icons/fa';

import { showToast } from '@/components/Toaster';
import { getActiveDownloadJob, handleVideoDownload } from '@/lib/videoDownloadHelper';
import { useVideoDownloadJobsStore } from '@/stores/videoDownloadJobsStore';
import { VideoJDStatus } from '../consts';
import { useVideoJD } from '../context/VideoJDContext';

interface AspectRatioOption {
  platform: string;
  format: string;
  icon: React.ReactNode;
  gradient: string;
}

const AspectRatioDownload: React.FC = () => {
  const { selectedVersion } = useVideoJD();
  const [downloadingRatio, setDownloadingRatio] = useState<string | null>(null);

  // Video download jobs store
  const {
    jobs: downloadJobs,
    addJob: addDownloadJob,
    updateJobProgress,
    removeJob,
    clearCompletedJobs,
  } = useVideoDownloadJobsStore();

  const aspectRatioOptions: AspectRatioOption[] = [
    {
      platform: 'LinkedIn',
      format: 'linkedin',
      icon: <Linkedin className="w-3 sm:w-4 h-3 sm:h-4" />,
      gradient: 'from-blue-500 to-blue-600',
    },
    {
      platform: 'Facebook',
      format: 'facebook',
      icon: <FaFacebook className="w-3 sm:w-4 h-3 sm:h-4" />,
      gradient: 'from-blue-600 to-blue-700',
    },
    {
      platform: 'Instagram',
      format: 'instagram',
      icon: <FaInstagram className="w-3 sm:w-4 h-3 sm:h-4" />,
      gradient: 'from-pink-500 to-purple-600',
    },
    {
      platform: 'Stories',
      format: 'stories',
      icon: <Smartphone className="w-3 sm:w-4 h-3 sm:h-4" />,
      gradient: 'from-purple-500 to-indigo-600',
    },
  ];

  // Cancel active download for a platform
  const handleCancelDownload = (platform: string) => {
    // Find and remove active job for this platform
    const activeJob = getActiveDownloadJob(downloadJobs, platform);
    if (activeJob) {
      removeJob(activeJob.id);
    }

    // Clear downloading state
    if (downloadingRatio === platform) {
      setDownloadingRatio(null);
    }
  };

  // Clear all completed/failed jobs on component mount
  React.useEffect(() => {
    clearCompletedJobs();
  }, [clearCompletedJobs]);

  const handleDownload = async (option: AspectRatioOption) => {
    if (!selectedVersion?.videoUrl || selectedVersion.status !== VideoJDStatus.COMPLETED) {
      showToast({
        message: 'Video must be completed before downloading',
        isSuccess: false,
      });
      return;
    }

    if (!selectedVersion.id) {
      showToast({
        message: 'Video ID not found. Please try again.',
        isSuccess: false,
      });
      return;
    }

    // Use the shared video download helper
    await handleVideoDownload(
      {
        platform: option.format,
        label: option.platform,
        videoJDId: selectedVersion.id,
        videoUrl: selectedVersion.videoUrl,
        quality: 'high',
        filename: `video-jd-${option.format}`,
      },
      {
        addDownloadJob,
        setDownloadingState: setDownloadingRatio,
        updateJobProgress,
      }
    );
  };

  // Don't show the component if there's no completed video
  if (!selectedVersion?.videoUrl || selectedVersion.status !== VideoJDStatus.COMPLETED) {
    return null;
  }

  return (
    <div className="space-y-3 sm:space-y-4 pt-2 sm:pt-3">
      <div className="text-center space-y-2 sm:space-y-3 mb-4">
        <div className="space-y-0.5 sm:space-y-1">
          <h4 className="text-sm sm:text-base font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            Share Your Masterpiece
          </h4>
          <p className="text-slate-300 text-xs">Download for social platforms</p>

          {/* Show clear button if there are active downloads */}
          {Object.values(downloadJobs).some(
            job => job.status === 'active' || job.status === 'queued'
          ) && (
            <button
              onClick={() => {
                Object.keys(downloadJobs).forEach(jobId => removeJob(jobId));
                setDownloadingRatio(null);
              }}
              className="mt-2 text-xs text-red-400 hover:text-red-300 transition-colors underline"
            >
              Cancel All Downloads
            </button>
          )}
        </div>
      </div>

      <div className="flex justify-center gap-2 sm:gap-3 max-w-sm sm:max-w-lg mx-auto flex-wrap">
        {aspectRatioOptions.map(option => (
          <motion.div
            key={option.platform}
            whileHover={{ scale: 1.05, y: -1 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 500, damping: 25 }}
            className="flex-1 min-w-0 max-w-[80px] sm:max-w-none sm:flex-none"
          >
            <button
              type="button"
              onClick={() => {
                const isDownloading =
                  downloadingRatio === option.format ||
                  !!getActiveDownloadJob(downloadJobs, option.format);
                if (isDownloading) {
                  handleCancelDownload(option.format);
                } else {
                  handleDownload(option);
                }
              }}
              className="group relative w-full rounded-lg sm:rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/5"
            >
              {/* Content */}
              <div className="relative px-2 sm:px-3 lg:px-4 py-2 sm:py-3 flex flex-col items-center space-y-1 sm:space-y-2">
                {downloadingRatio === option.format ||
                getActiveDownloadJob(downloadJobs, option.format) ? (
                  <div className="flex flex-col items-center space-y-1">
                    <Download className="w-3 sm:w-4 h-3 sm:h-4 text-white animate-spin" />
                    {getActiveDownloadJob(downloadJobs, option.format) && (
                      <div className="w-6 sm:w-8 lg:w-10 bg-white/20 rounded-full h-0.5 sm:h-1">
                        {/* eslint-disable-next-line react/forbid-dom-props */}
                        <div
                          className="bg-gradient-to-r from-purple-400 to-pink-400 h-0.5 sm:h-1 rounded-full transition-all duration-300"
                          style={{
                            width: `${getActiveDownloadJob(downloadJobs, option.format)?.progress || 0}%`,
                          }}
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div
                    className={`p-1 sm:p-1.5 lg:p-2 rounded-md sm:rounded-lg bg-gradient-to-br ${option.gradient.replace('/80', '').replace('/40', '').replace(' to-transparent', '')} shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <div className="text-white">{option.icon}</div>
                  </div>
                )}

                <span className="text-slate-200 group-hover:text-white font-medium text-[9px] sm:text-[10px] lg:text-xs transition-colors duration-200 leading-tight">
                  {option.platform}
                </span>

                {(downloadingRatio === option.format ||
                  getActiveDownloadJob(downloadJobs, option.format)) && (
                  <div className="text-[7px] sm:text-[8px] lg:text-[10px] text-slate-300 leading-tight text-center">
                    <div>
                      {getActiveDownloadJob(downloadJobs, option.format)
                        ? `Downloading... ${Math.round(getActiveDownloadJob(downloadJobs, option.format)?.progress || 0)}%`
                        : 'Preparing...'}
                    </div>
                    <div className="text-slate-400 text-[6px] sm:text-[7px] mt-0.5">
                      Click to cancel
                    </div>
                  </div>
                )}
              </div>
            </button>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default AspectRatioDownload;
