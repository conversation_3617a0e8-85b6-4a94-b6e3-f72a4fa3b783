import { format } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Circle,
  Clock,
  ExternalLink,
  FileText,
  FileVideo,
  Film,
  Globe,
  Loader2,
  Maximize2,
  Mic,
  PlayCircle,
  Radio,
  Sparkles,
  Video,
  Volume2,
  X,
} from 'lucide-react';
import React, { useState } from 'react';

interface VideoHistoryItem {
  id: string;
  clientId: string | null;
  createdAt: string;
  updatedAt: string;
  jobId: string;
  gender: string | null;
  languageCode: string | null;
  voiceId: string | null;
  tone: string | null;
  scriptLength: string;
  avatarId: string;
  voiceSpeed: number | null;
  voicePitch: number | null;
  generatedScript: string;
  synthesiaVideoId: string | null;
  status: string;
  videoUrl: string | null;
  jobType: string | null;
  type: 'LIVE_RECORDING' | 'AI_AVATAR';
  virtualBackgroundType: string;
  virtualBackgroundImageUrl: string | null;
  recordingDuration: number | null;
  recordingSettings: {
    frameRate: number;
    resolution: string;
    audioQuality: string;
    videoQuality: string;
  } | null;
}

interface VideoHistorySliderProps {
  isOpen: boolean;
  onClose: () => void;
  history: VideoHistoryItem[];
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return <CheckCircle className="w-4 h-4 text-green-400" />;
    case 'RECORDING':
      return <Radio className="w-4 h-4 text-red-400" />;
    case 'GENERATING':
      return <Loader2 className="w-4 h-4 text-amber-400 animate-spin" />;
    case 'SCRIPT_GENERATED':
      return <Circle className="w-4 h-4 text-cyan-400" />;
    case 'FAILED':
      return <AlertCircle className="w-4 h-4 text-red-400" />;
    default:
      return <Circle className="w-4 h-4 text-gray-400" />;
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'Completed';
    case 'RECORDING':
      return 'Recording in Progress';
    case 'GENERATING':
      return 'Generating Video';
    case 'SCRIPT_GENERATED':
      return 'Script Ready';
    case 'FAILED':
      return 'Failed';
    default:
      return status;
  }
};

const getTypeIcon = (type: 'LIVE_RECORDING' | 'AI_AVATAR') => {
  if (type === 'LIVE_RECORDING') {
    return <Mic className="w-4 h-4" />;
  }
  return <Sparkles className="w-4 h-4" />;
};

const VideoPlayer: React.FC<{ videoUrl: string; onClose: () => void }> = ({
  videoUrl,
  onClose,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/90 z-[60] flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        className="relative w-full max-w-4xl"
        onClick={e => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute -top-12 right-0 p-2 bg-black/10 hover:bg-black/20 rounded-lg transition-colors"
        >
          <X className="w-5 h-5 text-white" />
        </button>
        <video src={videoUrl} controls autoPlay className="w-full rounded-lg shadow-2xl" />
      </motion.div>
    </motion.div>
  );
};

const VideoHistoryItem: React.FC<{
  item: VideoHistoryItem;
  index: number;
  isExpanded: boolean;
  onToggle: () => void;
}> = ({ item, index, isExpanded, onToggle }) => {
  const [selectedVideoUrl, setSelectedVideoUrl] = useState<string | null>(null);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.05 }}
        className="bg-black/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden hover:bg-black/10 transition-all"
      >
        {/* Accordion Header */}
        <button
          onClick={onToggle}
          className="w-full p-5 flex items-center justify-between hover:bg-black/5 transition-colors"
        >
          <div className="flex items-center gap-3">
            <div
              className={`p-2 rounded-lg backdrop-blur-sm ${
                item.type === 'LIVE_RECORDING'
                  ? 'bg-purple-500/20 text-purple-400'
                  : 'bg-blue-500/20 text-blue-400'
              }`}
            >
              {getTypeIcon(item.type)}
            </div>
            <div className="text-left">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-white">
                  {item.type === 'LIVE_RECORDING' ? 'Live Recording' : 'AI Avatar'}
                </h3>
                <div className="flex items-center gap-1.5">
                  {getStatusIcon(item.status)}
                  <span className="text-sm text-gray-300">{getStatusLabel(item.status)}</span>
                </div>
              </div>
              <div className="flex items-center gap-4 mt-1 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3.5 h-3.5" />
                  {format(new Date(item.createdAt), 'MMM d, yyyy')}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-3.5 h-3.5" />
                  {format(new Date(item.createdAt), 'h:mm a')}
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            )}
          </div>
        </button>

        {/* Accordion Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="p-5 pt-0 border-t border-white/10">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                  {/* Left Column - Script and Metadata */}
                  <div className="space-y-4">
                    {/* Script Preview */}
                    {item.generatedScript && (
                      <div>
                        <h4 className="text-sm font-medium text-blue-300 mb-3 flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Script Content
                        </h4>
                        <div className="text-sm text-gray-300 bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5">
                          {item.generatedScript}
                        </div>
                      </div>
                    )}

                    {/* Metadata */}
                    <div className="space-y-2">
                      {item.languageCode && (
                        <div className="flex items-center gap-2 text-sm">
                          <Globe className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-400">Language:</span>
                          <span className="text-gray-300">{item.languageCode}</span>
                        </div>
                      )}
                      {item.scriptLength && (
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-400">Length:</span>
                          <span className="text-gray-300 capitalize">{item.scriptLength}</span>
                        </div>
                      )}
                      {item.tone && (
                        <div className="flex items-center gap-2 text-sm">
                          <Volume2 className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-400">Tone:</span>
                          <span className="text-gray-300">{item.tone}</span>
                        </div>
                      )}
                      {item.type === 'LIVE_RECORDING' && item.recordingDuration && (
                        <div className="flex items-center gap-2 text-sm">
                          <Film className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-400">Duration:</span>
                          <span className="text-gray-300">{item.recordingDuration}s</span>
                        </div>
                      )}
                    </div>

                    {/* Recording Settings for Live Recordings */}
                    {item.type === 'LIVE_RECORDING' && item.recordingSettings && (
                      <div className="pt-3 border-t border-white/10">
                        <div className="text-sm">
                          <span className="text-gray-400">Recording Settings:</span>
                          <div className="flex items-center gap-3 mt-1">
                            <span className="text-gray-300">
                              {item.recordingSettings.resolution}
                            </span>
                            <span className="text-gray-500">•</span>
                            <span className="text-gray-300">
                              {item.recordingSettings.frameRate} fps
                            </span>
                            <span className="text-gray-500">•</span>
                            <span className="text-gray-300">
                              {item.recordingSettings.audioQuality} audio
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Right Column - Video Preview */}
                  <div>
                    <h4 className="text-sm font-medium text-blue-300 mb-3 flex items-center gap-2">
                      <PlayCircle className="w-4 h-4" />
                      Video Preview
                    </h4>
                    <div className="relative aspect-video bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10">
                      {item.videoUrl ? (
                        <>
                          <video
                            src={item.videoUrl}
                            className="w-full h-full object-contain"
                            controls
                          />
                          <div className="absolute top-2 right-2 flex gap-2">
                            <button
                              onClick={() => setSelectedVideoUrl(item.videoUrl!)}
                              className="p-2 bg-black/50 backdrop-blur-sm hover:bg-black/70 text-white rounded-lg transition-colors"
                              title="Fullscreen"
                            >
                              <Maximize2 className="w-4 h-4" />
                            </button>
                            <a
                              href={item.videoUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-2 bg-black/50 backdrop-blur-sm hover:bg-black/70 text-white rounded-lg transition-colors"
                              title="Open in new tab"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          </div>
                        </>
                      ) : (
                        <div className="w-full h-full flex flex-col items-center justify-center text-gray-500">
                          <FileVideo className="w-12 h-12 mb-2" />
                          <p className="text-sm">
                            {item.status === 'RECORDING'
                              ? 'Recording in progress...'
                              : item.status === 'GENERATING'
                                ? 'Generating video...'
                                : 'Video not available'}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Video Player Overlay */}
      <AnimatePresence>
        {selectedVideoUrl && (
          <VideoPlayer videoUrl={selectedVideoUrl} onClose={() => setSelectedVideoUrl(null)} />
        )}
      </AnimatePresence>
    </>
  );
};

export const VideoHistoryModal: React.FC<VideoHistorySliderProps> = ({
  isOpen,
  onClose,
  history,
}) => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  const handleToggle = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  // Reset expanded state when closing
  React.useEffect(() => {
    if (!isOpen) {
      setExpandedIndex(null);
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Slider Panel */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
            className="fixed right-0 top-0 h-full w-full md:w-[60vw] bg-black/5 backdrop-blur-xl shadow-2xl z-50 overflow-hidden flex flex-col border-l border-white/10"
          >
            {/* Header */}
            <div className="relative bg-gradient-to-r from-blue-600/20 to-cyan-600/20 backdrop-blur-xl p-6 flex-shrink-0 border-b border-white/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2.5 bg-black/20 backdrop-blur-md rounded-xl">
                    <Film className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Video History</h2>
                    <p className="text-sm text-white/80 mt-0.5">
                      {history.length} {history.length === 1 ? 'version' : 'versions'} total
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-black/20 rounded-lg transition-colors backdrop-blur-sm"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-6">
              {history.length === 0 ? (
                <div className="text-center py-12">
                  <Video className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-300">No video history available</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {history.map((item, index) => (
                    <VideoHistoryItem
                      key={item.id}
                      item={item}
                      index={index}
                      isExpanded={expandedIndex === index}
                      onToggle={() => handleToggle(index)}
                    />
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
