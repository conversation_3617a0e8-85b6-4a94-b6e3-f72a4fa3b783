import React, { useState } from 'react';

import { VideoJDType } from '../consts';
import { useVideoJD } from '../context/VideoJDContext';
import ScriptSettings from './ScriptSettings';
import VideoRecordingFlow from './VideoRecordingFlow';
import VideoTypeSelector from './VideoTypeSelector';

enum ViewMode {
  SCRIPT_SETTINGS = 'SCRIPT_SETTINGS',
  VIDEO_TYPE_SELECTION = 'VIDEO_TYPE_SELECTION',
  VIDEO_RECORDING = 'VIDEO_RECORDING',
}

export const LeftColumn: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewMode>(ViewMode.SCRIPT_SETTINGS);
  const [selectedVideoType, setSelectedVideoType] = useState<VideoJDType>(VideoJDType.AI_AVATAR);

  const handleVideoTypeSelection = () => {
    setCurrentView(ViewMode.VIDEO_TYPE_SELECTION);
  };

  const handleVideoTypeConfirm = () => {
    if (selectedVideoType === VideoJDType.LIVE_RECORDING) {
      setCurrentView(ViewMode.VIDEO_RECORDING);
    } else {
      // For AI Avatar, continue with existing flow
      setCurrentView(ViewMode.SCRIPT_SETTINGS);
    }
  };

  const handleRecordingComplete = (videoUrl: string) => {
    // Handle the completed recording
    setCurrentView(ViewMode.SCRIPT_SETTINGS);
  };

  const handleBackToScriptSettings = () => {
    setCurrentView(ViewMode.SCRIPT_SETTINGS);
  };

  const handleBackToVideoTypeSelection = () => {
    setCurrentView(ViewMode.VIDEO_TYPE_SELECTION);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case ViewMode.SCRIPT_SETTINGS:
        return <ScriptSettings />;

      case ViewMode.VIDEO_TYPE_SELECTION:
        return (
          <VideoTypeSelector
            selectedType={selectedVideoType}
            onTypeChange={setSelectedVideoType}
            onProceed={handleVideoTypeConfirm}
            onBack={handleBackToScriptSettings}
          />
        );

      case ViewMode.VIDEO_RECORDING:
        return (
          <VideoRecordingFlow
            onBack={handleBackToVideoTypeSelection}
            onComplete={handleRecordingComplete}
          />
        );

      default:
        return null;
    }
  };

  return <div className="space-y-6">{renderCurrentView()}</div>;
};
