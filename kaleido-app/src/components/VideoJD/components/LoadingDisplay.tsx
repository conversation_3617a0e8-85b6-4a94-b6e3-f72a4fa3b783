import { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, ArrowLeft, Video, X } from 'lucide-react';

import { getMessagesByTag } from '@/components/FileUploader/LoadingMessages';
import SmallLoader from '@/components/Layouts/SmallLoader';
import { Button } from '@/components/ui/button';

import { VideoJDStatus } from '../consts';

interface LoadingDisplayProps {
  isGenerating: boolean;
  onClose: () => void;
  status?: VideoJDStatus;
}

const LoadingDisplay: React.FC<LoadingDisplayProps> = ({ isGenerating, onClose, status }) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const videoMessages = getMessagesByTag('video_generation');

  useEffect(() => {
    if (isGenerating) {
      const interval = setInterval(() => {
        setCurrentMessageIndex(prev => (prev === videoMessages.length - 1 ? 0 : prev + 1));
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [isGenerating, videoMessages.length]);

  if (!isGenerating) return null;

  const isCompleted = status === VideoJDStatus.COMPLETED;
  const isFailed = status === VideoJDStatus.FAILED;

  const getStatusContent = () => {
    if (isFailed) {
      return {
        icon: <AlertCircle className="w-16 h-16 text-red-500" />,
        title: 'Video Generation Failed',
        titleColor: 'text-red-500',
      };
    }

    if (isCompleted) {
      return {
        icon: <Video className="w-16 h-16 text-green-500" />,
        title: 'Video Generation Complete',
        titleColor: 'text-green-500',
      };
    }

    return {
      icon: (
        <div className="relative flex items-center justify-center">
          <Video className="w-16 h-16 text-purple-500" />
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [1, 0.5, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            <Video className="w-16 h-16 text-purple-500" />
          </motion.div>
        </div>
      ),
      title: 'Generating Your Video',
      titleColor: 'text-purple-500',
    };
  };

  const { icon, title, titleColor } = getStatusContent();

  return (
    <div className="fixed inset-0 bg-gray-100/90 dark:bg-gray-900/90 backdrop-blur-xl z-[9999] flex flex-col items-center">
      {/* Header */}
      <div className="w-full flex items-center justify-between p-6 border-b border-gray-300/30 dark:border-gray-700/30">
        <div className="flex-1">
          {(isCompleted || isFailed) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-700 dark:text-white hover:bg-gray-200/70 dark:hover:bg-gray-700/70 gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Go Back
            </Button>
          )}
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Video Generation Status
        </h2>
        <div className="flex-1 flex justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="hover:bg-gray-200/70 dark:hover:bg-gray-700/70 text-gray-700 dark:text-white"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center justify-center gap-12 max-w-lg mx-auto px-6 text-center">
        {/* Status Icon and Title */}
        <motion.div
          className="text-center flex flex-col items-center"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {icon}
          <h2 className={`text-3xl font-bold mt-6 ${titleColor}`}>{title}</h2>
        </motion.div>

        {/* Loading Messages */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentMessageIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            {isFailed ? (
              <p className="text-gray-500 dark:text-gray-400 text-lg">
                Please try again or contact support if the issue persists.
              </p>
            ) : (
              <>
                <div className="flex items-center justify-center gap-3 mb-4">
                  {!isCompleted && <SmallLoader size="sm" />}
                  <p className="text-gray-800 dark:text-white text-xl font-medium">
                    {isCompleted ? 'Your video is ready!' : videoMessages[currentMessageIndex].text}
                  </p>
                </div>
                {!isCompleted && (
                  <>
                    <motion.div
                      className="w-64 h-1.5 bg-gray-300/50 dark:bg-gray-700/50 rounded-full mx-auto mt-8 overflow-hidden"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{
                        duration: 3,
                        ease: 'linear',
                        repeat: Infinity,
                      }}
                    >
                      <div className="h-full bg-gradient-to-r from-purple-500 to-indigo-500" />
                    </motion.div>
                    <p className="text-gray-500 dark:text-gray-400 mt-8 text-sm">
                      Feel free to leave this page. We'll notify you when your video is ready!
                    </p>
                  </>
                )}
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default LoadingDisplay;
