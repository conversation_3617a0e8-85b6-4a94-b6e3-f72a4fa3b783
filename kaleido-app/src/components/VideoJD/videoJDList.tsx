import { useEffect, useRef, useState } from 'react';

import {
  AlertCircle,
  Briefcase,
  Building2,
  CheckCircle2,
  Clock,
  Factory,
  FileText,
  PenLine,
  Video,
} from 'lucide-react';

import { IJob } from '@/entities/interfaces';
import { cn } from '@/lib/utils';
import { useJobStateStore, jobDataArrayToIJobArray } from '@/stores/unifiedJobStore';

import GenericTable from '../Layouts/GenericTable';
import { VideoJDStatus } from './consts';

interface Column {
  key: string;
  label: string;
  icon: any;
  render?: (value: any, row?: any) => React.ReactNode;
}

const VideoJDList = ({
  jobs = [],
  onJobClick,
  hideDelete = false,
}: {
  jobs: IJob[];
  onJobClick?: (job: IJob) => void;
  hideDelete?: boolean;
}) => {
  const [tableData, setTableData] = useState<IJob[]>(jobs);
  // Add a ref to store the current table data for comparison
  const tableDataRef = useRef<IJob[]>(jobs);

  // Update local state when props change
  useEffect(() => {
    setTableData(jobs);
    tableDataRef.current = jobs;
  }, [jobs]);

  // Subscribe to job state changes using Zustand store
  useEffect(() => {
    // Subscribe to job state changes
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        // Only update if the data is different
        const convertedJobs = jobDataArrayToIJobArray(jobs);
        if (JSON.stringify(tableDataRef.current) !== JSON.stringify(convertedJobs)) {
          setTableData(convertedJobs);
          tableDataRef.current = convertedJobs;
        } else {
        }
      }
    );

    // Clean up subscription
    return () => {
      unsubscribe();
    };
  }, []);

  const handleRowClick = async (job: IJob) => {
    if (onJobClick) {
      onJobClick(job);
    } else {
      // After any interaction that might change the job's status, just mark as updated
      // The store will handle the state updates automatically
      useJobStateStore.getState().markJobAsUpdated(job.id);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case VideoJDStatus.COMPLETED:
        return <CheckCircle2 className="w-4 h-4" />;
      case VideoJDStatus.GENERATING:
        return <Video className="w-4 h-4 animate-pulse" />;
      case VideoJDStatus.SCRIPT_GENERATED:
        return <PenLine className="w-4 h-4" />;
      case VideoJDStatus.FAILED:
        return <AlertCircle className="w-4 h-4" />;
      default:
        return '-';
    }
  };

  const formatStatus = (status: string) => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const columns: Column[] = [
    {
      key: 'companyName',
      label: 'Company',
      icon: Building2,
    },

    {
      key: 'department',
      label: 'Department',
      icon: Factory,
      render: value => value || '-',
    },
    {
      key: 'jobType',
      label: 'Position',
      icon: Briefcase,
    },
    {
      key: 'videoJDs',
      label: 'Video JDs',
      icon: FileText,
      render: videoJDs => {
        if (!videoJDs?.length) return '-';
        const completedJD = videoJDs.find((videoJD: any) => videoJD.status === 'COMPLETED');
        const displayJD = completedJD || videoJDs[0];
        return (
          <div className="flex gap-2">
            <span
              key={displayJD.id}
              className={cn(
                'px-3 py-1.5 rounded-full text-[11px] flex items-center gap-2 text-white/80',
                displayJD.status
                  ? [
                      displayJD.status === VideoJDStatus.COMPLETED &&
                        'bg-[var(--success-bg, rgba(22, 163, 74, 0.1))]',
                      displayJD.status === VideoJDStatus.FAILED &&
                        'bg-[var(--error-bg, rgba(220, 38, 38, 0.1))]',
                      displayJD.status === VideoJDStatus.GENERATING &&
                        'bg-[var(--info-bg, rgba(37, 99, 235, 0.1))]',
                      displayJD.status === VideoJDStatus.SCRIPT_GENERATED && 'bg-purple-500/20',
                      displayJD.status === VideoJDStatus.PENDING && 'bg-gray-500/20',
                    ]
                  : 'bg-gray-500/20'
              )}
            >
              <span
                className={cn(
                  displayJD.status
                    ? [
                        displayJD.status === VideoJDStatus.COMPLETED &&
                          'text-[var(--success-color, #22c55e)]',
                        displayJD.status === VideoJDStatus.FAILED &&
                          'text-[var(--error-color, #ef4444)]',
                        displayJD.status === VideoJDStatus.GENERATING &&
                          'text-[var(--info-color, #2563eb)]',
                        displayJD.status === VideoJDStatus.SCRIPT_GENERATED && 'text-purple-500',
                        displayJD.status === VideoJDStatus.PENDING && 'text-gray-500',
                      ]
                    : 'text-gray-500'
                )}
              >
                {getStatusIcon(displayJD.status)}
              </span>
              <span className="flex items-center gap-1.5">
                {displayJD.status ? formatStatus(displayJD.status) : '-'}
                {displayJD.status === VideoJDStatus.GENERATING && (
                  <span className="w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse" />
                )}
              </span>
            </span>
          </div>
        );
      },
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      icon: Clock,
      render: value => {
        if (!value) return '-';
        return new Date(value).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });
      },
    },
  ];

  return (
    <GenericTable
      data={tableData}
      columns={columns}
      onRowClick={handleRowClick}
      hideDelete={hideDelete}
      expandableContent={job => (
        <div className="p-4 bg-black/20">
          <h3 className="text-lg font-semibold mb-2">Latest Video JD Details</h3>
          {job.videoJDs?.length > 0 ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-400">Language</p>
                <p>{job.videoJDs[0].languageCode || '-'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Script Preview</p>
                <p className="line-clamp-2">{job.videoJDs[0].generatedScript || '-'}</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-400">No video JDs created yet</p>
          )}
        </div>
      )}
    />
  );
};

export default VideoJDList;
