import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';

import { showToast } from '@/components/Toaster';
import { showVideoJDNotification } from '@/components/VideoJDNotification';
import { getAllAvatars, getAvatarsByLanguage, isLanguageSupported } from '@/constants/languages';
import { IVideoJD } from '@/entities/interfaces';
import apiHelper from '@/lib/apiHelper';
import { useJobStateStore } from '@/stores/unifiedJobStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useVideoJDQueueStore } from '@/stores/videoJDQueueStore';
import { useVideoRecordingStore } from '@/stores/videoRecordingStore';
import { Avatar, AvatarGender } from '@/types/avatar';

import { SCRIPTS_SETTINGS, VideoJDStatus } from '../consts';

export type VideoType = 'live' | 'ai' | null;

interface VideoJDContextType {
  settings: {
    gender: AvatarGender;
    avatarId: string;
    videoJDId?: string;
    clientId?: string;
  };
  setSettings: (settings: any) => void;
  scriptsSettings: {
    id: number;
    tone: string;
    language: string;
    scriptLength: string;
  };
  setScriptSettings: (settings: any) => void;
  generatedScript: string;
  setGeneratedScript: (script: string) => void;
  versions: IVideoJD[];
  setVersions: (versions: IVideoJD[] | ((prev: IVideoJD[]) => IVideoJD[])) => void;
  isGeneratingScript: boolean;
  setIsGeneratingScript: (isGenerating: boolean) => void;
  isGeneratingVideo: boolean;
  setIsGeneratingVideo: (isGenerating: boolean) => void;
  isHistoryOpen: boolean;
  setIsHistoryOpen: (isOpen: boolean) => void;
  selectedVersion: IVideoJD | undefined;
  setSelectedVersion: (version: IVideoJD | undefined) => void;
  handleVersionSelect: (version: IVideoJD) => void;
  handleDeleteScript: (version: IVideoJD) => Promise<void>;
  jobData: any; // Simple job data from API
  selectedAvatar: Avatar | null;
  updateSelectedAvatar: (avatar: Avatar | null) => void;
  isProcessing: boolean;
  handleGenderChange: (gender: AvatarGender) => void;
  handleAvatarSelect: (avatar: Avatar) => void;
  availableAvatars: Avatar[];
  allAvatars: Avatar[];
  filteredAvatars: Avatar[];
  isVersionSelected: boolean;
  handleGenerateVideo: () => Promise<void>;
  handleGenerateScript: () => Promise<void>;
  hasAvatarInteraction: boolean;
  setHasAvatarInteraction: (hasInteraction: boolean) => void;
  currentScript: string;
  setCurrentScript: (script: string) => void;
  handleLanguageChange: (language: string) => void;
  pollVideoStatus: (synthesiaVideoId: string) => Promise<void>;
  // New video type selection state
  videoType: VideoType;
  setVideoType: (type: VideoType) => void;
  isCreationMode: boolean;
  setIsCreationMode: (isCreation: boolean) => void;
  hasExistingVideo: boolean;
  existingVideoUrl: string | null;
  existingVideoType: 'synthesia' | 'live' | null;
  handleLiveRecordingClick: () => void;
  handleAIAvatarClick: () => void;
  resetToVideoTypeSelection: () => void;
}

const VideoJDContext = createContext<VideoJDContextType | undefined>(undefined);

export const useVideoJD = () => {
  const context = useContext(VideoJDContext);
  if (!context) {
    throw new Error('useVideoJD must be used within a VideoJDProvider');
  }
  return context;
};

interface VideoJDProviderProps {
  children: React.ReactNode;
  job: any; // Simple job data from VideoJDDetailsSettings
}

interface GenerateVideoResponse {
  synthesiaVideoId: string;
}

interface VideoStatusResponse {
  status: VideoJDStatus;
}

interface VideoJDResponse {
  videoUrl: string;
}

export const VideoJDProvider: React.FC<VideoJDProviderProps> = ({ children, job }) => {
  const router = useRouter();
  const { setJobId, setGeneratedScript: setStoreScript, setVideoJDId } = useVideoRecordingStore();
  const videoJDQueueStore = useVideoJDQueueStore();

  const getValidLanguageCode = (code: string | undefined): string => {
    if (code && isLanguageSupported(code)) {
      return code;
    }
    return 'en-US';
  };

  // Use the video JD data from the job prop (which comes from VideoJDDetailsSettings)
  const videoJDs = job?.videoJDs || [];
  const mostRecentVideoJD = videoJDs[videoJDs.length - 1];
  const defaultLanguage = getValidLanguageCode(mostRecentVideoJD?.languageCode);

  const getInitialGenderAndAvatar = useCallback(() => {
    const firstVersion = videoJDs[0];
    const validLanguage = getValidLanguageCode(firstVersion?.languageCode);
    // Use all avatars instead of language-specific ones
    const avatars = getAllAvatars();

    if (firstVersion?.avatarId) {
      const existingAvatar = avatars.find(avatar => avatar.id === firstVersion.avatarId);
      if (existingAvatar) {
        return {
          gender: existingAvatar.gender === 'male' ? AvatarGender.MALE : AvatarGender.FEMALE,
          avatarId: existingAvatar.id,
        };
      }
    }

    // Default to first female avatar if no previous selection
    const femaleAvatars = avatars.filter(a => a.gender === 'female');
    const firstAvatar = femaleAvatars[0] || avatars[0];
    return {
      gender: firstAvatar?.gender === 'male' ? AvatarGender.MALE : AvatarGender.FEMALE,
      avatarId: firstAvatar?.id || '',
    };
  }, [videoJDs]);

  const [settings, setSettings] = useState(getInitialGenderAndAvatar);

  const [scriptsSettings, setScriptSettings] = useState(() => {
    const firstVersion = videoJDs[0];
    return {
      ...SCRIPTS_SETTINGS[0],
      language: firstVersion?.languageCode || 'en-US',
      scriptLength: firstVersion?.scriptLength || 'medium',
    };
  });

  const [generatedScript, setGeneratedScript] = useState('');
  const [versions, setVersions] = useState<IVideoJD[]>(videoJDs);
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<IVideoJD | undefined>(() => {
    // Get the first version from the list if available
    const firstVersion = videoJDs[0];

    if (!firstVersion && job?.id) {
      // If no versions exist, create a default one
      return {
        id: `${job.id}`,
        jobId: job.id,
        clientId: job?.clientId || '',
        status: VideoJDStatus.PENDING,
        gender: settings.gender,
        avatarId: settings.avatarId,
        languageCode: scriptsSettings.language,
        generatedScript: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        voiceSpeed: 1,
        voicePitch: 1,
      } as IVideoJD;
    }

    return firstVersion;
  });
  const [selectedAvatar, setSelectedAvatar] = useState<Avatar | null>(() => {
    const validLanguage = getValidLanguageCode(defaultLanguage);
    const avatars = getAllAvatars();

    let foundAvatar = null;

    // First try to find by the settings avatarId
    const initialSettings = getInitialGenderAndAvatar();
    if (initialSettings.avatarId) {
      const avatar = avatars.find(a => a.id === initialSettings.avatarId);
      if (avatar) {
        foundAvatar = {
          ...avatar,
          thumbnailUrl: avatar.thumbnailMediumUrl,
          thumbnailMediumUrl: avatar.thumbnailMediumUrl,
          language: validLanguage,
        };
      }
    }

    // If not found, try mostRecentVideoJD
    if (!foundAvatar && mostRecentVideoJD?.avatarId) {
      const avatar = avatars.find(avatar => avatar.id === mostRecentVideoJD.avatarId);
      if (avatar) {
        foundAvatar = {
          ...avatar,
          thumbnailUrl: avatar.thumbnailMediumUrl,
          thumbnailMediumUrl: avatar.thumbnailMediumUrl,
          language: validLanguage,
        };
      }
    }

    // If still not found, get first avatar matching gender
    if (!foundAvatar) {
      const genderAvatars = avatars.filter(a => a.gender === initialSettings.gender.toLowerCase());
      const avatar = genderAvatars[0] || avatars[0];
      if (avatar) {
        foundAvatar = {
          ...avatar,
          thumbnailUrl: avatar.thumbnailMediumUrl,
          thumbnailMediumUrl: avatar.thumbnailMediumUrl,
          language: validLanguage,
        };
      }
    }

    return foundAvatar;
  });
  const [hasAvatarInteraction, setHasAvatarInteraction] = useState(false);
  const [currentScript, setCurrentScript] = useState('');

  // Update script values and versions when videoJDs data becomes available
  useEffect(() => {
    if (videoJDs && videoJDs.length > 0) {
      const latestVideoJD = videoJDs[0];
      if (latestVideoJD.generatedScript) {
        setGeneratedScript(latestVideoJD.generatedScript);
        setCurrentScript(latestVideoJD.generatedScript);
        // Update the store when we have a script
        setStoreScript(latestVideoJD.generatedScript);
      }
      setVersions(videoJDs);

      // Update selectedVersion if we don't have one or if it's different
      if (!selectedVersion || selectedVersion.id !== latestVideoJD.id) {
        setSelectedVersion(latestVideoJD);
      }
    }
  }, [videoJDs, selectedVersion]);

  // New video type selection state
  const [videoType, setVideoType] = useState<VideoType>(null);
  const [isCreationMode, setIsCreationMode] = useState(false);

  // Detect existing videos
  const hasExistingVideo = useMemo(() => {
    const latestVideoJD = videoJDs[0];
    return Boolean(latestVideoJD?.videoUrl || latestVideoJD?.status === VideoJDStatus.COMPLETED);
  }, [videoJDs]);

  const existingVideoUrl = useMemo(() => {
    const latestVideoJD = videoJDs[0];
    return latestVideoJD?.videoUrl || null;
  }, [videoJDs]);

  const existingVideoType = useMemo((): 'synthesia' | 'live' | null => {
    const latestVideoJD = videoJDs[0];
    if (!latestVideoJD?.videoUrl) return null;

    // Check the type field to determine if it's live recording or AI avatar
    if (latestVideoJD.type === 'LIVE_RECORDING') {
      return 'live';
    } else if (latestVideoJD.type === 'AI_AVATAR') {
      return 'synthesia';
    }

    // Fallback: if no type field, assume synthesia for backward compatibility
    return 'synthesia';
  }, [videoJDs]);

  // Initialize selectedAvatar when loading a version
  useEffect(() => {
    if (selectedVersion?.avatarId) {
      const currentAvatar = getAvatarsByLanguage(selectedVersion.languageCode).find(
        avatar => avatar.id === selectedVersion.avatarId
      );

      if (currentAvatar) {
        const mappedAvatar = {
          ...currentAvatar,
          thumbnailUrl: currentAvatar.thumbnailMediumUrl,
          thumbnailMediumUrl: currentAvatar.thumbnailMediumUrl,
          language: selectedVersion.languageCode,
        };
        setSelectedAvatar(mappedAvatar);
      }
    }
  }, [selectedVersion]);

  // Ensure avatar is set when we have an avatarId in settings
  useEffect(() => {
    if (settings.avatarId && !selectedAvatar) {
      const avatar = allAvatars.find(a => a.id === settings.avatarId);
      if (avatar) {
        setSelectedAvatar(avatar);
      }
    }
  }, [settings.avatarId, selectedAvatar]);

  // Update handleVersionSelect to not trigger hasAvatarInteraction
  const handleVersionSelect = useCallback((version: IVideoJD) => {
    setSelectedVersion(version);
    setSettings({
      gender: version.gender,
      avatarId: version?.avatarId ?? '',
    });
    setScriptSettings(prev => ({
      ...prev,
      language: version.languageCode,
      tone: prev.tone,
      scriptLength: version.scriptLength || 'medium',
    }));
    const script = version.generatedScript || '';
    setGeneratedScript(script);
    setCurrentScript(script); // Keep both scripts in sync

    const currentAvatar = getAvatarsByLanguage(version.languageCode).find(
      avatar => avatar.id === version.avatarId
    );

    if (currentAvatar) {
      const mappedAvatar = {
        ...currentAvatar,
        thumbnailUrl: currentAvatar.thumbnailMediumUrl,
        thumbnailMediumUrl: currentAvatar.thumbnailMediumUrl,
        language: version.languageCode,
      };
      setSelectedAvatar(mappedAvatar);
    }
  }, []);

  const handleDeleteScript = useCallback(
    async (version: IVideoJD) => {
      try {
        await apiHelper.delete(`/video-jd/${version.id}`);
        setVersions(prevVersions => prevVersions.filter(v => v.id !== version.id));

        // If the deleted version was selected, clear the selection
        if (selectedVersion?.id === version.id) {
          setSelectedVersion(undefined);
        }

        // Update the job state store to notify all components
        useJobStateStore.getState().markJobAsUpdated(job.id);

        showToast({
          message: 'Script deleted successfully',
          isSuccess: true,
        });
      } catch (error) {
        console.error('Error deleting script:', error);
        showToast({
          message: 'Failed to delete script',
          isSuccess: false,
        });
      }
    },
    [selectedVersion]
  );

  // Only update avatar without triggering interaction
  const updateSelectedAvatar = useCallback((avatar: Avatar | null) => {
    setSelectedAvatar(avatar);
    if (avatar) {
      setSettings(prev => ({
        ...prev,
        avatarId: avatar.id,
        gender: avatar.gender === 'male' ? AvatarGender.MALE : AvatarGender.FEMALE,
      }));
    }
  }, []);

  // Get all avatars regardless of language
  const allAvatars = useMemo(() => {
    return getAllAvatars().map(avatar => ({
      ...avatar,
      thumbnailUrl: avatar.thumbnailMediumUrl,
      thumbnailMediumUrl: avatar.thumbnailMediumUrl,
      language: scriptsSettings.language, // This is just for compatibility, not for filtering
    }));
  }, [scriptsSettings.language]);

  // We're not filtering by language anymore, just keeping this for compatibility
  const filteredAvatars = useMemo(() => {
    return allAvatars;
  }, [allAvatars]);

  // Get available avatars for current gender only (not filtered by language)
  const availableAvatars = useMemo(() => {
    return allAvatars
      .filter(avatar => avatar.gender === settings.gender.toLowerCase())
      .map(avatar => ({
        ...avatar,
        thumbnailUrl: avatar.thumbnailMediumUrl,
        thumbnailMediumUrl: avatar.thumbnailMediumUrl,
        language: scriptsSettings.language, // This is just for compatibility, not for filtering
      }));
  }, [allAvatars, settings.gender, scriptsSettings.language]);

  // Handle language changes - now only affects script generation, not avatar selection
  const handleLanguageChange = useCallback((language: string) => {
    setScriptSettings(prev => ({
      ...prev,
      language,
    }));

    // No longer need to change avatar when language changes
    // The avatar selection is now independent of language
  }, []);

  // We no longer need to change avatar when language changes
  // This effect is only needed for gender changes
  useEffect(() => {
    const currentGenderAvatars = availableAvatars;
    const isCurrentAvatarAvailable = currentGenderAvatars.some(
      avatar => avatar.id === settings.avatarId
    );

    if (!isCurrentAvatarAvailable && currentGenderAvatars.length > 0) {
      handleAvatarSelect(currentGenderAvatars[0]);
    }
  }, [availableAvatars, settings.avatarId]);

  const handleGenderChange = useCallback(
    (gender: AvatarGender) => {
      const genderKey = gender.toLowerCase() as 'male' | 'female';

      // Get avatars of the selected gender from all avatars
      const genderAvatars = allAvatars
        .filter(avatar => avatar.gender === genderKey)
        .map(avatar => ({
          ...avatar,
          thumbnailUrl: avatar.thumbnailMediumUrl,
          thumbnailMediumUrl: avatar.thumbnailMediumUrl,
          language: scriptsSettings.language, // Just for compatibility
        }));

      // Try to keep the same avatar if it exists for the new gender
      const currentAvatar = availableAvatars.find(avatar => avatar.id === settings.avatarId);
      const sameNameAvatar = currentAvatar
        ? genderAvatars.find(avatar => avatar.name === currentAvatar.name)
        : null;

      const defaultAvatar = sameNameAvatar || genderAvatars[0];

      if (defaultAvatar) {
        handleAvatarSelect(defaultAvatar);
        setSettings(prev => ({
          ...prev,
          gender,
        }));
      }
    },
    [allAvatars, settings.avatarId, availableAvatars, scriptsSettings.language]
  );

  // This is the only function that should set hasAvatarInteraction to true
  const handleAvatarSelect = useCallback((avatar: Avatar) => {
    setSelectedAvatar(avatar);
    setSettings(prev => ({
      ...prev,
      avatarId: avatar.id,
      gender: avatar.gender === 'male' ? AvatarGender.MALE : AvatarGender.FEMALE,
    }));
  }, []);

  const startPollingVideoStatus = useCallback(
    async (synthesiaVideoId: string) => {
      const pollInterval = setInterval(async () => {
        try {
          const data = await apiHelper.get<VideoStatusResponse>(
            `/video-jd/status/${synthesiaVideoId}`
          );
          setIsGeneratingVideo(true);

          if (data.status === VideoJDStatus.COMPLETED) {
            clearInterval(pollInterval);
            const updatedVideoJD = await apiHelper.get<VideoJDResponse>(
              `/video-jd/${selectedVersion?.id}`
            );

            setSelectedVersion(prevVersion => ({
              ...prevVersion!,
              status: VideoJDStatus.COMPLETED,
              videoUrl: updatedVideoJD.videoUrl,
            }));

            // Update versions list with the completed status
            setVersions(prevVersions =>
              prevVersions.map(v =>
                v.id === selectedVersion?.id
                  ? {
                      ...v,
                      status: VideoJDStatus.COMPLETED,
                      videoUrl: updatedVideoJD.videoUrl,
                    }
                  : v
              )
            );

            showToast({
              message: 'Video generation completed!',
              isSuccess: true,
            });

            // Show VideoJD notification
            showVideoJDNotification({
              message: 'Your video job description is ready!',
              videoUrl: updatedVideoJD.videoUrl,
            });

            setIsGeneratingVideo(false);

            // Update the videoJDStore directly to ensure it has the latest status
            if (synthesiaVideoId) {
              try {
                // Import the videoJDStore directly to avoid circular dependencies
                const { useVideoJDStore } = await import('@/stores/videoJDStore');

                // First, stop tracking if it's still being tracked
                if (useVideoJDStore.getState().generatingVideoJDs[synthesiaVideoId]) {
                  useVideoJDStore
                    .getState()
                    .updateVideoJDStatus(synthesiaVideoId, VideoJDStatus.COMPLETED);
                }
              } catch (error) {
                console.error('Error updating videoJDStore:', error);
              }
            }

            // Update the job state store to notify all components about video completion
            useJobStateStore.getState().markJobAsUpdated(job.id);

            // Use jobEditStore to refresh job data if needed
            try {
              await useJobStore.getState().fetchJobById(job.id, true);
            } catch (error) {
              console.error('Error refreshing job data after video completion:', error);
            }
          } else if (data.status === VideoJDStatus.GENERATING) {
            setSelectedVersion(prevVersion => ({
              ...prevVersion!,
              status: VideoJDStatus.GENERATING,
            }));
            setIsGeneratingVideo(true);
          } else if (data.status === VideoJDStatus.FAILED) {
            clearInterval(pollInterval);
            setSelectedVersion(prevVersion => ({
              ...prevVersion!,
              status: VideoJDStatus.FAILED,
            }));

            // Update versions list with the failed status
            setVersions(prevVersions =>
              prevVersions.map(v =>
                v.id === selectedVersion?.id
                  ? {
                      ...v,
                      status: VideoJDStatus.FAILED,
                    }
                  : v
              )
            );

            showToast({
              message: 'Video generation failed. Please try again.',
              isSuccess: false,
            });

            // Update the job state store to notify all components about video failure
            useJobStateStore.getState().markJobAsUpdated(job.id);

            // Use jobEditStore to refresh job data if needed
            try {
              await useJobStore.getState().fetchJobById(job.id, true);
            } catch (error) {
              console.error('Error refreshing job data after video failure:', error);
            }
          }
        } catch (error) {
          console.error('Error polling video status:', error);
          clearInterval(pollInterval);
          showToast({
            message: 'Failed to check video status',
            isSuccess: false,
          });

          // For error case, just mark as updated without forcing a full refresh
          useJobStateStore.getState().markJobAsUpdated(job.id);
        }
      }, 60000); // Poll every 60 seconds (1 minute)

      // Clear interval after 30 minutes
      setTimeout(() => clearInterval(pollInterval), 30 * 60 * 1000);
    },
    [selectedVersion?.id]
  );

  const handleGenerateVideo = useCallback(async () => {
    try {
      // Prevent multiple clicks
      if (!selectedVersion?.id || isGeneratingVideo) {
        return;
      }

      // Use selectedAvatar if available, otherwise try to get it from settings
      const avatarToUse =
        selectedAvatar ||
        (settings.avatarId ? allAvatars.find(a => a.id === settings.avatarId) : null);

      if (!avatarToUse) {
        throw new Error('Please select an avatar before generating a video');
      }

      setIsGeneratingVideo(true);

      // Update version status to GENERATING before making the API call
      setSelectedVersion(prev => ({
        ...prev!,
        status: VideoJDStatus.GENERATING,
      }));

      // Update versions list to show generating status
      setVersions(prevVersions =>
        prevVersions.map(v =>
          v.id === selectedVersion.id ? { ...v, status: VideoJDStatus.GENERATING } : v
        )
      );

      const data = await apiHelper.post<GenerateVideoResponse>(
        `/video-jd/${selectedVersion.id}/generate?clientId=${job?.clientId || ''}`,
        {
          test: false,
          visibility: 'public',
          aspectRatio: '16:9',
          title: `${job?.jobType || 'Job'}-${job?.id || ''}`,
          description: 'AI-generated job description video',
          input: [
            {
              scriptText: currentScript,
              avatar: avatarToUse.id,
              background: '',
            },
          ],
          languageCode: scriptsSettings.language,
          tone: scriptsSettings.tone,
          callbackId: selectedVersion.id,
          soundtrack: 'corporate',
        }
      );

      // Update the version with current script and status
      await apiHelper.patch(`/video-jd/${selectedVersion.id}/patch`, {
        avatarId: avatarToUse.id,
        languageCode: scriptsSettings.language,
        tone: scriptsSettings.tone,
        generatedScript: currentScript,
        status: VideoJDStatus.GENERATING,
      });

      // Update the job state store to notify all components
      useJobStateStore.getState().markJobAsUpdated(job.id);

      // Add this video JD to the queue tracking store
      // Note: The backend automatically creates the queue monitoring job
      // This is just for frontend tracking in case we need manual intervention
      if (data.synthesiaVideoId) {
        const queueJobId = `manual-${selectedVersion.id}-${Date.now()}`;
        videoJDQueueStore.addJob({
          id: queueJobId,
          status: 'generating',
          progress: 10,
          videoJDId: selectedVersion.id,
          synthesiaVideoId: data.synthesiaVideoId,
          jobTitle: job?.jobType,
          createdAt: new Date().toISOString(),
          message: 'Video generation started',
        });
      }

      showToast({
        message: 'Your video is being generated. This may take a few minutes.',
        isSuccess: true,
      });

      await startPollingVideoStatus(data.synthesiaVideoId);
    } catch (error) {
      console.error('Error:', error);
      // Reset status if generation fails
      setSelectedVersion(prev => ({
        ...prev!,
        status: VideoJDStatus.SCRIPT_GENERATED,
      }));

      // Update versions list with failed status
      setVersions(prevVersions =>
        prevVersions.map(v =>
          v.id === selectedVersion?.id ? { ...v, status: VideoJDStatus.SCRIPT_GENERATED } : v
        )
      );

      showToast({
        message:
          error instanceof Error ? error.message : 'Failed to generate video. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsGeneratingVideo(false);
    }
  }, [
    selectedVersion,
    selectedAvatar,
    currentScript,
    job?.clientId,
    job?.id,
    job?.jobType,
    scriptsSettings.language,
    scriptsSettings.tone,
    startPollingVideoStatus,
    isGeneratingVideo,
    settings.avatarId,
    allAvatars,
  ]);

  const handleGenerateScript = async () => {
    if (!job?.id) return;

    try {
      setIsGeneratingScript(true);
      const response = await apiHelper.post(`/video-jd/generate-script`, {
        settings: {
          id: selectedVersion?.id,
          clientId: job?.clientId || '',
          jobId: job.id,
          gender: settings.gender,
          languageCode: scriptsSettings.language,
          tone: scriptsSettings.tone,
          scriptLength: scriptsSettings.scriptLength,
          avatarId: settings.avatarId,
          status: VideoJDStatus.SCRIPT_GENERATED,
          jobType: job?.jobType || 'Unknown Position',
        },
      });

      if (response.generatedScript) {
        setGeneratedScript(response.generatedScript);
        setCurrentScript(response.generatedScript); // Keep both scripts in sync
        setVersions(prevVersions =>
          prevVersions.map(v =>
            v.id === selectedVersion.id ? { ...v, generatedScript: response.generatedScript } : v
          )
        );

        // Update the video recording store to ensure all components have the latest script
        setStoreScript(response.generatedScript);

        // Update the job state store to notify all components
        useJobStateStore.getState().markJobAsUpdated(job.id);

        showToast({
          message: 'Script generated successfully',
          isSuccess: true,
        });
      }
    } catch (error) {
      console.error('Error generating script:', error);
      showToast({
        message: 'Failed to generate script',
        isSuccess: false,
      });
    } finally {
      setIsGeneratingScript(false);
    }
  };

  // Handler for live recording click
  const handleLiveRecordingClick = useCallback(() => {
    // Store data in Zustand store and include videoId in URL for tracking

    if (job?.id) {
      setJobId(job.id);
    }
    setStoreScript(generatedScript);

    // Set videoJDId if available
    if (selectedVersion?.id) {
      setVideoJDId(selectedVersion.id);
    }

    // Navigate to video recording page with videoId in URL for tracking
    const params = new URLSearchParams();
    if (selectedVersion?.id) {
      params.set('videoId', selectedVersion.id);
    }
    if (job?.id) {
      params.set('jobId', job.id);
    }

    const url = params.toString() ? `/video-recording?${params.toString()}` : '/video-recording';
    router.push(url);
  }, [
    job?.id,
    generatedScript,
    selectedVersion?.id,
    setJobId,
    setStoreScript,
    setVideoJDId,
    router,
  ]);

  // Handler for AI avatar click
  const handleAIAvatarClick = useCallback(() => {
    setVideoType('ai');
    setIsCreationMode(true);
  }, []);

  // Handler to reset back to video type selection
  const resetToVideoTypeSelection = useCallback(() => {
    setVideoType(null);
    setIsCreationMode(true);
  }, []);

  const value = {
    settings,
    setSettings,
    scriptsSettings,
    setScriptSettings,
    generatedScript,
    setGeneratedScript,
    versions,
    setVersions,
    isGeneratingScript,
    setIsGeneratingScript,
    isGeneratingVideo,
    setIsGeneratingVideo,
    isHistoryOpen,
    setIsHistoryOpen,
    selectedVersion,
    setSelectedVersion,
    handleVersionSelect,
    handleDeleteScript,
    jobData: job, // Use the simple job data passed from VideoJDDetailsSettings
    selectedAvatar,
    updateSelectedAvatar,
    isProcessing: isGeneratingVideo,
    handleGenderChange,
    handleAvatarSelect,
    availableAvatars,
    allAvatars,
    filteredAvatars,
    isVersionSelected: selectedVersion !== undefined,
    handleGenerateVideo,
    handleGenerateScript,
    hasAvatarInteraction,
    setHasAvatarInteraction,
    currentScript,
    setCurrentScript,
    handleLanguageChange,
    pollVideoStatus: startPollingVideoStatus,
    // New video type selection properties
    videoType,
    setVideoType,
    isCreationMode,
    setIsCreationMode,
    hasExistingVideo,
    existingVideoUrl,
    existingVideoType,
    handleLiveRecordingClick,
    handleAIAvatarClick,
    resetToVideoTypeSelection,
  };

  return <VideoJDContext.Provider value={value}>{children}</VideoJDContext.Provider>;
};
