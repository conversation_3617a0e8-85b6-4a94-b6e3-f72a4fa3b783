import apiHelper from '@/lib/apiHelper';
import { render, waitFor } from '@testing-library/react';

import { VideoJDStatus } from '../consts';
import { useVideoJD, VideoJDProvider } from './VideoJDContext';

// Mock dependencies
jest.mock('@/lib/apiHelper');
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobStateStore: {
    getState: jest.fn(() => ({
      markJobAsUpdated: jest.fn(),
      refreshJob: jest.fn(),
    })),
  },
  useJobStore: {
    getState: jest.fn(() => ({
      fetchJobById: jest.fn(),
      onWorkerComplete: jest.fn(),
    })),
  },
}));

jest.mock('@/stores/videoJDStore', () => ({
  useVideoJDStore: {
    getState: () => ({
      updateVideoJDStatus: jest.fn(),
      generatingVideoJDs: {},
    }),
  },
}));

jest.mock('../../VideoJDNotification', () => ({
  showVideoJDNotification: jest.fn(),
}));

// Mock window.location.reload
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: {
    reload: mockReload,
  },
  writable: true,
});

// Mock console.log to verify logging
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();

// Test component that uses the VideoJD context
const TestComponent = () => {
  const {
    pollVideoStatus,
    isGeneratingVideo,
    videoType,
    isCreationMode,
    resetToVideoTypeSelection,
  } = useVideoJD();

  return (
    <div>
      <button onClick={() => pollVideoStatus('test-synthesia-id')} data-testid="poll-button">
        Poll Status
      </button>
      <div data-testid="generating-status">
        {isGeneratingVideo ? 'Generating' : 'Not Generating'}
      </div>
      <div data-testid="video-type">{videoType || 'null'}</div>
      <div data-testid="creation-mode">{isCreationMode ? 'true' : 'false'}</div>
      <button onClick={resetToVideoTypeSelection} data-testid="reset-button">
        Reset to Video Type Selection
      </button>
    </div>
  );
};

describe('VideoJDContext - Full Page Refresh on Completion', () => {
  const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;

  const mockJob = {
    id: 'test-job-id',
    title: 'Test Job',
    clientId: 'test-client-id',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockConsoleLog.mockClear();
    mockApiHelper.get.mockClear();

    // Clear any existing intervals
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('should trigger full page refresh when video generation completes successfully', async () => {
    // Mock API responses
    mockApiHelper.get
      .mockResolvedValueOnce({
        // First call - video is generating
        status: VideoJDStatus.GENERATING,
      })
      .mockResolvedValueOnce({
        // Second call - video is completed
        status: VideoJDStatus.COMPLETED,
        videoUrl: 'https://example.com/video.mp4',
      })
      .mockResolvedValueOnce({
        // Third call - get updated video JD
        videoUrl: 'https://example.com/video.mp4',
        status: VideoJDStatus.COMPLETED,
      });

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    // Start polling
    const pollButton = getByTestId('poll-button');
    pollButton.click();

    // Fast-forward through the polling interval
    jest.advanceTimersByTime(60000); // First poll (60 seconds)
    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
    });

    jest.advanceTimersByTime(60000); // Second poll - completion (60 seconds)

    // Wait for all async operations to complete
    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledTimes(3);
    });

    // Fast-forward the refresh timeout
    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      // Check that the video generation state is updated correctly
      expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
    });
  });

  it('should not trigger refresh when video generation fails', async () => {
    mockApiHelper.get
      .mockResolvedValueOnce({
        status: VideoJDStatus.GENERATING,
      })
      .mockResolvedValueOnce({
        status: VideoJDStatus.FAILED,
      });

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    const pollButton = getByTestId('poll-button');
    pollButton.click();

    jest.advanceTimersByTime(60000); // First poll (60 seconds)
    jest.advanceTimersByTime(60000); // Second poll - failure (60 seconds)

    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledTimes(2);
    });

    // Wait a bit more to ensure no additional API calls are made
    jest.advanceTimersByTime(5000);

    // Should have made the status call but stopped polling after failure
    expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
  });

  it('should not trigger refresh when video is still generating', async () => {
    mockApiHelper.get.mockResolvedValue({
      status: VideoJDStatus.GENERATING,
    });

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    const pollButton = getByTestId('poll-button');
    pollButton.click();

    // Run several polling cycles
    for (let i = 0; i < 3; i++) {
      jest.advanceTimersByTime(60000); // 60 seconds per poll
      await waitFor(() => {
        // Wait for the polling to complete
        expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
      });
    }

    // Should have made at least 3 status calls during polling
    expect(mockApiHelper.get).toHaveBeenCalledTimes(3);
    expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
  });

  it('should update job state store before triggering refresh', async () => {
    const mockMarkJobAsUpdated = jest.fn();
    const mockRefreshJobData = jest.fn();

    // Mock the job state store
    const { useJobStateStore } = require('@/stores/unifiedJobStore');
    useJobStateStore.getState.mockReturnValue({
      markJobAsUpdated: mockMarkJobAsUpdated,
    });

    // Mock the job store
    const { useJobStore } = require('@/stores/unifiedJobStore');
    useJobStore.getState.mockReturnValue({
      fetchJobById: mockRefreshJobData,
    });

    mockApiHelper.get
      .mockResolvedValueOnce({
        status: VideoJDStatus.GENERATING,
      })
      .mockResolvedValueOnce({
        status: VideoJDStatus.COMPLETED,
        videoUrl: 'https://example.com/video.mp4',
      })
      .mockResolvedValueOnce({
        videoUrl: 'https://example.com/video.mp4',
        status: VideoJDStatus.COMPLETED,
      })
      .mockResolvedValueOnce({
        success: true,
        data: mockJob,
      });

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    const pollButton = getByTestId('poll-button');
    pollButton.click();

    jest.advanceTimersByTime(60000); // First poll (60 seconds)
    jest.advanceTimersByTime(60000); // Second poll - completion (60 seconds)

    await waitFor(() => {
      expect(mockMarkJobAsUpdated).toHaveBeenCalledWith('test-job-id');
      expect(mockRefreshJobData).toHaveBeenCalledWith('test-job-id', true);
    });

    jest.advanceTimersByTime(3000); // Trigger polling

    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
    });
  });

  it('should handle API errors gracefully and not trigger refresh', async () => {
    mockApiHelper.get
      .mockResolvedValueOnce({
        status: VideoJDStatus.GENERATING,
      })
      .mockRejectedValueOnce(new Error('API Error'));

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    const pollButton = getByTestId('poll-button');
    pollButton.click();

    jest.advanceTimersByTime(60000); // First poll (60 seconds)
    jest.advanceTimersByTime(60000); // Second poll - error (60 seconds)

    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledTimes(2);
    });

    // Wait and ensure polling was attempted
    jest.advanceTimersByTime(60000);
    expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
  });

  it('should show notification before triggering refresh', async () => {
    const { showVideoJDNotification } = require('../../VideoJDNotification');

    mockApiHelper.get
      .mockResolvedValueOnce({
        status: VideoJDStatus.GENERATING,
      })
      .mockResolvedValueOnce({
        status: VideoJDStatus.COMPLETED,
        videoUrl: 'https://example.com/video.mp4',
      })
      .mockResolvedValueOnce({
        videoUrl: 'https://example.com/video.mp4',
        status: VideoJDStatus.COMPLETED,
      })
      .mockResolvedValueOnce({
        success: true,
        data: mockJob,
      });

    const { getByTestId } = render(
      <VideoJDProvider job={mockJob}>
        <TestComponent />
      </VideoJDProvider>
    );

    const pollButton = getByTestId('poll-button');
    pollButton.click();

    jest.advanceTimersByTime(60000); // First poll (60 seconds)
    jest.advanceTimersByTime(60000); // Second poll - completion (60 seconds)

    await waitFor(() => {
      expect(showVideoJDNotification).toHaveBeenCalledWith({
        message: 'Your video job description is ready!',
        videoUrl: 'https://example.com/video.mp4',
      });
    });

    jest.advanceTimersByTime(3000); // Trigger polling

    await waitFor(() => {
      expect(mockApiHelper.get).toHaveBeenCalledWith('/video-jd/status/test-synthesia-id');
    });
  });

  describe('Video Type Selection Reset', () => {
    it('should reset video type to null and set creation mode to true when resetToVideoTypeSelection is called', async () => {
      const { getByTestId } = render(
        <VideoJDProvider job={mockJob}>
          <TestComponent />
        </VideoJDProvider>
      );

      // Initially, video type should be null and creation mode should be false
      expect(getByTestId('video-type')).toHaveTextContent('null');
      expect(getByTestId('creation-mode')).toHaveTextContent('false');

      // Click the reset button
      const resetButton = getByTestId('reset-button');
      resetButton.click();

      // After reset, video type should be null and creation mode should be true
      await waitFor(() => {
        expect(getByTestId('video-type')).toHaveTextContent('null');
        expect(getByTestId('creation-mode')).toHaveTextContent('true');
      });
    });
  });
});
