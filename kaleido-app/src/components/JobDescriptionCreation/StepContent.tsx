'use client';

import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import React, { FC } from 'react';

import { useJobs } from '@/contexts/jobs/JobsContext';

// Define base props that all step components will receive
interface BaseStepComponentProps {
  handleNext?: () => void;
  handleBack?: () => void;
  handleSkip?: () => void;
  handlePublish?: () => void;
  onEdit?: () => void;
}

// Create a type guard to ensure StepComponent has the correct props
type StepComponentType = FC<BaseStepComponentProps>;

const StepContent: FC = () => {
  const searchParams = useSearchParams() ?? new URLSearchParams();
  const currentStep = searchParams.get('step');
  const isPublished = currentStep === 'published';

  const {
    activeStep: step,
    steps,
    handleNext,
    handleBack,
    handleSkip,
    setActiveStep,
    updateURLAndStorage,
  } = useJobs();

  if (!steps[step]) {
    console.error('Invalid step:', step, 'Available steps:', steps.length);
    return null;
  }

  // Cast the component to ensure it has the correct props
  const StepComponent = steps[step].component as unknown as StepComponentType;

  // Special styling for published step to ensure it's centered and takes full width
  // Also ensure responsive behavior for all steps
  const containerClasses = isPublished
    ? 'w-full h-full flex justify-center items-center'
    : 'w-full h-full flex flex-col overflow-hidden';

  return (
    <div className={`${containerClasses} overflow-visible max-w-full pb-16`}>
      <StepComponent
        handleNext={handleNext}
        handleBack={handleBack}
        handleSkip={handleSkip}
        handlePublish={() => {
          setActiveStep(steps.length - 2);
          updateURLAndStorage(steps.length - 2);
        }}
      />
    </div>
  );
};

export default StepContent;
