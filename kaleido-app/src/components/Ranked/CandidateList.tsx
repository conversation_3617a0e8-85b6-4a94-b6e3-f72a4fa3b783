import { Filter, Search, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import JobDetailsModal from './JobDetailsModal';
import { ScoutJob } from './types';
import SmoothButton from '../ui/SmoothButton';

import { AdvancedFilterPanel } from '@/components/CandidateFilters';
import CandidateProfileCard from '@/components/Candidates/CandidateProfileCard';
import { useJobFilters } from '@/hooks/useJobFilters';
import apiHelper from '@/lib/apiHelper';
import { Candidate } from '@/services/candidate.service';
import { useJobStore } from '@/stores/unifiedJobStore';

interface CandidateListProps {
  jobs: ScoutJob[];
}

const CandidateList: React.FC<CandidateListProps> = ({ jobs }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedJob, setSelectedJob] = useState<ScoutJob | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loadingCandidates, setLoadingCandidates] = useState(true);

  // Get job store state and methods
  const { currentJob, candidates: storeCandidates, stats, fetchCandidates } = useJobStore();

  // Use our custom hook for job filtering
  const {
    filteredJobs,
    filters,
    searchTerm,
    loading,
    handleFilterChange,
    handleSearchChange,
    clearFilters,
  } = useJobFilters({ jobs });

  useEffect(() => {
    const jobId = searchParams.get('jobId');
    if (jobId) {
      const job = jobs.find(j => j.id === jobId);
      if (job) {
        setSelectedJob(job);
      }
    }
  }, [searchParams, jobs]);

  // Subscribe to job store updates for real-time candidate updates
  useEffect(() => {
    const unsubscribe = useJobStore.subscribe(
      state => ({
        candidates: state.candidates,
        allCandidatesFlat: state.allCandidatesFlat,
        stats: state.stats,
      }),
      (current, previous) => {
        // Update local candidates when store changes
        if (current.allCandidatesFlat && current.allCandidatesFlat.length > 0) {
          // Transform store candidates to match the expected format
          const transformedCandidates = current.allCandidatesFlat.map(candidate => ({
            ...candidate,
            myProfileImage: candidate.profileImage || undefined,
            sourceType: candidate.sourceType || 'MANUAL',
            yearsOfExperience: candidate.yearsOfExperience || 0,
          }));
          setCandidates(transformedCandidates as any);
          setLoadingCandidates(false);
        }
      },
      {
        equalityFn: Object.is,
        fireImmediately: true,
      }
    );

    return () => {
      unsubscribe();
    };
  }, []);

  // Fetch candidates initially
  useEffect(() => {
    const fetchInitialCandidates = async () => {
      // First check if we have a selected job in the store
      const jobId = searchParams.get('jobId') || currentJob?.id;

      if (jobId && fetchCandidates) {
        setLoadingCandidates(true);
        try {
          // Try to fetch from job store first
          await fetchCandidates(jobId);
        } catch (error) {
          console.error('Error fetching candidates from store:', error);
        }
      }

      // If no job or store fetch failed, use the API directly
      if (!storeCandidates || (storeCandidates && Object.keys(storeCandidates).length === 0)) {
        setLoadingCandidates(true);
        try {
          const response = await apiHelper.get('/candidates?limit=12');
          setCandidates(response.items || []);
        } catch (error) {
          console.error('Error fetching candidates:', error);
          // For demo purposes, generate mock candidates if API fails
          const mockCandidates = Array.from({ length: 12 }, (_, i) => ({
            id: `candidate-${i}`,
            jobId: 'job-123',
            fullName: `Candidate ${i + 1}`,
            firstName: `First${i}`,
            lastName: `Last${i}`,
            jobTitle: ['Software Engineer', 'Product Manager', 'UX Designer', 'Data Scientist'][
              i % 4
            ],
            location: ['San Francisco, CA', 'New York, NY', 'Austin, TX', 'Remote'][i % 4],
            myProfileImage:
              i % 3 === 0
                ? undefined
                : `https://randomuser.me/api/portraits/${i % 2 ? 'women' : 'men'}/${i + 10}.jpg`,
            summary: 'Professional with experience in technology',
            skills: ['React', 'TypeScript', 'UI/UX', 'Product Management'].slice(0, (i % 4) + 1),
            experience: [
              {
                title: 'Senior Developer',
                company: 'Tech Co',
                duration: 24,
              },
            ],
            profileUrl: '#',
            linkedinUrl: 'https://linkedin.com',
            githubUrl: 'https://github.com',
            sourceType: 'LINKEDIN',
            status: 'NEW',
            yearsOfExperience: 3 + (i % 5),
          }));
          setCandidates(mockCandidates);
        } finally {
          setLoadingCandidates(false);
        }
      }
    };

    fetchInitialCandidates();
  }, [searchParams, currentJob?.id, fetchCandidates, storeCandidates]);

  const handleJobClick = (job: ScoutJob) => {
    setSelectedJob(job);
    router.push(`?jobId=${job.id}`, { scroll: false });
  };

  const handleCloseModal = () => {
    setSelectedJob(null);
    router.push('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter Bar */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full p-2.5 pl-10 text-sm text-white bg-gray-800/50 rounded-lg border border-gray-700 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search for jobs, companies, locations, or skills..."
            value={searchTerm}
            onChange={e => handleSearchChange(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"
              onClick={() => handleSearchChange('')}
              aria-label="Clear search"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        <SmoothButton
          label={showFilters ? 'Hide Filters' : 'Show Filters'}
          icon={<Filter className="w-4 h-4" />}
          onClick={() => setShowFilters(!showFilters)}
          className="px-4 py-2.5 min-w-[140px]"
        />
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div className="p-5 rounded-xl bg-gray-800/30 backdrop-blur-sm border border-gray-700">
          <AdvancedFilterPanel
            onFilterChange={handleFilterChange}
            jobData={jobs}
            loading={loading}
          />
        </div>
      )}

      {/* Results Summary */}
      <div className="flex justify-between items-center flex-wrap gap-3">
        <h2 className="text-xl font-bold text-white">
          {loading ? (
            <span className="flex items-center gap-2">
              <svg
                className="animate-spin h-5 w-5 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Loading...
            </span>
          ) : (
            `${candidates.length} ${candidates.length === 1 ? 'Candidate' : 'Candidates'} Found`
          )}
        </h2>

        {/* Active Filters Summary */}
        <div className="flex gap-2 flex-wrap">
          {/* Search Term Tag */}
          {searchTerm && (
            <div className="px-3 py-1 rounded-full bg-indigo-500/20 text-indigo-300 text-xs font-medium flex items-center gap-1">
              <span>Search: {searchTerm}</span>
              <button
                onClick={() => handleSearchChange('')}
                className="ml-1 hover:text-white"
                aria-label="Clear search term"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Department Filter */}
          {filters.department && (
            <div className="px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 text-xs font-medium flex items-center gap-1">
              <span>Department: {filters.department}</span>
              <button
                onClick={() => handleFilterChange({ ...filters, department: undefined })}
                className="ml-1 hover:text-white"
                aria-label="Clear department filter"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Experience Level Filter */}
          {filters.experienceLevel && (
            <div className="px-3 py-1 rounded-full bg-purple-500/20 text-purple-300 text-xs font-medium flex items-center gap-1">
              <span>Experience: {filters.experienceLevel}</span>
              <button
                onClick={() => handleFilterChange({ ...filters, experienceLevel: undefined })}
                className="ml-1 hover:text-white"
                aria-label="Clear experience level filter"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Location Filter */}
          {filters.location && filters.location.length > 0 && (
            <div className="px-3 py-1 rounded-full bg-teal-500/20 text-teal-300 text-xs font-medium flex items-center gap-1">
              <span>
                {filters.location.length === 1
                  ? `Location: ${filters.location[0]}`
                  : `${filters.location.length} locations`}
              </span>
              <button
                onClick={() => handleFilterChange({ ...filters, location: undefined })}
                className="ml-1 hover:text-white"
                aria-label="Clear location filter"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Skills Filter */}
          {filters.skills && filters.skills.length > 0 && (
            <div className="px-3 py-1 rounded-full bg-amber-500/20 text-amber-300 text-xs font-medium flex items-center gap-1">
              <span>
                {filters.skills.length === 1
                  ? `Skill: ${filters.skills[0]}`
                  : `${filters.skills.length} skills`}
              </span>
              <button
                onClick={() => handleFilterChange({ ...filters, skills: undefined })}
                className="ml-1 hover:text-white"
                aria-label="Clear skills filter"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          )}

          {/* Clear All Button */}
          {(searchTerm ||
            Object.keys(filters).some(
              key => filters[key as keyof typeof filters] !== undefined
            )) && (
            <button
              onClick={clearFilters}
              className="px-3 py-1 rounded-full bg-gray-700/50 text-gray-300 text-xs font-medium hover:bg-gray-700 transition-colors flex items-center gap-1"
              aria-label="Clear all filters"
            >
              Clear All
              <X className="w-3 h-3 ml-1" />
            </button>
          )}
        </div>
      </div>

      {/* No Results State */}
      {candidates.length === 0 && !loadingCandidates && (
        <div className="flex flex-col items-center justify-center p-12 rounded-xl bg-gray-800/20 border border-gray-700">
          <div className="w-16 h-16 mb-4 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-white mb-2">No candidates found</h3>
          <p className="text-gray-400 text-center max-w-md">
            We couldn't find any candidates matching your search or filters. Try adjusting your
            criteria or search term.
          </p>
          <button
            onClick={clearFilters}
            className="mt-4 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Clear All Filters
          </button>
        </div>
      )}

      {/* Candidates Grid with Shimmer Loading Effect */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {loadingCandidates
          ? // Shimmer loading effect for candidate cards
            Array.from({ length: 12 }).map((_, index) => (
              <div
                key={index}
                className="relative overflow-hidden rounded-xl w-full h-80 animate-pulse"
                style={{
                  background:
                    'linear-gradient(to bottom, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.9))',
                }}
              >
                <div className="absolute inset-0 rounded-xl border border-indigo-900/30" />
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-purple-500/5 to-pink-500/5 opacity-40 mix-blend-overlay"></div>
                <div className="relative h-full p-6 flex flex-col items-center">
                  {/* Profile image skeleton */}
                  <div className="relative mb-4">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500/20 via-purple-500/15 to-pink-500/20 rounded-full opacity-30 blur-md"></div>
                    <div className="relative h-24 w-24 bg-gray-800/70 rounded-full"></div>
                  </div>

                  {/* Name skeleton */}
                  <div className="h-7 bg-gray-800/70 rounded-md w-4/5 mb-1"></div>

                  {/* Separator skeleton */}
                  <div className="w-full flex items-center justify-center mt-1 mb-5">
                    <div className="flex-grow h-px bg-indigo-500/15 max-w-[80px]"></div>
                    <div className="w-2 h-2 rounded-full bg-indigo-500/25 mx-2"></div>
                    <div className="flex-grow h-px bg-indigo-500/15 max-w-[80px]"></div>
                  </div>

                  {/* Info items skeleton */}
                  <div className="w-full space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-sky-500/10 rounded-full"></div>
                      <div className="h-5 bg-gray-800/70 rounded-md flex-1"></div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-emerald-500/10 rounded-full"></div>
                      <div className="h-5 bg-gray-800/70 rounded-md flex-1"></div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-purple-500/10 rounded-full"></div>
                      <div className="h-5 bg-gray-800/70 rounded-md flex-1"></div>
                      <div className="h-6 w-6 bg-indigo-500/30 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          : // Actual candidate cards
            candidates.map(candidate => (
              <CandidateProfileCard key={candidate.id} candidate={candidate} />
            ))}
      </div>

      <JobDetailsModal job={selectedJob!} isOpen={!!selectedJob} onClose={handleCloseModal} />
    </div>
  );
};

export default CandidateList;
