import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { Building2, Star, Users } from 'lucide-react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cell,
  LabelList,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from 'recharts';

import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';

import { Separator } from '../ui/separator';
import { Skeleton } from '../ui/skeleton';

// Define RankedJobsStats interface locally
interface RankedJobsStats {
  totalJobs: number;
  totalCandidates: number;
  averageMatchScore: number;
  jobsWithCandidates: number;
  topTierCandidates: number;
  secondTierCandidates: number;
  jobsByDepartment: { [department: string]: number };
  candidatesByMatchScore: {
    excellent: number; // 90-100
    veryGood: number; // 80-89
    good: number; // 70-79
    fair: number; // 50-69
    poor: number; // 0-49
  };
  recentActivity: {
    newJobsLast7Days: number;
    newJobsLast30Days: number;
    newCandidatesLast7Days: number;
    newCandidatesLast30Days: number;
  };
}

// Modern gradient colors
const GRADIENT_COLORS = {
  primary: 'from-indigo-500 via-purple-500 to-pink-500',
  secondary: 'from-cyan-400 via-teal-400 to-emerald-400',
  tertiary: 'from-rose-400 via-fuchsia-500 to-indigo-500',
  quaternary: 'from-amber-400 via-orange-500 to-rose-500',
  dark: 'from-slate-900 via-slate-800 to-slate-700',
};

// Chart colors
const CHART_COLORS = {
  excellent: '#10B981', // emerald-500
  veryGood: '#3B82F6', // blue-500
  good: '#8B5CF6', // violet-500
  fair: '#F59E0B', // amber-500
  poor: '#EF4444', // red-500
  primary: '#8B5CF6', // violet-500 (adding primary color)
  secondary: '#3B82F6', // blue-500
  tertiary: '#10B981', // emerald-500
  quaternary: '#F59E0B', // amber-500
  departments: ['#6366F1', '#8B5CF6', '#EC4899', '#F43F5E', '#F97316'], // indigo, violet, pink, rose, orange
  activity: ['#3B82F6', '#06B6D4', '#8B5CF6', '#EC4899'], // blue, cyan, violet, pink
};

interface RankedJobsStatsProps {
  page?: number;
  limit?: number;
}

const RankedJobsStats: React.FC<RankedJobsStatsProps> = ({ page = 1, limit = 10 }) => {
  const [stats, setStats] = useState<RankedJobsStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(page);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        // Use the correct method from apiHelper with pagination
        const response = await apiHelper.get<RankedJobsStats>(
          `jobs/ranked-jobs-stats?page=${currentPage}&limit=${limit}`
        );
        setStats(response);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch job statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [currentPage, limit]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-[250px] w-full rounded-xl" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-[200px] w-full rounded-xl" />
          <Skeleton className="h-[200px] w-full rounded-xl" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="w-full backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 bg-white/5"
      >
        <div className="p-6">
          <div className="text-center p-6 text-red-400">
            <p>{error}</p>
            <button
              onClick={() => {
                // Refresh data through Zustand stores instead of full page reload
                invalidateJobsCache();
                refreshJobs(true);
                // Clear error and retry loading
                setError(null);
                setLoading(true);
              }}
              className="mt-4 px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 font-medium"
            >
              Retry
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  if (!stats) {
    return null;
  }

  // Prepare data for charts
  const candidatesByScoreData = [
    {
      name: 'Excellent',
      label: '90-100',
      value: stats.candidatesByMatchScore.excellent,
      color: CHART_COLORS.excellent,
      displayValue: '0%',
    },
    {
      name: 'Very Good',
      label: '80-89',
      value: stats.candidatesByMatchScore.veryGood,
      color: CHART_COLORS.veryGood,
      displayValue: '0%',
    },
    {
      name: 'Good',
      label: '70-79',
      value: stats.candidatesByMatchScore.good,
      color: CHART_COLORS.good,
      displayValue: '6%',
    },
    {
      name: 'Fair',
      label: '50-69',
      value: stats.candidatesByMatchScore.fair,
      color: CHART_COLORS.fair,
      displayValue: '8%',
    },
    {
      name: 'Poor',
      label: '0-49',
      value: stats.candidatesByMatchScore.poor,
      color: CHART_COLORS.poor,
      displayValue: '86%',
    },
  ].filter(item => item.value > 0); // Only show categories with values

  const departmentData = Object.entries(stats.jobsByDepartment).map(([name, value], index) => ({
    name: name,
    fullName: name,
    value,
    color: CHART_COLORS.departments[index % CHART_COLORS.departments.length],
  }));

  const recentActivityData = [
    {
      name: 'Jobs (7d)',
      fullName: 'New Jobs (Last 7 Days)',
      value: stats.recentActivity.newJobsLast7Days,
      color: CHART_COLORS.activity[0],
    },
    {
      name: 'Jobs (30d)',
      fullName: 'New Jobs (Last 30 Days)',
      value: stats.recentActivity.newJobsLast30Days,
      color: CHART_COLORS.activity[1],
    },
    {
      name: 'Candidates (7d)',
      fullName: 'New Candidates (Last 7 Days)',
      value: stats.recentActivity.newCandidatesLast7Days,
      color: CHART_COLORS.activity[2],
    },
    {
      name: 'Candidates (30d)',
      fullName: 'New Candidates (Last 30 Days)',
      value: stats.recentActivity.newCandidatesLast30Days,
      color: CHART_COLORS.activity[3],
    },
  ];

  // Custom tooltips
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-slate-800 border border-slate-700 p-3 rounded-lg shadow-xl backdrop-blur-lg">
          <p className="font-medium text-white">{payload[0].payload.fullName || label}</p>
          <p className="text-slate-300">
            Value: <span className="text-white font-semibold">{payload[0].value}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const StatsCard = ({
    title,
    value,
    icon,
    color,
  }: {
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
  }) => {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        className="bg-gradient-to-br from-slate-800/30 to-slate-900/10 backdrop-blur-lg p-3 rounded-xl border border-white/5 shadow-lg group"
      >
        <div className="flex items-center justify-between mb-1">
          <p className="text-xs text-slate-400 group-hover:text-slate-300 transition-colors">
            {title}
          </p>
          <div
            className="w-7 h-7 flex items-center justify-center rounded-full bg-slate-800/50 group-hover:bg-slate-700/50 transition-colors"
            style={{ color }}
          >
            {React.cloneElement(icon as React.ReactElement, { size: 14 })}
          </div>
        </div>
        <p
          className="text-2xl font-bold bg-clip-text text-transparent"
          style={{
            color,
            WebkitTextFillColor: 'transparent',
            backgroundImage: `linear-gradient(to right, ${color}, ${color})`,
          }}
        >
          {value}
        </p>
      </motion.div>
    );
  };

  // Custom pie chart label
  const renderCustomizedPieLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
    index,
    name,
    value,
  }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (value === 0) return null;

    return value >= 3 ? (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor="middle"
        dominantBaseline="central"
        fontWeight="500"
        fontSize="12"
      >
        {`${name} (${(percent * 100).toFixed(0)}%)`}
      </text>
    ) : null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <div className="w-full backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 bg-white/5">
        <div className="bg-gradient-to-br from-slate-800/20 to-slate-900/10 p-4">
          <h2 className="text-xl font-bold mb-1 bg-gradient-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent">
            Ranked Jobs Overview
          </h2>
          <p className="text-sm text-slate-400 mb-4">
            Overview of your job listings and candidate pool
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            <StatsCard
              title="Total Jobs"
              value={stats.totalJobs}
              color={CHART_COLORS.primary}
              icon={<Building2 />}
            />
            <StatsCard
              title="Total Candidates"
              value={stats.totalCandidates}
              color={CHART_COLORS.secondary}
              icon={<Users />}
            />
            <StatsCard
              title="Avg Match Score"
              value={stats.averageMatchScore}
              color={CHART_COLORS.tertiary}
              icon={<Star />}
            />
            <StatsCard
              title="Top Candidates"
              value={stats.topTierCandidates}
              color={CHART_COLORS.quaternary}
              icon={<Star />}
            />
          </div>

          <Separator className="my-4 bg-slate-700/50" />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-lg rounded-xl p-3 border border-white/5"
            >
              <h3 className="text-sm font-medium mb-2 text-white">Candidates by Match Score</h3>
              <ResponsiveContainer width="100%" height={220}>
                <PieChart>
                  <Pie
                    data={candidatesByScoreData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    labelLine={false}
                    label={renderCustomizedPieLabel}
                  >
                    {candidatesByScoreData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend
                    verticalAlign="bottom"
                    height={20}
                    iconSize={8}
                    formatter={(value, entry, index) => (
                      <span className="text-xs text-slate-300">{`${value} (${candidatesByScoreData[index].label})`}</span>
                    )}
                  />
                </PieChart>
              </ResponsiveContainer>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-lg rounded-xl p-3 border border-white/5"
            >
              <h3 className="text-sm font-medium mb-2 text-white">Jobs by Department</h3>
              <ResponsiveContainer width="100%" height={220}>
                <BarChart
                  data={departmentData}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                  layout="vertical"
                >
                  <XAxis type="number" hide />
                  <YAxis
                    type="category"
                    dataKey="name"
                    width={120}
                    tick={{ fill: '#94a3b8', fontSize: 10 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" radius={[0, 4, 4, 0]}>
                    {departmentData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                    <LabelList
                      dataKey="value"
                      position="right"
                      fill="#ffffff"
                      fontSize={10}
                      fontWeight="500"
                    />
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </motion.div>
          </div>

          <Separator className="my-4 bg-slate-700/50" />

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-lg rounded-xl p-3 border border-white/5"
          >
            <h3 className="text-sm font-medium mb-2 text-white">Recent Activity</h3>
            <ResponsiveContainer width="100%" height={160}>
              <BarChart
                data={recentActivityData}
                margin={{ top: 5, right: 5, left: 5, bottom: 25 }}
              >
                <XAxis dataKey="name" tick={{ fill: '#94a3b8', fontSize: 10 }} interval={0} />
                <YAxis hide />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                  {recentActivityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                  <LabelList
                    dataKey="value"
                    position="top"
                    fill="#ffffff"
                    fontSize={10}
                    fontWeight="500"
                  />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default RankedJobsStats;
