'use client';

import 'react-quill/dist/quill.snow.css';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Code, ListChecks } from 'lucide-react';

import RequiredFieldIndicator from '@/components/common/RequiredFieldIndicator';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import SectionHeader from '@/components/ui/SectionHeader';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { Box, Typography } from '@mui/material';

import TextEditor from '../../TextEditor';
import StepLayout from './layout/StepLayout';

const JobResponsibilitiesAndSkills: React.FC = () => {
  const { job, updateJobDescription } = useJobs();
  const { generateResponsibilities } = useJobsStore();
  const previousIndustry = useRef(job.department);
  const [isLoading, setIsLoading] = useState(false);

  // Combined function to generate both skills and responsibilities
  // Now simplified since we have debouncing at the store level
  const generateSkillsAndResponsibilities = useCallback(
    async (isAddingMore = false) => {
      if (!job.department) return;

      try {
        setIsLoading(true);

        // The store now handles debouncing and preventing duplicate requests
        const resp = await generateResponsibilities({
          department: job.department,
          jobType: job.jobType,
          skills: job.skills,
          experience: job.experience,
        });

        if (resp.skills && resp.responsibilities) {
          const processedSkills = resp.skills.map((skill: string) =>
            skill.trim().endsWith('.') ? skill : `${skill.trim()}.`
          );
          const processedResponsibilities = resp.responsibilities.map((responsibility: string) =>
            responsibility.trim().endsWith('.') ? responsibility : `${responsibility.trim()}.`
          );

          if (isAddingMore) {
            updateJobDescription('skills', [...(job.skills || []), ...processedSkills]);
            updateJobDescription('jobResponsibilities', [
              ...(job.jobResponsibilities || []),
              ...processedResponsibilities,
            ]);
          } else {
            updateJobDescription('skills', processedSkills);
            updateJobDescription('jobResponsibilities', processedResponsibilities);
          }
        }
      } catch (error) {
        console.error('Error generating skills and responsibilities:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [job.department, job.jobType, job.skills, generateResponsibilities, updateJobDescription]
  );

  useEffect(() => {
    // If industry changed, clear skills and responsibilities
    if (previousIndustry.current !== job.department) {
      previousIndustry.current = job.department;
      updateJobDescription('skills', []);
      updateJobDescription('jobResponsibilities', []);
    }

    // Generate initial data if needed
    const shouldGenerateInitialData =
      !job.skills?.length && !job.jobResponsibilities?.length && job.department;

    if (shouldGenerateInitialData) {
      generateSkillsAndResponsibilities();
    }
  }, [job.department, generateSkillsAndResponsibilities]);

  return (
    <StepLayout
      title="Job Responsibilities & Skills"
      description="Define the responsibilities and skills for this role."
      icon={Code}
    >
      {isLoading && <ColorfulSmokeyOrbLoader text="Generating..." useModalBg={false} />}

      <Box className="px-2">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Responsibilities Section */}
          <Box className="mb-4 flex flex-col h-full">
            <SectionHeader title="Job Responsibilities" icon={ListChecks} />
            <Typography variant="body2" className="mb-4 text-white/80 text-xs">
              Define the key responsibilities for this position.{' '}
              <RequiredFieldIndicator variant="text" />
            </Typography>
            <div className="flex-grow min-h-[300px] mt-2">
              <TextEditor
                value={
                  Array.isArray(job.jobResponsibilities) ? job.jobResponsibilities.join('\n') : ''
                }
                onChange={value =>
                  updateJobDescription(
                    'jobResponsibilities',
                    value.split('\n').filter(resp => resp.trim() !== '')
                  )
                }
                placeholder="Enter job responsibilities here..."
              />
            </div>
          </Box>

          {/* Skills Section */}
          <Box className="mb-4 flex flex-col h-full">
            <SectionHeader title="Technical Skills" icon={Code} />
            <Typography variant="body2" className="mb-4 text-white/80 text-xs">
              Define the technical skills required for this position.{' '}
              <RequiredFieldIndicator variant="text" />
            </Typography>
            <div className="flex-grow min-h-[300px] mt-2">
              <TextEditor
                value={Array.isArray(job.skills) ? job.skills.join('\n') : ''}
                onChange={value =>
                  updateJobDescription(
                    'skills',
                    value.split('\n').filter(skill => skill.trim() !== '')
                  )
                }
                placeholder="Enter job skills here..."
              />
            </div>
          </Box>
        </div>
      </Box>
    </StepLayout>
  );
};

export default JobResponsibilitiesAndSkills;
