import { Box, Stack } from '@mui/material';
import { DEPARTMENTS, JOB_TYPES } from '@/constants/jobOptions';

import { Factory } from 'lucide-react';
import React from 'react';
import StepLayout from './layout/StepLayout';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import { useJobs } from '@/contexts/jobs/JobsContext';

const DepartmentAndJobType: React.FC = () => {
  const { job, updateJobDescription } = useJobs();

  const handleDepartmentChange = (newValue: string) => {
    updateJobDescription('department', newValue);
    // Clear skills and responsibilities when department changes
    updateJobDescription('skills', '');
    updateJobDescription('jobResponsibilities', '');
  };

  const handleJobTypeChange = (newValue: string) => {
    updateJobDescription('jobType', newValue);
    updateJobDescription('skills', '');
    updateJobDescription('jobResponsibilities', '');
  };

  return (
    <StepLayout
      title="Role Details"
      description="Tell us about the department, position type, and experience level"
      icon={Factory}
    >
      <Stack spacing={3}>
        <Box>
          <StyledSelect
            label="Department"
            options={DEPARTMENTS.map(department => ({ value: department, label: department }))}
            value={job.department || ''}
            onChange={handleDepartmentChange}
            placeholder="Select department"
          />
        </Box>
        <Box>
          <StyledSelect
            label="Position Type"
            options={JOB_TYPES.map(jobType => ({ value: jobType, label: jobType }))}
            value={job.jobType || ''}
            onChange={handleJobTypeChange}
            placeholder="Select job type"
          />
        </Box>
      </Stack>
    </StepLayout>
  );
};

export default DepartmentAndJobType;
