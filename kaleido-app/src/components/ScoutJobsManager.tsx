'use client';

import React, { useEffect, useState } from 'react';

import { useScoutJobsStore } from '@/stores/scoutJobsStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import dynamic from 'next/dynamic';
import { createScoutConfig } from './shared/GenericStatusManager/configs/scoutConfig';

// Dynamically import GenericStatusManager with SSR disabled
const GenericStatusManager = dynamic(
  () =>
    import('./shared/GenericStatusManager/GenericStatusManager').then(mod => ({
      default: mod.GenericStatusManager,
    })),
  { ssr: false }
);

export const ScoutJobsManager: React.FC = () => {
  // Use state to track client-side rendering
  const [mounted, setMounted] = useState(false);

  // Status manager stores
  const {
    jobs: scoutJobs,
    activeJobs: activeScoutJobs,
    updateJobStatus: updateScoutJob,
    removeJob: removeScoutJob,
  } = useScoutJobsStore();

  // Convert ScoutJob to StatusJob format
  const convertedScoutJobs = Object.fromEntries(
    Object.entries(scoutJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.message,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.createdAt, // Use createdAt as fallback since updatedAt may not exist
        totalProfiles: job.totalProfiles,
        processedProfiles: job.processedProfiles,
        successCount: job.successCount,
        failedCount: job.failedCount,
        duplicateCount: job.duplicateCount,
        results: job.results,
        duplicateInfo: job.duplicateInfo,
        data: job.data,
        metadata: job.metadata || {},
      },
    ])
  );

  // Only render on client-side to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR or initial hydration
  if (!mounted) {
    return null;
  }

  // Only render the component after client-side hydration is complete
  return (
    <div id="scout-jobs-manager-container">
      <GenericStatusManager
        jobs={convertedScoutJobs}
        activeJobs={activeScoutJobs}
        config={createScoutConfig()}
        onUpdateJob={(jobId: string, updates: any) => {
          updateScoutJob(jobId, updates.status, updates);
        }}
        onRemoveJob={removeScoutJob}
        onComplete={async (actualJobId: string) => {
          // actualJobId is the real job ID (UUID), not the worker job ID
          const jobStore = useJobStore.getState();

          // Call onWorkerComplete to handle any store-level cleanup
          await jobStore.onWorkerComplete(actualJobId, 'scout');

          // Refresh job criteria to update recentCandidates and other data
          // This ensures any page displaying candidates will have fresh data
          await jobStore.fetchJobCriteria(actualJobId, true);
        }}
      />
    </div>
  );
};
