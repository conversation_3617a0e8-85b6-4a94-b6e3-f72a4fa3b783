'use client';

import React, { useState, useCallback } from 'react';
import { ICandidate } from '@/entities/interfaces';
import { CandidateSelectionProvider, useCandidateSelection } from '@/contexts/CandidateSelectionContext';
import { SimplifiedCandidateView2 } from './SimplifiedCandidateView2WithSelection';
import SelectionActionsSlider from './SelectionActionsSlider';
import ComparisonOptionsSlider from './ComparisonOptionsSlider';
import { comparisonService } from '@/services/comparison.service';
import { ComparisonType } from '@/types/comparison.types';
import { showToast } from '@/components/Toaster';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Users, CheckSquare } from 'lucide-react';

interface CandidateListWithSelectionProps {
  candidates: ICandidate[];
  jobId: string;
  onCandidateSelect?: (candidate: ICandidate) => void;
  onStatusUpdate?: () => void;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
}

const CandidateListContent: React.FC<CandidateListWithSelectionProps> = ({
  candidates,
  jobId,
  onCandidateSelect,
  onStatusUpdate,
  topCandidateThreshold,
  secondTierCandidateThreshold,
}) => {
  const router = useRouter();
  const [showComparisonOptions, setShowComparisonOptions] = useState(false);
  const [isComparing, setIsComparing] = useState(false);
  
  const {
    selectedCandidates,
    isSelectionMode,
    setSelectionMode,
    clearSelection,
    selectedCount,
  } = useCandidateSelection();

  const handleToggleSelectionMode = () => {
    setSelectionMode(!isSelectionMode);
    if (isSelectionMode) {
      clearSelection();
    }
  };

  const handleCompare = () => {
    if (selectedCount < 2) {
      showToast({
        message: 'Please select at least 2 candidates to compare',
        type: 'warning',
      });
      return;
    }
    setShowComparisonOptions(true);
  };

  const handleComparisonOptionSelect = async (type: ComparisonType, customPrompt?: string) => {
    setShowComparisonOptions(false);
    setIsComparing(true);

    try {
      const comparisonData = {
        jobId,
        candidateIds: selectedCandidates.map(c => c.id),
        comparisonType: type,
        userPrompt: customPrompt,
      };

      const result = await comparisonService.createComparison(comparisonData);
      
      showToast({
        message: 'Comparison started! Processing your request...',
        type: 'info',
      });

      // Poll for completion
      const comparison = await comparisonService.pollForCompletion(result.id);
      
      // Navigate to comparison results page
      router.push(`/jobs/${jobId}/comparisons/${comparison.id}`);
      
      // Clear selection after successful comparison
      clearSelection();
    } catch (error) {
      console.error('Error creating comparison:', error);
      showToast({
        message: 'Failed to create comparison. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsComparing(false);
    }
  };

  const handleSendVideoIntro = async () => {
    // TODO: Implement video intro email functionality
    showToast({
      message: 'Video intro email feature coming soon!',
      type: 'info',
    });
  };

  const handleDelete = async () => {
    // TODO: Implement bulk delete functionality
    showToast({
      message: 'Delete feature coming soon!',
      type: 'info',
    });
  };

  const handleExport = async () => {
    // TODO: Implement export functionality
    showToast({
      message: 'Export feature coming soon!',
      type: 'info',
    });
  };

  const handleShare = async () => {
    // TODO: Implement share functionality
    showToast({
      message: 'Share feature coming soon!',
      type: 'info',
    });
  };

  return (
    <div className="relative">
      {/* Selection Mode Toggle */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-sm border-b border-border p-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold">Candidates</h2>
            <div className="text-sm text-muted-foreground">
              {candidates.length} total
            </div>
          </div>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleToggleSelectionMode}
            className={`
              px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200
              ${isSelectionMode 
                ? 'bg-purple-500/20 border border-purple-400/30 text-purple-400' 
                : 'bg-white/10 border border-white/20 text-white/90 hover:bg-white/15'
              }
            `}
          >
            {isSelectionMode ? (
              <>
                <CheckSquare className="w-4 h-4" />
                <span>Cancel Selection</span>
              </>
            ) : (
              <>
                <Users className="w-4 h-4" />
                <span>Select to Compare</span>
              </>
            )}
          </motion.button>
        </div>

        {/* Selection count indicator */}
        {isSelectionMode && selectedCount > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-3 text-sm text-purple-400"
          >
            {selectedCount} selected {selectedCount === 1 ? 'candidate' : 'candidates'}
            {selectedCount >= 3 && ' (maximum reached)'}
          </motion.div>
        )}
      </div>

      {/* Candidates List */}
      <div className="space-y-4 p-4">
        {candidates.map((candidate) => (
          <div key={candidate.id} className="relative">
            <SimplifiedCandidateView2
              candidate={candidate}
              jobId={jobId}
              onCandidateSelect={onCandidateSelect}
              onStatusUpdate={onStatusUpdate}
              topCandidateThreshold={topCandidateThreshold}
              secondTierCandidateThreshold={secondTierCandidateThreshold}
            />
          </div>
        ))}
      </div>

      {/* Selection Actions Slider */}
      <SelectionActionsSlider
        isVisible={isSelectionMode && selectedCount > 0}
        selectedCandidates={selectedCandidates}
        onClose={() => {
          setSelectionMode(false);
          clearSelection();
        }}
        onCompare={handleCompare}
        onSendVideoIntro={handleSendVideoIntro}
        onDelete={handleDelete}
        onExport={handleExport}
        onShare={handleShare}
      />

      {/* Comparison Options Slider */}
      <ComparisonOptionsSlider
        isVisible={showComparisonOptions}
        selectedCandidates={selectedCandidates}
        onClose={() => setShowComparisonOptions(false)}
        onSelectOption={handleComparisonOptionSelect}
      />

      {/* Loading overlay */}
      {isComparing && (
        <div className="fixed inset-0 z-[70] bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-card border border-border rounded-lg p-6 flex flex-col items-center gap-4"
          >
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin" />
            <p className="text-lg font-medium">Creating comparison...</p>
            <p className="text-sm text-muted-foreground">This may take a moment</p>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export const CandidateListWithSelection: React.FC<CandidateListWithSelectionProps> = (props) => {
  return (
    <CandidateSelectionProvider>
      <CandidateListContent {...props} />
    </CandidateSelectionProvider>
  );
};