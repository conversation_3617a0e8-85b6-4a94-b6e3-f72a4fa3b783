import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { Mail, Medal, Menu, Star, Target, Trophy, X } from 'lucide-react';

import FloatingPagination from '@/components/common/FloatingPagination';
import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';
import { ApiResponse } from '@/lib/apiHelper.utils';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import { CandidateStatus } from '@/types/candidate.types';

import { MatchedCandidateCard } from './MatchedCandidateCard';
import { ShortlistedCandidateItem } from './ShortlistedCandidateItem';
import { CandidateListItem } from './CandidateListItem';

// Using a local interface definition to include activityHistory
interface ICandidate {
  id: string;
  fullName: string;
  firstName?: string;
  lastName?: string;
  jobTitle: string;
  location: string;
  skills: string[];
  experience?: Array<{
    title: string;
    company: string;
    duration: string;
    startDate: string | null;
    endDate: string | null;
  }>;
  status?: string;
  jobId?: string;
  email?: string;
  jobStats?: {
    topCandidateThreshold: number;
    secondTierCandidateThreshold: number;
    jobTitle?: string;
    companyName?: string;
    department?: string;
  };
  evaluation?: {
    matchScore: number;
    rank?: number | null;
    lastEvaluatedAt: string;
    criterionMatchedOn: string[];
    detailedScoreAnalysis: {
      specificCriteriaMatched: {
        skillsMatch: number;
        experienceRelevance: number;
        locationAndAvailability: number;
      };
    };
  };
  activityHistory?: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
    performedBy: string;
    metadata: Record<string, any>;
  }>;
}

interface ScoutedCandidateListProps {
  candidates: ICandidate[] | GroupedCandidates;
  itemsPerPage?: number;
  currentPage: number;
  onPageChange?: (page: number) => void;
  onSelectCandidate?: (candidate: ICandidate) => void;
  selectedCandidateId?: string;
  jobId?: string;
}

interface GroupedCandidates {
  shortlisted: ICandidate[];
  topTier: ICandidate[];
  secondTier: ICandidate[];
  others: ICandidate[];
  unranked: ICandidate[];
}

const groupCandidatesByTier = (candidates: ICandidate[]): GroupedCandidates => {
  // First sort all candidates by match score
  const sortedCandidates = [...candidates].sort((a, b) => {
    const scoreA = a.evaluation?.matchScore ?? 0;
    const scoreB = b.evaluation?.matchScore ?? 0;
    return scoreB - scoreA;
  });

  // Get job-specific thresholds from the first candidate's jobStats
  // Default to 80% for top tier and 60% for second tier if not specified
  const jobStats = sortedCandidates[0]?.jobStats;

  // Handle threshold values that might be in decimal (0.8), percentage (80), or incorrect values (5000)
  // Only use thresholds if they are properly set on the job
  let topThreshold: number | undefined = jobStats?.topCandidateThreshold;
  let secondThreshold: number | undefined = jobStats?.secondTierCandidateThreshold;

  if (topThreshold !== undefined) {
    // Fix incorrect threshold values (like 5000, 4500) - these should be percentages
    if (topThreshold > 100) {
      topThreshold = 80; // Fallback for corrupted data
    } else if (topThreshold <= 1) {
      // Normalize decimal format to percentage
      topThreshold = topThreshold * 100;
    }
  }

  if (secondThreshold !== undefined) {
    // Fix incorrect threshold values (like 4500) - these should be percentages
    if (secondThreshold > 100) {
      secondThreshold = 60; // Fallback for corrupted data
    } else if (secondThreshold <= 1) {
      // Normalize decimal format to percentage
      secondThreshold = secondThreshold * 100;
    }
  }

  // Then group by evaluation score while maintaining sort order
  return sortedCandidates.reduce(
    (acc: GroupedCandidates, candidate) => {
      // First check if candidate is shortlisted
      if (candidate.status === 'SHORTLISTED') {
        acc.shortlisted.push(candidate);
        // Return early to prevent shortlisted candidates from being added to tier arrays
        return acc;
      }

      // Only group by tier if thresholds are properly set
      if (topThreshold !== undefined && secondThreshold !== undefined) {
        // Then group by tier based on match score
        let matchScore = candidate.evaluation?.matchScore ?? 0;

        // Match scores can be in decimal format (0.0-1.0) or percentage format (0-100)
        // Based on the data provided, scores like 55 are already in percentage format
        // Only convert if the score is clearly in decimal format (≤ 1.0)
        if (matchScore <= 1.0) {
          matchScore = matchScore * 100;
        }

        if (matchScore === 0 || !candidate.evaluation?.rank) {
          // Candidates with score 0 or no rank go to unranked
          acc.unranked.push(candidate);
        } else if (matchScore >= topThreshold) {
          acc.topTier.push(candidate);
        } else if (matchScore >= secondThreshold) {
          acc.secondTier.push(candidate);
        } else {
          acc.others.push(candidate);
        }
      } else {
        // If no thresholds are set, check if candidate should be unranked
        if (candidate.evaluation?.matchScore === 0 || !candidate.evaluation?.rank) {
          acc.unranked.push(candidate);
        } else {
          acc.others.push(candidate);
        }
      }
      return acc;
    },
    { shortlisted: [], topTier: [], secondTier: [], others: [], unranked: [] }
  );
};

interface ActionButtonProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant: 'primary' | 'secondary';
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, label, onClick, variant, disabled }) => (
  <button
    type="button"
    onClick={e => {
      e.stopPropagation();
      onClick();
    }}
    className={`
      flex items-center gap-1.5 px-3 py-1.5 rounded-md text-sm font-medium
      transition-colors duration-200
      ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    `}
    style={{
      backgroundColor:
        variant === 'primary' ? 'var(--button-primary-bg)' : 'var(--button-secondary-bg)',
      color: variant === 'primary' ? 'var(--button-primary-text)' : 'var(--button-secondary-text)',
    }}
    aria-label={label}
    disabled={disabled}
  >
    {icon}
    {label}
  </button>
);

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  action: string;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({ isOpen, onClose, onConfirm, action }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50 bg-background/80 dark:bg-background/80">
      <div className="rounded-lg p-6 max-w-md w-full mx-4 relative shadow-xl bg-background border border-gray-200/5">
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 transition-colors text-foreground"
          aria-label="Close modal"
        >
          <X className="w-5 h-5" />
        </button>
        <h3 className="text-lg font-semibold mb-4 text-foreground">Confirm Action</h3>
        <p className="mb-6 text-foreground">Are you sure you want to {action}?</p>
        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 rounded-md transition-colors bg-secondary text-secondary-foreground"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => {
              onConfirm();
              onClose();
            }}
            className="px-4 py-2 rounded-md transition-colors bg-primary text-primary-foreground"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

interface SectionProps {
  title: string;
  count: number;
  className: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  candidates?: ICandidate[];
  showActions?: boolean;
  icon?: React.ReactNode;
  checkedCandidates?: Record<string, boolean>;
}

interface BulkEmailResponse {
  success: number;
  failed: number;
  total: number;
  tier?: 'TOP' | 'SECOND' | 'OTHER';
  shortlistedOnly?: boolean;
}

const Section: React.FC<SectionProps> = ({
  title,
  count,
  className,
  isExpanded,
  onToggle,
  children,
  candidates = [],
  showActions = false,
  icon,
  checkedCandidates = {},
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalAction, setModalAction] = useState('');
  const [pendingAction, setPendingAction] = useState<() => void>(() => () => {});
  const [isLoading, setIsLoading] = useState(false);
  const [isActionsExpanded, setIsActionsExpanded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  // Set isMounted to true after component mounts (for SSR compatibility)
  // Use a ref to prevent unnecessary re-renders and avoid infinite loops
  const mountedRef = useRef(false);

  useEffect(() => {
    if (!mountedRef.current) {
      mountedRef.current = true;
      setIsMounted(true);
    }
    // Don't reset mountedRef.current in cleanup to prevent re-mounting issues
  }, []); // Empty dependency array - only run once on mount

  // Helper to determine tier from title
  const getTierFromTitle = (title: string): 'TOP' | 'SECOND' | 'OTHER' => {
    if (title.includes('Top Tier')) return 'TOP';
    if (title.includes('Second Tier')) return 'SECOND';
    return 'OTHER';
  };

  const handleHeaderClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggle();
  };

  const sendBulkEmails = async (
    candidateIds: string[],
    jobId: string,
    tier: 'TOP' | 'SECOND' | 'OTHER',
    shortlistedOnly: boolean = false
  ) => {
    try {
      setIsLoading(true);
      const response = await apiHelper.post<ApiResponse<BulkEmailResponse>>(
        '/email/bulk-shortlist',
        {
          jobId,
          candidateIds,
          tier,
          shortlistedOnly,
          emailType: 'SHORTLISTED', // Add this to track the email type in activity history
        }
      );

      if (!response.data) {
        throw new Error('Failed to send emails');
      }

      const result = response.data;
      if (result.success === result.total) {
        showToast({
          message: `Successfully sent ${result.total} email${result.total !== 1 ? 's' : ''} to ${shortlistedOnly ? 'shortlisted ' : ''}${tier.toLowerCase()} tier candidates.`,
          isSuccess: true,
        });
      } else if (result.success > 0) {
        showToast({
          message: `Sent ${result.success} out of ${result.total} emails to ${shortlistedOnly ? 'shortlisted ' : ''}${tier.toLowerCase()} tier candidates. ${result.failed} failed.`,
          isSuccess: false,
        });
      } else {
        showToast({
          message: `Failed to send any emails to ${shortlistedOnly ? 'shortlisted ' : ''}${tier.toLowerCase()} tier candidates. Please try again.`,
          isSuccess: false,
        });
      }

      return result;
    } catch (error) {
      showToast({
        message: error instanceof Error ? error.message : 'Failed to send emails',
        isSuccess: false,
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailAll = () => {
    const tier = getTierFromTitle(title);
    setModalAction(`email all candidates in ${tier.toLowerCase()} tier`);
    setPendingAction(() => async () => {
      try {
        const candidateIds = candidates.map(c => c.id);
        const jobId = candidates[0]?.jobId;

        if (!jobId || candidateIds.length === 0) {
          showToast({
            message: `No candidates found in ${tier.toLowerCase()} tier to email`,
            isSuccess: false,
          });
          return;
        }

        await sendBulkEmails(candidateIds, jobId, tier, false);
      } catch (error) {
        console.error('Failed to send bulk emails:', error);
      }
    });
    setModalOpen(true);
  };

  const handleEmailSelected = () => {
    // Filter out candidates that have already been emailed
    const selectedCandidateIds = candidates
      .filter(candidate => checkedCandidates[candidate.id])
      .filter(candidate => {
        // Skip candidates that have already been emailed
        if (candidate.activityHistory) {
          const hasBeenEmailed = candidate.activityHistory.some(
            activity =>
              activity.type === 'EMAIL_SENT' &&
              activity.metadata?.emailType === 'SHORTLISTED' &&
              activity.metadata?.jobId === candidate.jobId
          );
          return !hasBeenEmailed;
        }
        return true;
      })
      .map(candidate => candidate.id);

    if (selectedCandidateIds.length === 0) {
      showToast({
        message:
          'No candidates selected to email or all selected candidates have already been emailed',
        isSuccess: false,
      });
      return;
    }

    const tier = getTierFromTitle(title);
    setModalAction(
      `email ${selectedCandidateIds.length} selected candidate${selectedCandidateIds.length !== 1 ? 's' : ''}`
    );
    setPendingAction(() => async () => {
      try {
        const jobId = candidates[0]?.jobId;

        if (!jobId) {
          showToast({
            message: 'No job ID found for selected candidates',
            isSuccess: false,
          });
          return;
        }

        await sendBulkEmails(selectedCandidateIds, jobId, tier, false);
      } catch (error) {
        console.error('Failed to send emails to selected candidates:', error);
      }
    });
    setModalOpen(true);
  };

  return (
    <div className="overflow-hidden">
      <div className="flex flex-col border-b border-gray-300/40 dark:border-gray-700/50">
        <div
          onClick={handleHeaderClick}
          className={`w-full px-4 py-3 flex items-center justify-between section-header ${className} transition-colors cursor-pointer text-foreground border-b border-gray-300/5`}
        >
          <div className="flex items-center">
            {icon && <div className="ml-1 mr-3">{icon}</div>}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-foreground">{title}</span>
              <span className="inline-flex items-center justify-center min-w-[20px] h-[20px] text-xs font-medium rounded-full px-1.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                {count}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {showActions && (
              <button
                type="button"
                onClick={e => {
                  e.stopPropagation();
                  if (menuButtonRef.current) {
                    const rect = menuButtonRef.current.getBoundingClientRect();
                    setMenuPosition({
                      top: rect.bottom + window.scrollY,
                      left: rect.left,
                    });
                  }
                  setIsActionsExpanded(!isActionsExpanded);
                }}
                className="flex items-center justify-center p-1.5 rounded-full transition-all duration-200 shadow-sm bg-secondary text-secondary-foreground"
                ref={menuButtonRef}
                aria-label="Open actions menu"
              >
                <Menu className="w-4 h-4" />
              </button>
            )}
            <svg
              className={`w-4 h-4 transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''} text-foreground/60`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>

          {showActions &&
            isActionsExpanded &&
            isMounted &&
            createPortal(
              <AnimatePresence>
                <div
                  className="fixed inset-0 z-[100]"
                  onClick={e => {
                    e.stopPropagation();
                    setIsActionsExpanded(false);
                  }}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="fixed z-[101] w-56 backdrop-blur-xl rounded-lg shadow-xl overflow-hidden"
                    style={{
                      top: menuPosition.top + 'px',
                      left: menuPosition.left + 'px',
                      border: '1px solid var(--card-border)',
                      boxShadow:
                        '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)',
                    }}
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                  >
                    <div className="py-2">
                      <button
                        type="button"
                        onClick={handleEmailAll}
                        className="w-full px-4 py-3 flex items-center gap-3 transition-colors text-left hover:bg-[var(--button-secondary-bg)] text-[var(--info-color)]"
                        disabled={isLoading}
                        style={{
                          color: 'var(--info-color)',
                        }}
                      >
                        <Mail className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                        <span className="text-sm font-medium">
                          {isLoading ? 'Sending...' : 'Email All'}
                        </span>
                      </button>

                      {candidates.some(c => checkedCandidates[c.id]) && (
                        <>
                          <div className="my-1 border-t border-[var(--card-border)]"></div>
                          <button
                            type="button"
                            onClick={handleEmailSelected}
                            className="w-full px-4 py-3 flex items-center gap-3 transition-colors text-left hover:bg-[var(--button-secondary-bg)]"
                            disabled={isLoading}
                            style={{
                              color: 'var(--info-color)',
                            }}
                          >
                            <Mail className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                            <span className="text-sm font-medium">Email Selected</span>
                          </button>
                        </>
                      )}
                    </div>
                  </motion.div>
                </div>
              </AnimatePresence>,
              document.body
            )}
        </div>
      </div>
      {isExpanded && <div className="px-4 py-3 bg-muted/30">{children}</div>}
      <ConfirmModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onConfirm={pendingAction}
        action={modalAction}
      />
    </div>
  );
};

export const MatchedCandidateList: React.FC<ScoutedCandidateListProps> = ({
  candidates,
  itemsPerPage = 5,
  currentPage,
  onPageChange,
  onSelectCandidate,
  selectedCandidateId,
  jobId,
}) => {
  const { updateCandidateStatus } = useMatchRankDetailsStore();
  const jobStore = useJobStore();
  // Determine if candidates is already grouped or needs grouping
  const isGroupedData = !Array.isArray(candidates);
  const candidatesArray = isGroupedData ? [] : (candidates as ICandidate[]);

  // State for tracking checked candidates
  const [checkedCandidates, setCheckedCandidates] = useState<Record<string, boolean>>({});
  const [shortlistingCandidateId, setShortlistingCandidateId] = useState<string | null>(null);

  // Group candidates by tier - memoized to prevent infinite loops
  // If data is already grouped, use it directly; otherwise group the flat array
  const groupedCandidates = useMemo(() => {
    if (isGroupedData) {
      const grouped = candidates as GroupedCandidates;
      return grouped;
    }
    const grouped = groupCandidatesByTier(candidatesArray);
    return grouped;
  }, [candidates, isGroupedData, candidatesArray]);

  // Track if initial auto-expand has been applied
  const [initialExpansionApplied, setInitialExpansionApplied] = useState(false);

  // Initialize with empty array - we'll set this based on counts
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  // Determine which sections should be expanded based on counts
  // Use useMemo to calculate counts and prevent unnecessary re-calculations
  const candidateCounts = useMemo(
    () => ({
      shortlisted: groupedCandidates.shortlisted?.length || 0,
      topTier: groupedCandidates.topTier?.length || 0,
      secondTier: groupedCandidates.secondTier?.length || 0,
      others: groupedCandidates.others?.length || 0,
      unranked: groupedCandidates.unranked?.length || 0,
    }),
    [
      groupedCandidates.shortlisted?.length,
      groupedCandidates.topTier?.length,
      groupedCandidates.secondTier?.length,
      groupedCandidates.others?.length,
      groupedCandidates.unranked?.length,
    ]
  );

  // Simplified expansion logic - only run once when data changes
  useEffect(() => {
    if (initialExpansionApplied) return;

    const {
      shortlisted: shortlistedCount,
      topTier: topTierCount,
      secondTier: secondTierCount,
      others: othersCount,
      unranked: unrankedCount,
    } = candidateCounts;

    const totalCount =
      shortlistedCount + topTierCount + secondTierCount + othersCount + unrankedCount;

    if (totalCount > 0) {
      const sectionsToExpand: string[] = [];

      // Expand sections with highest priority data first
      if (shortlistedCount > 0) sectionsToExpand.push('shortlisted');
      if (topTierCount > 0) sectionsToExpand.push('top-tier');
      if (secondTierCount > 0 && sectionsToExpand.length === 0)
        sectionsToExpand.push('second-tier');
      if (othersCount > 0 && sectionsToExpand.length === 0) sectionsToExpand.push('others');
      if (unrankedCount > 0 && sectionsToExpand.length === 0) sectionsToExpand.push('unranked');

      setExpandedSections(sectionsToExpand);
    }

    setInitialExpansionApplied(true);
  }, [
    candidateCounts.shortlisted,
    candidateCounts.topTier,
    candidateCounts.secondTier,
    candidateCounts.others,
    candidateCounts.unranked,
    initialExpansionApplied,
  ]);

  // Note: Auto-selection logic removed from here to prevent infinite loops
  // The parent MatchedCandidatesComposition handles candidate auto-selection

  // Simplified state management without complex subscriptions
  // The parent component handles data fetching and updates

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section) ? prev.filter(s => s !== section) : [...prev, section]
    );
  };

  const handleCheckCandidate = (candidate: ICandidate, checked: boolean) => {
    setCheckedCandidates(prev => ({
      ...prev,
      [candidate.id]: checked,
    }));
  };

  const renderCandidateGroup = (
    candidates: ICandidate[],
    title: string,
    className: string,
    sectionKey: string,
    showActions: boolean = false,
    alwaysShow: boolean = false,
    icon?: React.ReactNode
  ) => {
    // Handle undefined or null candidates array
    const candidatesList = candidates || [];

    if (!candidatesList.length && !alwaysShow) return null;

    const totalPages = Math.ceil(candidatesList.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentCandidates = candidatesList.slice(startIndex, endIndex);

    return (
      <Section
        title={title}
        count={candidatesList.length}
        className={className}
        isExpanded={expandedSections.includes(sectionKey)}
        onToggle={() => toggleSection(sectionKey)}
        showActions={showActions}
        candidates={currentCandidates}
        icon={icon}
        checkedCandidates={checkedCandidates}
      >
        {candidatesList.length > 0 ? (
          <>
            <div className="space-y-1" onClick={e => e.stopPropagation()}>
              {currentCandidates.map((candidate, index) => (
                <CandidateListItem
                  key={candidate.id}
                  candidate={candidate}
                  onSelect={onSelectCandidate}
                  isSelected={candidate.id === selectedCandidateId}
                  isShortlisted={candidate.status === 'SHORTLISTED'}
                />
              ))}
            </div>
            {totalPages > 1 && (
              <div className="flex justify-center pt-2">
                <FloatingPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={onPageChange}
                  position="center"
                />
              </div>
            )}
          </>
        ) : (
          <p className="text-sm text-foreground/60">No candidates in this tier yet.</p>
        )}
      </Section>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-none px-4 py-2.5 border-b border-gray-400/20">
        <p className="text-md font-medium text-foreground">Candidate Rankings</p>
      </div>
      <div className="flex-1 overflow-y-auto">
        <div className="divide-y-0">
          {renderCandidateGroup(
            groupedCandidates.shortlisted,
            'Shortlisted',
            'font-medium',
            'shortlisted',
            true,
            true,
            <Star className="w-5 h-5" style={{ color: '#fbbf24', fill: '#fbbf24' }} />
          )}
          {renderCandidateGroup(
            groupedCandidates.topTier,
            'Top Candidates',
            'font-medium',
            'top-tier',
            true,
            true,
            <Trophy
              className="w-5 h-5"
              style={{ color: 'var(--warning-color)', fill: 'var(--warning-color)' }}
            />
          )}
          {renderCandidateGroup(
            groupedCandidates.secondTier,
            'Second Tier Candidates',
            'font-medium',
            'second-tier',
            true,
            true,
            <Medal className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
          )}
          {renderCandidateGroup(
            groupedCandidates.others,
            'Other Candidates',
            'font-medium',
            'others',
            false,
            true,
            <Target
              className="w-5 h-5"
              style={{ color: 'var(--foreground-color)', opacity: 0.5 }}
            />
          )}
          {renderCandidateGroup(
            groupedCandidates.unranked,
            'Unranked Candidates',
            'font-medium',
            'unranked',
            false,
            true,
            <Target
              className="w-5 h-5"
              style={{ color: 'var(--foreground-color)', opacity: 0.3 }}
            />
          )}
        </div>
      </div>
    </div>
  );
};
