'use client';

import StatusTimeline from '@/components/shared/StatusTimeline';
import { showToast } from '@/components/Toaster';
import { ICandidate } from '@/entities/interfaces';
import { useJobStore } from '@/stores/unifiedJobStore';
import { CandidateStatus } from '@/types/candidate.types';
import { motion, AnimatePresence } from 'framer-motion';
import React, { useState, useEffect } from 'react';
import { Menu, X, ChevronLeft, ChevronRight, Check } from 'lucide-react';
import AnimatedScoreCard from './AnimatedScoreCard';
import { DetailedEvaluationModal } from './CandidateInfo/DetailedEvaluationModal';
import { ExperienceSection } from './CandidateInfo/ExperienceSection';
import { MatchReasoning } from './CandidateInfo/MatchReasoningModal';
import { SkillsSection } from './CandidateInfo/SkillsSection';
import { useCandidateSelection } from '@/contexts/CandidateSelectionContext';

interface SimplifiedCandidateView2Props {
  candidate: ICandidate;
  jobId: string;
  onCandidateSelect?: (candidate: ICandidate) => void;
  onStatusUpdate?: () => void;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
}

type TabType = 'overview' | 'status' | 'detailed-analysis' | 'experience' | 'match-reasoning';

export const SimplifiedCandidateView2: React.FC<SimplifiedCandidateView2Props> = ({
  candidate,
  jobId,
  onCandidateSelect,
  onStatusUpdate,
  topCandidateThreshold = 80,
  secondTierCandidateThreshold = 60,
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [isShortlisting, setIsShortlisting] = useState(false);
  const [showDetailedAnalysis, setShowDetailedAnalysis] = useState(false);
  const [showMatchReasoning, setShowMatchReasoning] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const jobStore = useJobStore();
  const { isSelectionMode, isSelected, toggleCandidate, selectedCount } = useCandidateSelection();

  // Auto-collapse sidebar on smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarCollapsed(true);
      } else {
        setIsSidebarCollapsed(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleShortlistToggle = async () => {
    if (isShortlisting || !jobId) return;

    setIsShortlisting(true);
    const isCurrentlyShortlisted = candidate.status === CandidateStatus.SHORTLISTED;
    const newStatus = isCurrentlyShortlisted ? CandidateStatus.NEW : CandidateStatus.SHORTLISTED;

    try {
      // Use optimistic update
      jobStore.updateCandidateStatusOptimistic(jobId, candidate.id, newStatus);

      showToast({
        message: isCurrentlyShortlisted ? 'Removed from shortlist' : 'Added to shortlist',
        isSuccess: true,
      });

      onStatusUpdate?.();
    } catch (error) {
      console.error('Error updating shortlist status:', error);
      showToast({
        message: 'Failed to update shortlist status',
        isSuccess: false,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  const handleCheckboxToggle = () => {
    if (!isSelectionMode) return;
    
    if (selectedCount >= 3 && !isSelected(candidate.id)) {
      showToast({
        message: 'You can only compare up to 3 candidates at a time',
        type: 'warning',
      });
      return;
    }
    
    toggleCandidate(candidate);
  };

  const tabs = [
    { id: 'overview' as const, label: 'Overview' },
    { id: 'status' as const, label: 'Status' },
    { id: 'detailed-analysis' as const, label: 'Detailed Analysis' },
    { id: 'experience' as const, label: 'Experience' },
    { id: 'match-reasoning' as const, label: 'Match Reasoning' },
  ];

  const handleDownload = () => {
    // TODO: Implement download functionality
    showToast({ message: 'Download feature coming soon', type: 'info' });
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    showToast({ message: 'Share feature coming soon', type: 'info' });
  };

  return (
    <div className="h-full flex flex-col lg:flex-row bg-background text-foreground relative">
      {/* Selection Checkbox - Only visible in selection mode */}
      <AnimatePresence>
        {isSelectionMode && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
            className="absolute top-4 left-4 z-50"
          >
            <button
              onClick={handleCheckboxToggle}
              className={`
                w-6 h-6 rounded-md border-2 transition-all duration-200 flex items-center justify-center
                ${isSelected(candidate.id) 
                  ? 'bg-purple-500 border-purple-500' 
                  : 'bg-white/10 border-gray-400 hover:border-purple-400'
                }
              `}
            >
              {isSelected(candidate.id) && (
                <Check className="w-4 h-4 text-white" strokeWidth={3} />
              )}
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
        className={`fixed top-4 ${isSelectionMode ? 'left-14' : 'left-4'} z-50 lg:hidden p-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 transition-all duration-200`}
      >
        <Menu className="w-5 h-5 text-gray-300" />
      </button>

      {/* Left Sidebar - Tab Navigation */}
      <div
        className={`
          ${isSidebarCollapsed ? 'w-16' : 'w-64'} 
          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          fixed lg:relative inset-y-0 left-0 z-40
          flex-shrink-0 border-r border-gray-300/10 bg-gray-900/95 lg:bg-gray-900/10 
          backdrop-blur-lg lg:backdrop-blur-none
          flex flex-col transition-all duration-300
        `}
      >
        {/* Header with collapse toggle */}
        <div
          className={`${isSidebarCollapsed ? 'px-2' : 'px-4'} py-4 border-b border-gray-300/10 flex items-center justify-between h-16`}
        >
          {!isSidebarCollapsed ? (
            <>
              <h2 className="text-sm font-semibold text-gray-300">Navigation</h2>
              <button
                onClick={() => setIsSidebarCollapsed(true)}
                className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
              >
                <ChevronLeft className="w-4 h-4 text-gray-400" />
              </button>
            </>
          ) : (
            <div className="w-full flex justify-center">
              <button
                onClick={() => setIsSidebarCollapsed(false)}
                className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
              >
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          )}

          {/* Mobile close button */}
          <button
            onClick={() => setIsMobileSidebarOpen(false)}
            className="lg:hidden p-1.5 rounded hover:bg-gray-800/20 transition-colors"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex-1 overflow-y-auto p-2">
          <div className="space-y-1">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  if (window.innerWidth < 1024) {
                    setIsMobileSidebarOpen(false);
                  }
                }}
                className={`
                  w-full text-left px-3 py-3 rounded-lg transition-all duration-200 flex items-center gap-3
                  ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 text-white'
                      : 'text-gray-300 hover:bg-gray-800/20 hover:text-white border border-transparent'
                  }
                `}
              >
                <div className="w-2 h-2 rounded-full bg-current opacity-60" />
                {!isSidebarCollapsed && <span className="text-sm font-medium">{tab.label}</span>}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Overlay */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto lg:pl-0 pl-0">
        <div className={`p-4 sm:p-6 lg:p-8 pt-16 lg:pt-6 ${isSelectionMode ? 'lg:pl-12' : ''}`}>
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'overview' && (
              <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
                  {/* Left Column - Candidate Information */}
                  <div className="space-y-6">
                    <div className="bg-card rounded-lg p-4 sm:p-6 border border-border">
                      <h3 className="text-lg font-semibold mb-4">Candidate Information</h3>

                      <div className="space-y-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm text-muted-foreground">Full Name</label>
                            <p className="font-medium break-words">{candidate.fullName}</p>
                          </div>
                          <div>
                            <label className="text-sm text-muted-foreground">Current Title</label>
                            <p className="font-medium break-words">{candidate.jobTitle}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm text-muted-foreground">Location</label>
                            <p className="font-medium break-words">
                              {candidate.location || 'Not specified'}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm text-muted-foreground">
                              Total Experience
                            </label>
                            <p className="font-medium">
                              {(candidate as any).yearsOfExperience
                                ? `${(candidate as any).yearsOfExperience} Years`
                                : 'Not specified'}
                            </p>
                          </div>
                        </div>

                        {(candidate as any).email && (
                          <div>
                            <label className="text-sm text-muted-foreground">Email</label>
                            <p className="font-medium break-all">{(candidate as any).email}</p>
                          </div>
                        )}

                        {(candidate as any).phone && (
                          <div>
                            <label className="text-sm text-muted-foreground">Phone</label>
                            <p className="font-medium">{(candidate as any).phone}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Additional sections */}
                    {candidate.skills && candidate.skills.length > 0 && (
                      <SkillsSection skills={candidate.skills} />
                    )}
                  </div>

                  {/* Right Column - Match Score */}
                  <div className="flex items-start justify-center xl:justify-start">
                    <div className="w-full max-w-md">
                      <AnimatedScoreCard
                        candidate={candidate as any}
                        matchScore={candidate.evaluation?.matchScore}
                        rank={candidate.evaluation?.rank}
                        topCandidateThreshold={topCandidateThreshold}
                        secondTierCandidateThreshold={secondTierCandidateThreshold}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'status' && (
              <div className="max-w-4xl mx-auto">
                <div className="rounded-xl p-4 sm:p-6 border bg-card border-border">
                  <h3 className="text-lg font-semibold mb-6">Application Status Timeline</h3>
                  <div className="overflow-x-auto">
                    <StatusTimeline
                      currentStatus={candidate.status as CandidateStatus}
                      onStatusClick={newStatus => {
                        // Handle status update
                      }}
                      size="lg"
                      showAllStatuses={true}
                    />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'detailed-analysis' && (
              <div className="max-w-4xl mx-auto">
                <div className="rounded-xl p-4 sm:p-6 border bg-card border-border">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
                    <h3 className="text-lg font-semibold">Detailed Analysis</h3>
                    <button
                      onClick={() => setShowDetailedAnalysis(true)}
                      className="px-4 py-2 rounded-lg bg-primary hover:bg-primary/80 text-primary-foreground transition-colors text-sm font-medium w-full sm:w-auto"
                    >
                      View Full Analysis
                    </button>
                  </div>

                  {candidate.evaluation && (
                    <div className="space-y-4">
                      {/* Match Score Summary */}
                      <div className="p-4 rounded-lg bg-muted">
                        <h4 className="font-medium mb-2">Match Score</h4>
                        <p className="text-2xl font-bold text-primary">
                          {Math.round(
                            candidate.evaluation.matchScore > 1
                              ? candidate.evaluation.matchScore
                              : candidate.evaluation.matchScore * 100
                          )}
                          %
                        </p>
                      </div>

                      {/* Criteria Breakdown */}
                      {candidate.evaluation.detailedScoreAnalysis?.specificCriteriaMatched && (
                        <div className="space-y-3">
                          <h4 className="font-medium">Criteria Breakdown</h4>
                          <div className="space-y-2">
                            {Object.entries(
                              candidate.evaluation.detailedScoreAnalysis.specificCriteriaMatched
                            ).map(([key, value]) => (
                              <div
                                key={key}
                                className="flex flex-col sm:flex-row sm:items-center justify-between p-3 rounded-lg bg-muted gap-2"
                              >
                                <span className="text-sm capitalize break-words">
                                  {key.replace(/([A-Z])/g, ' $1').trim()}
                                </span>
                                <span className="font-medium text-right">
                                  {Math.round(
                                    (value as number) > 1
                                      ? (value as number)
                                      : (value as number) * 100
                                  )}
                                  %
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'experience' && (
              <div className="max-w-4xl mx-auto">
                <ExperienceSection
                  experiences={candidate.experience || []}
                  currentPage={0}
                  totalPages={1}
                  onPageChange={() => {}}
                />
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Detailed Evaluation Modal */}
      {candidate.evaluation && (
        <DetailedEvaluationModal
          open={showDetailedAnalysis}
          onOpenChange={setShowDetailedAnalysis}
          evaluation={candidate.evaluation}
        />
      )}

      {/* Match Reasoning Modal */}
      {showMatchReasoning && candidate.evaluation?.yourReasoningForScoring && (
        <MatchReasoning reasoningText={candidate.evaluation.yourReasoningForScoring} />
      )}
    </div>
  );
};