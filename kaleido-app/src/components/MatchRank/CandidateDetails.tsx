import React, { useEffect, useState } from 'react';

import { useSearchParams } from 'next/navigation';

import { showToast } from '@/components/Toaster';
import { Candidate } from '@/entities/Candidate.entities';
import { ICandidate } from '@/entities/interfaces';
import { useJobThresholds } from '@/hooks/useJobThresholds';
import apiHelper from '@/lib/apiHelper';
import { MinimalCandidate, useJobStore } from '@/stores/unifiedJobStore';
import { CandidateStatus } from '@/types/candidate.types';

import AnimatedScoreCard from './AnimatedScoreCard';
import { CandidateInfoSection } from './CandidateInfo/CandidateInfoSection';
import { DetailedAnalysisSection } from './CandidateInfo/DetailedAnalysisSection';
import { ExperienceSection } from './CandidateInfo/ExperienceSection';
import { HeaderSection } from './CandidateInfo/HeaderSection';
import { MatchReasoning } from './CandidateInfo/MatchReasoningModal';
import { RecommendationsSection } from './CandidateInfo/RecommendationsSection';
import { SkillsSection } from './CandidateInfo/SkillsSection';
import { TimelineSection } from './CandidateInfo/TimelineSection';

// Type guard to check if we have full candidate data
const isFullCandidate = (candidate: MinimalCandidate | ICandidate): candidate is ICandidate => {
  return 'summary' in candidate && 'experience' in candidate && 'location' in candidate;
};

// Helper function to safely access evaluation properties
const getSafeEvaluation = (candidate: MinimalCandidate | ICandidate) => {
  const baseEvaluation = {
    matchScore: 0,
    rank: 0,
    lastEvaluatedAt: '',
    criterionMatchedOn: [],
    yourReasoningForScoring: '',
    recommendations: [],
    jobStats: {
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
    },
    detailedScoreAnalysis: {
      areasOfStrength: [],
      areasForImprovement: [],
      overallMatchPercentage: 0,
      specificCriteriaMatched: {
        skillsMatch: 0,
        experienceRelevance: 0,
        locationAndAvailability: 0,
      },
      detailedReasoning: {
        skillsMatch: 0,
        experienceRelevance: {
          industryExpertise: 0,
          yearsOfRelevantExperience: 0,
          totalYearsOfWorkExperience: 0,
        },
        locationAndAvailability: 0,
      },
      missingCriticalRequirements: [],
    },
  };

  // For MinimalCandidate, merge with available data
  if (!isFullCandidate(candidate)) {
    const evaluation = candidate.evaluation || candidate.evaluations?.[0]?.evaluation;
    return {
      ...baseEvaluation,
      matchScore: candidate.evaluation?.matchScore || candidate.evaluations?.[0]?.matchScore || 0,
      rank: candidate.evaluation?.rank || candidate.evaluations?.[0]?.evaluation?.rank || 0,
    };
  }

  // For ICandidate, merge with full evaluation
  return {
    ...baseEvaluation,
    ...(candidate.evaluation || {}),
  };
};

interface Experience {
  title: string;
  company: string;
  startDate: string;
  endDate: string | null;
  duration: string;
}

interface DetailedScoreAnalysis {
  areasOfStrength: string[];
  areasForImprovement: string[];
  overallMatchPercentage: number;
  specificCriteriaMatched: {
    skillsMatch: number;
    experienceRelevance: number;
    locationAndAvailability: number;
  };
  detailedReasoning: {
    skillsMatch: number;
    experienceRelevance: {
      industryExpertise: number;
      yearsOfRelevantExperience: number;
      totalYearsOfWorkExperience: number;
    };
    locationAndAvailability: number;
  };
  missingCriticalRequirements: string[];
}

interface CandidateDetailsProps {
  candidate: MinimalCandidate | ICandidate;
  onClose: () => void;
  onSelectCandidate?: (candidate: CandidateDetailsProps['candidate']) => void;
  isMatchedView?: boolean;
  isAtsCandidate?: boolean;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
  jobId?: string;
}

const CandidateDetails: React.FC<CandidateDetailsProps> = ({
  candidate,
  onClose,
  onSelectCandidate,
  topCandidateThreshold,
  secondTierCandidateThreshold,
  jobId: propJobId,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [showReasoning, setShowReasoning] = useState(false);
  const [isShortlisting, setIsShortlisting] = useState(false);
  const [fullCandidate, setFullCandidate] = useState<ICandidate | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [forceRefresh, setForceRefresh] = useState(0);
  const searchParams = useSearchParams();
  const jobId = propJobId || searchParams?.get('jobId');

  // Early return if no candidate data
  if (!candidate) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">No candidate data available</p>
      </div>
    );
  }

  // Get store functions for updating data after actions
  const jobStore = useJobStore();

  // Check if we have minimal data (lacks detailed fields)
  const isMinimalData = !isFullCandidate(candidate);

  // Fetch full candidate details on-demand
  useEffect(() => {
    const fetchFullDetails = async () => {
      if (!isMinimalData || !jobId || isLoadingDetails) return;

      setIsLoadingDetails(true);
      try {
        const fullDetails = await jobStore.fetchCandidateById(jobId, candidate.id);
        if (fullDetails) {
          setFullCandidate(fullDetails);
        }
      } catch (error) {
        console.error('Error fetching full candidate details:', error);
      } finally {
        setIsLoadingDetails(false);
      }
    };

    fetchFullDetails();
  }, [candidate.id, jobId, isMinimalData]);

  // Separate effect for force refresh
  useEffect(() => {
    if (forceRefresh > 0 && jobId && !isLoadingDetails) {
      const refreshDetails = async () => {
        setIsLoadingDetails(true);
        try {
          const fullDetails = await jobStore.fetchCandidateById(jobId, candidate.id);
          if (fullDetails) {
            setFullCandidate(fullDetails);
          }
        } catch (error) {
          console.error('Error refreshing candidate details:', error);
        } finally {
          setIsLoadingDetails(false);
        }
      };

      refreshDetails();
    }
  }, [forceRefresh, candidate.id, jobId]);

  // Get the latest candidate from store if available (for optimistic updates)
  const storeCandidateFlat = jobStore.allCandidatesFlat.find(c => c.id === candidate.id);

  // Use full candidate data if available, but merge with store candidate status for optimistic updates
  const displayCandidate = fullCandidate
    ? { ...fullCandidate, status: storeCandidateFlat?.status || fullCandidate.status }
    : storeCandidateFlat || candidate;

  // Function to refresh candidate data (used after enhancement)
  const refreshCandidateData = async () => {
    if (!jobId) return;

    try {
      // Force a complete refresh by incrementing the forceRefresh counter
      setForceRefresh(prev => prev + 1);

      // Clear existing data to force a fresh fetch
      setFullCandidate(null);
      setIsLoadingDetails(false);

      // The useEffect will automatically trigger a new fetch
    } catch (error) {
      console.error('Error refreshing candidate details:', error);
    }
  };

  // Function to update candidate status via API
  const updateCandidateStatus = async (
    candidateId: string,
    status: CandidateStatus,
    jobId: string,
    notes?: string
  ) => {
    const requestData = {
      status,
      notes: notes || 'Status updated internally without email notification',
      jobId,
    };

    const response = await apiHelper.patch(`/candidates/${candidateId}/status`, requestData);
    return response;
  };

  // Handle shortlist toggle
  const handleShortlistToggle = async () => {
    if (isShortlisting || !jobId) return;

    setIsShortlisting(true);
    const isCurrentlyShortlisted = displayCandidate.status === CandidateStatus.SHORTLISTED;
    const newStatus = isCurrentlyShortlisted ? CandidateStatus.NEW : CandidateStatus.SHORTLISTED;

    try {
      // Use optimistic update to immediately update the UI
      jobStore.updateCandidateStatusOptimistic(jobId, displayCandidate.id, newStatus);

      showToast({
        message: isCurrentlyShortlisted ? 'Removed from shortlist' : 'Added to shortlist',
        isSuccess: true,
      });

      // The optimistic update function handles the API call in the background
      // If it fails, it will automatically revert the change
    } catch (error) {
      console.error('Error updating shortlist status:', error);
      showToast({
        message: 'Failed to update shortlist status',
        isSuccess: false,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  // Get thresholds from the store instead of hardcoded defaults
  const {
    topCandidateThreshold: storeTopThreshold,
    secondTierCandidateThreshold: storeSecondThreshold,
  } = useJobThresholds({
    fallbackJob: (displayCandidate as any).jobStats
      ? {
          topCandidateThreshold: (displayCandidate as any).jobStats.topCandidateThreshold,
          secondTierCandidateThreshold: (displayCandidate as any).jobStats
            .secondTierCandidateThreshold,
        }
      : undefined,
  });

  // Use props first, then store values, then fallback to candidate data
  const finalTopThreshold = topCandidateThreshold || storeTopThreshold;
  const finalSecondThreshold = secondTierCandidateThreshold || storeSecondThreshold;

  // Ensure jobStats is properly populated
  const jobStats = {
    topCandidateThreshold: finalTopThreshold,
    secondTierCandidateThreshold: finalSecondThreshold,
  };

  // Create enriched candidate with jobStats instead of mutating the original
  const enrichedCandidate = displayCandidate
    ? {
        ...displayCandidate,
        jobStats,
        evaluation: displayCandidate.evaluation
          ? {
              ...displayCandidate.evaluation,
              jobStats,
            }
          : undefined,
      }
    : null;

  // Create a safe evaluation object with defaults for all properties
  const safeEvaluation = getSafeEvaluation(displayCandidate);

  const experiencesPerPage = 2;
  const candidateExperience = isFullCandidate(displayCandidate)
    ? displayCandidate.experience || []
    : [];
  const totalPages = Math.ceil(candidateExperience.length / experiencesPerPage);
  const paginatedExperience = candidateExperience.slice(
    currentPage * experiencesPerPage,
    (currentPage + 1) * experiencesPerPage
  );

  if (!displayCandidate) {
    return null;
  }

  // Show loading state for details while fetching
  if (isLoadingDetails) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">Loading candidate details...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-x-hidden bg-background text-foreground">
      <HeaderSection
        fullName={displayCandidate.fullName || 'Unknown'}
        jobTitle={displayCandidate.jobTitle || 'No Title'}
        experience={candidateExperience}
        onClose={onClose}
        onShowReasoning={() => setShowReasoning(true)}
        candidate={{
          id: displayCandidate.id || '',
          fullName: displayCandidate.fullName || 'Unknown',
          jobTitle: displayCandidate.jobTitle || 'No Title',
          location: isFullCandidate(displayCandidate) ? displayCandidate.location || '' : '',
          email: isFullCandidate(displayCandidate) ? (displayCandidate as any).email || '' : '',
          phone: isFullCandidate(displayCandidate) ? (displayCandidate as any).phone || '' : '',
          yearsOfExperience: isFullCandidate(displayCandidate)
            ? (displayCandidate as any).yearsOfExperience || 0
            : 0,
          status: (displayCandidate.status as CandidateStatus) || CandidateStatus.NEW,
        }}
        evaluation={safeEvaluation}
        jobId={jobId || 'unknown'}
        onCandidateAction={() => {
          // Refresh candidate data when actions are performed
          onSelectCandidate?.(displayCandidate);
        }}
        onShortlistToggle={handleShortlistToggle}
        isShortlisting={isShortlisting}
      />

      <div className="flex-1 overflow-y-auto overflow-x-hidden bg-background text-foreground">
        <div className="p-3 md:p-6 pb-16">
          {/* Mobile view: Score card at the top */}
          <div className="block md:hidden mb-4">
            <AnimatedScoreCard
              key={`score-card-mobile-${displayCandidate.id}`}
              matchScore={safeEvaluation.matchScore}
              candidate={displayCandidate as unknown as Candidate}
              rank={safeEvaluation.rank}
              topCandidateThreshold={finalTopThreshold}
              secondTierCandidateThreshold={finalSecondThreshold}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
            <div className="lg:col-span-2 space-y-4 md:space-y-6">
              <CandidateInfoSection
                candidate={displayCandidate as any}
                evaluation={safeEvaluation}
                onSelectCandidate={() => {
                  // Refresh full candidate data after enhancement
                  refreshCandidateData();
                  onSelectCandidate?.(displayCandidate);
                }}
                onStatusUpdate={refreshCandidateData}
                jobId={jobId || ''}
              />

              <ExperienceSection
                experiences={paginatedExperience}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />

              <SkillsSection
                skills={isFullCandidate(displayCandidate) ? displayCandidate.skills || [] : []}
              />
            </div>

            {/* Desktop view: Right sidebar with score and analysis */}
            <div className="hidden md:block lg:col-span-1">
              <div className="sticky top-4 space-y-4 md:space-y-6">
                <AnimatedScoreCard
                  key={`score-card-desktop-${displayCandidate.id}`}
                  matchScore={safeEvaluation.matchScore}
                  candidate={displayCandidate as unknown as Candidate}
                  rank={safeEvaluation.rank}
                  topCandidateThreshold={finalTopThreshold}
                  secondTierCandidateThreshold={finalSecondThreshold}
                />

                {(safeEvaluation as any).recommendations &&
                  (safeEvaluation as any).recommendations.length > 0 && (
                    <RecommendationsSection
                      recommendations={(safeEvaluation as any).recommendations}
                    />
                  )}

                <DetailedAnalysisSection
                  specificCriteriaMatched={
                    safeEvaluation.detailedScoreAnalysis?.specificCriteriaMatched || {
                      skillsMatch: 0,
                      experienceRelevance: 0,
                      locationAndAvailability: 0,
                    }
                  }
                  detailedReasoning={
                    safeEvaluation.detailedScoreAnalysis?.detailedReasoning || {
                      skillsMatch: 0,
                      experienceRelevance: {
                        industryExpertise: 0,
                        yearsOfRelevantExperience: 0,
                      },
                      locationAndAvailability: 0,
                    }
                  }
                  areasOfStrength={safeEvaluation.detailedScoreAnalysis?.areasOfStrength || []}
                  areasForImprovement={
                    safeEvaluation.detailedScoreAnalysis?.areasForImprovement || []
                  }
                  evaluation={{
                    ...safeEvaluation,
                    jobStats: {
                      topCandidateThreshold: finalTopThreshold,
                      secondTierCandidateThreshold: finalSecondThreshold,
                    },
                  }}
                />

                <TimelineSection
                  createdAt={
                    typeof displayCandidate.createdAt === 'string'
                      ? displayCandidate.createdAt
                      : displayCandidate.createdAt?.toString() || ''
                  }
                  updatedAt={
                    typeof displayCandidate.updatedAt === 'string'
                      ? displayCandidate.updatedAt
                      : displayCandidate.updatedAt?.toString() || ''
                  }
                />
              </div>
            </div>

            {/* Mobile view: Analysis sections below main content */}
            <div className="block md:hidden space-y-4 col-span-1">
              {(safeEvaluation as any).recommendations &&
                (safeEvaluation as any).recommendations.length > 0 && (
                  <RecommendationsSection
                    recommendations={(safeEvaluation as any).recommendations}
                  />
                )}

              <DetailedAnalysisSection
                specificCriteriaMatched={
                  safeEvaluation.detailedScoreAnalysis?.specificCriteriaMatched || {
                    skillsMatch: 0,
                    experienceRelevance: 0,
                    locationAndAvailability: 0,
                  }
                }
                detailedReasoning={
                  safeEvaluation.detailedScoreAnalysis?.detailedReasoning || {
                    skillsMatch: 0,
                    experienceRelevance: {
                      industryExpertise: 0,
                      yearsOfRelevantExperience: 0,
                    },
                    locationAndAvailability: 0,
                  }
                }
                areasOfStrength={safeEvaluation.detailedScoreAnalysis?.areasOfStrength || []}
                areasForImprovement={
                  safeEvaluation.detailedScoreAnalysis?.areasForImprovement || []
                }
                evaluation={{
                  ...safeEvaluation,
                  jobStats: {
                    topCandidateThreshold: finalTopThreshold,
                    secondTierCandidateThreshold: finalSecondThreshold,
                  },
                }}
              />

              <TimelineSection
                createdAt={
                  typeof displayCandidate.createdAt === 'string'
                    ? displayCandidate.createdAt
                    : displayCandidate.createdAt?.toString() || ''
                }
                updatedAt={
                  typeof displayCandidate.updatedAt === 'string'
                    ? displayCandidate.updatedAt
                    : displayCandidate.updatedAt?.toString() || ''
                }
              />
            </div>
          </div>
        </div>
      </div>

      {showReasoning && (
        <div className="fixed inset-0 backdrop-blur-sm z-50 bg-background/80 dark:bg-background/80">
          <div className="fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] z-50 w-[90vw] md:w-[70vw] lg:w-[50vw]">
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowReasoning(false)}
                className="absolute -top-2 -right-2 w-8 h-8 flex items-center justify-center rounded-full transition-colors z-10 bg-secondary text-secondary-foreground"
              >
                ✕
              </button>
              <MatchReasoning reasoningText={safeEvaluation.yourReasoningForScoring} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CandidateDetails;
