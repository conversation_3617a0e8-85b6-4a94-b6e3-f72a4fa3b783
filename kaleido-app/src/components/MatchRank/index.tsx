import React, { useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { showToast } from '@/components/Toaster';
import { IJob } from '@/entities/interfaces';
import { useJobsList } from '@/hooks/useJobsList';
import { useJobStateStore } from '@/stores/unifiedJobStore';

import { handleMatchRankNavigationComplete } from '@/utils/candidateNavigation';

import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import FileUploader from '../FileUploader';
import PaginationDetailsLayout from '../Layouts/PaginationDetailsLayout';
import { GenericStatusManager } from '../shared/GenericStatusManager/GenericStatusManager';
import { createMatchRankConfig } from '../shared/GenericStatusManager/configs/matchRankConfig';
import MatchRankList from './MatchRankList';

const MatchRank: React.FC = () => {
  const [isUploaderOpen, setUploaderOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  const { jobs, isLoading, currentPage, totalPages, setCurrentPage } = useJobsList({
    itemsPerPage: 20,
    // Removed forceRefresh - using Zustand store updates instead
  });

  // Status manager stores
  const {
    jobs: matchRankJobs,
    activeJobs: activeMatchRankJobs,
    updateJobStatus: updateMatchRankJob,
    removeJob: removeMatchRankJob,
  } = useMatchRankJobsStore();

  // Convert MatchRankJob to StatusJob format
  const convertedMatchRankJobs = Object.fromEntries(
    Object.entries(matchRankJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.error,
        message: job.message,
        createdAt: job.createdAt,
        notifiedCompletion: job.notifiedCompletion,
        errorCount: job.errorCount,
        metadata: job.metadata || {},
      },
    ])
  );

  // Check if we have a selected job and are just switching modes
  const jobId = searchParams?.get('jobId');
  const selectedJob = jobId ? jobs.find(job => job.id === jobId) : undefined;
  const hasSelectedJobData = selectedJob && Object.keys(selectedJob).length > 1;

  // Don't show loading if we're just switching modes and already have job data
  const shouldShowLoading = isLoading && !hasSelectedJobData;

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
  }

  const handleCloseDetails = () => {
    setUploaderOpen(false);
    router.push('/jobs');
  };

  const handleJobClick = (job: IJob) => {
    // Navigate to the appropriate page based on job status
    if (job.status === 'NEW') {
      // If job is new, go to edit page to set up criteria
      router.push(`/jobs/${job.id}/edit`);
    } else {
      // If job has candidates, go to candidates page
      router.push(`/jobs/${job.id}/candidates`);
    }
  };

  return (
    <>
      {/* Show file uploader modal if needed */}
      {isUploaderOpen && selectedJob && (
        <FileUploader
          jobId={selectedJob.id}
          selectedJob={selectedJob}
          onClose={handleCloseDetails}
          onUploadComplete={async data => {
            setUploaderOpen(false);
            // Show toast notification for successful upload
            if (data && data.candidates) {
              showToast({
                message: `${data.candidates.length} candidates uploaded successfully`,
                isSuccess: true,
              });
            }
          }}
        />
      )}

      <PaginationDetailsLayout
        items={jobs as any}
        itemsPerPage={20}
        currentPage={currentPage}
        totalPages={totalPages}
        isLoading={shouldShowLoading}
        idParam="jobId"
        onPageChange={setCurrentPage}
        renderContent={paginatedJobs => (
          <MatchRankList jobs={paginatedJobs as IJob[]} onJobClick={handleJobClick} />
        )}
      />

      {/* Add the GenericStatusManager component */}
      <GenericStatusManager
        jobs={convertedMatchRankJobs}
        activeJobs={activeMatchRankJobs}
        config={createMatchRankConfig()}
        onUpdateJob={(jobId: string, updates: any) => {
          updateMatchRankJob(jobId, updates.status, updates);
        }}
        onRemoveJob={removeMatchRankJob}
        onComplete={async jobId => {
          // Update the job state store when a match rank job completes
          useJobStateStore.getState().markJobAsUpdated(jobId);

          // Navigate based to whether candidates exist
          await handleMatchRankNavigationComplete(router, jobId, true);
        }}
      />
    </>
  );
};

export default MatchRank;
