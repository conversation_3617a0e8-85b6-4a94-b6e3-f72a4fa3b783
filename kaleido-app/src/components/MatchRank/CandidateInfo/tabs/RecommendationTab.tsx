import { CheckCircle2, <PERSON><PERSON><PERSON>, Star, ThumbsDown, ThumbsUp, XCircle } from 'lucide-react';

import { useJobThresholds } from '@/hooks/useJobThresholds';
import { InterviewFocusAreas } from '../InterviewFocusAreas';
import { MatchScoreProgressBar } from '../components/MatchScoreProgressBar';

export const RecommendationTab = ({ evaluation, interviewFocusAreas }) => {
  if (!evaluation) return null;

  // Get job thresholds from the store instead of props drilling
  // This will use the currently selected job or fallback to evaluation.jobStats
  const { topCandidateThreshold, secondTierCandidateThreshold, minThreshold } = useJobThresholds({
    fallbackJob: evaluation.jobStats
      ? {
          topCandidateThreshold: evaluation.jobStats.topCandidateThreshold,
          secondTierCandidateThreshold: evaluation.jobStats.secondTierCandidateThreshold,
        }
      : undefined,
  });

  // Use the thresholds from the hook
  const topThreshold = topCandidateThreshold;
  const secondThreshold = secondTierCandidateThreshold;
  const { matchScore, interviewRecommendation } = evaluation;

  // Calculate hiring recommendation based on match score and thresholds
  const getHiringRecommendation = (score: number) => {
    if (score >= topThreshold) {
      return 'STRONG_HIRE';
    } else if (score >= secondThreshold) {
      return 'HIRE';
    } else {
      // Any candidate below secondThreshold is not recommended
      return 'NOT_RECOMMENDED';
    }
  };

  const calculatedRecommendation = getHiringRecommendation(matchScore);

  // Calculate interview recommendation based on hiring recommendation - only recommend candidates above minimum threshold
  const shouldRecommendInterview =
    calculatedRecommendation === 'STRONG_HIRE' || calculatedRecommendation === 'HIRE';

  // Helper function to get recommendation color and background
  const getRecommendationStyle = (recommendation: string) => {
    switch (recommendation) {
      case 'STRONG_HIRE':
        return {
          color: 'var(--success-color)',
          bgColor: 'rgba(34, 197, 94, 0.15)',
          borderColor: 'var(--success-color)',
          icon: Star,
          label: 'Strong Match',
          description: 'Exceptional fit based on criteria',
        };
      case 'HIRE':
        return {
          color: 'var(--success-color)',
          bgColor: 'rgba(34, 197, 94, 0.12)',
          borderColor: 'var(--success-color)',
          icon: ThumbsUp,
          label: 'Good Match',
          description: 'Meets key requirements',
        };
      case 'CONSIDER':
        return {
          color: 'var(--warning-color)',
          bgColor: 'rgba(251, 146, 60, 0.1)',
          borderColor: 'var(--warning-color)',
          icon: Fingerprint,
          label: 'Consider',
          description: 'May warrant further review',
        };
      case 'NOT_RECOMMENDED':
        return {
          color: 'var(--error-color)',
          bgColor: 'rgba(239, 68, 68, 0.15)',
          borderColor: 'var(--error-color)',
          icon: ThumbsDown,
          label: 'Limited Match',
          description: 'Below threshold criteria',
        };
      default:
        return {
          color: 'var(--foreground-color)',
          bgColor: 'var(--card-bg)',
          borderColor: 'var(--card-border)',
          icon: CheckCircle2,
          label: 'Unknown',
          description: 'No recommendation available',
        };
    }
  };

  const recommendationStyle = getRecommendationStyle(calculatedRecommendation);
  const RecommendationIcon = recommendationStyle.icon;

  // Create a separate header text for the final recommendation to avoid repetition
  const finalRecommendationHeaderText =
    calculatedRecommendation === 'NOT_RECOMMENDED' ? 'Limited Match' : recommendationStyle.label;

  return (
    <div className="space-y-3">
      {/* Compact Hiring Recommendation Header */}
      <div className="rounded-xl p-4 border border-slate-200/10 bg-gradient-to-br from-slate-900/5 to-gray-800/5">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-3 rounded-lg bg-gradient-to-br from-slate-900/5 to-gray-800/5">
            <RecommendationIcon className="w-6 h-6 text-white/70" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-bold" style={{ color: 'var(--foreground-color)' }}>
              Assessment Summary
            </h3>
            <p className="text-xs" style={{ color: 'var(--foreground-color)', opacity: 0.7 }}>
              Analysis based on provided criteria
            </p>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold" style={{ color: recommendationStyle.color }}>
              {finalRecommendationHeaderText}
            </div>
            <div className="text-xs" style={{ color: 'var(--foreground-color)', opacity: 0.6 }}>
              {matchScore}% Match
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Compact Hiring Decision Card */}
          <div className="p-3 rounded-lg border border-slate-200/10 bg-gradient-to-br from-slate-900/5 to-gray-800/5">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-slate-900/5 to-gray-800/5">
                <RecommendationIcon className="w-4 h-4 text-white/70" />
              </div>
              <div>
                <h4 className="font-bold text-sm" style={{ color: 'var(--foreground-color)' }}>
                  Candidate Insights
                </h4>
              </div>
            </div>
            <div className="space-y-1.5">
              <div className="text-md font-bold" style={{ color: recommendationStyle.color }}>
                {recommendationStyle.label}
              </div>
              <p className="text-xs" style={{ color: 'var(--foreground-color)', opacity: 0.8 }}>
                {recommendationStyle.description}
              </p>
            </div>
          </div>

          {/* Compact Interview Recommendation Card */}
          <div className="p-3 rounded-lg border border-slate-200/10 bg-gradient-to-br from-slate-900/5 to-gray-800/5">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-slate-900/5 to-gray-800/5">
                {shouldRecommendInterview ? (
                  <CheckCircle2 className="w-4 h-4 text-white/70" />
                ) : (
                  <XCircle className="w-4 h-4 text-white/70" />
                )}
              </div>
              <div>
                <h4 className="font-bold text-sm" style={{ color: 'var(--foreground-color)' }}>
                  Interview Insights
                </h4>
              </div>
            </div>
            <div className="space-y-1.5">
              <div
                className="text-md font-bold"
                style={{
                  color: shouldRecommendInterview ? 'var(--success-color)' : 'var(--error-color)',
                }}
              >
                {shouldRecommendInterview ? 'Great Fit for Interview' : 'Below Interview Threshold'}
              </div>
              <p className="text-xs" style={{ color: 'var(--foreground-color)', opacity: 0.8 }}>
                {shouldRecommendInterview
                  ? 'Match score suggests further consideration'
                  : 'Match score below suggested threshold'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Match Score Progress Bar - only show if thresholds are available */}
      {topThreshold > 0 && secondThreshold > 0 && (
        <MatchScoreProgressBar
          matchScore={matchScore}
          topThreshold={topThreshold}
          secondThreshold={secondThreshold}
          showLabels={true}
          title="Match Score Overview"
          subtitle="Based on evaluation criteria"
        />
      )}

      {/* Interview Focus Areas Component */}
      <InterviewFocusAreas
        interviewFocusAreas={interviewFocusAreas}
        shouldRecommendInterview={shouldRecommendInterview}
        startExpanded={false}
        showImage={true}
      />
    </div>
  );
};
