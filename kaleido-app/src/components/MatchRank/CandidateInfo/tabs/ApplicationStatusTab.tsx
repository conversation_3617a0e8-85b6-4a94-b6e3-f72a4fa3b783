'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import StatusTimeline from '@/components/shared/StatusTimeline';
import StatusUpdateModalWithDate from '@/components/shared/StatusUpdateModalWithDate';
import { CandidateStatus } from '@/types/candidate.types';
import { useJobStore } from '@/stores/unifiedJobStore';
import { showToast } from '@/components/Toaster';
import ColorfulLoader from '@/components/Layouts/ColourfulLoader';
import { ICandidate } from '@/entities/interfaces';

interface ApplicationStatusTabProps {
  candidate: ICandidate;
  jobId?: string;
  companyName?: string;
  jobTitle?: string;
  onStatusUpdate?: () => void;
}

export const ApplicationStatusTab: React.FC<ApplicationStatusTabProps> = ({
  candidate,
  jobId,
  companyName = 'Your Company',
  jobTitle = 'Open Position',
  onStatusUpdate,
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(candidate.status as CandidateStatus);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<CandidateStatus | null>(null);

  const { updateCandidateStatusWithMessage } = useJobStore();

  // Update current status when candidate prop changes
  useEffect(() => {
    setCurrentStatus(candidate.status as CandidateStatus);
  }, [candidate.status]);

  const handleStatusClick = (status: CandidateStatus) => {
    if (!jobId) {
      console.error('JobId is required for status update');
      return;
    }

    setPendingStatus(status);
    setIsModalOpen(true);
  };

  const handleConfirmStatusUpdate = async (data: any) => {
    if (!pendingStatus || !jobId) return;

    setIsUpdating(true);

    try {
      // Update the UI immediately for instant feedback
      setCurrentStatus(pendingStatus);

      // Call the store method for optimistic update
      await updateCandidateStatusWithMessage(jobId, candidate.id, pendingStatus, data.message, {
        candidateEmail: (candidate as any).email,
        ...(data.date && { interviewDate: data.date }),
        ...(data.meetingLink && { meetingLink: data.meetingLink }),
        ...(data.startDate && { startDate: data.startDate }),
        ...(data.onboardingLink && { onboardingLink: data.onboardingLink }),
      });

      showToast({
        message: `Status updated to ${pendingStatus}`,
        type: 'success',
      });

      onStatusUpdate?.();
    } catch (error) {
      console.error('Error updating status:', error);

      // Revert the status on error
      setCurrentStatus(candidate.status as CandidateStatus);

      showToast({
        message: 'Failed to update status',
        type: 'error',
      });
    } finally {
      setIsUpdating(false);
      setIsModalOpen(false);
      setPendingStatus(null);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div
        className="flex flex-col rounded-3xl w-full min-h-[600px] overflow-hidden relative"
        style={{
          background:
            'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
          backdropFilter: 'blur(20px)',
        }}
      >
        {/* Full Width Header Image with Curved Edges */}
        <div className="w-full h-40 sm:h-48 md:h-56 relative overflow-hidden">
          <Image
            src="/images/narrow/expand-6.webp"
            alt="Status Update"
            fill
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-purple-700/30 to-transparent"></div>
        </div>

        <div className="text-center mb-8 sm:mb-12 px-8 sm:px-12 pt-8 sm:pt-10">
          <p className="text-lg sm:text-xl md:text-2xl mb-4 sm:mb-6 font-light tracking-wide text-white/90">
            Select a status to update
          </p>
          <p className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 sm:mb-12 bg-gradient-to-r from-purple-300 via-pink-300 to-purple-400 bg-clip-text text-transparent leading-tight">
            {candidate.fullName}'s application
          </p>
        </div>

        <div className="w-full px-8 sm:px-12 mb-8 sm:mb-12">
          <StatusTimeline
            currentStatus={currentStatus}
            onStatusClick={handleStatusClick}
            size="lg"
            showAllStatuses={false}
          />
        </div>

        {/* Loading overlay with ColorfulLoader */}
        {isUpdating && (
          <div className="absolute inset-0 z-30 rounded-3xl">
            <ColorfulLoader text="Updating status" useModalBg={true} />
          </div>
        )}
      </div>

      {/* Status Update Modal */}
      <StatusUpdateModalWithDate
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setPendingStatus(null);
        }}
        currentStatus={currentStatus}
        newStatus={pendingStatus || (currentStatus as CandidateStatus)}
        onConfirm={handleConfirmStatusUpdate}
        isLoading={isUpdating}
        candidateName={candidate.fullName}
        jobTitle={jobTitle}
        candidateEmail={(candidate as any).email}
        companyName={companyName}
      />
    </div>
  );
};
