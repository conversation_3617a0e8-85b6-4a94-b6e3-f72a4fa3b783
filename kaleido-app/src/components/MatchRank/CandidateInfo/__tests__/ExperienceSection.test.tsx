import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ExperienceSection } from '../ExperienceSection';
import { StandardizedProfile } from '@/shared/types/profile.types';

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Building: ({ className, ...props }: any) => (
    <div data-testid="building-icon" className={className} {...props} />
  ),
  ChevronLeft: ({ className, ...props }: any) => (
    <div data-testid="chevron-left-icon" className={className} {...props} />
  ),
  ChevronRight: ({ className, ...props }: any) => (
    <div data-testid="chevron-right-icon" className={className} {...props} />
  ),
  Calendar: ({ className, ...props }: any) => (
    <div data-testid="calendar-icon" className={className} {...props} />
  ),
  Clock: ({ className, ...props }: any) => (
    <div data-testid="clock-icon" className={className} {...props} />
  ),
  Briefcase: ({ className, ...props }: any) => (
    <div data-testid="briefcase-icon" className={className} {...props} />
  ),
}));

type Experience = StandardizedProfile['experience'][number];

const mockExperiences: Experience[] = [
  {
    title: 'Senior Software Engineer',
    company: 'Tech Corp',
    startDate: '2022-01-01',
    endDate: '',
    duration: 24,
    location: 'Remote',
    description: 'Leading development team',
  },
  {
    title: 'Software Engineer',
    company: 'StartupXYZ',
    startDate: '2020-06-01',
    endDate: '2021-12-31',
    duration: 18,
    location: 'San Francisco, CA',
    description: 'Full-stack development',
  },
  {
    title: 'Junior Developer',
    company: 'DevCorp',
    startDate: '2019-01-01',
    endDate: '2020-05-31',
    duration: 16,
    location: 'New York, NY',
    description: 'Frontend development',
  },
];

const longExperienceList: Experience[] = Array.from({ length: 12 }, (_, index) => ({
  title: `Position ${index + 1}`,
  company: `Company ${index + 1}`,
  startDate: `2020-0${(index % 9) + 1}-01`,
  endDate: index === 0 ? '' : `2021-0${(index % 9) + 1}-01`,
  duration: 12 + index,
  location: 'Location',
  description: `Description for position ${index + 1}`,
}));

describe('ExperienceSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing when no experiences provided', () => {
      render(<ExperienceSection experiences={[]} />);
      expect(screen.queryByText('Current Position')).not.toBeInTheDocument();
      expect(screen.queryByText('Previous Experience')).not.toBeInTheDocument();
    });

    it('renders most recent experience as featured when experiences provided', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.getByText('Current Position')).toBeInTheDocument();
      expect(screen.getByText('Senior Software Engineer')).toBeInTheDocument();
      expect(screen.getByText('Tech Corp')).toBeInTheDocument();
    });

    it('renders previous experience section when multiple experiences exist', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.getByText('Previous Experience')).toBeInTheDocument();
      expect(screen.getByText('(2 positions)')).toBeInTheDocument();
    });

    it('renders correct icons for different sections', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.getAllByTestId('briefcase-icon')).toHaveLength(1);
      expect(screen.getAllByTestId('building-icon')).toHaveLength(4); // 1 for current + 3 for previous
      expect(screen.getAllByTestId('calendar-icon')).toHaveLength(1);
      expect(screen.getAllByTestId('clock-icon')).toHaveLength(1);
    });
  });

  describe('Date Formatting', () => {
    it('formats dates correctly for current position', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.getByText(/Jan 2022 - Present/)).toBeInTheDocument();
    });

    it('formats dates correctly for previous positions', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.getByText(/Jun 2020 - Dec 2021/)).toBeInTheDocument();
      expect(screen.getByText(/Jan 2019 - May 2020/)).toBeInTheDocument();
    });

    it('handles invalid dates gracefully', () => {
      const experiencesWithInvalidDates: Experience[] = [
        {
          title: 'Test Position',
          company: 'Test Company',
          startDate: 'invalid-date',
          endDate: 'another-invalid-date',
          duration: 12,
          location: 'Test Location',
          description: 'Test description',
        },
      ];

      render(<ExperienceSection experiences={experiencesWithInvalidDates} />);

      // Should still render the position without throwing errors
      expect(screen.getByText('Test Position')).toBeInTheDocument();
      expect(screen.getByText('Test Company')).toBeInTheDocument();
    });
  });

  describe('Duration Formatting', () => {
    it('formats duration in months correctly', () => {
      const experiences: Experience[] = [
        {
          title: 'Short Term Role',
          company: 'Test Company',
          startDate: '2023-01-01',
          endDate: '2023-06-01',
          duration: 5,
          location: 'Remote',
          description: 'Short term role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('5 months')).toBeInTheDocument();
    });

    it('formats duration in years correctly', () => {
      const experiences: Experience[] = [
        {
          title: 'Long Term Role',
          company: 'Test Company',
          startDate: '2020-01-01',
          endDate: '2022-01-01',
          duration: 24,
          location: 'Remote',
          description: 'Long term role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('2 years')).toBeInTheDocument();
    });

    it('formats duration in years and months correctly', () => {
      const experiences: Experience[] = [
        {
          title: 'Mixed Duration Role',
          company: 'Test Company',
          startDate: '2020-01-01',
          endDate: '2021-08-01',
          duration: 19,
          location: 'Remote',
          description: 'Mixed duration role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('1 year, 7 months')).toBeInTheDocument();
    });

    it('handles string duration input', () => {
      const experiences: Experience[] = [
        {
          title: 'String Duration Role',
          company: 'Test Company',
          startDate: '2023-01-01',
          endDate: '2023-07-01',
          duration: '6' as any,
          location: 'Remote',
          description: 'String duration role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('6 months')).toBeInTheDocument();
    });

    it('handles invalid duration gracefully', () => {
      const experiences: Experience[] = [
        {
          title: 'Invalid Duration Role',
          company: 'Test Company',
          startDate: '2023-01-01',
          endDate: '2023-07-01',
          duration: 'invalid' as any,
          location: 'Remote',
          description: 'Invalid duration role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('Invalid Duration Role')).toBeInTheDocument();
    });

    it('handles undefined duration', () => {
      const experiences: Experience[] = [
        {
          title: 'No Duration Role',
          company: 'Test Company',
          startDate: '2023-01-01',
          endDate: '2023-07-01',
          duration: undefined as any,
          location: 'Remote',
          description: 'No duration role',
        },
      ];

      render(<ExperienceSection experiences={experiences} />);
      expect(screen.getByText('No Duration Role')).toBeInTheDocument();
    });
  });

  describe('Pagination', () => {
    it('shows pagination controls when there are more than 5 previous experiences', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
      expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Next page')).toBeInTheDocument();
    });

    it('disables previous button on first page', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      const prevButton = screen.getByLabelText('Previous page');
      expect(prevButton).toBeDisabled();
    });

    it('enables next button when there are more pages', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      const nextButton = screen.getByLabelText('Next page');
      expect(nextButton).not.toBeDisabled();
    });

    it('navigates to next page when next button clicked', async () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
      });
    });

    it('navigates to previous page when previous button clicked', async () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      // Go to page 2 first
      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
      });

      // Then go back to page 1
      const prevButton = screen.getByLabelText('Previous page');
      fireEvent.click(prevButton);

      await waitFor(() => {
        expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
      });
    });

    it('disables next button on last page', async () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      // Navigate to last page
      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton); // Page 2

      await waitFor(() => {
        expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
      });

      fireEvent.click(nextButton); // Page 3

      await waitFor(() => {
        expect(screen.getByText('Page 3 of 3')).toBeInTheDocument();
        expect(nextButton).toBeDisabled();
      });
    });

    it('does not show pagination when experiences fit on one page', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      expect(screen.queryByText(/Page \d+ of \d+/)).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Previous page')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Next page')).not.toBeInTheDocument();
    });
  });

  describe('External Pagination Props', () => {
    const mockOnPageChange = jest.fn();

    beforeEach(() => {
      mockOnPageChange.mockClear();
    });

    it('uses external pagination props when provided', () => {
      render(
        <ExperienceSection
          experiences={longExperienceList}
          currentPage={1}
          totalPages={5}
          onPageChange={mockOnPageChange}
        />
      );

      expect(screen.getByText('Page 2 of 5')).toBeInTheDocument();
    });

    it('calls external onPageChange when navigation buttons clicked', async () => {
      render(
        <ExperienceSection
          experiences={longExperienceList}
          currentPage={1}
          totalPages={5}
          onPageChange={mockOnPageChange}
        />
      );

      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton);

      expect(mockOnPageChange).toHaveBeenCalledWith(2);
    });

    it('calls external onPageChange with correct page number for previous', async () => {
      render(
        <ExperienceSection
          experiences={longExperienceList}
          currentPage={2}
          totalPages={5}
          onPageChange={mockOnPageChange}
        />
      );

      const prevButton = screen.getByLabelText('Previous page');
      fireEvent.click(prevButton);

      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty experience objects gracefully', () => {
      const emptyExperiences: Experience[] = [
        {
          title: '',
          company: '',
          startDate: '',
          endDate: '',
          duration: 0,
          location: '',
          description: '',
        },
      ];

      render(<ExperienceSection experiences={emptyExperiences} />);

      // Should still render structure without crashing
      expect(screen.getByText('Current Position')).toBeInTheDocument();
    });

    it('handles single experience correctly', () => {
      const singleExperience: Experience[] = [mockExperiences[0]];

      render(<ExperienceSection experiences={singleExperience} />);

      expect(screen.getByText('Current Position')).toBeInTheDocument();
      expect(screen.queryByText('Previous Experience')).not.toBeInTheDocument();
    });

    it('handles experiences with missing fields', () => {
      const incompleteExperiences: Experience[] = [
        {
          title: 'Complete Role',
          company: 'Complete Company',
          startDate: '2023-01-01',
          endDate: '',
          duration: 12,
          location: 'Remote',
          description: 'Complete description',
        },
        {
          title: 'Incomplete Role',
          company: 'Incomplete Company',
          // Missing fields
        } as Experience,
      ];

      expect(() => {
        render(<ExperienceSection experiences={incompleteExperiences} />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for pagination buttons', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Next page')).toBeInTheDocument();
    });

    it('has proper titles for pagination buttons', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      const prevButton = screen.getByTitle('Previous page');
      const nextButton = screen.getByTitle('Next page');

      expect(prevButton).toBeInTheDocument();
      expect(nextButton).toBeInTheDocument();
    });

    it('properly disables buttons when they should not be interactive', () => {
      render(<ExperienceSection experiences={longExperienceList} />);

      const prevButton = screen.getByLabelText('Previous page');
      expect(prevButton).toHaveAttribute('disabled');
      expect(prevButton).toHaveClass('disabled:cursor-not-allowed');
    });
  });

  describe('Responsive Design Classes', () => {
    it('applies responsive classes for mobile layout', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      const container = screen.getByText('Current Position').closest('.relative');
      expect(container).toHaveClass('p-8');
    });

    it('applies responsive classes for experience cards', () => {
      render(<ExperienceSection experiences={mockExperiences} />);

      // Check that the previous experience cards have the right structure
      const previousSection = screen.getByText('Previous Experience');
      expect(previousSection).toBeInTheDocument();

      // Check for responsive padding classes in the main sections
      const featuredSection = screen.getByText('Current Position').closest('.relative');
      expect(featuredSection).toHaveClass('relative', 'p-8');
    });
  });
});
