import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';

import { ChevronDown, ChevronUp, Search, Unlock } from 'lucide-react';

import { CreditButton } from '@/components/common/CreditButton';
import CulturalFitSetupModal from '@/components/CultureFit/CulturalFitSetupModal';
import StatusTimeline from '@/components/shared/StatusTimeline';
import StatusUpdateModalWithDate, {
  StatusUpdateData,
} from '@/components/shared/StatusUpdateModalWithDate';
import { showToast } from '@/components/Toaster';
import { apiClient } from '@/lib/apiHelper';
import { useJobStore } from '@/stores/unifiedJobStore';
// cspell:disable-next-line
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { CandidateStatus } from '@/types/candidate.types';
import { Experience } from '@/types/jobSeeker';
import { CreditActionType } from '@/types/subscription';

import { BasicInfo } from './BasicInfo';
import { ContactInfo } from './ContactInfo';
import { SocialProfiles } from './SocialProfiles';
import { VideoIntroButton } from './VideoIntroButton';

interface CandidateInfoSectionProps {
  candidate: {
    id: string;
    fullName: string;
    jobTitle: string;
    location: string;
    email?: string;
    phone: string | null;
    yearsOfExperience: number | null;
    experience: Experience[];
    contacted: boolean;
    contactMethod: string | null;
    preferredLocation: string | null;
    isRemoteOnly: boolean;
    linkedinUrl: string;
    githubUrl: string;
    profileUrl: string;
    summary?: string;
    status: CandidateStatus;
    source?: string;
    sourceType?: string;
    videoIntroEmailSent?: boolean; // New computed field from backend
    emailCorrespondence?: Array<{
      id: string;
      type: 'SENT' | 'RECEIVED';
      subject: string;
      content: string;
      from: string;
      to: string;
      timestamp: Date;
      emailType: 'interview' | 'offer' | 'status' | 'general';
      metadata?: {
        jobId?: string;
        jobTitle?: string;
        companyName?: string;
        [key: string]: any;
      };
    }>;
  };
  evaluation?: any;
  onSelectCandidate: () => void;
  onStatusUpdate?: () => void;
  jobId: string;
}

export const CandidateInfoSection: React.FC<CandidateInfoSectionProps> = ({
  candidate,
  evaluation,
  onSelectCandidate,
  onStatusUpdate,
  jobId,
}) => {
  const { updateCandidateStatus, updateCandidateData, checkCulturalFitSetup } =
    useMatchRankDetailsStore();
  const jobStore = useJobStore();
  const [isShortlisting, setIsShortlisting] = useState(false);
  const [isEmailingAndShortlisting, setIsEmailingAndShortlisting] = useState(false);
  const [showCulturalFitSetupModal, setShowCulturalFitSetupModal] = useState(false);
  const [culturalFitSetupMessage, setCulturalFitSetupMessage] = useState('');
  const [culturalFitJobId, setCulturalFitJobId] = useState('');
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [isStatusTimelineOpen, setIsStatusTimelineOpen] = useState(false);
  const [statusToChange, setStatusToChange] = useState<CandidateStatus | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [isReadMore, setIsReadMore] = useState(false);
  const [pendingStatusUpdate, setPendingStatusUpdate] = useState<StatusUpdateData | null>(null);
  const [pendingStatusType, setPendingStatusType] = useState<
    'status' | 'email' | 'shortlist' | null
  >(null);

  // Create portal container for the modal
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    // Create or get the portal container
    let container = document.getElementById('status-timeline-portal');
    if (!container) {
      container = document.createElement('div');
      container.id = 'status-timeline-portal';
      document.body.appendChild(container);
    }

    setPortalContainer(container);

    // Clean up on unmount
    return () => {
      if (container && container.parentNode && !isStatusTimelineOpen) {
        container.parentNode.removeChild(container);
      }
    };
  }, [isStatusTimelineOpen]);

  const handleEmailAndShortlist = async () => {
    if (isEmailingAndShortlisting || !candidate.email || !jobId) return;

    setIsEmailingAndShortlisting(true);
    try {
      // Send email notification about the current status
      // This will use the current status (which might already be shortlisted)
      const statusToNotify = candidate.status;

      // Prepare date sensitive information with a message
      const dateSensitiveInfo = {
        message: `Email notification sent for ${statusToNotify} status`,
      };

      // Update the status to the same status but with email notification
      const response = await updateCandidateStatus(
        candidate.id,
        statusToNotify,
        jobId,
        JSON.stringify(dateSensitiveInfo)
      );

      // Handle cultural fit setup if needed
      if (response && checkCulturalFitSetup(response)) {
        const setupResponse = response as Record<string, any>;

        // Store pending status update
        setPendingStatusUpdate({
          message: `Email notification sent for ${statusToNotify} status`,
        });
        setPendingStatusType('email');

        setCulturalFitSetupMessage(
          setupResponse.message ||
            'Video Introduction questionnaires can be set up to enhance candidate evaluation.'
        );
        setCulturalFitJobId(setupResponse.jobId || jobId);
        setShowCulturalFitSetupModal(true);
      } else if (response) {
        showToast({
          message: 'Email notification sent successfully',
          isSuccess: true,
        });

        onSelectCandidate();
        onStatusUpdate?.();
      }
    } catch (error) {
      console.error('Error sending email notification:', error);
      showToast({
        message: error.message ?? 'Failed to send email notification',
        isSuccess: false,
      });
    } finally {
      setIsEmailingAndShortlisting(false);
    }
  };

  const handleShortlist = async () => {
    if (isShortlisting || !jobId) return;

    setIsShortlisting(true);
    try {
      const newStatus = isShortlisted ? CandidateStatus.NEW : CandidateStatus.SHORTLISTED;

      // Use a simple notes message instead of dateSensitiveInfo
      // This avoids triggering the email sending logic in the backend
      const notes = 'Status updated internally without email notification';

      const response = await updateCandidateStatus(candidate.id, newStatus, jobId, notes);

      // Handle cultural fit setup if needed
      if (response && checkCulturalFitSetup(response)) {
        const setupResponse = response as Record<string, any>;

        // Store pending status update
        setPendingStatusUpdate({
          message: 'Status updated internally without email notification',
        });
        setPendingStatusType('shortlist');

        setCulturalFitSetupMessage(
          setupResponse.message ||
            'Video Introduction questionnaires can be set up to enhance candidate evaluation.'
        );
        setCulturalFitJobId(setupResponse.jobId || jobId);
        setShowCulturalFitSetupModal(true);
      } else if (response) {
        showToast({
          message: isShortlisted ? 'Removed from shortlist' : 'Added to shortlist',
          isSuccess: true,
        });

        // Update the store to immediately reflect the change in the candidate list
        // Update will be handled by the onCandidateStatusChange method
        if (jobId) {
          await jobStore.onCandidateStatusChange(jobId, candidate.id);
        }

        onSelectCandidate();
        onStatusUpdate?.();
      }
    } catch (error) {
      console.error('Error updating shortlist status:', error);
      showToast({
        message: 'Failed to update shortlist status',
        isSuccess: false,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  const handleStatusChange = async (statusData: StatusUpdateData) => {
    if (!jobId || !statusToChange) return;

    setIsUpdatingStatus(true);
    try {
      // Format date sensitive information
      const dateSensitiveInfo = {
        ...(statusData.date && { date: statusData.date }),
        ...(statusData.meetingLink && { meetingLink: statusData.meetingLink }),
        ...(statusData.startDate && { startDate: statusData.startDate }),
        ...(statusData.onboardingLink && { onboardingLink: statusData.onboardingLink }),
        message: statusData.message,
      };

      const response = await updateCandidateStatus(
        candidate.id,
        statusToChange,
        jobId,
        JSON.stringify(dateSensitiveInfo)
      );

      // Handle cultural fit setup if needed
      if (response && checkCulturalFitSetup(response)) {
        const setupResponse = response as Record<string, any>;

        // Store pending status update
        setPendingStatusUpdate(statusData);
        setPendingStatusType('status');

        setCulturalFitSetupMessage(
          setupResponse.message ||
            'Video Introduction questionnaires can be set up to enhance candidate evaluation.'
        );
        setCulturalFitJobId(setupResponse.jobId || jobId);
        setShowCulturalFitSetupModal(true);
      } else if (response) {
        showToast({
          message: `Status updated to ${statusToChange}`,
          isSuccess: true,
        });
        onSelectCandidate();
        onStatusUpdate?.();
      }
    } catch (error) {
      console.error('Error updating status:', error);
      showToast({
        message: 'Failed to update status',
        isSuccess: false,
      });
    } finally {
      setIsUpdatingStatus(false);
      setIsStatusModalOpen(false);
      setStatusToChange(null);
    }
  };

  // This function can be used if cultural fit status handling is needed in the future
  // Keeping it commented out to avoid unused function warnings
  /*
  const handleCulturalFitStatus = async (candidateId: string, status: CandidateStatus) => {
    if (!jobId || !candidate.email || status !== CandidateStatus.CULTURAL_FIT_ANSWERED) return;

    try {
      const response = await updateCandidateStatus(candidateId, status, jobId);

      // Check if the response has the needsCulturalFitSetup property
      if (response && typeof response === 'object' && 'needsCulturalFitSetup' in response) {
        // Use a type with optional properties to avoid type errors
        const setupResponse = response as Record<string, any>;

        // Show the cultural fit setup modal
        setCulturalFitSetupMessage(
          setupResponse.message || 'Video Introduction questionnaires need to be set up.'
        );
        setCulturalFitJobId(setupResponse.jobId || jobId);
        setShowCulturalFitSetupModal(true);
        return false;
      } else if (response) {
        showToast({
          message: 'Video Introduction status updated successfully',
          isSuccess: true,
        });
        onSelectCandidate();
        onStatusUpdate?.();
        return true;
      }
    } catch (error) {
      console.error('Error updating cultural fit status:', error);
      showToast({
        message: 'Failed to update cultural fit status',
        isSuccess: false,
      });
    }
    return false;
  };
  */

  const isShortlisted = candidate.status === CandidateStatus.SHORTLISTED;

  // Check if candidate is scouted (check both source and sourceType fields)
  const isScoutedCandidate =
    candidate.source === 'LINKEDIN_SCOUT' || candidate.sourceType === 'LINKEDIN_SCOUT';

  // Check if email is locked (needs unlocking)
  const isEmailLocked =
    candidate.email === '<EMAIL>' ||
    candidate.email ===
      'Email available after profile enhancement - Contact via LinkedIn or enhance profile for direct email';

  // Handle unlock candidate details
  const handleUnlockDetails = async () => {
    try {
      showToast({
        message: 'Unlocking candidate details...',
        type: 'info',
      });

      // Make API call to enhance candidate details
      const response: any = await apiClient.post(`/candidates/${candidate.id}/enhance`);

      if (response && response.candidate) {
        // Extract the enhanced candidate data from the response
        const enhancedCandidate = response.candidate;

        // Create a complete enhanced candidate object
        const enhancedCandidateData = {
          // Contact information
          ...(enhancedCandidate.email && { email: enhancedCandidate.email }),
          ...(enhancedCandidate.phone && { phone: enhancedCandidate.phone }),

          // LinkedIn and social profiles
          ...(enhancedCandidate.linkedinUrl && { linkedinUrl: enhancedCandidate.linkedinUrl }),
          ...(enhancedCandidate.githubUrl && { githubUrl: enhancedCandidate.githubUrl }),
          ...(enhancedCandidate.profileUrl && { profileUrl: enhancedCandidate.profileUrl }),

          // Professional details
          ...(enhancedCandidate.summary && { summary: enhancedCandidate.summary }),
          ...(enhancedCandidate.experience && { experience: enhancedCandidate.experience }),
          ...(enhancedCandidate.skills && { skills: enhancedCandidate.skills }),
          ...(enhancedCandidate.currentCompany && {
            currentCompany: enhancedCandidate.currentCompany,
          }),
          ...(enhancedCandidate.jobTitle && { jobTitle: enhancedCandidate.jobTitle }),

          // Additional information
          ...(enhancedCandidate.yearsOfExperience !== undefined && {
            yearsOfExperience: enhancedCandidate.yearsOfExperience,
          }),
          ...(enhancedCandidate.location && { location: enhancedCandidate.location }),
          ...(enhancedCandidate.preferredLocation && {
            preferredLocation: enhancedCandidate.preferredLocation,
          }),
          ...(enhancedCandidate.isRemoteOnly !== undefined && {
            isRemoteOnly: enhancedCandidate.isRemoteOnly,
          }),

          // Name fields
          ...(enhancedCandidate.firstName && { firstName: enhancedCandidate.firstName }),
          ...(enhancedCandidate.lastName && { lastName: enhancedCandidate.lastName }),
          ...(enhancedCandidate.fullName && { fullName: enhancedCandidate.fullName }),
        };

        // Update the candidate data instantly in the store
        updateCandidateData(candidate.id, enhancedCandidateData);

        // Invalidate any cached data for this candidate
        jobStore.invalidateCache(`candidate-${candidate.id}`);
        jobStore.invalidateCache(`job-${jobId}`);
        jobStore.invalidateCache(`candidates-${jobId}`);

        // Force refresh the candidate data from the backend
        if (jobId) {
          try {
            // First, fetch the updated candidate directly
            const updatedCandidate = await jobStore.fetchCandidateById(jobId, candidate.id);

            // Update the store with the fetched data
            if (updatedCandidate) {
              updateCandidateData(candidate.id, updatedCandidate);
            }

            // Then refresh the entire job data to ensure consistency
            await jobStore.fetchJobById(jobId, true);

            // Finally, refresh the candidates list
            await jobStore.fetchCandidates(jobId, jobStore.currentPage, jobStore.filters);
          } catch (error) {
            console.error('Error refreshing candidate data:', error);
          }
        }

        // Call the callbacks to trigger parent refresh
        if (onStatusUpdate) {
          onStatusUpdate();
        }

        // Small delay to ensure all updates have propagated
        setTimeout(() => {
          onSelectCandidate();
        }, 100);

        showToast({
          message: 'Candidate details unlocked successfully!',
          type: 'success',
        });
      } else {
        showToast({
          message: 'Enhanced data received but candidate information is missing',
          type: 'warning',
        });
      }
    } catch (error: any) {
      console.error('Error unlocking candidate details:', error);

      // Handle specific error cases
      if (error.status === 402) {
        // Credit/subscription error - already handled by apiHelper interceptor
        showToast({
          message: 'Insufficient credits to unlock candidate details',
          type: 'error',
        });
      } else if (error.status === 404) {
        showToast({
          message: 'Candidate not found',
          type: 'error',
        });
      } else {
        showToast({
          message: 'Failed to unlock candidate details. Please try again.',
          type: 'error',
        });
      }
    }
  };

  // Function to handle status click from timeline
  const handleStatusClick = (newStatus: CandidateStatus) => {
    setStatusToChange(newStatus);
    setIsStatusTimelineOpen(false);
    setIsStatusModalOpen(true);
  };

  const handleSkipCulturalFitSetup = async () => {
    if (!pendingStatusUpdate || !pendingStatusType) {
      return;
    }

    try {
      setIsEmailingAndShortlisting(true);
      setIsShortlisting(true);
      setIsUpdatingStatus(true);

      let response: any;

      if (pendingStatusType === 'email') {
        // Retry email notification with skip flag
        const statusToNotify = candidate.status;
        const dateSensitiveInfo = {
          message: pendingStatusUpdate.message,
          skipCulturalFit: true,
        };

        response = await updateCandidateStatus(
          candidate.id,
          statusToNotify,
          jobId,
          JSON.stringify(dateSensitiveInfo)
        );

        if (response) {
          showToast({
            message: 'Email notification sent successfully',
            isSuccess: true,
          });
        }
      } else if (pendingStatusType === 'shortlist') {
        // Retry shortlist with skip flag
        const newStatus = isShortlisted ? CandidateStatus.NEW : CandidateStatus.SHORTLISTED;
        const notesWithSkip = JSON.stringify({
          message: pendingStatusUpdate.message,
          skipCulturalFit: true,
        });

        response = await updateCandidateStatus(candidate.id, newStatus, jobId, notesWithSkip);

        if (response) {
          showToast({
            message: isShortlisted ? 'Removed from shortlist' : 'Added to shortlist',
            isSuccess: true,
          });
        }
      } else if (pendingStatusType === 'status' && statusToChange) {
        // Retry status change with skip flag
        const dateSensitiveInfo = {
          ...(pendingStatusUpdate.date && { date: pendingStatusUpdate.date }),
          ...(pendingStatusUpdate.meetingLink && { meetingLink: pendingStatusUpdate.meetingLink }),
          ...(pendingStatusUpdate.startDate && { startDate: pendingStatusUpdate.startDate }),
          ...(pendingStatusUpdate.onboardingLink && {
            onboardingLink: pendingStatusUpdate.onboardingLink,
          }),
          message: pendingStatusUpdate.message,
          skipCulturalFit: true,
        };

        response = await updateCandidateStatus(
          candidate.id,
          statusToChange,
          jobId,
          JSON.stringify(dateSensitiveInfo)
        );

        if (response) {
          showToast({
            message: `Status updated to ${statusToChange}`,
            isSuccess: true,
          });
        }
      }

      if (response) {
        onSelectCandidate();
        onStatusUpdate?.();
      }

      // Clear pending status update
      setPendingStatusUpdate(null);
      setPendingStatusType(null);
      setStatusToChange(null);
    } catch (error) {
      console.error('Error updating status after skip:', error);
      showToast({
        message: 'Failed to update status',
        isSuccess: false,
      });
    } finally {
      setIsEmailingAndShortlisting(false);
      setIsShortlisting(false);
      setIsUpdatingStatus(false);
    }
  };

  // Function to get truncated text (first sentence or 150 characters, whichever is shorter)
  const getTruncatedText = (text: string): string => {
    if (!text) return '';

    // First try to get the first sentence
    const sentenceMatch = text.match(/^(.*?[.!?])(?:\s|$)/);
    const firstSentence = sentenceMatch ? sentenceMatch[1] : null;

    // If we have a first sentence and it's reasonable length (under 200 chars), use it
    if (firstSentence && firstSentence.length <= 200) {
      return firstSentence;
    }

    // Otherwise, truncate at 150 characters and find the last complete word
    if (text.length <= 150) {
      return text;
    }

    const truncated = text.substring(0, 150);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    return lastSpaceIndex > 100 ? truncated.substring(0, lastSpaceIndex) : truncated;
  };

  // Check if summary needs truncation (either multiple sentences or long text)
  const needsTruncation = (text: string): boolean => {
    if (!text) return false;

    // Check if there are multiple sentences
    const sentenceEndCount = (text.match(/[.!?](?:\s|$)/g) || []).length;
    if (sentenceEndCount > 1) return true;

    // Check if text is longer than 150 characters
    return text.length > 150;
  };

  return (
    <>
      <section
        className="rounded-xl p-3 sm:p-4 md:p-6 overflow-hidden relative"
        style={{
          border: '1px solid var(--card-border)',
          backgroundColor: 'var(--card-bg)',
        }}
      >
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-wrap items-center gap-2 xs:gap-3">
            <h2
              className="text-lg xs:text-xl font-semibold flex items-center tracking-tight"
              style={{ color: 'var(--foreground-color)' }}
            >
              Candidate Information
            </h2>
            {/* Scouted chip */}
            {isScoutedCandidate && (
              <div className="flex items-center gap-1 xs:gap-1.5 bg-gradient-to-r from-purple-600/90 to-indigo-700/90 backdrop-blur-sm px-2 py-0.5 xs:px-2.5 xs:py-1 rounded-full border border-purple-500/30 shadow-lg">
                <Search className="w-2.5 h-2.5 xs:w-3 xs:h-3 text-white" />
                <span className="text-xs font-medium text-white">Scouted</span>
              </div>
            )}
          </div>
        </div>

        <div className="">
          <div className="w-full">
            <BasicInfo
              fullName={candidate.fullName}
              jobTitle={candidate.jobTitle}
              location={candidate.location}
              email={candidate.email}
              phone={candidate.phone}
              yearsOfExperience={candidate.yearsOfExperience}
              experience={candidate.experience}
              evaluation={evaluation}
            />
          </div>

          <div className="grid grid-cols-1 *:grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mt-4">
            <ContactInfo
              contactMethod={candidate.contactMethod}
              preferredLocation={candidate.preferredLocation}
              isRemoteOnly={candidate.isRemoteOnly}
            />
            <SocialProfiles
              linkedinUrl={candidate.linkedinUrl}
              githubUrl={candidate.githubUrl}
              profileUrl={candidate.profileUrl}
            />
          </div>
        </div>
        {candidate.summary && (
          <div className="mt-4 sm:mt-6">
            <label className="text-sm" style={{ color: 'var(--foreground-color)', opacity: 0.7 }}>
              Summary
            </label>
            <p className="mt-1 text-sm sm:text-base" style={{ color: 'var(--foreground-color)' }}>
              {isReadMore ? candidate.summary : `${getTruncatedText(candidate.summary)}...`}
            </p>
            {needsTruncation(candidate.summary) && (
              <button
                type="button"
                className="text-xs sm:text-sm mt-2 flex items-center gap-1"
                style={{ color: 'var(--info-color)' }}
                onClick={() => setIsReadMore(!isReadMore)}
              >
                {isReadMore ? (
                  <>
                    <ChevronUp className="w-4 h-4" />
                    Read Less
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4" />
                    Read More
                  </>
                )}
              </button>
            )}
          </div>
        )}

        {/* Unlock Details Button - shown when email is locked */}
        {isEmailLocked && (
          <div className="mt-4 sm:mt-6 flex justify-center">
            <CreditButton
              actionType={CreditActionType.UNLOCK_CANDIDATE_DETAILS}
              onClick={handleUnlockDetails}
              variant="rank"
              className=""
              showCreditCost={true}
            >
              <Unlock className="w-4 h-4 mr-2" />
              Unlock Full Details
            </CreditButton>
          </div>
        )}

        {/* Video Intro Button - positioned at the bottom of the card */}
        <VideoIntroButton
          candidateId={candidate.id}
          candidateName={candidate.fullName}
          candidateEmail={candidate.email}
          jobId={jobId}
          jobTitle={candidate.jobTitle}
          videoIntroEmailSent={candidate.videoIntroEmailSent}
          emailCorrespondence={candidate.emailCorrespondence}
        />
      </section>

      {/* Status Timeline Modal - Full Screen Portal */}
      {isStatusTimelineOpen &&
        portalContainer &&
        ReactDOM.createPortal(
          <div className="fixed inset-0 z-[9999] flex items-center justify-center overflow-hidden">
            <div
              className="absolute inset-0 backdrop-blur-xl"
              style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)' }}
              onClick={() => setIsStatusTimelineOpen(false)}
            ></div>

            {/* Main Content - Centered */}
            <div className="relative z-10 flex items-center justify-center w-full p-4 sm:p-8 overflow-y-auto">
              <div
                className="flex flex-col rounded-3xl max-w-4xl w-full min-h-[600px] overflow-hidden relative"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
                  backdropFilter: 'blur(20px)',
                }}
              >
                {/* Close Button */}
                <button
                  type="button"
                  onClick={() => setIsStatusTimelineOpen(false)}
                  className="absolute top-4 right-4 z-20 p-2 rounded-full bg-black/20 hover:bg-black/40 transition-colors focus:outline-none"
                  aria-label="Close"
                >
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>

                {/* Full Width Header Image with Curved Edges */}
                <div className="w-full h-40 sm:h-48 md:h-56 relative overflow-hidden">
                  <Image
                    src="/images/narrow/expand-6.webp"
                    alt="Status Update"
                    fill
                    className="w-full h-full object-cover object-center"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
                  <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-purple-700/30 to-transparent"></div>
                </div>

                <div className="text-center mb-8 sm:mb-12 px-8 sm:px-12 pt-8 sm:pt-10">
                  <p className="text-lg sm:text-xl md:text-2xl mb-4 sm:mb-6 font-light tracking-wide text-white/90">
                    Select a status to update
                  </p>
                  <p className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 sm:mb-12 bg-gradient-to-r from-purple-300 via-pink-300 to-purple-400 bg-clip-text text-transparent leading-tight">
                    {candidate.fullName}'s application
                  </p>
                </div>

                <div className="w-full px-8 sm:px-12 mb-8 sm:mb-12">
                  <StatusTimeline
                    currentStatus={candidate.status}
                    onStatusClick={handleStatusClick}
                    size="lg"
                    showAllStatuses={false}
                  />
                </div>
              </div>
            </div>
          </div>,
          portalContainer
        )}

      {/* Status Update Modal - Full Screen Portal */}
      {isStatusModalOpen &&
        statusToChange &&
        portalContainer &&
        ReactDOM.createPortal(
          <StatusUpdateModalWithDate
            isOpen={isStatusModalOpen}
            onClose={() => setIsStatusModalOpen(false)}
            currentStatus={candidate.status}
            newStatus={statusToChange}
            onConfirm={handleStatusChange}
            isLoading={isUpdatingStatus}
            candidateName={candidate.fullName}
            jobTitle={candidate.jobTitle || 'Open Position'} // Ensure jobTitle is never null
            candidateEmail={candidate.email}
            companyName="Your Company" // Replace with actual company name if available
          />,
          portalContainer
        )}

      {/* Video Introduction Setup Modal */}
      <CulturalFitSetupModal
        isOpen={showCulturalFitSetupModal}
        onClose={() => {
          setShowCulturalFitSetupModal(false);
          setPendingStatusUpdate(null);
          setPendingStatusType(null);
          setStatusToChange(null);
        }}
        jobId={culturalFitJobId}
        message={culturalFitSetupMessage}
        onSkip={handleSkipCulturalFitSetup}
      />
    </>
  );
};
