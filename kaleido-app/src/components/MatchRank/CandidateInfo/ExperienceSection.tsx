import React, { useState } from 'react';

import { Building, ChevronLeft, ChevronRight, Calendar, Clock, Briefcase } from 'lucide-react';

import { StandardizedProfile } from '@/shared/types/profile.types';

type Experience = StandardizedProfile['experience'][number];

interface ExperienceSectionProps {
  experiences: Experience[];
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
}

export const ExperienceSection: React.FC<ExperienceSectionProps> = ({
  experiences,
  currentPage: propCurrentPage,
  totalPages: propTotalPages,
  onPageChange: propOnPageChange,
}) => {
  // Local pagination state
  const ITEMS_PER_PAGE = 5;
  const [localCurrentPage, setLocalCurrentPage] = useState(0);

  // Use prop values if provided, otherwise use local state
  const currentPage = propCurrentPage ?? localCurrentPage;
  const onPageChange = propOnPageChange ?? setLocalCurrentPage;

  // Function to format date from ISO string to human-readable format
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric',
    });
  };

  // Function to format duration from months to years and months
  const formatDuration = (duration: string | number | undefined) => {
    if (!duration) return '';

    const months = typeof duration === 'string' ? parseInt(duration, 10) : duration;
    if (isNaN(months)) return duration.toString();

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (years === 0) {
      return `${remainingMonths} ${remainingMonths === 1 ? 'month' : 'months'}`;
    } else if (remainingMonths === 0) {
      return `${years} ${years === 1 ? 'year' : 'years'}`;
    } else {
      return `${years} ${years === 1 ? 'year' : 'years'}, ${remainingMonths} ${remainingMonths === 1 ? 'month' : 'months'}`;
    }
  };

  // Get the most recent experience (first one in the array)
  const mostRecentExperience = experiences[0];
  const otherExperiences = experiences.slice(1);

  // Calculate total pages for local pagination
  const totalOtherExperiences = otherExperiences.length;
  const localTotalPages = Math.ceil(totalOtherExperiences / ITEMS_PER_PAGE);
  const totalPages = propTotalPages ?? localTotalPages;

  // Get paginated experiences
  const startIndex = currentPage * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedExperiences = otherExperiences.slice(startIndex, endIndex);

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Most Recent Experience - Featured Section */}
      {mostRecentExperience && (
        <div className="relative w-full">
          {/* Glassmorphic background */}
          <div className="absolute inset-0 rounded-2xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-purple-500/10"></div>
            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent"></div>
            <div className="absolute inset-0 backdrop-blur-xl"></div>
          </div>

          {/* Content */}
          <div className="relative p-8">
            <div className="flex items-start gap-4">
              {/* Icon */}
              <div className="flex-shrink-0 p-3 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg">
                <Briefcase className="w-8 h-8 text-purple-400" />
              </div>

              {/* Experience Details */}
              <div className="flex-1 space-y-3">
                <div>
                  <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Current Position
                  </h3>
                  <h2 className="text-2xl font-bold text-foreground">
                    {mostRecentExperience.title}
                  </h2>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-foreground/80">
                  <div className="flex items-center gap-2">
                    <Building className="w-4 h-4 text-purple-400" />
                    <span className="text-lg break-words">{mostRecentExperience.company}</span>
                  </div>
                  <div className="hidden sm:block w-px h-4 bg-white/20" />
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-pink-400" />
                    <span className="text-sm">
                      {formatDate(mostRecentExperience.startDate || '')} -{' '}
                      {mostRecentExperience.endDate
                        ? formatDate(mostRecentExperience.endDate)
                        : 'Present'}
                    </span>
                  </div>
                  <div className="hidden sm:block w-px h-4 bg-white/20" />
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-purple-400" />
                    <span className="text-sm">{formatDuration(mostRecentExperience.duration)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Previous Experience Section */}
      {otherExperiences.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-slate-900/5 to-gray-800/5">
              <Building className="w-5 h-5 text-white/70" />
            </div>
            <h3 className="text-lg font-semibold text-white">Previous Experience</h3>
            <span className="text-sm text-white/60">
              ({otherExperiences.length} {otherExperiences.length === 1 ? 'position' : 'positions'})
            </span>
          </div>

          <div className="grid gap-4">
            {paginatedExperiences.map((exp, index) => (
              <div
                key={`${exp.company}-${exp.title}-${index}`}
                className="relative rounded-lg p-4 sm:p-6 border border-slate-200/10 bg-gradient-to-br from-slate-900/5 to-gray-800/5 hover:from-slate-900/10 hover:to-gray-800/10 transition-all duration-200"
              >
                <div className="flex items-start gap-4">
                  {/* Company icon placeholder */}
                  <div className="flex-shrink-0 p-2 bg-white/5 rounded-lg">
                    <Building className="w-5 h-5 text-white/50" />
                  </div>

                  {/* Experience details */}
                  <div className="flex-1 space-y-2">
                    <h4 className="text-lg font-semibold text-white break-words">{exp.title}</h4>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-sm">
                      <span className="text-purple-400 font-medium break-words">{exp.company}</span>
                      <span className="hidden sm:inline text-white/40">•</span>
                      <span className="text-white/60">
                        {formatDate(exp.startDate || '')} -{' '}
                        {exp.endDate ? formatDate(exp.endDate) : 'Present'}
                      </span>
                      <span className="hidden sm:inline text-white/40">•</span>
                      <span className="text-white/60">{formatDuration(exp.duration)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-4 mt-6">
              <button
                onClick={() => onPageChange(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
                className="p-2 rounded-lg disabled:opacity-50 disabled:hover:bg-transparent transition-colors hover:bg-white/10 disabled:cursor-not-allowed"
                title="Previous page"
                aria-label="Previous page"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <span className="text-sm text-white/60">
                Page {currentPage + 1} of {totalPages}
              </span>
              <button
                onClick={() => onPageChange(Math.min(totalPages - 1, currentPage + 1))}
                disabled={currentPage === totalPages - 1}
                className="p-2 rounded-lg disabled:opacity-50 disabled:hover:bg-transparent transition-colors hover:bg-white/10 disabled:cursor-not-allowed"
                title="Next page"
                aria-label="Next page"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
