import { jest } from '@jest/globals';
import { render, screen, waitFor } from '@testing-library/react';

import { ICandidate } from '@/entities/interfaces';
import { MatchedCandidatesComposition } from './MatchedCandidatesComposition';

// Mock the stores
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobStateStore: {
    subscribe: jest.fn(() => jest.fn()), // Mock subscribe method that returns unsubscribe function
    getState: jest.fn(() => ({
      refreshJobData: jest.fn(),
      jobs: [],
    })),
  },
  useJobStore: () => ({
    currentJob: null,
    fetchCandidates: jest.fn(),
  }),
}));

// Mock store with controllable state
const mockSetSelectedCandidate = jest.fn();
const mockRefreshJobData = jest.fn();

const mockSelectedCandidate: ICandidate | null = null;
const mockSelectedJobId = 'test-job-id';

jest.mock('@/stores/matchrankDetailsStore', () => ({
  useMatchRankDetailsStore: () => ({
    selectedCandidate: mockSelectedCandidate,
    setSelectedCandidate: mockSetSelectedCandidate,
    refreshJobData: mockRefreshJobData,
    selectedJobId: mockSelectedJobId,
  }),
}));

jest.mock('@/hooks/useJobThresholds', () => ({
  useJobThresholds: ({ fallbackJob }: { fallbackJob?: any }) => ({
    topCandidateThreshold: fallbackJob?.topCandidateThreshold || 80,
    secondTierCandidateThreshold: fallbackJob?.secondTierCandidateThreshold || 60,
  }),
}));

// Mock the child components
jest.mock('../CandidateDetails', () => {
  return function MockCandidateDetails() {
    return <div data-testid="candidate-details">Candidate Details</div>;
  };
});

jest.mock('../MatchedCandidateList', () => ({
  MatchedCandidateList: function MockMatchedCandidateList({
    candidates,
    onSelectCandidate,
  }: {
    candidates: ICandidate[] | any;
    onSelectCandidate: (candidate: ICandidate) => void;
  }) {
    // Handle both array and grouped candidates
    const candidatesList = Array.isArray(candidates)
      ? candidates
      : [
          ...(candidates.shortlisted || []),
          ...(candidates.topTier || []),
          ...(candidates.secondTier || []),
          ...(candidates.others || []),
          ...(candidates.unranked || []),
        ];

    return (
      <div data-testid="candidate-list">
        {candidatesList.map((candidate: ICandidate) => (
          <div
            key={candidate.id}
            data-testid={`candidate-${candidate.id}`}
            onClick={() => onSelectCandidate(candidate)}
          >
            {candidate.fullName} - Tier: {candidate.tier} - Score:{' '}
            {candidate.evaluation?.matchScore}
          </div>
        ))}
      </div>
    );
  },
}));

describe('MatchedCandidatesComposition', () => {
  const mockCandidates: ICandidate[] = [
    {
      id: 'candidate-1',
      fullName: 'John Doe',
      jobTitle: 'Senior Developer',
      location: 'New York',
      skills: ['JavaScript', 'React'],
      status: 'ACTIVE',
      contacted: false,
      tier: 'TOP',
      evaluation: {
        matchScore: 85,
        lastEvaluatedAt: new Date(),
        criterionMatchedOn: ['skills', 'experience'],
      },
      currentCompany: 'Previous Company',
      yearsOfExperience: 5,
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    } as unknown as ICandidate,
    {
      id: 'candidate-2',
      fullName: 'Jane Smith',
      jobTitle: 'Frontend Developer',
      location: 'San Francisco',
      skills: ['React', 'TypeScript'],
      status: 'SHORTLISTED',
      contacted: true,
      tier: 'SECOND',
      evaluation: {
        matchScore: 65,
        lastEvaluatedAt: new Date(),
        criterionMatchedOn: ['skills'],
      },
      currentCompany: 'Another Company',
      yearsOfExperience: 3,
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    } as unknown as ICandidate,
    {
      id: 'candidate-3',
      fullName: 'Bob Johnson',
      jobTitle: 'Junior Developer',
      location: 'Austin',
      skills: ['JavaScript'],
      status: 'ACTIVE',
      contacted: false,
      tier: 'OTHER',
      evaluation: {
        matchScore: 45,
        lastEvaluatedAt: new Date(),
        criterionMatchedOn: ['skills'],
      },
      currentCompany: 'Startup Inc',
      yearsOfExperience: 1,
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    } as unknown as ICandidate,
  ];

  const defaultProps = {
    candidates: mockCandidates,
    currentPage: 1,
    onPageChange: jest.fn(),
    isAtsJob: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render candidates list with job threshold data', () => {
    render(<MatchedCandidatesComposition {...defaultProps} />);

    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
    expect(screen.getByTestId('candidate-candidate-1')).toBeInTheDocument();
    expect(screen.getByTestId('candidate-candidate-2')).toBeInTheDocument();
    expect(screen.getByTestId('candidate-candidate-3')).toBeInTheDocument();
  });

  it('should extract thresholds from candidate jobStats', () => {
    render(<MatchedCandidatesComposition {...defaultProps} />);

    // The useJobThresholds hook should receive fallbackJob with thresholds from first candidate
    expect(screen.getByText(/John Doe - Tier: TOP - Score: 85/)).toBeInTheDocument();
    expect(screen.getByText(/Jane Smith - Tier: SECOND - Score: 65/)).toBeInTheDocument();
    expect(screen.getByText(/Bob Johnson - Tier: OTHER - Score: 45/)).toBeInTheDocument();
  });

  it('should handle candidates with different tiers correctly', () => {
    render(<MatchedCandidatesComposition {...defaultProps} />);

    // Verify all tier types are displayed
    expect(screen.getByText(/Tier: TOP/)).toBeInTheDocument();
    expect(screen.getByText(/Tier: SECOND/)).toBeInTheDocument();
    expect(screen.getByText(/Tier: OTHER/)).toBeInTheDocument();
  });

  it('should handle empty candidates array', () => {
    const emptyProps = {
      ...defaultProps,
      candidates: [],
    };

    render(<MatchedCandidatesComposition {...emptyProps} />);

    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
    expect(screen.queryByTestId(/candidate-candidate-/)).not.toBeInTheDocument();
  });

  it('should handle candidates without jobStats gracefully', () => {
    const candidatesWithoutJobStats = mockCandidates.map(candidate => ({
      ...candidate,
      jobStats: undefined,
    }));

    const propsWithoutJobStats = {
      ...defaultProps,
      candidates: candidatesWithoutJobStats,
    };

    render(<MatchedCandidatesComposition {...propsWithoutJobStats} />);

    // Should still render candidates even without jobStats
    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
    expect(screen.getByTestId('candidate-candidate-1')).toBeInTheDocument();
  });

  it('should display placeholder when no candidate is selected', () => {
    render(<MatchedCandidatesComposition {...defaultProps} />);

    expect(screen.getByText('Select a candidate to view details')).toBeInTheDocument();
    expect(screen.queryByTestId('candidate-details')).not.toBeInTheDocument();
  });

  it('should handle ATS job flag correctly', () => {
    const atsProps = {
      ...defaultProps,
      isAtsJob: true,
    };

    render(<MatchedCandidatesComposition {...atsProps} />);

    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
  });

  it('should pass correct threshold values to useJobThresholds hook', () => {
    const candidatesWithCustomThresholds = mockCandidates.map(candidate => ({
      ...candidate,
      jobStats: {
        topCandidateThreshold: 90,
        secondTierCandidateThreshold: 70,
        jobTitle: 'Test Job Title',
        companyName: 'Test Company',
        department: 'Engineering',
      },
    }));

    const customProps = {
      ...defaultProps,
      candidates: candidatesWithCustomThresholds,
    };

    render(<MatchedCandidatesComposition {...customProps} />);

    // The component should still render correctly with custom thresholds
    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
    expect(screen.getByTestId('candidate-candidate-1')).toBeInTheDocument();
  });

  it('should handle pagination props correctly', () => {
    const onPageChangeMock = jest.fn();
    const paginationProps = {
      ...defaultProps,
      currentPage: 2,
      onPageChange: onPageChangeMock,
    };

    render(<MatchedCandidatesComposition {...paginationProps} />);

    expect(screen.getByTestId('candidate-list')).toBeInTheDocument();
    // The MatchedCandidateList component should receive the pagination props
  });

  it('should maintain responsive layout structure', () => {
    render(<MatchedCandidatesComposition {...defaultProps} />);

    // Check for responsive layout classes - look for the container div with the correct classes
    const container = screen.getByTestId('candidate-list').parentElement?.parentElement;
    expect(container).toHaveClass('w-full', 'md:w-[25%]');
  });

  describe('Candidate Selection Priority Tests', () => {
    // Create mock candidates for different tiers
    const createMockCandidate = (id: string, name: string, tier: string): ICandidate =>
      ({
        id,
        fullName: name,
        tier,
        evaluation: {
          matchScore: tier === 'TOP' ? 90 : tier === 'SECOND' ? 70 : tier === 'OTHER' ? 50 : 0,
          lastEvaluatedAt: new Date(),
          criterionMatchedOn: ['skills'],
        },
        status: 'ACTIVE',
        contacted: false,
      }) as unknown as ICandidate;

    const mockGroupedCandidates = {
      topTier: [
        createMockCandidate('top-1', 'Top Candidate 1', 'TOP'),
        createMockCandidate('top-2', 'Top Candidate 2', 'TOP'),
      ],
      secondTier: [
        createMockCandidate('second-1', 'Second Tier 1', 'SECOND'),
        createMockCandidate('second-2', 'Second Tier 2', 'SECOND'),
      ],
      others: [
        createMockCandidate('other-1', 'Other Candidate 1', 'OTHER'),
        createMockCandidate('other-2', 'Other Candidate 2', 'OTHER'),
      ],
      unranked: [
        createMockCandidate('unranked-1', 'Unranked Candidate 1', 'UNRANKED'),
        createMockCandidate('unranked-2', 'Unranked Candidate 2', 'UNRANKED'),
      ],
      shortlisted: [],
    };

    it('should prioritize topTier candidates first', async () => {
      const propsWithGrouped = {
        ...defaultProps,
        candidates: mockGroupedCandidates,
      };

      render(<MatchedCandidatesComposition {...propsWithGrouped} />);

      // Wait for auto-selection to occur
      await waitFor(() => {
        expect(mockSetSelectedCandidate).toHaveBeenCalledWith(mockGroupedCandidates.topTier[0]);
      });
    });

    it('should select secondTier when topTier is empty', async () => {
      const candidatesWithoutTopTier = {
        ...mockGroupedCandidates,
        topTier: [],
      };

      const propsWithGrouped = {
        ...defaultProps,
        candidates: candidatesWithoutTopTier,
      };

      render(<MatchedCandidatesComposition {...propsWithGrouped} />);

      await waitFor(() => {
        expect(mockSetSelectedCandidate).toHaveBeenCalledWith(
          candidatesWithoutTopTier.secondTier[0]
        );
      });
    });

    it('should select others when topTier and secondTier are empty', async () => {
      const candidatesWithoutTopAndSecond = {
        ...mockGroupedCandidates,
        topTier: [],
        secondTier: [],
      };

      const propsWithGrouped = {
        ...defaultProps,
        candidates: candidatesWithoutTopAndSecond,
      };

      render(<MatchedCandidatesComposition {...propsWithGrouped} />);

      await waitFor(() => {
        expect(mockSetSelectedCandidate).toHaveBeenCalledWith(
          candidatesWithoutTopAndSecond.others[0]
        );
      });
    });

    it('should select unranked when only unranked candidates exist', async () => {
      const candidatesOnlyUnranked = {
        topTier: [],
        secondTier: [],
        others: [],
        unranked: mockGroupedCandidates.unranked,
        shortlisted: [],
      };

      const propsWithGrouped = {
        ...defaultProps,
        candidates: candidatesOnlyUnranked,
      };

      render(<MatchedCandidatesComposition {...propsWithGrouped} />);

      await waitFor(() => {
        expect(mockSetSelectedCandidate).toHaveBeenCalledWith(candidatesOnlyUnranked.unranked[0]);
      });
    });

    it('should not auto-select when all arrays are empty', async () => {
      const emptyCandidates = {
        topTier: [],
        secondTier: [],
        others: [],
        unranked: [],
        shortlisted: [],
      };

      const propsWithGrouped = {
        ...defaultProps,
        candidates: emptyCandidates,
      };

      render(<MatchedCandidatesComposition {...propsWithGrouped} />);

      // Wait a bit to ensure no selection happens
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockSetSelectedCandidate).not.toHaveBeenCalled();
    });
  });
});
