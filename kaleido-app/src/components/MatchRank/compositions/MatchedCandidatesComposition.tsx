import { ICandidate } from '@/entities/interfaces';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';

import CandidateDetails from '../CandidateDetails';
import { MatchedCandidateList } from '../MatchedCandidateList';

interface GroupedCandidates {
  topTier: ICandidate[];
  secondTier: ICandidate[];
  others: ICandidate[];
  unranked: ICandidate[];
  shortlisted: ICandidate[];
}

interface MatchedCandidatesCompositionProps {
  candidates: ICandidate[] | GroupedCandidates;
  currentPage: number;
  onPageChange: (page: number) => void;
  isAtsJob?: boolean;
  jobId?: string;
  fetchCandidateById?: (jobId: string, candidateId: string) => Promise<ICandidate | null>;
}

// Helper function to group candidates by tier
const groupCandidatesByTier = (
  candidates: ICandidate[],
  topThreshold: number,
  secondThreshold: number
) => {
  const sortedCandidates = [...candidates].sort((a, b) => {
    const scoreA = a.evaluation?.matchScore ?? 0;
    const scoreB = b.evaluation?.matchScore ?? 0;
    return scoreB - scoreA;
  });

  const topThresholdDecimal = topThreshold / 100;
  const secondThresholdDecimal = secondThreshold / 100;

  return sortedCandidates.reduce(
    (acc: any, candidate) => {
      if (candidate.status === 'SHORTLISTED') {
        acc.shortlisted.push(candidate);
        return acc;
      }

      const matchScore = candidate.evaluation?.matchScore ?? 0;

      if (matchScore >= topThresholdDecimal) {
        acc.topTier.push(candidate);
      } else if (matchScore >= secondThresholdDecimal) {
        acc.secondTier.push(candidate);
      } else if (matchScore === 0 || !candidate.evaluation?.rank) {
        acc.unranked.push(candidate);
      } else {
        acc.others.push(candidate);
      }
      return acc;
    },
    { shortlisted: [], topTier: [], secondTier: [], others: [], unranked: [] }
  );
};

// Helper function to get the highest ranking candidate
const getHighestRankingCandidate = (groupedCandidates: GroupedCandidates): ICandidate | null => {
  if (groupedCandidates.topTier?.length > 0) {
    return groupedCandidates.topTier[0];
  }
  if (groupedCandidates.secondTier?.length > 0) {
    return groupedCandidates.secondTier[0];
  }
  if (groupedCandidates.others?.length > 0) {
    return groupedCandidates.others[0];
  }
  if (groupedCandidates.unranked?.length > 0) {
    return groupedCandidates.unranked[0];
  }
  return null;
};

const MatchedCandidatesCompositionComponent: React.FC<MatchedCandidatesCompositionProps> = ({
  candidates,
  currentPage,
  onPageChange,
  isAtsJob = false,
  jobId,
  fetchCandidateById,
}) => {
  const { selectedCandidate, setSelectedCandidate } = useMatchRankDetailsStore();

  // Use unified job store - single source of truth
  const { currentJob, fetchCandidates } = useJobStore();

  // Enhanced candidate selection that fetches full details with optimistic updates
  const handleCandidateSelect = useCallback(
    async (candidate: ICandidate) => {
      try {
        // Clear any existing selection first to force a refresh
        setSelectedCandidate(null);

        // Small delay to ensure the component unmounts
        await new Promise(resolve => setTimeout(resolve, 50));

        // Optimistic update - show selection immediately
        setSelectedCandidate(candidate);

        // Always try to fetch fresh data from the backend
        if (jobId && candidate.id) {
          // Use jobStore's fetchCandidateById if fetchCandidateById prop is not provided
          const fetchFunction = fetchCandidateById || useJobStore.getState().fetchCandidateById;

          const fullCandidate = await fetchFunction(jobId, candidate.id);
          if (fullCandidate) {
            // Update with full details when ready
            setSelectedCandidate(fullCandidate);
          }
        }
      } catch (error) {
        console.error('Error fetching candidate details:', error);
        // Keep the optimistic update even if full fetch fails
      }
    },
    [fetchCandidateById, jobId, setSelectedCandidate]
  );

  // Handle candidate status changes with store updates
  const handleCandidateStatusChange = useCallback(
    async (candidateId: string, newStatus: string) => {
      if (!jobId) return;

      try {
        // Optimistic update
        if (selectedCandidate && selectedCandidate.id === candidateId) {
          setSelectedCandidate({
            ...selectedCandidate,
            status: newStatus,
          });
        }

        // Update store and refresh data
        await fetchCandidates(jobId);
      } catch (error) {
        console.error('Error updating candidate status:', error);
        // Revert optimistic update if needed
        if (selectedCandidate && selectedCandidate.id === candidateId) {
          // Refresh the candidate data
          if (fetchCandidateById) {
            const refreshedCandidate = await fetchCandidateById(jobId, candidateId);
            if (refreshedCandidate) {
              setSelectedCandidate(refreshedCandidate);
            }
          }
        }
      }
    },
    [jobId, selectedCandidate, setSelectedCandidate, fetchCandidates, fetchCandidateById]
  );

  // Get job thresholds from unified store
  const topCandidateThreshold = currentJob?.topCandidateThreshold ?? 0;
  const secondTierCandidateThreshold = currentJob?.secondTierCandidateThreshold ?? 0;

  // Determine if candidates is already grouped or needs grouping
  const isGroupedData = candidates && typeof candidates === 'object' && 'topTier' in candidates;

  // Convert candidates to array format if needed
  const candidatesArray = useMemo(() => {
    if (!candidates) return [];

    if (isGroupedData) {
      const grouped = candidates as GroupedCandidates;
      return [
        ...grouped.shortlisted,
        ...grouped.topTier,
        ...grouped.secondTier,
        ...grouped.others,
        ...(grouped.unranked || []),
      ];
    }

    return candidates as ICandidate[];
  }, [candidates, isGroupedData]);

  // Get grouped candidates data with memoization
  const groupedCandidates = useMemo(() => {
    if (!candidates) {
      return { shortlisted: [], topTier: [], secondTier: [], others: [], unranked: [] };
    }

    return isGroupedData
      ? (candidates as GroupedCandidates)
      : groupCandidatesByTier(candidatesArray, topCandidateThreshold, secondTierCandidateThreshold);
  }, [
    candidates,
    isGroupedData,
    candidatesArray,
    topCandidateThreshold,
    secondTierCandidateThreshold,
  ]);

  // Calculate total candidates count with memoization
  const totalCandidatesCount = useMemo(() => {
    if (!candidates) return 0;

    return isGroupedData
      ? groupedCandidates.shortlisted.length +
          groupedCandidates.topTier.length +
          groupedCandidates.secondTier.length +
          groupedCandidates.others.length +
          (groupedCandidates.unranked?.length || 0)
      : candidatesArray.length;
  }, [candidates, isGroupedData, groupedCandidates, candidatesArray.length]);

  // Use ref to track auto-selection to prevent infinite loops
  const autoSelectedRef = useRef(false);

  // Effect to auto-select the highest ranking candidate when the component mounts or candidates change
  useEffect(() => {
    if (totalCandidatesCount > 0) {
      if (!selectedCandidate && !autoSelectedRef.current) {
        const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
        if (highestRankingCandidate) {
          autoSelectedRef.current = true;
          setSelectedCandidate(highestRankingCandidate);
        }
      } else if (selectedCandidate) {
        const candidateExists = candidatesArray.some(c => c.id === selectedCandidate.id);
        if (!candidateExists) {
          const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
          if (highestRankingCandidate) {
            setSelectedCandidate(highestRankingCandidate);
          } else {
            setSelectedCandidate(null);
          }
        }
        autoSelectedRef.current = false;
      }
    } else if (selectedCandidate) {
      setSelectedCandidate(null);
      autoSelectedRef.current = false;
    }
  }, [
    totalCandidatesCount,
    selectedCandidate?.id,
    groupedCandidates,
    candidatesArray,
    setSelectedCandidate,
  ]);

  return (
    <div className="flex flex-col md:flex-row w-full h-full overflow-hidden bg-background text-foreground">
      {/* List Panel - Full width on mobile, 25% on desktop */}
      <div className="w-full md:w-[25%] h-[40vh] md:h-full md:border-r border-gray-300/10 bg-background/95 dark:bg-background/95 flex flex-col">
        <div className="flex-1 overflow-y-auto">
          <MatchedCandidateList
            candidates={groupedCandidates}
            currentPage={currentPage}
            onPageChange={onPageChange}
            onSelectCandidate={handleCandidateSelect}
            selectedCandidateId={selectedCandidate?.id}
            jobId={jobId}
          />
        </div>
      </div>

      {/* Details Panel - Full width on mobile, 75% on desktop */}
      <div className="w-full md:w-[75%] h-[60vh] md:h-full overflow-hidden bg-background text-foreground flex flex-col">
        {selectedCandidate ? (
          <div className="flex-1 overflow-y-auto">
            <CandidateDetails
              key={`candidate-details-${selectedCandidate.id}`}
              candidate={selectedCandidate as any}
              onClose={() => setSelectedCandidate(null)}
              isMatchedView={true}
              isAtsCandidate={isAtsJob}
              topCandidateThreshold={topCandidateThreshold}
              secondTierCandidateThreshold={secondTierCandidateThreshold}
              jobId={jobId}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-foreground">
            <p>Select a candidate to view details</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Memoized component to prevent unnecessary re-renders
export const MatchedCandidatesComposition = React.memo(
  MatchedCandidatesCompositionComponent,
  (prevProps, nextProps) => {
    // Only re-render if these critical props change
    return (
      prevProps.currentPage === nextProps.currentPage &&
      prevProps.isAtsJob === nextProps.isAtsJob &&
      prevProps.jobId === nextProps.jobId &&
      // Deep comparison for candidates would be expensive, so we'll let React handle it
      JSON.stringify(prevProps.candidates) === JSON.stringify(nextProps.candidates)
    );
  }
);
