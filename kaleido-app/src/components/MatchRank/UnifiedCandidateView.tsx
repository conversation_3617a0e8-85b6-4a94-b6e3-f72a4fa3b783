'use client';

import { NavigationButton } from '@/components/common/NavigationButton';
import { ICandidate } from '@/entities/interfaces';
import { motion } from 'framer-motion';
import {
  BarChart3,
  CheckCircle2,
  Clock,
  FileText,
  MapPin,
  Settings,
  Target,
  User,
  Users,
  Video,
  Menu,
  X,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import AnimatedScoreCard from './AnimatedScoreCard';
import { ExperienceSection } from './CandidateInfo/ExperienceSection';
import { ApplicationStatusTab } from './CandidateInfo/tabs/ApplicationStatusTab';
import { DetailedScoringTab } from './CandidateInfo/tabs/DetailedScoringTab';
import { LocationTab } from './CandidateInfo/tabs/LocationTab';
import { OverviewTab } from './CandidateInfo/tabs/OverviewTab';
import { OverviewTabContent } from './CandidateInfo/tabs/OverviewTabContent';
import { ProfileTab } from './CandidateInfo/tabs/ProfileTab';
import { RecommendationTab } from './CandidateInfo/tabs/RecommendationTab';
import { SharedSections } from './CandidateInfo/tabs/SharedSections';
import { TeamFitTab } from './CandidateInfo/tabs/TeamFitTab';
import { VideoIntroTab } from './CandidateInfo/tabs/VideoIntroTab';

interface UnifiedCandidateViewProps {
  candidate: ICandidate;
  jobId: string;
  jobTitle?: string;
  onCandidateSelect?: (candidate: ICandidate) => void;
  onStatusUpdate?: () => void;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
  stats?: {
    totalCandidates: number;
    shortlistedCount: number;
  };
}

type TabType =
  | 'overview'
  | 'status'
  | 'match-analysis'
  | 'experience'
  | 'match-reasoning'
  | 'video-intro'
  | 'profile';

export const UnifiedCandidateView: React.FC<UnifiedCandidateViewProps> = ({
  candidate,
  jobId,
  jobTitle,
  onCandidateSelect,
  onStatusUpdate,
  topCandidateThreshold = 80,
  secondTierCandidateThreshold = 60,
  stats,
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [detailedAnalysisSubTab, setDetailedAnalysisSubTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const tabs = [
    { id: 'overview' as const, label: 'Overview', icon: FileText },
    { id: 'match-analysis' as const, label: 'Match Analysis', icon: BarChart3 },
    { id: 'video-intro' as const, label: 'Video Intro', icon: Video },
    { id: 'profile' as const, label: 'Profile', icon: User },
    { id: 'status' as const, label: 'Application Status', icon: Clock },
    // { id: 'experience' as const, label: 'Experience', icon: Briefcase },
    // { id: 'match-reasoning' as const, label: 'Match Reasoning', icon: Sparkles },
  ];

  // Handle tab parameter from URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      const tabParam = searchParams.get('tab') as TabType;
      if (tabParam && tabs.some(tab => tab.id === tabParam)) {
        setActiveTab(tabParam);
      }

      // Also check for sub-tab parameter for match-analysis
      const subTabParam = searchParams.get('subtab');
      if (subTabParam) {
        setDetailedAnalysisSubTab(subTabParam);
      }
    }
  }, []);

  // Update URL when tab changes
  const handleTabChange = (tabId: TabType) => {
    setActiveTab(tabId);
    // Update URL without navigation
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('tab', tabId);
    // Clear subtab if not on match-analysis
    if (tabId !== 'match-analysis') {
      searchParams.delete('subtab');
    }
    const newUrl = `${currentPath}?${searchParams.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };

  // Update URL when sub-tab changes
  const handleSubTabChange = (subTabId: string) => {
    setDetailedAnalysisSubTab(subTabId);
    // Update URL without navigation
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('subtab', subTabId);
    const newUrl = `${currentPath}?${searchParams.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };

  const handleEditClick = () => {
    const returnTo = `/jobs/${jobId}/candidates`;
    router.push(`/jobs/${jobId}/edit?returnTo=${encodeURIComponent(returnTo)}`);
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="fixed top-4 right-4 z-50 md:hidden p-2 rounded-lg bg-black/10 backdrop-blur-xl border border-gray-700/50"
      >
        <Menu className="w-5 h-5 text-gray-300" />
      </button>

      {/* Header with Tabs */}
      <div className="flex-none border-b border-gray-300/10 bg-background/95 h-[80px] relative">
        <div className="px-4 sm:px-6 h-full flex items-center justify-between relative">
          {/* Desktop Tab Navigation */}
          <div className="hidden md:flex overflow-x-auto h-full">
            {tabs.map((tab, index) => (
              <React.Fragment key={tab.id}>
                {index > 0 && <div className="w-px bg-gray-300/10 self-center h-6" />}
                <button
                  onClick={() => handleTabChange(tab.id)}
                  className={`
                  relative px-4 h-full flex items-center gap-2 text-sm font-medium transition-all whitespace-nowrap
                  ${
                    activeTab === tab.id
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                  }
                `}
                >
                  {/* Gradient background for active tab */}
                  {activeTab === tab.id && (
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/10 to-pink-600/20" />
                  )}

                  {/* Content */}
                  <span className="relative z-10 flex items-center gap-2">
                    <tab.icon className="w-4 h-4" />
                    {tab.label}
                  </span>

                  {/* Bottom border for active tab */}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
                  )}
                </button>
              </React.Fragment>
            ))}
          </div>

          {/* Mobile Active Tab Display - Centered */}
          <div className="md:hidden absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2">
            <div className="flex items-center gap-2 text-sm font-medium text-primary">
              {(() => {
                const currentTab = tabs.find(tab => tab.id === activeTab);
                if (currentTab) {
                  const IconComponent = currentTab.icon;
                  return <IconComponent className="w-4 h-4" />;
                }
                return null;
              })()}
              <span>{tabs.find(tab => tab.id === activeTab)?.label}</span>
            </div>
          </div>

          {/* Right side - Action Buttons */}
          <div className="flex items-center gap-2 sm:gap-4 h-full">
            {/* Vertical Separator before stats */}
            <div className="h-8 w-px bg-gray-300/20" />

            <NavigationButton icon={Settings} onClick={handleEditClick}>
              <span className="hidden sm:inline">Edit Criteria</span>
              <span className="sm:hidden">Edit</span>
            </NavigationButton>
          </div>
        </div>
      </div>

      {/* Mobile Tab Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div
            className="absolute inset-0 bg-black/50"
            onClick={() => setIsMobileMenuOpen(false)}
          />
          <div className="absolute top-0 right-0 h-full w-80 max-w-[80vw] bg-black/10 backdrop-blur-xl border-l border-gray-700 p-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">Navigation</h3>
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <X className="w-5 h-5 text-gray-300" />
              </button>
            </div>
            <div className="space-y-2">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    handleTabChange(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`
                    w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center gap-3
                    ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 text-white'
                        : 'text-gray-300 hover:bg-gray-800/50 hover:text-white border border-transparent'
                    }
                  `}
                >
                  <tab.icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="p-4 sm:p-6 lg:p-8"
        >
          {activeTab === 'overview' && (
            <OverviewTabContent
              candidate={candidate}
              jobId={jobId}
              jobTitle={jobTitle}
              onCandidateSelect={onCandidateSelect}
              onStatusUpdate={onStatusUpdate}
              topCandidateThreshold={topCandidateThreshold}
              secondTierCandidateThreshold={secondTierCandidateThreshold}
            />
          )}

          {activeTab === 'status' && (
            <ApplicationStatusTab
              candidate={candidate}
              jobId={jobId}
              companyName="Your Company"
              jobTitle={jobTitle || 'Open Position'}
              onStatusUpdate={onStatusUpdate}
            />
          )}

          {activeTab === 'match-analysis' && candidate.evaluation && (
            <div className="w-full max-w-7xl mx-auto space-y-6">
              {/* Sub-tabs for detailed analysis - responsive */}
              <div className="mb-6">
                {/* Desktop sub-tabs */}
                <div className="hidden md:flex items-center gap-1 p-1 bg-muted/20 rounded-xl w-fit">
                  {[
                    { id: 'overview', label: 'Overview', icon: BarChart3 },
                    { id: 'skills-experience', label: 'Skills', icon: Target },
                    { id: 'location', label: 'Location', icon: MapPin },
                    { id: 'team-fit', label: 'Team Fit', icon: Users },
                    { id: 'recommendation', label: 'Final Summary', icon: CheckCircle2 },
                  ].map(subTab => (
                    <button
                      key={subTab.id}
                      onClick={() => handleSubTabChange(subTab.id)}
                      className={`
                        relative flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all
                        ${
                          detailedAnalysisSubTab === subTab.id
                            ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                            : 'text-muted-foreground hover:text-foreground hover:bg-muted/30'
                        }
                      `}
                    >
                      <subTab.icon className="w-4 h-4" />
                      <span>{subTab.label}</span>
                    </button>
                  ))}
                </div>

                {/* Mobile sub-tabs dropdown */}
                <div className="md:hidden">
                  <select
                    value={detailedAnalysisSubTab}
                    onChange={e => handleSubTabChange(e.target.value)}
                    className="w-full p-3 rounded-lg bg-muted/20 border border-gray-300/20 text-foreground focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    {[
                      { id: 'overview', label: 'Overview' },
                      { id: 'skills-experience', label: 'Skills & Experience' },
                      { id: 'location', label: 'Location' },
                      { id: 'team-fit', label: 'Team Fit' },
                      { id: 'recommendation', label: 'Final Summary' },
                    ].map(subTab => (
                      <option key={subTab.id} value={subTab.id}>
                        {subTab.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Match Reasoning as a full-width intro when on overview tab */}
              {detailedAnalysisSubTab === 'overview' &&
                candidate.evaluation.yourReasoningForScoring && (
                  <div className="relative w-full">
                    {/* Glassmorphic background */}
                    <div className="absolute inset-0 rounded-2xl overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-purple-500/10"></div>
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent"></div>
                      <div className="absolute inset-0 backdrop-blur-xl"></div>
                    </div>

                    {/* Content */}
                    <div className="relative p-8">
                      <div className="flex items-start gap-4">
                        {/* Large quote icon */}
                        <div className="flex-shrink-0 text-purple-400/30">
                          <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                          </svg>
                        </div>

                        {/* Reasoning text with better typography */}
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-3 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                            Match Reasoning
                          </h3>
                          <p className="text-lg leading-relaxed text-foreground/90 font-light italic">
                            {candidate.evaluation.yourReasoningForScoring}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

              {/* Grid layout for content - responsive */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content */}
                <div
                  className={`${detailedAnalysisSubTab === 'overview' ? 'lg:col-span-3' : 'lg:col-span-2'}`}
                >
                  {detailedAnalysisSubTab === 'overview' && (
                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                      <div className="xl:col-span-2">
                        <OverviewTab
                          matchScore={candidate.evaluation.matchScore}
                          yourReasoningForScoring={''} // We're showing it above, so don't show it again
                          specificCriteriaMatched={
                            candidate.evaluation.detailedScoreAnalysis?.specificCriteriaMatched || {
                              skillsMatch: 0,
                              experienceRelevance: 0,
                              locationAndAvailability: 0,
                            }
                          }
                          hiringRecommendation={''}
                          jobId={jobId}
                        />
                      </div>
                      <div className="xl:col-span-1">
                        <div className="sticky top-6">
                          <AnimatedScoreCard
                            candidate={candidate as any}
                            matchScore={candidate.evaluation.matchScore}
                            rank={candidate.evaluation.rank}
                            topCandidateThreshold={topCandidateThreshold}
                            secondTierCandidateThreshold={secondTierCandidateThreshold}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {detailedAnalysisSubTab === 'skills-experience' && (
                    <div className="space-y-6">
                      <DetailedScoringTab
                        detailedReasoning={
                          candidate.evaluation.detailedScoreAnalysis?.detailedReasoning || {
                            skillsMatch: 0,
                            experienceRelevance: {
                              industryExpertise: 0,
                              yearsOfRelevantExperience: 0,
                            },
                            locationAndAvailability: 0,
                          }
                        }
                        matchScore={candidate.evaluation.matchScore}
                        specificCriteriaMatched={
                          candidate.evaluation.detailedScoreAnalysis?.specificCriteriaMatched || {
                            skillsMatch: 0,
                            experienceRelevance: 0,
                            locationAndAvailability: 0,
                          }
                        }
                        toSentenceCase={(str: string) =>
                          str
                            .replace(/([A-Z])/g, ' $1')
                            .trim()
                            .toLowerCase()
                            .replace(/^./, s => s.toUpperCase())
                        }
                      />

                      <SharedSections
                        areasOfStrength={
                          candidate.evaluation.detailedScoreAnalysis?.areasOfStrength || []
                        }
                        areasForImprovement={
                          candidate.evaluation.detailedScoreAnalysis?.areasForImprovement || []
                        }
                        missingCriticalRequirements={
                          candidate.evaluation.detailedScoreAnalysis?.missingCriticalRequirements ||
                          []
                        }
                        detailedReasoning={
                          candidate.evaluation.detailedScoreAnalysis?.detailedReasoning || {}
                        }
                        teamFitAssessment={{
                          skillComplementarity: 0,
                          uniquePerspectives: [],
                          collaborationPotential: 0,
                          diversityContribution: 'Not assessed',
                          leadershipFit: 'Not assessed',
                          teamDynamicsImpact: 'Not assessed',
                        }}
                        interviewFocusAreas={[]}
                      />
                    </div>
                  )}

                  {detailedAnalysisSubTab === 'location' && (
                    <LocationTab
                      detailedReasoning={
                        candidate.evaluation.detailedScoreAnalysis?.detailedReasoning || {}
                      }
                    />
                  )}

                  {detailedAnalysisSubTab === 'team-fit' && (
                    <TeamFitTab
                      teamFitAssessment={{
                        skillComplementarity: 0,
                        uniquePerspectives: [],
                        collaborationPotential: 0,
                        diversityContribution: 'Not assessed',
                        leadershipFit: 'Not assessed',
                        teamDynamicsImpact: 'Not assessed',
                      }}
                    />
                  )}

                  {detailedAnalysisSubTab === 'recommendation' && (
                    <RecommendationTab
                      evaluation={candidate.evaluation}
                      interviewFocusAreas={
                        candidate.evaluation?.detailedScoreAnalysis?.interviewFocusAreas || []
                      }
                    />
                  )}
                </div>

                {/* Right Column - Match Score Card (only show for non-overview tabs on desktop) */}
                {detailedAnalysisSubTab !== 'overview' && (
                  <div className="hidden lg:block lg:col-span-1">
                    <div className="sticky top-6">
                      <AnimatedScoreCard
                        candidate={candidate as any}
                        matchScore={candidate.evaluation.matchScore}
                        rank={candidate.evaluation.rank}
                        topCandidateThreshold={topCandidateThreshold}
                        secondTierCandidateThreshold={secondTierCandidateThreshold}
                      />
                    </div>
                  </div>
                )}

                {/* Mobile Score Card for non-overview tabs */}
                {detailedAnalysisSubTab !== 'overview' && (
                  <div className="lg:hidden col-span-1 order-first">
                    <AnimatedScoreCard
                      candidate={candidate as any}
                      matchScore={candidate.evaluation.matchScore}
                      rank={candidate.evaluation.rank}
                      topCandidateThreshold={topCandidateThreshold}
                      secondTierCandidateThreshold={secondTierCandidateThreshold}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'experience' && (
            <ExperienceSection experiences={candidate.experience || []} />
          )}

          {activeTab === 'match-reasoning' && (
            <div className="w-full max-w-4xl mx-auto">
              {candidate.evaluation?.yourReasoningForScoring ? (
                <div className="rounded-xl p-4 sm:p-6 border bg-card border-border">
                  <h3 className="text-lg font-semibold mb-4">Match Reasoning</h3>
                  <p className="text-muted-foreground whitespace-pre-line break-words">
                    {candidate.evaluation.yourReasoningForScoring}
                  </p>
                </div>
              ) : (
                <div className="rounded-xl p-4 sm:p-6 border bg-card border-border text-center">
                  <p className="text-muted-foreground">No match reasoning available</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'video-intro' && <VideoIntroTab candidate={candidate} jobId={jobId} />}

          {activeTab === 'profile' && <ProfileTab candidate={candidate} />}
        </motion.div>
      </div>
    </div>
  );
};
