import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CandidateListItem } from '../CandidateListItem';

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  // eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text
  default: (props: any) => <img {...props} alt={props.alt || ''} />,
}));

describe('CandidateListItem', () => {
  const mockCandidate = {
    id: '1',
    fullName: '<PERSON>',
    name: '<PERSON>', // Some components use name instead of fullName
    profileImageUrl: 'https://example.com/profile.jpg',
    evaluation: {
      matchScore: 0.85, // 85%
    },
    status: 'NEW',
  };

  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic rendering', () => {
    it('should render candidate name', () => {
      render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should render match score as percentage', () => {
      render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      expect(screen.getByText('85%')).toBeInTheDocument();
    });

    it('should handle match score greater than 1', () => {
      const candidateWithHighScore = {
        ...mockCandidate,
        evaluation: {
          matchScore: 92, // Already a percentage
        },
      };

      render(
        <CandidateListItem
          candidate={candidateWithHighScore}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('92%')).toBeInTheDocument();
    });

    it('should handle missing evaluation data', () => {
      const candidateWithoutEvaluation = {
        ...mockCandidate,
        evaluation: undefined,
      };

      render(
        <CandidateListItem
          candidate={candidateWithoutEvaluation}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('0%')).toBeInTheDocument();
    });
  });

  describe('Profile image handling', () => {
    it('should render profile image when available', () => {
      render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      const img = screen.getByAltText('John Doe');
      expect(img).toHaveAttribute('src', 'https://example.com/profile.jpg');
      expect(img).toHaveAttribute('width', '28');
      expect(img).toHaveAttribute('height', '28');
    });

    it('should render initials when no profile image', () => {
      const candidateWithoutImage = {
        ...mockCandidate,
        profileImageUrl: null,
      };

      render(
        <CandidateListItem
          candidate={candidateWithoutImage}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('should handle single name for initials', () => {
      const candidateWithSingleName = {
        ...mockCandidate,
        fullName: 'Madonna',
        profileImageUrl: null,
      };

      render(
        <CandidateListItem
          candidate={candidateWithSingleName}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('M')).toBeInTheDocument();
    });

    it('should handle multiple names for initials', () => {
      const candidateWithMultipleNames = {
        ...mockCandidate,
        fullName: 'John Paul Jones',
        profileImageUrl: null,
      };

      render(
        <CandidateListItem
          candidate={candidateWithMultipleNames}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('JP')).toBeInTheDocument();
    });

    it('should apply consistent background color based on name', () => {
      const candidateWithoutImage = {
        ...mockCandidate,
        profileImageUrl: null,
      };

      const { container } = render(
        <CandidateListItem
          candidate={candidateWithoutImage}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      const avatarDiv = container.querySelector('div[class*="bg-"][class*="500"]');
      expect(avatarDiv).toBeInTheDocument();
      // The color should be consistent for the same name
      expect(avatarDiv?.className).toMatch(
        /bg-(blue|purple|green|yellow|pink|indigo|red|teal)-500/
      );
    });
  });

  describe('Selection state', () => {
    it('should apply selected styles when selected', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={true} onSelect={mockOnSelect} />
      );

      // Check for gradient backgrounds
      expect(container.querySelector('.from-purple-600\\/5')).toBeInTheDocument();
      expect(container.querySelector('.border-purple-500\\/10')).toBeInTheDocument();
    });

    it('should apply hover styles when not selected', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      const listItem = container.firstChild as HTMLElement;
      expect(listItem.className).toContain('hover:bg-white/[0.02]');
    });

    it('should use different text colors based on selection', () => {
      const { rerender } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      // Not selected - gray text
      expect(screen.getByText('John Doe')).toHaveClass('text-gray-400');
      expect(screen.getByText('85%')).toHaveClass('text-gray-500');

      // Selected - different colors
      rerender(
        <CandidateListItem candidate={mockCandidate} isSelected={true} onSelect={mockOnSelect} />
      );

      expect(screen.getByText('John Doe')).toHaveClass('text-gray-100');
      expect(screen.getByText('85%')).toHaveClass('text-purple-400');
    });
  });

  describe('Shortlisted status', () => {
    it('should show star icon when shortlisted prop is true', () => {
      render(
        <CandidateListItem
          candidate={mockCandidate}
          isSelected={false}
          isShortlisted={true}
          onSelect={mockOnSelect}
        />
      );

      const star = document.querySelector('.text-yellow-500');
      expect(star).toBeInTheDocument();
      expect(star).toHaveClass('fill-yellow-500');
    });

    it('should show star icon when candidate status is SHORTLISTED', () => {
      const shortlistedCandidate = {
        ...mockCandidate,
        status: 'SHORTLISTED',
      };

      render(
        <CandidateListItem
          candidate={shortlistedCandidate}
          isSelected={false}
          isShortlisted={false}
          onSelect={mockOnSelect}
        />
      );

      const star = document.querySelector('.text-yellow-500');
      expect(star).toBeInTheDocument();
    });

    it('should not show star icon when not shortlisted', () => {
      render(
        <CandidateListItem
          candidate={mockCandidate}
          isSelected={false}
          isShortlisted={false}
          onSelect={mockOnSelect}
        />
      );

      const star = document.querySelector('.text-yellow-500');
      expect(star).not.toBeInTheDocument();
    });
  });

  describe('Interaction', () => {
    it('should call onSelect when clicked', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      const clickableDiv = container.firstChild as HTMLElement;
      fireEvent.click(clickableDiv);

      expect(mockOnSelect).toHaveBeenCalledWith(mockCandidate);
    });

    it('should not crash when onSelect is not provided', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} />
      );

      // Should not throw when clicking
      const clickableDiv = container.firstChild as HTMLElement;
      expect(() => fireEvent.click(clickableDiv)).not.toThrow();
    });
  });

  describe('Edge cases', () => {
    it('should handle empty name gracefully', () => {
      const candidateWithEmptyName = {
        ...mockCandidate,
        fullName: '',
        profileImageUrl: null,
      };

      const { container } = render(
        <CandidateListItem
          candidate={candidateWithEmptyName}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      // Should render without crashing
      expect(container.firstChild).toBeInTheDocument();
      // Check for the score which should still be rendered
      expect(screen.getByText('85%')).toBeInTheDocument();
    });

    it('should handle very long names with truncation', () => {
      const candidateWithLongName = {
        ...mockCandidate,
        fullName: 'This is a very long name that should be truncated in the UI',
      };

      render(
        <CandidateListItem
          candidate={candidateWithLongName}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      const nameElement = screen.getByText(candidateWithLongName.fullName);
      expect(nameElement).toHaveClass('truncate');
    });

    it('should handle decimal match scores correctly', () => {
      const candidateWithDecimalScore = {
        ...mockCandidate,
        evaluation: {
          matchScore: 0.856, // 85.6%
        },
      };

      render(
        <CandidateListItem
          candidate={candidateWithDecimalScore}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      // Should round to 86%
      expect(screen.getByText('86%')).toBeInTheDocument();
    });

    it('should handle null evaluation gracefully', () => {
      const candidateWithNullEvaluation = {
        ...mockCandidate,
        evaluation: null,
      };

      render(
        <CandidateListItem
          candidate={candidateWithNullEvaluation}
          isSelected={false}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('0%')).toBeInTheDocument();
    });
  });

  describe('Responsive design', () => {
    it('should use appropriate sizes for elements', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      // Avatar size
      const avatar = container.querySelector('.w-7.h-7');
      expect(avatar).toBeInTheDocument();

      // Text sizes
      expect(screen.getByText('John Doe')).toHaveClass('text-sm');
      expect(screen.getByText('85%')).toHaveClass('text-xs');

      // Star icon size
      if (mockCandidate.status === 'SHORTLISTED') {
        const star = container.querySelector('.w-3.h-3');
        expect(star).toBeInTheDocument();
      }
    });

    it('should handle layout with flex properties', () => {
      const { container } = render(
        <CandidateListItem candidate={mockCandidate} isSelected={false} onSelect={mockOnSelect} />
      );

      // Main container should have flex layout
      const mainContainer = container.querySelector('.flex.items-center.gap-2\\.5');
      expect(mainContainer).toBeInTheDocument();

      // Name container should be flexible
      const nameContainer = container.querySelector('.flex-1.min-w-0');
      expect(nameContainer).toBeInTheDocument();

      // Score container should not shrink
      const scoreContainer = container.querySelector('.flex-shrink-0');
      expect(scoreContainer).toBeInTheDocument();
    });
  });
});
