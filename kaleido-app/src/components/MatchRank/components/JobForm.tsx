import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useJobsStore, useJobStateStore, useJobStore } from '@/stores/unifiedJobStore';
import { Book, Briefcase, Eye, FileText, User, Users } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useTheme } from '@/components/common/styles/themeHelpers';

import SaveChangesSlider from '@/components/common/SaveChangesSlider';
import FileUploader from '@/components/FileUploader';
import { showToast } from '@/components/Toaster';
import useIntercom from '@/hooks/useIntercom';
import { EventCategory } from '@/services/intercomEvents';
import StyledInput from '../../common/styledInputs/StyledInput';

interface JobFormProps {
  formData: {
    jobTitle: string;
    jobType: string;
    jobDescription: string;
    department: string;
    topCandidateThreshold: number;
    secondTierCandidateThreshold: number;
    requirements: string;
    status: string;
  };
  validationErrors: {
    topCandidateThreshold?: string;
    secondTierCandidateThreshold?: string;
  };
  jobId: string;
  onUploaderClose: () => void;
  onUploadComplete: (data: any) => void;
  onSaveComplete?: () => void; // Callback when save is completed successfully
  isProcessing: boolean;
  hasUnsavedChanges: boolean;
  isAtsJob?: boolean;
  onViewDetails?: () => void;
  fullJobData?: any; // Full job data including matchRankCost
  fullWidth?: boolean; // New prop to control layout
  useJobEditStore?: boolean; // Whether to use the new job edit store instead of MatchRank store
  onCancel?: () => void; // Optional cancel handler
}

// SaveButton component removed - using inline buttons in fixed footer

// Helper function to format job information
export const formatJobInformation = (jobData: any, isAtsJob?: boolean) => {
  if (!jobData) return '';

  // If we already have a jobDescription, use it as-is
  // if (jobData.jobDescription) {
  //   return jobData.jobDescription;
  // }

  // // If it's an ATS job and has finalDraft, use that
  // if (isAtsJob && jobData.finalDraft) {
  //   return jobData.finalDraft;
  // }

  // Otherwise, format from individual fields
  const sections = [
    // Job Overview Section
    'Job Overview',
    `\nJob Title: ${jobData.jobType || jobData.jobTitle || 'Not Specified'}`,
    `\nDepartment: ${jobData.department || 'Not Specified'}`,
    `\nExperience Level: ${jobData.experienceLevel || 'Not Specified'}`,
    jobData.experience ? `\nYears of Experience: ${jobData.experience}` : '',

    // Location and Type Section
    '\n\nLocation and Type',
    jobData.location?.length > 0 ? `\nLocation: ${jobData.location.join(', ')}` : '',
    jobData.typeOfJob ? `\nJob Type: ${jobData.typeOfJob}` : '',
    jobData.typeOfHiring ? `\nType of Hiring: ${jobData.typeOfHiring}` : '',

    // Education and Language Section
    jobData.education?.length > 0
      ? '\n\nEducation Requirements:\n' +
        jobData.education.map((edu: string) => `- ${edu}`).join('\n')
      : '',
    jobData.language?.length > 0
      ? '\n\nLanguage Requirements:\n' +
        jobData.language.map((lang: string) => `- ${lang}`).join('\n')
      : '',

    // Skills Section
    jobData.skills?.length > 0
      ? '\n\nRequired Technical Skills:\n' +
        jobData.skills.map((skill: string) => `- ${skill}`).join('\n')
      : '',
    jobData.softSkills?.length > 0
      ? '\n\nRequired Soft Skills:\n' +
        jobData.softSkills.map((skill: string) => `- ${skill}`).join('\n')
      : '',

    // Responsibilities Section
    jobData.jobResponsibilities?.length > 0
      ? '\n\nKey Responsibilities:\n' +
        jobData.jobResponsibilities.map((resp: string) => `- ${resp}`).join('\n')
      : '',

    // Requirements Section
    jobData.requirements?.length > 0
      ? '\n\nAdditional Requirements:\n' +
        (Array.isArray(jobData.requirements) ? jobData.requirements : [])
      : [],
  ];
  return sections.filter(Boolean).join('');
};

export const JobForm: React.FC<JobFormProps> = ({
  formData: initialFormData,
  validationErrors: initialValidationErrors,
  jobId,
  onUploaderClose,
  onUploadComplete,
  onSaveComplete,
  isProcessing,
  hasUnsavedChanges: initialHasUnsavedChanges,
  isAtsJob = false,
  onViewDetails,
  fullJobData,
  useJobEditStore: useJobEditStoreFlag = false,
  onCancel,
}) => {
  // Get the current theme
  const currentTheme = useTheme();
  const isDarkTheme = currentTheme === 'dark';

  // Use fullJobData as the selected job (the new way)
  const selectedJob = fullJobData;

  // Get Intercom tracking
  const { trackEvent } = useIntercom();

  // Use unified job store
  const jobStore = useJobStore();
  const { updateJob } = jobStore;

  // Use additional stores for compatibility with tests
  const { setHasFormChanges, setJobDescriptionChanged } = useMatchRankDetailsStore();
  const { updateJobMatchRankCriteria } = useJobsStore();
  const { updateJob: updateJobInStore } = useJobStateStore.getState();

  // Local state for form data and validation
  const [formData, setFormData] = useState(initialFormData);
  const [validationErrors, setValidationErrors] = useState(initialValidationErrors);
  const [hasUnsavedChanges, setLocalHasUnsavedChanges] = useState(initialHasUnsavedChanges);
  const [isSaving, setIsSaving] = useState(false);

  const isResumeUploadActive = jobStore.isResumeUploadActive;

  // Initialize form data when props change (but prevent infinite loops)
  const initialFormDataRef = useRef(initialFormData);
  const hasInitializedRef = useRef(false);
  const lastSaveTimeRef = useRef(0);

  useEffect(() => {
    // Only initialize once on mount
    if (!hasInitializedRef.current) {
      setFormData(initialFormData);
      setValidationErrors(initialValidationErrors);
      setLocalHasUnsavedChanges(initialHasUnsavedChanges);
      setHasFormChanges(initialHasUnsavedChanges);
      initialFormDataRef.current = initialFormData;
      hasInitializedRef.current = true;
      return;
    }

    // Check if props have actually changed by comparing with previous values
    const hasPropsChanged =
      JSON.stringify(initialFormDataRef.current) !== JSON.stringify(initialFormData);

    // Only update if props changed AND it's not immediately after a save (to prevent overwriting user edits)
    const timeSinceLastSave = Date.now() - lastSaveTimeRef.current;
    const isRecentSave = timeSinceLastSave < 2000; // 2 seconds

    if (hasPropsChanged && !isRecentSave) {
      setFormData(initialFormData);
      setValidationErrors(initialValidationErrors);
      setLocalHasUnsavedChanges(initialHasUnsavedChanges);
      setHasFormChanges(initialHasUnsavedChanges);
      initialFormDataRef.current = initialFormData;
    }
  }, [
    JSON.stringify(initialFormData),
    JSON.stringify(initialValidationErrors),
    initialHasUnsavedChanges,
    setHasFormChanges,
  ]);

  // Update form data when selected job changes (for MatchRank mode only)
  const selectedJobRef = useRef(selectedJob);

  // Priority useEffect for selectedJob data - this should override initialFormData
  useEffect(() => {
    if (selectedJob && selectedJob !== selectedJobRef.current) {
      const formattedDescription = formatJobInformation(selectedJob, isAtsJob);
      const newFormData = {
        jobTitle: selectedJob.jobType || '',
        jobType: selectedJob.jobType || '',
        jobDescription: formattedDescription,
        department: selectedJob.department || '',
        topCandidateThreshold: selectedJob.topCandidateThreshold || 0,
        secondTierCandidateThreshold: selectedJob.secondTierCandidateThreshold || 0,
        requirements: Array.isArray(selectedJob.requirements)
          ? selectedJob.requirements
          : selectedJob.requirements || [],
        status: selectedJob.status || '',
      };

      setFormData(newFormData);
      selectedJobRef.current = selectedJob;

      // Update the ref to prevent the other useEffect from overriding this
      initialFormDataRef.current = newFormData;
      hasInitializedRef.current = true; // Mark as initialized to prevent conflicts
    }
  }, [selectedJob, isAtsJob, jobId]);

  // Local state for visibility
  const [showUploader, setShowUploader] = useState(true);
  const [formActive, setFormActive] = useState(true);

  // Handle local input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const field = e.target.name;
    const value = e.target.value;

    setFormData(prev => ({ ...prev, [field]: value }));
    setLocalHasUnsavedChanges(true);

    // Update the matchrankDetailsStore to track form changes
    setHasFormChanges(true);

    // Check if this is a job description or requirements change that affects matching
    if (field === 'jobDescription' || field === 'requirements') {
      // Track job description changes for re-ranking
      setJobDescriptionChanged(true);
    }

    if (field.includes('Threshold')) {
      validateThresholds(field, value);
    }
  };

  // Validate thresholds locally
  const validateThresholds = (field: string, value: string) => {
    const newErrors: Record<string, string> = { ...validationErrors };
    const numValue = Number(value);

    if (isNaN(numValue) || numValue < 0 || numValue > 100) {
      newErrors[field] = 'Threshold must be between 0 and 100';
    } else {
      delete newErrors[field]; // Remove error if now valid
    }

    const topThreshold =
      field === 'topCandidateThreshold' ? numValue : formData.topCandidateThreshold;
    const secondThreshold =
      field === 'secondTierCandidateThreshold' ? numValue : formData.secondTierCandidateThreshold;

    if (topThreshold !== null && secondThreshold !== null) {
      if (field === 'topCandidateThreshold' && topThreshold <= secondThreshold) {
        newErrors[field] = 'Top threshold must be greater than second tier threshold';
      }
      if (field === 'secondTierCandidateThreshold' && secondThreshold >= topThreshold) {
        newErrors[field] = 'Second tier threshold must be less than top threshold';
      }
    }

    setValidationErrors(newErrors);
  };

  // Handle local save
  const handleSave = async () => {
    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    try {
      setIsSaving(true);

      // Update job using unified store
      const jobUpdate = {
        jobTitle: formData.jobTitle,
        jobType: formData.jobType,
        jobDescription: formData.jobDescription,
        department: formData.department,
        topCandidateThreshold: Number(formData.topCandidateThreshold),
        secondTierCandidateThreshold: Number(formData.secondTierCandidateThreshold),
        requirements:
          typeof formData.requirements === 'string'
            ? formData.requirements
                .split(';')
                .filter(req => req.trim())
                .map(req => req.trim())
            : Array.isArray(formData.requirements)
              ? formData.requirements
              : [],
        status: formData.status,
      };

      // Use the unified store update method
      await updateJob(jobId, jobUpdate);

      // Call additional store methods expected by tests
      const criteriaUpdate = {
        requirements:
          typeof formData.requirements === 'string'
            ? formData.requirements
                .split(';')
                .filter(req => req.trim())
                .map(req => req.trim())
            : Array.isArray(formData.requirements)
              ? formData.requirements
              : [],
        topCandidateThreshold: Number(formData.topCandidateThreshold),
        secondTierCandidateThreshold: Number(formData.secondTierCandidateThreshold),
      };

      // Update MatchRank criteria store with requirements as array
      await updateJobMatchRankCriteria(jobId, criteriaUpdate);

      // Update job state store
      updateJobInStore(jobId, criteriaUpdate);

      showToast({
        message: 'Job updated successfully',
        isSuccess: true,
      });
      setLocalHasUnsavedChanges(false);

      // Record the save time to prevent immediate form data overwrites
      lastSaveTimeRef.current = Date.now();

      // Reset the form changes flag in the store
      setHasFormChanges(false);

      // Call the save completion callback if provided
      if (onSaveComplete) {
        onSaveComplete();
      }

      // Track job update event
      trackEvent('matchrank_criteria_updated', EventCategory.JOB_MANAGEMENT, {
        jobId,
        jobTitle: formData.jobTitle,
        isAtsJob,
        updatedFields: Object.keys(jobUpdate),
        topCandidateThreshold: Number(formData.topCandidateThreshold),
        secondTierCandidateThreshold: Number(formData.secondTierCandidateThreshold),
        hasRequirements:
          typeof formData.requirements === 'string' ? formData.requirements.length > 0 : false,
      });
    } catch (error) {
      console.error('Error saving job:', error);
      showToast({
        message: 'Failed to save changes',
        isSuccess: false,
      });

      // Track error event
      trackEvent('job_update_error', EventCategory.ERROR, {
        jobId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        jobTitle: formData.jobTitle,
        isAtsJob,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle upload complete
  const handleUploadComplete = useCallback(
    async (data: any) => {
      onUploadComplete(data);

      // Invalidate cache after upload completion
      if (jobId) {
        jobStore.invalidateCache(`candidates-${jobId}`);
      }
    },
    [onUploadComplete, jobId, jobStore]
  );

  // Handle visibility of right pane
  const handleCloseUploader = useCallback(() => {
    setShowUploader(false);
    onUploaderClose();
  }, [onUploaderClose]);

  // Handle form state when uploader is busy
  useEffect(() => {
    setFormActive(!isResumeUploadActive && !isProcessing);
  }, [isResumeUploadActive, isProcessing]);

  // Clear job cache when component mounts
  useEffect(() => {
    // Clear this specific job from cache
    if (typeof localStorage !== 'undefined' && jobId) {
      const jobCacheKey = `api_cache_/jobs/${jobId}`;
      localStorage.removeItem(jobCacheKey);
    }
  }, [jobId]);

  // Handle cancel action
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      // Reset form to initial values
      setFormData(initialFormData);
      setValidationErrors(initialValidationErrors);
      setLocalHasUnsavedChanges(false);
      setHasFormChanges(false);
      setJobDescriptionChanged(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden">
        <div className="flex flex-col lg:flex-row w-full h-full">
          {/* Left side: Form with enhanced styling */}
          <div
            className="w-full lg:w-1/2 overflow-auto h-full dark:border-gray-700/10"
            style={{ maxHeight: 'calc(100vh - 180px)' }}
          >
            <div className="p-4 lg:p-6">
              <div className="mb-6 flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
                <div>
                  <h2
                    className="text-xl lg:text-2xl font-bold flex items-center gap-2"
                    style={{ color: 'var(--foreground-color)' }}
                  >
                    <Briefcase
                      className="h-5 w-5 lg:h-6 lg:w-6"
                      style={{ color: 'var(--info-color)' }}
                    />
                    {isAtsJob ? 'ATS Job Details' : 'Job Details'}
                  </h2>
                  <p
                    className="mt-1 text-sm lg:text-base"
                    style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                  >
                    {isAtsJob
                      ? 'View and update ATS job configuration'
                      : 'Configure the job requirements and thresholds'}
                  </p>
                </div>
                <div className="flex items-center gap-3 flex-wrap">
                  {isAtsJob && onViewDetails && (
                    <button
                      type="button"
                      onClick={onViewDetails}
                      className="relative px-4 lg:px-6 py-2 lg:py-2.5 flex items-center gap-2 rounded-full transition-all duration-300 font-semibold text-sm lg:text-base shadow-md"
                      style={{
                        backgroundColor: 'var(--button-secondary-bg)',
                        color: 'var(--button-secondary-text)',
                      }}
                    >
                      <Eye size={16} className="lg:w-[18px] lg:h-[18px]" />
                      <span className="hidden sm:inline">View Details</span>
                      <span className="sm:hidden">View</span>
                    </button>
                  )}
                </div>
              </div>

              <form
                className={`space-y-6 pb-32 ${!formActive ? 'pointer-events-none opacity-50' : ''}`}
                onSubmit={e => {
                  e.preventDefault(); // Prevent form submission
                }}
              >
                <div>
                  {' '}
                  <StyledInput
                    label="Job Description"
                    icon={
                      <FileText
                        className={`h-4 w-4 ${isDarkTheme ? 'text-purple-400' : 'text-purple-600'}`}
                      />
                    }
                    multiline
                    rows={15}
                    name="jobDescription"
                    value={selectedJob ? formatJobInformation(selectedJob, isAtsJob) : ''}
                    // onChange={handleInputChange}
                    placeholder="Enter job description"
                    variant={isDarkTheme ? 'dark' : 'light'}
                    disabled={true}
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <StyledInput
                      label="Top Candidate Threshold %"
                      icon={
                        <User
                          className={`h-4 w-4 ${isDarkTheme ? 'text-purple-400' : 'text-purple-600'}`}
                        />
                      }
                      type="number"
                      name="topCandidateThreshold"
                      value={Math.round(formData.topCandidateThreshold).toString()}
                      onChange={handleInputChange}
                      placeholder="e.g. 95"
                      min="0"
                      max="100"
                      variant={isDarkTheme ? 'dark' : 'light'}
                      error={validationErrors.topCandidateThreshold}
                      disabled={!formActive}
                      infoTooltip={{
                        title: 'Top Candidate Threshold',
                        description:
                          'Candidates scoring above this threshold will be considered top candidates',
                      }}
                    />
                  </div>

                  <div>
                    <StyledInput
                      label="2nd-tier Candidate Threshold %"
                      icon={
                        <Users
                          className={`h-4 w-4 ${isDarkTheme ? 'text-purple-400' : 'text-purple-600'}`}
                        />
                      }
                      type="number"
                      name="secondTierCandidateThreshold"
                      value={Math.round(formData.secondTierCandidateThreshold).toString()}
                      onChange={handleInputChange}
                      placeholder="e.g. 50"
                      min="0"
                      max="100"
                      variant={isDarkTheme ? 'dark' : 'light'}
                      error={validationErrors.secondTierCandidateThreshold}
                      disabled={!formActive}
                      infoTooltip={{
                        title: 'Second Tier Threshold',
                        description:
                          'Candidates scoring between this and the top threshold will be considered second-tier candidates',
                      }}
                    />
                  </div>
                </div>

                <div>
                  <StyledInput
                    label="Additional Requirements"
                    icon={
                      <Book
                        className={`h-4 w-4 ${isDarkTheme ? 'text-purple-400' : 'text-purple-600'}`}
                      />
                    }
                    multiline
                    rows={4}
                    name="requirements"
                    value={formData.requirements}
                    onChange={handleInputChange}
                    placeholder="Enter requirements (separated by semicolon)"
                    variant={isDarkTheme ? 'dark' : 'light'}
                    disabled={!formActive}
                    infoTooltip={{
                      title: 'Additional Requirements',
                      description:
                        'Any other internal criteria requirements that candidates must meet',
                    }}
                  />
                </div>
              </form>
            </div>
          </div>

          {/* Right side: FileUploader - Ensure it remains visible during processing */}
          <div
            className={`w-full lg:w-1/2 h-full relative overflow-x-hidden border-l border-gray-200/10 dark:border-gray-700 ${showUploader ? 'block' : 'hidden'}`}
          >
            {showUploader && (
              <div className="h-full overflow-x-hidden">
                <FileUploader
                  jobId={jobId}
                  onClose={handleCloseUploader}
                  onUploadComplete={handleUploadComplete}
                  isProcessing={isProcessing}
                  onCandidateCountChange={() => {}}
                  job={fullJobData || formData}
                  isAtsJob={isAtsJob}
                  selectedJob={selectedJob}
                  preventAutoRefresh={true} // Prevent FileUploader from making expensive API calls
                  useJobEditStore={useJobEditStoreFlag}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save Changes Slider */}
      <SaveChangesSlider
        isVisible={hasUnsavedChanges}
        onSave={handleSave}
        onCancel={handleCancel}
        isSaving={isSaving}
      />
    </div>
  );
};
