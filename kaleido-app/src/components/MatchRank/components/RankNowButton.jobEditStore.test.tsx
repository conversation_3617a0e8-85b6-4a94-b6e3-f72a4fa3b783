import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import { render, screen } from '@testing-library/react';
import { RankNowButton } from './RankNowButton';

// Mock all the stores
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobStore: jest.fn(),
  useJobStateStore: {
    getState: () => ({
      markJobAsUpdated: jest.fn(),
    }),
  },
}));
jest.mock('@/stores/matchrankDetailsStore');
jest.mock('@/stores/matchRankJobsStore');

// mockUseJobEditStore is no longer needed - using jobStore instead
const mockUseMatchRankDetailsStore = useMatchRankDetailsStore as jest.MockedFunction<
  typeof useMatchRankDetailsStore
>;
const mockUseMatchRankJobsStore = useMatchRankJobsStore as jest.MockedFunction<
  typeof useMatchRankJobsStore
>;
// Remove this line as we'll mock directly in tests

describe('RankNowButton with jobStore', () => {
  const mockProps = {
    jobId: 'test-job-123',
    candidateCount: 5,
    isThresholdValid: () => true,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock jobStore
    (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
      currentJob: {
        id: 'test-job-123',
        candidates: [{ id: 'candidate-1' }, { id: 'candidate-2' }],
        candidateEvaluations: [], // No evaluations = unranked candidates
      },
      jobId: 'test-job-123',
      hasUnsavedChanges: false,
      isResumeUploadActive: false,
      hasThresholdChanges: jest.fn(() => false),
      hasPendingChanges: jest.fn(() => false),
      fetchJobById: jest.fn(),
      fetchJobCriteria: jest.fn().mockResolvedValue({}),
      candidates: {
        unranked: [],
        matched: [],
        shortlisted: [],
        rejected: [],
      },
    } as any);

    // Mock matchRankDetailsStore
    mockUseMatchRankDetailsStore.mockReturnValue({
      isProcessing: false,
      handleRanking: jest.fn(),
      viewMode: 'edit',
      showUploader: false,
    } as any);

    // Mock matchRankJobsStore
    mockUseMatchRankJobsStore.mockImplementation(selector => {
      const state = {
        activeJobs: [],
        jobs: {},
        addJob: jest.fn(),
        updateJobStatus: jest.fn(),
        updateJobProgress: jest.fn(),
        removeJob: jest.fn(),
        clearCompletedJobs: jest.fn(),
      };
      return selector ? selector(state) : state;
    });

    // Credit cost is now handled via props or jobStore
  });

  it('should be disabled when useJobEditStore=true and no pending changes', () => {
    render(<RankNowButton {...mockProps} useJobEditStore={true} />);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('title', expect.stringContaining('No pending changes detected'));
  });

  it('should be enabled when useJobEditStore=true and has unsaved changes', () => {
    (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
      currentJob: { id: 'test-job-123', candidates: [], candidateEvaluations: [] },
      jobId: 'test-job-123',
      hasUnsavedChanges: true,
      isResumeUploadActive: false,
      hasThresholdChanges: jest.fn(() => true),
      hasPendingChanges: jest.fn(() => true),
      fetchJobById: jest.fn(),
      fetchJobCriteria: jest.fn().mockResolvedValue({}),
      candidates: { unranked: [], matched: [], shortlisted: [], rejected: [] },
    } as any);

    render(<RankNowButton {...mockProps} useJobEditStore={true} />);

    const button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
  });

  it('should be enabled when useJobEditStore=true and has unranked candidates', () => {
    (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
      currentJob: {
        id: 'test-job-123',
        candidates: [{ id: 'candidate-1' }],
        candidateEvaluations: [], // No evaluations = unranked
      },
      jobId: 'test-job-123',
      hasUnsavedChanges: false,
      isResumeUploadActive: false,
      hasThresholdChanges: jest.fn(() => true), // Enable via threshold changes
      hasPendingChanges: jest.fn(() => true), // Should return true due to unranked candidates
      fetchJobById: jest.fn(),
      fetchJobCriteria: jest.fn().mockResolvedValue({}),
      candidates: { unranked: [{ id: 'candidate-1' }], matched: [], shortlisted: [], rejected: [] },
    } as any);

    render(<RankNowButton {...mockProps} useJobEditStore={true} />);

    const button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
  });

  it('should be enabled when useJobEditStore=true and has active uploads', () => {
    (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
      currentJob: { id: 'test-job-123', candidates: [], candidateEvaluations: [] },
      jobId: 'test-job-123',
      hasUnsavedChanges: false,
      isResumeUploadActive: true,
      hasThresholdChanges: jest.fn(() => true), // Enable via threshold changes
      hasPendingChanges: jest.fn(() => true), // Should return true due to active uploads
      fetchJobById: jest.fn(),
      fetchJobCriteria: jest.fn().mockResolvedValue({}),
      candidates: { unranked: [], matched: [], shortlisted: [], rejected: [] },
    } as any);

    render(<RankNowButton {...mockProps} useJobEditStore={true} />);

    const button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
  });

  it('should use legacy logic when useJobEditStore=false', () => {
    // Mock legacy store behavior - need to handle both direct calls and selector calls
    mockUseMatchRankDetailsStore.mockImplementation((selector?: any) => {
      if (selector) {
        // Mock selectHasFormEdits and selectHasUnrankedCandidates to return true
        return true;
      }
      return {
        isProcessing: false,
        handleRanking: jest.fn(),
        viewMode: 'edit',
        showUploader: false,
      };
    });

    render(
      <RankNowButton
        {...mockProps}
        useJobEditStore={false}
        matchRankCost={{
          success: true,
          creditCost: 10,
          unevaluatedCandidatesCount: 5,
          isValid: true,
          message: '',
          availableCredits: 100,
        }}
      />
    );

    const button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
  });
});
