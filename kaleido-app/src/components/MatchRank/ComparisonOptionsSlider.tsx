import { AnimatePresence, motion } from 'framer-motion';
import { X, <PERSON>R<PERSON>, Clock, Edit2, Spark<PERSON> } from 'lucide-react';
import React, { useState } from 'react';
import { COMPARISON_OPTIONS, ComparisonType } from '@/types/comparison.types';
import { ICandidate } from '@/entities/interfaces';

interface ComparisonOptionsSliderProps {
  isVisible: boolean;
  selectedCandidates: ICandidate[];
  onClose: () => void;
  onSelectOption: (type: ComparisonType, customPrompt?: string) => void;
  className?: string;
}

export const ComparisonOptionsSlider: React.FC<ComparisonOptionsSliderProps> = ({
  isVisible,
  selectedCandidates,
  onClose,
  onSelectOption,
  className = '',
}) => {
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedOption, setSelectedOption] = useState<ComparisonType | null>(null);

  const handleOptionClick = (type: ComparisonType) => {
    if (type === ComparisonType.CUSTOM) {
      setShowCustomPrompt(true);
      setSelectedOption(type);
    } else {
      onSelectOption(type);
    }
  };

  const handleCustomSubmit = () => {
    if (customPrompt.trim()) {
      onSelectOption(ComparisonType.CUSTOM, customPrompt);
      setCustomPrompt('');
      setShowCustomPrompt(false);
    }
  };

  const candidateNames = selectedCandidates.map(c => c.fullName).join(', ');

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: '100%', opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: '100%', opacity: 0 }}
          transition={{
            type: 'spring',
            damping: 25,
            stiffness: 300,
          }}
          className={`fixed bottom-0 left-0 right-0 z-[60] ${className}`}
        >
          {/* Backdrop for bottom sheet */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Bottom sheet container */}
          <motion.div
            className="relative bg-gradient-to-b from-slate-900 to-purple-950/50 backdrop-blur-xl rounded-t-3xl shadow-2xl shadow-purple-500/20 border-t border-purple-400/20"
            initial={{ borderRadius: '0 0 0 0' }}
            animate={{ borderRadius: '24px 24px 0 0' }}
          >
            {/* Handle bar */}
            <div className="flex justify-center pt-4">
              <div className="w-12 h-1.5 bg-white/20 rounded-full" />
            </div>

            {/* Header */}
            <div className="px-6 pt-4 pb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Choose Comparison Type</h2>
                  <p className="text-white/70 mt-1">
                    Comparing {selectedCandidates.length} candidates
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X className="w-6 h-6 text-white/70" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="px-6 pb-8 max-h-[60vh] overflow-y-auto">
              {!showCustomPrompt ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {COMPARISON_OPTIONS.map((option) => (
                    <motion.button
                      key={option.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleOptionClick(option.id as ComparisonType)}
                      className="group relative p-6 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-purple-400/30 rounded-xl transition-all duration-200 text-left overflow-hidden"
                    >
                      {/* Background gradient on hover */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity" />

                      <div className="relative z-10">
                        <div className="flex items-start justify-between mb-3">
                          <span className="text-3xl">{option.icon}</span>
                          <div className="flex items-center gap-1 text-xs text-white/50">
                            <Clock className="w-3 h-3" />
                            <span>{option.estimatedTime}</span>
                          </div>
                        </div>
                        
                        <h3 className="text-lg font-semibold text-white mb-2">
                          {option.name}
                        </h3>
                        
                        <p className="text-sm text-white/70 leading-relaxed">
                          {option.description}
                        </p>

                        <div className="mt-4 flex items-center text-purple-400 text-sm font-medium">
                          <span>Start comparison</span>
                          <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </motion.button>
                  ))}

                  {/* Custom comparison option */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleOptionClick(ComparisonType.CUSTOM)}
                    className="group relative p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/10 hover:from-purple-500/20 hover:to-pink-500/20 border border-purple-400/30 hover:border-purple-400/50 rounded-xl transition-all duration-200 text-left overflow-hidden"
                  >
                    <div className="relative z-10">
                      <div className="flex items-start justify-between mb-3">
                        <span className="text-3xl">✨</span>
                        <Sparkles className="w-5 h-5 text-purple-400" />
                      </div>
                      
                      <h3 className="text-lg font-semibold text-white mb-2">
                        Custom Comparison
                      </h3>
                      
                      <p className="text-sm text-white/70 leading-relaxed">
                        Create your own comparison criteria or ask specific questions about the candidates
                      </p>

                      <div className="mt-4 flex items-center text-purple-400 text-sm font-medium">
                        <Edit2 className="w-4 h-4 mr-1" />
                        <span>Write custom prompt</span>
                      </div>
                    </div>
                  </motion.button>
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  <div>
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      What would you like to compare?
                    </label>
                    <textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="E.g., Compare their experience with remote team management, or their potential to grow into a senior leadership role..."
                      className="w-full h-32 px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20 resize-none"
                      autoFocus
                    />
                  </div>

                  <div className="text-sm text-white/60">
                    <p>💡 Tips for better comparisons:</p>
                    <ul className="mt-2 space-y-1 ml-4">
                      <li>• Be specific about what aspects to compare</li>
                      <li>• Consider your team's current needs</li>
                      <li>• Ask about scenarios they might face</li>
                    </ul>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      onClick={() => {
                        setShowCustomPrompt(false);
                        setCustomPrompt('');
                      }}
                      className="flex-1 px-4 py-2.5 bg-white/10 hover:bg-white/15 border border-white/20 text-white rounded-lg transition-colors"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleCustomSubmit}
                      disabled={!customPrompt.trim()}
                      className="flex-1 px-4 py-2.5 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white rounded-lg transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Start Comparison
                    </button>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ComparisonOptionsSlider;