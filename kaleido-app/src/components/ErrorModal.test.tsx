import { fireEvent, render, screen } from '@testing-library/react';

import ErrorModal from './ErrorModal';

// Mock the stores
const mockRefreshJobs = jest.fn();
const mockInvalidateJobsCache = jest.fn();

jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: () => ({
    refreshJobs: mockRefreshJobs,
    invalidateJobsCache: mockInvalidateJobsCache,
  }),
}));

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

// Mock createPortal to render in the same DOM
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (children: any) => children,
}));

describe('ErrorModal', () => {
  const mockOnClose = jest.fn();
  const mockOnRetry = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockRefreshJobs.mockClear();
    mockInvalidateJobsCache.mockClear();
  });

  describe('Basic Rendering', () => {
    it('should not render when isOpen is false', () => {
      render(<ErrorModal isOpen={false} />);
      expect(screen.queryByText('Oops! Something went wrong')).not.toBeInTheDocument();
    });

    it('should render when isOpen is true', () => {
      render(<ErrorModal isOpen={true} />);
      expect(screen.getByText('Oops! Something went wrong')).toBeInTheDocument();
    });

    it('should render with custom title and message', () => {
      render(
        <ErrorModal
          isOpen={true}
          title="Custom Error Title"
          message="This is a custom error message that should display as-is"
        />
      );
      expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
      // The message gets processed by getUserFriendlyMessage, so check for the default fallback
      expect(screen.getByText(/having trouble loading this page/)).toBeInTheDocument();
    });
  });

  describe('User-Friendly Message Conversion', () => {
    it('should convert network errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="Network error occurred" />);
      expect(screen.getByText(/trouble connecting to our servers/)).toBeInTheDocument();
    });

    it('should convert 404 errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="Error 404: Not found" />);
      expect(screen.getByText(/couldn't find what you're looking for/)).toBeInTheDocument();
    });

    it('should convert 500 errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="500 Internal Server Error" />);
      expect(screen.getByText(/servers are experiencing some issues/)).toBeInTheDocument();
    });

    it('should convert 401 errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="401 Unauthorized" />);
      expect(screen.getByText(/don't have permission to access/)).toBeInTheDocument();
    });

    it('should convert timeout errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="Request timed out" />);
      expect(screen.getByText(/taking longer than expected/)).toBeInTheDocument();
    });

    it('should use default message for unknown errors', () => {
      render(<ErrorModal isOpen={true} message="Some random error" />);
      expect(screen.getByText(/having trouble loading this page/)).toBeInTheDocument();
    });

    it('should use default message when no message is provided', () => {
      render(<ErrorModal isOpen={true} />);
      expect(screen.getByText(/having trouble loading this page/)).toBeInTheDocument();
    });
  });

  describe('Button Interactions', () => {
    it('should call onRetry when custom retry function is provided', () => {
      render(<ErrorModal isOpen={true} onRetry={mockOnRetry} onClose={mockOnClose} />);

      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(1);
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Default Refresh Behavior', () => {
    it('should handle default refresh when no onRetry is provided', () => {
      render(<ErrorModal isOpen={true} onClose={mockOnClose} />);

      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      expect(mockInvalidateJobsCache).toHaveBeenCalledTimes(1);
      expect(mockRefreshJobs).toHaveBeenCalledWith(true);
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('should have proper button types', () => {
      render(<ErrorModal isOpen={true} onClose={mockOnClose} />);

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('type', 'button');
      });
    });

    it('should stop propagation when modal content is clicked', () => {
      render(<ErrorModal isOpen={true} onClose={mockOnClose} />);

      const modalContent = screen.getByText('Oops! Something went wrong').closest('div');
      const stopPropagationSpy = jest.fn();

      const event = new MouseEvent('click', { bubbles: true });
      event.stopPropagation = stopPropagationSpy;

      fireEvent(modalContent!, event);
      expect(stopPropagationSpy).toHaveBeenCalled();
    });
  });

  describe('Portal Management', () => {
    it('should render modal content when open', () => {
      render(<ErrorModal isOpen={true} />);

      // Since we're mocking createPortal to render directly, just check that content is rendered
      expect(screen.getByText('Oops! Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });
  });
});
