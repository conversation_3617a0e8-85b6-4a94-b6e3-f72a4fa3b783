import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Activity,
  Briefcase,
  Calendar,
  CheckCircle2,
  Clock,
  Eye,
  Menu,
  Rocket,
  Settings,
  Trash2,
  User<PERSON>heck,
  Video,
  VideoOff,
  X,
} from 'lucide-react';

import { IJob } from '@/entities/interfaces';
import { useJobsStore, useJobStore } from '@/stores/unifiedJobStore';
import { formatTableValue } from '@/utils/formatters';
import { useRouter, useSearchParams } from 'next/navigation';
import { showToast } from '../Toaster';

interface MobileJobCardProps {
  job: IJob;
  onJobClick: (job: IJob) => void;
}

const MobileJobCard = ({ job, onJobClick }: MobileJobCardProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { deleteJob, fetchJobsByStatus } = useJobsStore();
  const { publishJob, unpublishJob } = useJobStore();

  // Set isMounted to true after component mounts (for SSR compatibility)
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (menuButtonRef.current) {
      const rect = menuButtonRef.current.getBoundingClientRect();
      setMenuPosition({
        top: rect.bottom + window.scrollY,
        right: window.innerWidth - rect.right,
      });
    }

    setIsMenuOpen(!isMenuOpen);
  };

  const handleManage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(false);
    router.push(`/jobs/${job.id}/manage`);
  };

  const handleCandidateScores = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(false);
    if (candidateStats > 0) {
      router.push(`/jobs/${job.id}/candidates`);
    } else {
      router.push(`/jobs/${job.id}/edit`);
    }
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(false);
    if (window.confirm('Are you sure you want to delete this job?')) {
      try {
        await deleteJob(job.id);
        showToast({
          message: 'Job deleted successfully',
          isSuccess: true,
        });
      } catch (error) {
        console.error('Error deleting job:', error);
        showToast({
          message: 'Failed to delete job',
          isSuccess: false,
        });
      }
    }
  };

  const handlePublishToggle = async (e: React.MouseEvent) => {
    e.stopPropagation();
    const isPublished = (job as any).isPublished || false;
    try {
      if (isPublished) {
        await unpublishJob(job.id, ['jobboard']);
        showToast({
          message: 'Job unpublished successfully',
          isSuccess: true,
        });
      } else {
        await publishJob(job.id, ['jobboard']);
        showToast({
          message: 'Job published successfully',
          isSuccess: true,
        });
      }
      // Refresh the jobs list through the store
      // Get current status filter from URL if any
      const currentStatus = searchParams.get('status') || 'ALL';
      const currentPage = parseInt(searchParams.get('page') || '1', 10);

      // Fetch jobs with the current filters to update the view
      await fetchJobsByStatus(currentPage, currentStatus);
    } catch (error) {
      console.error('Error updating job status:', error);
      showToast({
        message: 'Failed to update job status',
        isSuccess: false,
      });
    }
  };

  // Format the date
  const updatedDate = new Date(job.updatedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  // Get candidate stats - use totalCandidates directly from job object first, then fallback to candidateStats
  const candidateStats = job.totalCandidates ?? job['candidateStats']?.totalCandidates ?? 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full bg-black/10 backdrop-blur-md border border-white/20 rounded-lg mb-3 overflow-hidden"
      onClick={() => onJobClick(job)}
    >
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Briefcase className="w-4 h-4 text-pink-400" />
              <p className="font-medium text-white">{formatTableValue(job.jobType)}</p>
            </div>
            <div className="flex items-center gap-2 mb-2 text-sm">
              <Activity className="w-3.5 h-3.5 text-purple-400" />
              <span className="text-white/90">{formatTableValue(job.department)}</span>
            </div>
            <div className="mb-2 flex items-center justify-start gap-2">
              <div className="flex items-center gap-1.5">
                {(job as any).isPublished ? (
                  <span title="Published">
                    <CheckCircle2 className="h-3 w-3 text-green-500" />
                  </span>
                ) : (
                  <span title="Draft">
                    <Clock className="h-3 w-3 text-amber-500" />
                  </span>
                )}

                {/* Video icon after status icon */}
                <span title={job.hasVideoUrl ? 'Has video' : 'No video'}>
                  {job.hasVideoUrl ? (
                    <Video className="h-2.5 w-2.5 text-green-500" />
                  ) : (
                    <VideoOff className="h-2.5 w-2.5 text-gray-400" />
                  )}
                </span>
              </div>

              <button
                onClick={handlePublishToggle}
                className="group relative flex items-center gap-1 px-2.5 py-1 text-xs font-medium rounded-full transition-all duration-300 overflow-hidden"
                style={{
                  background: (job as any).isPublished
                    ? 'linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(107, 114, 128, 0.2) 50%, rgba(156, 163, 175, 0.1) 100%)'
                    : 'linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.3) 50%, rgba(16, 185, 129, 0.2) 100%)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: 'none',
                  boxShadow: (job as any).isPublished
                    ? 'inset 0 1px 2px rgba(0, 0, 0, 0.1)'
                    : 'inset 0 1px 2px rgba(255, 255, 255, 0.2), 0 2px 6px rgba(16, 185, 129, 0.2)',
                }}
              >
                <div
                  className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background:
                      'radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%)',
                  }}
                />
                {(job as any).isPublished ? (
                  <>
                    <X className="h-3 w-3 text-white/80 relative z-10" />
                    <span className="text-white/80 relative z-10">Unpublish</span>
                  </>
                ) : (
                  <>
                    <Rocket className="h-3 w-3 text-white/80 relative z-10" />
                    <span className="text-white/80 font-semibold relative z-10">Publish</span>
                  </>
                )}
              </button>
            </div>
            <div className="flex items-center gap-2 mb-2 text-sm">
              <Calendar className="w-3.5 h-3.5 text-indigo-400" />
              <span className="text-white/90">{updatedDate}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="flex items-center gap-1">
                <span className="text-purple-300">Candidates:</span>
                <span className="text-pink-300 font-medium">{candidateStats}</span>
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2 mt-1">
            {/* Eye icon for details */}
            <button
              type="button"
              onClick={e => {
                e.stopPropagation();
                onJobClick(job);
              }}
              className="p-2 rounded-full bg-white/10 hover:bg-pink-500/20 transition-colors"
              aria-label="View details"
            >
              <Eye className="w-4 h-4 text-pink-400" />
            </button>

            {/* Burger menu for actions */}
            <div className="relative">
              <button
                type="button"
                ref={menuButtonRef}
                onClick={toggleMenu}
                className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                aria-label="More actions"
              >
                <Menu className="w-4 h-4 text-white" />
              </button>

              {isMenuOpen &&
                isMounted &&
                createPortal(
                  <AnimatePresence>
                    <div
                      className="fixed inset-0 z-[100]"
                      onClick={e => {
                        e.stopPropagation();
                        setIsMenuOpen(false);
                      }}
                    >
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        className="fixed z-[101] w-56 bg-black/30 backdrop-blur-xl border border-white/20 rounded-lg shadow-xl overflow-hidden"
                        style={{
                          top: menuPosition.top + 'px',
                          right: menuPosition.right + 'px',
                          boxShadow:
                            '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)',
                        }}
                        onClick={e => e.stopPropagation()}
                      >
                        <div className="py-2">
                          <button
                            type="button"
                            onClick={handleManage}
                            className="w-full px-4 py-3 flex items-center gap-3 hover:bg-blue-500/20 transition-colors text-left"
                          >
                            <Settings className="w-4 h-4 text-blue-400" />
                            <span className="text-sm text-blue-100 font-medium">Manage</span>
                          </button>

                          <div className="border-t border-white/10 my-1"></div>

                          <button
                            type="button"
                            onClick={handleCandidateScores}
                            className="w-full px-4 py-3 flex items-center gap-3 hover:bg-purple-500/20 transition-colors text-left"
                          >
                            <UserCheck className="w-4 h-4 text-purple-400" />
                            <span className="text-sm text-purple-100 font-medium">
                              {candidateStats > 0 ? 'Assessments' : 'Evaluate Criteria'}
                            </span>
                          </button>

                          <div className="border-t border-white/10 my-1"></div>

                          <button
                            type="button"
                            onClick={handleDelete}
                            className="w-full px-4 py-3 flex items-center gap-3 hover:bg-red-500/20 transition-colors text-left"
                          >
                            <Trash2 className="w-4 h-4 text-red-400" />
                            <span className="text-sm text-red-100 font-medium">Delete</span>
                          </button>
                        </div>
                      </motion.div>
                    </div>
                  </AnimatePresence>,
                  document.body
                )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MobileJobCard;
