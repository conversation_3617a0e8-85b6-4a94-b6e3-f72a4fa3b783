import { showToast } from '@/components/Toaster';
import { I<PERSON><PERSON> } from '@/entities/interfaces';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useRef, useState } from 'react';
import { TableDropdownMenu } from '../ui/DropdownMenu';

interface JobActionsDropdownProps {
  job: IJob;
  onViewDetails: (job: IJob) => void;
}

const JobActionsDropdown: React.FC<JobActionsDropdownProps> = ({ job, onViewDetails }) => {
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const actionButtonRef = useRef<HTMLButtonElement>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { deleteJob, publishJob, unpublishJob } = useJobsStore();

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this job?')) {
      try {
        await deleteJob(job.id);
        showToast({
          message: 'Job deleted successfully',
          isSuccess: true,
        });
      } catch (error) {
        console.error('Error deleting job:', error);
        showToast({
          message: 'Failed to delete job',
          isSuccess: false,
        });
      }
    }
  };

  const handleEdit = () => {
    // Navigate to unified job view
    router.push(`/jobs/${job.id}/edit`);
  };

  const handlePublish = async () => {
    try {
      const isPublished = (job as any).isPublished || false;

      if (isPublished) {
        // If already published, unpublish
        await unpublishJob(job.id, ['jobboard']);
        showToast({
          message: 'Job unpublished successfully',
          isSuccess: true,
        });
      } else {
        // If not published, publish
        await publishJob(job.id, ['jobboard']);
        showToast({
          message: 'Job published successfully',
          isSuccess: true,
        });
      }

      // Navigate to page=1 and refresh the page
      const params = new URLSearchParams(searchParams.toString());
      params.set('page', '1');
      const newUrl = `${window.location.pathname}?${params.toString()}`;
      router.push(newUrl);
    } catch (error) {
      console.error('Error publishing/unpublishing job:', error);
      showToast({
        message: 'Failed to update job publication status',
        isSuccess: false,
      });
    }
  };

  return (
    <div className="relative inline-block text-left">
      <button
        type="button"
        ref={actionButtonRef}
        onClick={e => {
          e.stopPropagation();
          setOpenMenuId(openMenuId === job.id ? null : job.id);
        }}
        className="inline-flex items-center justify-center p-1.5 rounded-full bg-[var(--button-secondary-bg)] text-[var(--foreground-color)] hover:bg-[var(--button-secondary-hover)] hover:opacity-90 transition-colors"
        title="Actions"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 6h16M4 12h16M4 18h16"
          />
        </svg>
      </button>
      <TableDropdownMenu
        isOpen={openMenuId === job.id}
        onClose={() => setOpenMenuId(null)}
        triggerRef={actionButtonRef}
        onViewDetails={() => onViewDetails(job)}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onPublish={handlePublish}
        showCopyJobUrl={true}
        showPublish={true}
        isPublished={(job as any).isPublished || false}
        jobId={job.id}
      />
    </div>
  );
};

export default JobActionsDropdown;
