import React, { useState } from 'react';

import { MessageSquare } from 'lucide-react';

// Removed theme helpers - always use dark theme
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import ToneSelector from '@/components/steps/preview/ToneSelector';
import TextEditor from '@/components/TextEditor';
import { showToast } from '@/components/Toaster';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import SmoothButton from '@/components/ui/SmoothButton';
import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';

interface JobContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string;
  initialContent?: string;
}

const JobContentModal: React.FC<JobContentModalProps> = ({
  isOpen,
  onClose,
  jobId,
  initialContent = '',
}) => {
  const [selectedTone, setSelectedTone] = useState<string>('professional');
  const [content, setContent] = useState<string>(initialContent);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isScoutLoading, setIsScoutLoading] = useState<boolean>(false);
  const { updateJob } = useJobsStore();
  // Always use dark theme

  const handleGenerateContent = async () => {
    if (!jobId) {
      setError('Job ID is required to generate content');
      return;
    }

    try {
      setIsGenerating(true);
      setError(null);

      const response = await apiHelper.post('/jobs/generate-tone', {
        jobId,
        tone: selectedTone,
      });

      if (response.generatedJD) {
        setContent(response.generatedJD);
        showToast({ message: 'Content generated successfully' });
      } else {
        setError('Failed to generate content. Please try again.');
      }
    } catch (error) {
      setError('An error occurred while generating content.');
      showToast({ message: 'Failed to generate content', isSuccess: false });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = async () => {
    if (!content || !jobId) return;

    try {
      setIsSaving(true);
      // Save the content to the job using the updateJob function
      await updateJob(jobId, { finalDraft: content });

      showToast({ message: 'Content saved successfully' });
      onClose();
    } catch (error) {
      setError('Failed to save content');
      showToast({ message: 'Failed to save content', isSuccess: false });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] backdrop-blur-xl border shadow-lg bg-gray-900/90 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-white">
            Job Content Generator
          </DialogTitle>
        </DialogHeader>

        <div className="mt-4">
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <MessageSquare size={16} className="mr-2 text-gray-400" />
              <span className="text-sm font-medium text-gray-200">Content Tone</span>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="flex-grow w-full sm:w-auto">
                <ToneSelector
                  selectedTone={selectedTone}
                  onChange={setSelectedTone}
                  disabled={isGenerating}
                />
              </div>
              <SmoothButton
                label="Generate"
                variant="save"
                onClick={handleGenerateContent}
                disabled={isGenerating}
                className="whitespace-nowrap mt-2 sm:mt-0"
              />
            </div>
          </div>

          <div
            className="p-4 rounded-md my-4 overflow-y-auto bg-gray-800/50 shadow-inner shadow-black/20"
            style={{
              minHeight: '300px',
              maxHeight: '400px',
            }}
          >
            {isGenerating ? (
              <div className="flex flex-col items-center justify-center h-full">
                <ColorfulSmokeyOrbLoader text="Generating content..." />
              </div>
            ) : error ? (
              <div className="text-red-400 p-4">{error}</div>
            ) : (
              <TextEditor
                value={content}
                onChange={setContent}
                height="100%"
                placeholder="Generated content will appear here"
                isEditable={!isGenerating && !isSaving}
                noBorder={true}
                textColor="white"
              />
            )}
          </div>

          <div className="mt-4 flex justify-end space-x-3">
            <SmoothButton
              label="Cancel"
              variant="edit"
              onClick={onClose}
              className="text-sm px-4 py-2"
            />
            <SmoothButton
              label="Save"
              variant="save"
              onClick={handleSave}
              disabled={isGenerating || isSaving || !content}
              className="text-sm px-4 py-2"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default JobContentModal;
