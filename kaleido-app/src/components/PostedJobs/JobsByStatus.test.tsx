import React from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useCurrentUser } from '@/services/user.service';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { render, screen, waitFor } from '@testing-library/react';

import JobsByStatus from './JobsByStatus';

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock('@/services/user.service', () => ({
  useCurrentUser: jest.fn(),
}));

jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: Object.assign(jest.fn(), {
    getState: jest.fn(() => ({
      clearJobData: jest.fn(),
    })),
  }),
}));

jest.mock('@/hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(() => false),
}));

jest.mock('./PostedJobDetails', () => {
  return {
    PostedJobDetails: ({ job, onClose }: any) => (
      <div data-testid="posted-job-details">
        <div>Job ID: {job.id}</div>
        <div>Job Status: {job.status}</div>
        <button type="button" onClick={onClose}>
          Close
        </button>
      </div>
    ),
  };
});

describe('JobsByStatus', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
  };

  const mockUser = {
    sub: 'test-user-id',
    name: 'Test User',
  };

  const mockJobsStore = {
    jobsByStatus: { ALL: { jobs: [], count: 0 } },
    jobsByStatusPagination: {
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      itemsPerPage: 10,
    },
    isLoading: false,
    fetchJobsByStatus: jest.fn(),
    setSelectedJobId: jest.fn(),
    selectedJob: null,
    selectedJobId: null,
  };

  // Helper function to mock useJobsStore with selector pattern
  const mockUseJobsStore = (storeData: any) => {
    (useJobsStore as unknown as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(storeData);
      }
      return storeData;
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useCurrentUser as jest.Mock).mockReturnValue(mockUser);
    mockUseJobsStore(mockJobsStore);
  });

  describe('Job Details View with URL Parameters', () => {
    it('should show loading state when jobId is present but job is not loaded', () => {
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        selectedJob: null, // No job loaded yet
        selectedJobId: null, // No job ID set yet
        isLoading: true, // Show loading state
      });

      render(<JobsByStatus />);

      expect(screen.getByText('Loading job details...')).toBeInTheDocument();
    });

    it('should render PostedJobDetails when job is loaded with jobId', () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'MATCHED',
        jobType: 'Software Engineer',
        candidates: [{ id: 'candidate-1' }],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
        isLoading: false,
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } }, // Include the job in the list
      });

      render(<JobsByStatus />);

      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByText('Job ID: test-job-id')).toBeInTheDocument();
      expect(screen.getByText('Job Status: MATCHED')).toBeInTheDocument();
    });

    it('should set selectedJobId when jobId is present in URL', () => {
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);

      render(<JobsByStatus />);

      expect(mockJobsStore.setSelectedJobId).toHaveBeenCalledWith('test-job-id');
    });
  });

  describe('Job Status Display', () => {
    it('should render correctly when job status is NEW', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'NEW',
        jobType: 'Software Engineer',
        candidates: [],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id'),
        entries: jest.fn(() => [['jobId', 'test-job-id']]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
        isLoading: false,
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } },
      });

      render(<JobsByStatus />);

      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByText('Job Status: NEW')).toBeInTheDocument();
    });

    it('should render correctly when job has no candidates', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'MATCHED',
        jobType: 'Software Engineer',
        candidates: [],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id'),
        entries: jest.fn(() => [['jobId', 'test-job-id']]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
        isLoading: false,
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } },
      });

      render(<JobsByStatus />);

      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByText('Job Status: MATCHED')).toBeInTheDocument();
    });

    it('should render correctly when job has candidates', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'MATCHED',
        jobType: 'Software Engineer',
        candidates: [{ id: 'candidate-1' }],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id'),
        entries: jest.fn(() => [['jobId', 'test-job-id']]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
        isLoading: false,
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } },
      });

      render(<JobsByStatus />);

      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByText('Job Status: MATCHED')).toBeInTheDocument();
      // Component should not perform any redirects
      expect(mockRouter.replace).not.toHaveBeenCalled();
    });
  });

  describe('Empty State', () => {
    it('should show empty state when no jobs are available and no jobId', () => {
      const mockSearchParams = {
        get: jest.fn(() => null),
        toString: jest.fn(() => ''),
        entries: jest.fn(() => []),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);

      render(<JobsByStatus />);

      expect(screen.getByText('No Jobs Found')).toBeInTheDocument();
      expect(
        screen.getByText(
          "You don't have any jobs yet. Create your first job description to get started."
        )
      ).toBeInTheDocument();
    });
  });

  describe('URL Parameter Handling', () => {
    it('should not fetch jobs when jobId is present', () => {
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id'),
        entries: jest.fn(() => [['jobId', 'test-job-id']]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);

      render(<JobsByStatus />);

      expect(mockJobsStore.fetchJobsByStatus).not.toHaveBeenCalled();
    });

    it('should fetch jobs when no jobId is present and no data exists', () => {
      const fetchJobsByStatus = jest.fn();
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'status') return 'ALL';
          if (key === 'page') return '1';
          return null;
        }),
        toString: jest.fn(() => 'status=ALL&page=1'),
        entries: jest.fn(() => [
          ['status', 'ALL'],
          ['page', '1'],
        ]),
      };
      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        ...mockJobsStore,
        jobsByStatus: {}, // Empty object means no data exists
        fetchJobsByStatus,
      });

      render(<JobsByStatus />);

      expect(fetchJobsByStatus).toHaveBeenCalledWith(1, 'ALL');
    });
  });
});
