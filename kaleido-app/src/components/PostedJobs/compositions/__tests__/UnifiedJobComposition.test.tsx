import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UnifiedJobComposition } from '../UnifiedJobComposition';
import { useJobsStore, jobDataToIJob } from '@/stores/unifiedJobStore';
import { useRouter } from 'next/navigation';
import { IJob } from '@/entities/interfaces';

// Mock dependencies
jest.mock('@/stores/unifiedJobStore');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));
jest.mock('next/image', () => ({
  __esModule: true,
  // eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text
  default: (props: any) => <img {...props} alt={props.alt || ''} />,
}));
jest.mock('../../UnifiedJobView', () => ({
  UnifiedJobView: ({ jobId, embedded }: { jobId: string; embedded: boolean }) => (
    <div data-testid="unified-job-view">
      UnifiedJobView - Job ID: {jobId}, Embedded: {embedded.toString()}
    </div>
  ),
}));
jest.mock('@/components/ui/tooltip', () => {
  const React = require('react');
  return {
    TooltipProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Tooltip: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    TooltipTrigger: ({ children, asChild }: { children: React.ReactNode; asChild?: boolean }) =>
      asChild ? React.cloneElement(children as React.ReactElement, {}) : <>{children}</>,
    TooltipContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const mockPush = jest.fn();
const mockUseRouter = useRouter as jest.Mock;
const mockUseJobsStore = useJobsStore as jest.MockedFunction<typeof useJobsStore>;
const mockJobDataToIJob = jobDataToIJob as jest.MockedFunction<typeof jobDataToIJob>;

describe('UnifiedJobComposition', () => {
  // Helper function to mock the store with selector pattern
  const mockStoreWithState = (state: any) => {
    mockUseJobsStore.mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(state);
      }
      return state;
    });
  };
  const mockJobs: IJob[] = [
    {
      id: '1',
      jobTitle: 'Senior Developer',
      jobType: 'Full-time',
      location: 'San Francisco',
      status: 'NEW',
      createdAt: new Date('2024-01-01'),
      candidatesCount: 5,
    } as IJob,
    {
      id: '2',
      jobTitle: 'Product Manager',
      jobType: 'Contract',
      location: ['New York', 'Remote'],
      status: 'MATCHED',
      createdAt: new Date('2024-01-02'),
      totalCandidates: 10,
    } as IJob,
    {
      id: '3',
      jobTitle: 'UX Designer',
      status: 'INTERVIEWING',
      createdAt: new Date('2024-01-03'),
      candidateStats: { totalCandidates: 3 },
    } as IJob,
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({ push: mockPush });

    // Mock jobDataToIJob to return the same object
    mockJobDataToIJob.mockImplementation(job => job as IJob);

    // Default store state
    const mockStoreState = {
      jobsByStatus: {
        ALL: {
          jobs: mockJobs,
          count: mockJobs.length,
        },
      },
      fetchJobsByStatus: jest.fn().mockResolvedValue(undefined),
      isLoading: false,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId: jest.fn(),
    };

    // Mock useJobsStore to handle selector pattern
    mockUseJobsStore.mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(mockStoreState);
      }
      return mockStoreState;
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should render job list with all jobs', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Component displays jobType first, then jobTitle
    expect(screen.getByText('Full-time')).toBeInTheDocument();
    expect(screen.getByText('Contract')).toBeInTheDocument();
    expect(screen.getByText('UX Designer')).toBeInTheDocument(); // This one has no jobType
  });

  it('should show loading state', () => {
    mockStoreWithState({
      jobsByStatus: {},
      fetchJobsByStatus: jest.fn(),
      isLoading: true,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="1" />);

    expect(screen.getByText('Loading jobs...')).toBeInTheDocument();
  });

  it('should show empty state when no jobs', () => {
    mockStoreWithState({
      jobsByStatus: {
        ALL: {
          jobs: [],
          count: 0,
        },
      },
      fetchJobsByStatus: jest.fn(),
      isLoading: false,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="1" />);

    expect(screen.getByText('No jobs found')).toBeInTheDocument();
  });

  it('should highlight selected job', () => {
    mockStoreWithState({
      jobsByStatus: {
        ALL: {
          jobs: mockJobs,
          count: mockJobs.length,
        },
      },
      fetchJobsByStatus: jest.fn(),
      isLoading: false,
      selectedJob: mockJobs[0],
      selectedJobId: '1',
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="1" />);

    const selectedJobElement = screen.getByText('Full-time').closest('.group');
    expect(selectedJobElement).toHaveClass('from-purple-500/20');
  });

  it('should navigate to job when clicked', () => {
    render(<UnifiedJobComposition jobId="1" />);

    fireEvent.click(screen.getByText('Contract')); // jobType is displayed, not jobTitle

    expect(mockPush).toHaveBeenCalledWith('/jobs/2/manage');
  });

  it('should display job location correctly', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Single location
    expect(screen.getByText('San Francisco')).toBeInTheDocument();

    // Array location (should show first item)
    expect(screen.getByText('New York')).toBeInTheDocument();
  });

  it('should display candidate count badges', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Job with candidatesCount
    expect(screen.getByText('5')).toBeInTheDocument();

    // Job with totalCandidates
    expect(screen.getByText('10')).toBeInTheDocument();

    // Job with candidateStats
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('should display status icons correctly', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Check that status icons are rendered (by checking for Lucide icon components)
    const jobItems = screen.getAllByRole('heading', { level: 4 });
    expect(jobItems).toHaveLength(3);
  });

  it('should fetch jobs on mount', () => {
    const fetchJobsByStatus = jest.fn();
    mockStoreWithState({
      jobsByStatus: {},
      fetchJobsByStatus,
      isLoading: false,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="1" />);

    expect(fetchJobsByStatus).toHaveBeenCalledWith(1, 'ALL');
  });

  it('should set selected job ID on mount', () => {
    const setSelectedJobId = jest.fn();
    mockStoreWithState({
      jobsByStatus: {
        ALL: { jobs: mockJobs },
      },
      fetchJobsByStatus: jest.fn(),
      isLoading: false,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId,
    });

    render(<UnifiedJobComposition jobId="2" />);

    expect(setSelectedJobId).toHaveBeenCalledWith('2');
  });

  it('should navigate back to jobs list', () => {
    render(<UnifiedJobComposition jobId="1" />);

    const backButton = screen.getByText('Back to Jobs');
    fireEvent.click(backButton);

    expect(mockPush).toHaveBeenCalledWith('/jobs');
  });

  it('should toggle sidebar collapse', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Find collapse button (ChevronLeft icon button)
    const collapseButtons = screen.getAllByRole('button');
    const collapseButton = collapseButtons.find(
      btn => btn.querySelector('svg') && !btn.textContent?.includes('Back')
    );

    if (collapseButton) {
      fireEvent.click(collapseButton);

      // After collapse, the job titles should be hidden
      expect(screen.queryByText('Senior Developer')).not.toBeInTheDocument();
    }
  });

  it('should show UnifiedJobView when job is selected', () => {
    mockStoreWithState({
      jobsByStatus: {
        ALL: { jobs: mockJobs },
      },
      fetchJobsByStatus: jest.fn(),
      isLoading: false,
      selectedJob: mockJobs[0],
      selectedJobId: '1',
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="1" />);

    expect(screen.getByTestId('unified-job-view')).toBeInTheDocument();
    expect(screen.getByText('UnifiedJobView - Job ID: 1, Embedded: true')).toBeInTheDocument();
  });

  it('should show placeholder when no job selected', () => {
    render(<UnifiedJobComposition jobId="1" />);

    expect(screen.getByText('Select a job to view details')).toBeInTheDocument();
  });

  it('should handle mobile sidebar toggle', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Find mobile menu button
    const mobileMenuButton = screen.getAllByRole('button')[0]; // First button is the mobile menu
    fireEvent.click(mobileMenuButton);

    // Sidebar should be visible (translate-x-0 class applied)
    // Find the sidebar container (parent of Job List heading)
    const jobListHeading = screen.getByText('Job List');
    const sidebar = jobListHeading.closest('div')?.parentElement?.parentElement;
    expect(sidebar?.className).toContain('translate-x-0');
  });

  it('should close mobile sidebar when clicking overlay', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Open mobile sidebar
    const mobileMenuButton = screen.getAllByRole('button')[0];
    fireEvent.click(mobileMenuButton);

    // Click overlay (the div with bg-black/50 class)
    const overlay = document.querySelector('.bg-black\\/50');
    if (overlay) {
      fireEvent.click(overlay);
    }

    // Sidebar should be hidden again
    const jobListHeading = screen.getByText('Job List');
    const sidebar = jobListHeading.closest('div')?.parentElement?.parentElement;
    expect(sidebar?.className).toContain('-translate-x-full');
  });

  it('should format dates correctly', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Check date formatting (Jan 1, Jan 2, Jan 3)
    expect(screen.getByText('Jan 1')).toBeInTheDocument();
    expect(screen.getByText('Jan 2')).toBeInTheDocument();
    expect(screen.getByText('Jan 3')).toBeInTheDocument();
  });

  it('should handle jobs without location gracefully', () => {
    const jobsWithoutLocation = [
      {
        id: '4',
        jobTitle: 'No Location Job',
        status: 'NEW',
        createdAt: new Date('2024-01-04'),
      } as IJob,
    ];

    mockStoreWithState({
      jobsByStatus: {
        ALL: { jobs: jobsWithoutLocation },
      },
      fetchJobsByStatus: jest.fn(),
      isLoading: false,
      selectedJob: null,
      selectedJobId: null,
      setSelectedJobId: jest.fn(),
    });

    render(<UnifiedJobComposition jobId="4" />);

    expect(screen.getByText('No Location Job')).toBeInTheDocument();
    // Should not crash when location is missing
  });

  it('should handle collapsed sidebar tooltips', async () => {
    render(<UnifiedJobComposition jobId="1" />);

    // Collapse the sidebar
    const collapseButtons = screen.getAllByRole('button');
    const collapseButton = collapseButtons.find(
      btn => btn.querySelector('svg') && !btn.textContent?.includes('Back')
    );

    if (collapseButton) {
      fireEvent.click(collapseButton);

      // In collapsed state, hovering should show tooltip
      // Note: Since we mocked Tooltip components, we can't test actual tooltip behavior
      // but we can verify the structure is there
      const jobItems = document.querySelectorAll('.group');
      expect(jobItems.length).toBeGreaterThan(0);
    }
  });

  it('should auto-collapse sidebar on smaller screens', () => {
    // Mock window width to be small
    global.innerWidth = 800;
    global.dispatchEvent(new Event('resize'));

    render(<UnifiedJobComposition jobId="1" />);

    // Wait for resize handler
    waitFor(() => {
      const sidebar = document.querySelector('[class*="w-80"]');
      expect(sidebar).toHaveClass('w-20');
    });
  });

  it('should display correct status colors', () => {
    render(<UnifiedJobComposition jobId="1" />);

    // We can't directly test SVG colors due to mocking, but we can verify
    // that different status jobs are rendered by checking for their text
    expect(screen.getByText('Full-time')).toBeInTheDocument();
    expect(screen.getByText('Contract')).toBeInTheDocument();
    expect(screen.getByText('UX Designer')).toBeInTheDocument();

    // Each job should have different status
    expect(mockJobs[0].status).toBe('NEW');
    expect(mockJobs[1].status).toBe('MATCHED');
    expect(mockJobs[2].status).toBe('INTERVIEWING');
  });
});
