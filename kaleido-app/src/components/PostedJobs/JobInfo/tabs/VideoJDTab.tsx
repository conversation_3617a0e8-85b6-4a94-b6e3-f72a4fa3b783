import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { LeftColumn } from '@/components/VideoJD/components/LeftColumn';
import LoadingDisplay from '@/components/VideoJD/components/LoadingDisplay';
import { RightColumn } from '@/components/VideoJD/components/RightColumn';
import { VideoHistoryModal } from '@/components/VideoJD/components/VideoHistoryModal';
import { VideoJDStatus } from '@/components/VideoJD/consts';
import { VideoJDProvider } from '@/components/VideoJD/context/VideoJDContext';
import { IJob } from '@/entities/interfaces';
import apiHelper from '@/lib/apiHelper';
import { useVideoRecordingStore } from '@/stores/videoRecordingStore';
import { motion } from 'framer-motion';
import { FileVideo, History, Sparkles, Video } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface VideoJDTabProps {
  job: IJob;
}

export const VideoJDTab: React.FC<VideoJDTabProps> = ({ job }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [videoJDData, setVideoJDData] = useState<any>(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const { clearAll } = useVideoRecordingStore();

  // Fetch video JD data when component mounts
  useEffect(() => {
    const fetchVideoJDData = async () => {
      setIsLoading(true);
      try {
        const videoJDs = await apiHelper.get(`/video-jd/job/${job.id}`);

        if (videoJDs && videoJDs.length > 0) {
          const latestVideoJD = videoJDs[0];
          const jobData = {
            id: job.id,
            jobType: latestVideoJD.jobType || job.jobType || 'Unknown Position',
            clientId: latestVideoJD.clientId || job.clientId || '',
            videoJDs: videoJDs,
          };
          setVideoJDData(jobData);
        } else {
          // Create minimal job data if no video JDs found
          setVideoJDData({
            id: job.id,
            jobType: job.jobType || 'Unknown Position',
            clientId: job.clientId || '',
            videoJDs: [],
          });
        }
      } catch (error) {
        console.error('Error fetching video JD data:', error);
        // Create minimal job data on error
        setVideoJDData({
          id: job.id,
          jobType: job.jobType || 'Unknown Position',
          clientId: job.clientId || '',
          videoJDs: [],
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideoJDData();
  }, [job.id, job.jobType, job.clientId]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      clearAll();
    };
  }, [clearAll]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <ColourfulLoader text="Loading video JD data..." />
      </div>
    );
  }

  // Get the latest video JD data for display
  const latestVideoJD = videoJDData?.videoJDs?.[0];
  const isGenerating =
    latestVideoJD?.status === VideoJDStatus.GENERATING ||
    latestVideoJD?.status === VideoJDStatus.PENDING;

  return (
    <div className="w-full">
      {/* Hero Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden mb-8 -mt-8 -mx-8"
      >
        {/* Background with image and overlay */}
        <div className="relative h-[280px] overflow-hidden">
          {/* Background Image */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url('/images/insights/performance_analytics_turquoise.png')`,
              backgroundPosition: 'center 20%',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Gradient overlay - transparent at top, darker at bottom */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-blue-900/80" />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent" />

          {/* Content */}
          <div className="relative h-full flex items-center px-8 lg:px-12">
            <div className="max-w-7xl mx-auto w-full">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center relative"
              >
                <div className="flex justify-center mb-3">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
                    className="p-3 bg-white/20 backdrop-blur-md rounded-2xl border border-white/30"
                  >
                    <Video className="w-10 h-10 text-white" />
                  </motion.div>
                </div>

                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-3xl lg:text-4xl font-bold text-white mb-2"
                >
                  Video Job Description
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-base text-white/80 max-w-xl mx-auto mb-5"
                >
                  Create engaging video content with AI avatars or live recordings
                </motion.p>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex justify-center gap-4"
                >
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5">
                      <FileVideo className="w-4 h-4 text-white/70" />
                      <span className="text-sm font-semibold text-white">
                        {videoJDData?.videoJDs?.length || 0}
                      </span>
                    </div>
                    <p className="text-[10px] text-white/50">Video Versions</p>
                  </div>

                  <div className="w-px h-7 bg-white/15" />

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5">
                      <Sparkles className="w-4 h-4 text-white/70" />
                      <span className="text-sm font-semibold text-white">
                        {latestVideoJD ? 'Active' : 'Ready'}
                      </span>
                    </div>
                    <p className="text-[10px] text-white/50">Status</p>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>

          {/* View History Button - Positioned at bottom right */}
          {videoJDData?.videoJDs && videoJDData?.videoJDs?.length > 0 && (
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              onClick={() => setIsHistoryModalOpen(true)}
              className="absolute bottom-6 right-6 lg:right-8 flex items-center gap-2.5 px-5 py-2.5 bg-white/5 backdrop-blur-md hover:bg-white/25 border border-white/10 hover:border-white/30 transition-all rounded-xl text-white shadow-lg hover:shadow-xl"
            >
              <History className="w-4 h-4" />
              <span className="text-xs font-medium">View History</span>
              <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full">
                {videoJDData.videoJDs.length}
              </span>
            </motion.button>
          )}
        </div>
      </motion.div>

      {/* Main Content - Full Width */}
      <div className="px-8 pb-8">
        <VideoJDProvider job={videoJDData}>
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* Left Column - Script Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <LeftColumn />
            </motion.div>

            {/* Right Column - Video Preview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="sticky top-8">
                <RightColumn />
              </div>
            </motion.div>
          </div>
          {latestVideoJD?.synthesiaVideoId && (
            <LoadingDisplay
              isGenerating={isGenerating}
              onClose={() => {}}
              status={latestVideoJD?.status}
            />
          )}
        </VideoJDProvider>
      </div>

      {/* Video History Modal */}
      <VideoHistoryModal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        history={videoJDData?.videoJDs || []}
      />
    </div>
  );
};
