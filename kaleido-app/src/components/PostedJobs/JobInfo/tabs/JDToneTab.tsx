import { showToast } from '@/components/Toaster';
import { IJob } from '@/entities/interfaces';
import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { motion } from 'framer-motion';
import {
  Baby,
  Briefcase,
  Coffee,
  Edit3,
  FileText,
  Heart,
  Loader2,
  MessageCircle,
  MessageSquare,
  Palette,
  RefreshCw,
  Save,
  Smartphone,
  Sparkles,
  Star,
  Zap,
} from 'lucide-react';
import Markdown from 'markdown-to-jsx';
import React, { useEffect, useState } from 'react';

interface JDToneTabProps {
  job: IJob;
}

const toneIcons = {
  professional: Briefcase,
  friendly: Heart,
  casual: Coffee,
  enthusiastic: Zap,
  formal: Star,
  conversational: MessageCircle,
  'like for a 5 year old': Baby,
  'gen z': Smartphone,
};

const toneColors = {
  professional: 'from-blue-500 to-blue-600',
  friendly: 'from-pink-500 to-pink-600',
  casual: 'from-orange-500 to-orange-600',
  enthusiastic: 'from-yellow-500 to-yellow-600',
  formal: 'from-purple-500 to-purple-600',
  conversational: 'from-green-500 to-green-600',
  'like for a 5 year old': 'from-cyan-500 to-cyan-600',
  'gen z': 'from-indigo-500 to-indigo-600',
};

export const JDToneTab: React.FC<JDToneTabProps> = ({ job }) => {
  const [selectedTone, setSelectedTone] = useState<string>(job.generatedJDTone || 'professional');
  const [generatedJD, setGeneratedJD] = useState<string>(job.generatedJD || '');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const { updateJob } = useJobsStore();

  // Track changes
  useEffect(() => {
    const hasJDChanged = generatedJD !== (job.generatedJD || '');
    // Only consider it a saveable change if the JD has changed and tone hasn't changed
    // If tone changed, user needs to generate first
    const hasToneChanged = selectedTone !== (job.generatedJDTone || 'professional');
    setHasChanges(hasJDChanged && !hasToneChanged);
  }, [generatedJD, selectedTone, job.generatedJD, job.generatedJDTone]);

  const handleGenerateJD = async () => {
    if (!selectedTone || !job.id) return;

    setIsGenerating(true);
    try {
      const response = await apiHelper.post('/jobs/generate-tone', {
        jobId: job.id,
        tone: selectedTone,
      });

      if (response && response.generatedJD) {
        setGeneratedJD(response.generatedJD);
        // Update the selected tone to be the current tone after successful generation
        if (job.generatedJDTone !== selectedTone) {
          job.generatedJDTone = selectedTone;
        }
        showToast({
          message: 'Job description generated successfully with new tone',
          isSuccess: true,
        });
      } else if (response.success && response.generatedJD) {
        // Fallback for old API format
        setGeneratedJD(response.generatedJD);
        if (job.generatedJDTone !== selectedTone) {
          job.generatedJDTone = selectedTone;
        }
        showToast({
          message: 'Job description generated successfully with new tone',
          isSuccess: true,
        });
      } else {
        throw new Error(response?.message || 'Failed to generate job description');
      }
    } catch (error) {
      console.error('Error generating JD:', error);
      showToast({
        message: 'Failed to generate job description',
        isSuccess: false,
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = async () => {
    if (!hasChanges || !job.id) return;

    setIsSaving(true);
    try {
      await updateJob(job.id, {
        generatedJD: generatedJD,
        generatedJDTone: selectedTone,
      });

      showToast({
        message: 'Job description saved successfully',
        isSuccess: true,
      });
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving JD:', error);
      showToast({
        message: 'Failed to save job description',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleJDChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setGeneratedJD(e.target.value);
  };

  // Preprocess content to ensure proper line breaks
  const preprocessMarkdown = (content: string) => {
    return (
      content
        // Ensure each key:value pair is on its own line with proper spacing
        .replace(/^([^:\n]+):\s*(.+)$/gm, (match, key, value) => {
          return `**${key}:** ${value}  `;
        })
        // Add line breaks between different sections
        .replace(/\n/g, '  \n')
        // Clean up excessive line breaks
        .replace(/(\s*\n){3,}/g, '\n\n')
    );
  };

  return (
    <div className="w-full">
      {/* Hero Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden mb-8 -mt-8 -mx-8"
      >
        {/* Background with image and overlay */}
        <div className="relative h-[280px] overflow-hidden">
          {/* Background Image */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url('/images/insights/innovation_technology_revised.png')`,
              backgroundPosition: 'center 20%',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Dynamic color overlay based on selected tone - transparent at top */}
          <div className="absolute inset-0">
            <div
              className={`absolute inset-0 bg-gradient-to-b from-transparent via-purple-800/40 to-purple-900/70`}
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 via-transparent to-transparent" />

          {/* Content */}
          <div className="relative h-full flex items-center px-8 lg:px-12">
            <div className="max-w-7xl mx-auto w-full">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <div className="flex justify-center mb-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
                    className="p-4 bg-white/20 backdrop-blur-md rounded-2xl border border-white/30"
                  >
                    <Palette className="w-12 h-12 text-white" />
                  </motion.div>
                </div>

                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-3xl lg:text-4xl font-bold text-white mb-3"
                >
                  Craft Your Perfect Job Description
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-lg text-white/90 max-w-2xl mx-auto"
                >
                  Choose a tone that matches your company culture and generate compelling job
                  descriptions that attract the right candidates
                </motion.p>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Tone Selection Section - Full Width */}
      <div className="px-8 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-4 border border-white/[0.05]"
        >
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2 text-white">
              <Sparkles className="w-4 h-4 text-purple-400" />
              Select Tone
            </h3>

            {/* Tone Grid - Full width */}
            <div className="grid grid-cols-4 lg:grid-cols-8 gap-2">
              {Object.entries(toneIcons).map(([tone, icon]) => (
                <motion.button
                  key={tone}
                  onClick={() => setSelectedTone(tone)}
                  disabled={isGenerating}
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  className={`
                    flex flex-col items-center p-2 rounded-md border transition-all duration-200
                    ${
                      selectedTone === tone
                        ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-400/30'
                        : 'bg-white/[0.01] border-white/[0.03] hover:bg-white/[0.02] hover:border-white/[0.05]'
                    }
                    ${isGenerating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  `}
                >
                  <div
                    className={`p-1.5 rounded ${
                      selectedTone === tone ? 'bg-white/10' : 'bg-white/[0.03]'
                    }`}
                  >
                    {React.createElement(icon, {
                      className: `w-3.5 h-3.5 ${selectedTone === tone ? 'text-purple-400' : 'text-gray-500'}`,
                    })}
                  </div>
                  <p
                    className={`text-[10px] font-medium capitalize mt-1 text-center ${
                      selectedTone === tone ? 'text-purple-300' : 'text-gray-500'
                    }`}
                  >
                    {tone === 'like for a 5 year old'
                      ? '5 year old'
                      : tone === 'gen z'
                        ? 'Gen Z'
                        : tone}
                  </p>
                </motion.button>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-8 pb-8 space-y-6">
        {/* Generated JD Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl border border-white/[0.05]"
        >
          <div className="p-6 border-b border-gray-700/50">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-xl font-semibold flex items-center gap-3 text-white">
                <FileText className="w-5 h-5 text-blue-400" />
                Generated Job Description
              </h3>
              {!generatedJD || selectedTone !== (job.generatedJDTone || 'professional') ? (
                <motion.button
                  onClick={handleGenerateJD}
                  disabled={isGenerating || !job.id}
                  whileHover={!isGenerating && job.id ? { scale: 1.02 } : {}}
                  whileTap={!isGenerating && job.id ? { scale: 0.98 } : {}}
                  className={`
                    flex items-center gap-2 px-6 py-2.5 rounded-lg font-medium text-sm transition-all shadow-lg
                    ${
                      isGenerating || !job.id
                        ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                        : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 hover:shadow-xl'
                    }
                  `}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4" />
                      {!generatedJD ? 'Generate JD' : 'Regenerate JD'}
                    </>
                  )}
                </motion.button>
              ) : (
                hasChanges && (
                  <motion.button
                    onClick={handleSave}
                    disabled={isSaving}
                    whileHover={!isSaving ? { scale: 1.02 } : {}}
                    whileTap={!isSaving ? { scale: 0.98 } : {}}
                    className={`
                      flex items-center gap-2 px-6 py-2.5 rounded-lg font-medium text-sm transition-all shadow-lg
                      ${
                        isSaving
                          ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                          : 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 hover:shadow-xl'
                      }
                    `}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Save Changes
                      </>
                    )}
                  </motion.button>
                )
              )}
            </div>
            {generatedJD && (
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2 text-gray-400">
                  {React.createElement(
                    toneIcons[(job.generatedJDTone || 'professional') as keyof typeof toneIcons],
                    { className: 'w-3.5 h-3.5' }
                  )}
                  <span>
                    Current:{' '}
                    <strong className="text-gray-300 capitalize">
                      {job.generatedJDTone || 'professional'}
                    </strong>
                  </span>
                </div>
                {selectedTone !== (job.generatedJDTone || 'professional') && (
                  <>
                    <span className="text-gray-600">→</span>
                    <div className="flex items-center gap-2 text-purple-400">
                      {React.createElement(toneIcons[selectedTone as keyof typeof toneIcons], {
                        className: 'w-3.5 h-3.5',
                      })}
                      <span>
                        Selected: <strong className="capitalize">{selectedTone}</strong>
                      </span>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>

          <div className="p-6">
            {generatedJD ? (
              <div className="space-y-4">
                <div className="flex justify-end mb-2">
                  <motion.button
                    onClick={() => setIsEditMode(!isEditMode)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium bg-gray-800/50 text-gray-300 hover:bg-gray-800/70 hover:text-white transition-all"
                  >
                    <Edit3 className="w-4 h-4" />
                    {isEditMode ? 'Preview' : 'Edit'}
                  </motion.button>
                </div>

                {isEditMode ? (
                  <>
                    <textarea
                      value={generatedJD}
                      onChange={handleJDChange}
                      className="w-full min-h-[400px] p-6 rounded-xl bg-gray-900/50 border border-gray-700/50 text-gray-200 resize-y focus:outline-none focus:border-purple-500/50 transition-colors font-mono text-sm"
                      placeholder="Generated job description will appear here..."
                    />
                    <div className="flex items-start gap-2 text-sm text-gray-400">
                      <MessageSquare className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <p>
                        You can edit the generated content above. Changes will be highlighted until
                        saved.
                      </p>
                    </div>
                  </>
                ) : (
                  <div className="min-h-[400px] p-6 rounded-xl bg-gray-900/50 border border-gray-700/50 overflow-y-auto">
                    <Markdown
                      options={{
                        forceBlock: true,
                        overrides: {
                          h1: {
                            props: { className: 'text-3xl font-bold mb-6 mt-8 text-white' },
                          },
                          h2: {
                            props: { className: 'text-2xl font-semibold mb-4 mt-6 text-white' },
                          },
                          h3: {
                            props: { className: 'text-xl font-semibold mb-3 mt-5 text-white' },
                          },
                          p: {
                            props: { className: 'text-gray-200 mb-4 leading-relaxed' },
                          },
                          ul: {
                            props: { className: 'list-disc pl-6 mb-4 space-y-2 text-gray-200' },
                          },
                          ol: {
                            props: { className: 'list-decimal pl-6 mb-4 space-y-2 text-gray-200' },
                          },
                          li: {
                            props: { className: 'mb-2 text-gray-200' },
                          },
                          strong: {
                            props: { className: 'font-semibold text-white' },
                          },
                          em: {
                            props: { className: 'italic text-gray-300' },
                          },
                          code: {
                            props: {
                              className: 'text-purple-300 bg-gray-800 px-1.5 py-0.5 rounded',
                            },
                          },
                          pre: {
                            props: { className: 'bg-gray-800 p-4 rounded-lg overflow-x-auto my-6' },
                          },
                          blockquote: {
                            props: {
                              className:
                                'border-l-4 border-gray-600 pl-4 italic my-6 text-gray-300',
                            },
                          },
                          a: {
                            props: { className: 'text-purple-400 hover:text-purple-300' },
                          },
                          hr: {
                            props: { className: 'border-gray-700 my-8' },
                          },
                        },
                      }}
                    >
                      {preprocessMarkdown(generatedJD)}
                    </Markdown>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="p-4 bg-gray-800/30 rounded-full mb-4">
                  <MessageSquare className="w-12 h-12 text-gray-500" />
                </div>
                <p className="text-gray-300 text-lg mb-2">No generated job description yet</p>
                <p className="text-gray-400 max-w-md">
                  Select a tone above and click "Generate" to create a compelling job description
                  tailored to your chosen voice.
                </p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Tone Guide - Minimal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gradient-to-br from-white/[0.01] to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/[0.03]"
        >
          <div className="flex items-center gap-8 text-xs text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-400/50 rounded-full" />
              <span>
                <strong className="text-gray-400">Professional</strong>: Corporate &
                business-focused
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-pink-400/50 rounded-full" />
              <span>
                <strong className="text-gray-400">Friendly</strong>: Warm & welcoming
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-yellow-400/50 rounded-full" />
              <span>
                <strong className="text-gray-400">Enthusiastic</strong>: High-energy & dynamic
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
