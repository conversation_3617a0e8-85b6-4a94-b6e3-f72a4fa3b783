import React from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useCurrentUser } from '@/services/user.service';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { render, screen, waitFor } from '@testing-library/react';

import JobsByStatus from './JobsByStatus';

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock('@/services/user.service', () => ({
  useCurrentUser: jest.fn(),
}));

jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: Object.assign(jest.fn(), {
    getState: jest.fn(() => ({
      clearJobData: jest.fn(),
    })),
  }),
}));

jest.mock('@/hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(() => false),
}));

// Mock PostedJobDetails to return a simple component
jest.mock('./PostedJobDetails', () => {
  return {
    PostedJobDetails: ({ job }: any) => (
      <div data-testid="posted-job-details">
        <div data-testid="job-id">{job.id}</div>
        <div data-testid="job-status">{job.status}</div>
      </div>
    ),
  };
});

describe('JobsByStatus Integration Tests', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
  };

  const mockUser = {
    sub: 'test-user-id',
    name: 'Test User',
  };

  // Helper function to mock useJobsStore with selector pattern
  const mockUseJobsStore = (storeData: any) => {
    (useJobsStore as unknown as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(storeData);
      }
      return storeData;
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useCurrentUser as jest.Mock).mockReturnValue(mockUser);
  });

  describe('Core Functionality', () => {
    it('should call setSelectedJobId when jobId is present in URL', () => {
      const setSelectedJobId = jest.fn();
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };

      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        jobsByStatus: { ALL: { jobs: [], count: 0 } },
        jobsByStatusPagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 0,
          itemsPerPage: 10,
        },
        isLoading: false,
        fetchJobsByStatus: jest.fn(),
        setSelectedJobId,
        selectedJob: null,
        selectedJobId: null,
      });

      render(<JobsByStatus />);

      expect(setSelectedJobId).toHaveBeenCalledWith('test-job-id');
    });

    it('should render correctly when job status is NEW', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'NEW',
        jobType: 'Software Engineer',
        candidates: [],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };

      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } },
        jobsByStatusPagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10,
        },
        isLoading: false,
        fetchJobsByStatus: jest.fn(),
        setSelectedJobId: jest.fn(),
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
      });

      render(<JobsByStatus />);

      // Verify component renders correctly
      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByTestId('job-status')).toHaveTextContent('NEW');

      // Component should not perform redirects - navigation is handled at page level
      expect(mockRouter.replace).not.toHaveBeenCalled();
    });

    it('should render correctly when job has no candidates', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'MATCHED',
        jobType: 'Software Engineer',
        candidates: [],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };

      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        jobsByStatus: { ALL: { jobs: [mockJob], count: 1 } },
        jobsByStatusPagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10,
        },
        isLoading: false,
        fetchJobsByStatus: jest.fn(),
        setSelectedJobId: jest.fn(),
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
      });

      render(<JobsByStatus />);

      // Verify component renders correctly
      expect(screen.getByTestId('posted-job-details')).toBeInTheDocument();
      expect(screen.getByTestId('job-status')).toHaveTextContent('MATCHED');

      // Component should not perform redirects - navigation is handled at page level
      expect(mockRouter.replace).not.toHaveBeenCalled();
    });

    it('should render correctly when job has candidates', async () => {
      const mockJob = {
        id: 'test-job-id',
        status: 'MATCHED',
        jobType: 'Software Engineer',
        candidates: [{ id: 'candidate-1' }],
      };

      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          if (key === 'mode') return 'ranked';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id&mode=ranked'),
        entries: jest.fn(() => [
          ['jobId', 'test-job-id'],
          ['mode', 'ranked'],
        ]),
      };

      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        jobsByStatus: { ALL: { jobs: [], count: 0 } },
        jobsByStatusPagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 0,
          itemsPerPage: 10,
        },
        isLoading: false,
        fetchJobsByStatus: jest.fn(),
        setSelectedJobId: jest.fn(),
        selectedJob: mockJob,
        selectedJobId: 'test-job-id',
      });

      render(<JobsByStatus />);

      // Wait a bit to ensure no redirect happens
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockRouter.replace).not.toHaveBeenCalled();
    });

    it('should not fetch jobs when jobId is present', () => {
      const fetchJobsByStatus = jest.fn();
      const mockSearchParams = {
        get: jest.fn((key: string) => {
          if (key === 'jobId') return 'test-job-id';
          return null;
        }),
        toString: jest.fn(() => 'jobId=test-job-id'),
        entries: jest.fn(() => [['jobId', 'test-job-id']]),
      };

      (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
      mockUseJobsStore({
        jobsByStatus: { ALL: { jobs: [], count: 0 } },
        jobsByStatusPagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 0,
          itemsPerPage: 10,
        },
        isLoading: false,
        fetchJobsByStatus,
        setSelectedJobId: jest.fn(),
        selectedJob: null,
        selectedJobId: null,
      });

      render(<JobsByStatus />);

      expect(fetchJobsByStatus).not.toHaveBeenCalled();
    });
  });
});
