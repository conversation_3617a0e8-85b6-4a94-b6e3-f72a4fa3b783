import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BurgerMenu, NavigationBurgerMenu } from '../BurgerMenu';

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Menu: ({ className, ...props }: any) => (
    <div data-testid="menu-icon" className={className} {...props} />
  ),
  X: ({ className, ...props }: any) => (
    <div data-testid="x-icon" className={className} {...props} />
  ),
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('BurgerMenu', () => {
  const defaultProps = {
    isOpen: false,
    onToggle: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<BurgerMenu {...defaultProps} />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders menu icon when closed', () => {
      render(<BurgerMenu {...defaultProps} isOpen={false} />);
      expect(screen.getByTestId('menu-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('x-icon')).not.toBeInTheDocument();
    });

    it('renders close icon when open', () => {
      render(<BurgerMenu {...defaultProps} isOpen={true} />);
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('menu-icon')).not.toBeInTheDocument();
    });

    it('calls onToggle when clicked', () => {
      const mockOnToggle = jest.fn();
      render(<BurgerMenu {...defaultProps} onToggle={mockOnToggle} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnToggle).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('has correct aria-label when closed', () => {
      render(<BurgerMenu {...defaultProps} isOpen={false} />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Open menu');
    });

    it('has correct aria-label when open', () => {
      render(<BurgerMenu {...defaultProps} isOpen={true} />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Close menu');
    });

    it('is keyboard accessible', () => {
      const mockOnToggle = jest.fn();
      render(<BurgerMenu {...defaultProps} onToggle={mockOnToggle} />);

      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: 'Enter' });

      // The button should be focusable and activatable with keyboard
      expect(button).toBeInTheDocument();
    });
  });

  describe('Size Variants', () => {
    it('applies small size classes correctly', () => {
      render(<BurgerMenu {...defaultProps} size="sm" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-1.5');

      const icon = screen.getByTestId('menu-icon');
      expect(icon).toHaveClass('w-4', 'h-4');
    });

    it('applies medium size classes correctly (default)', () => {
      render(<BurgerMenu {...defaultProps} size="md" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-2');

      const icon = screen.getByTestId('menu-icon');
      expect(icon).toHaveClass('w-5', 'h-5');
    });

    it('applies large size classes correctly', () => {
      render(<BurgerMenu {...defaultProps} size="lg" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-3');

      const icon = screen.getByTestId('menu-icon');
      expect(icon).toHaveClass('w-6', 'h-6');
    });

    it('defaults to medium size when no size prop provided', () => {
      render(<BurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-2');

      const icon = screen.getByTestId('menu-icon');
      expect(icon).toHaveClass('w-5', 'h-5');
    });
  });

  describe('Variant Styles', () => {
    it('applies default variant styles', () => {
      render(<BurgerMenu {...defaultProps} variant="default" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'bg-gray-800/50',
        'backdrop-blur-sm',
        'border',
        'border-gray-700/50',
        'text-gray-300'
      );
    });

    it('applies integrated variant styles', () => {
      render(<BurgerMenu {...defaultProps} variant="integrated" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'bg-transparent',
        'hover:bg-white/10',
        'border-none',
        'text-gray-300'
      );
      expect(button).not.toHaveClass('bg-gray-800/50');
    });

    it('applies floating variant styles', () => {
      render(<BurgerMenu {...defaultProps} variant="floating" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'bg-gray-800/50',
        'backdrop-blur-sm',
        'border',
        'border-gray-700/50',
        'text-gray-300'
      );
    });

    it('defaults to default variant when no variant prop provided', () => {
      render(<BurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-gray-800/50');
    });
  });

  describe('Custom Classes', () => {
    it('applies custom className', () => {
      render(<BurgerMenu {...defaultProps} className="custom-class" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('merges custom className with existing classes', () => {
      render(<BurgerMenu {...defaultProps} className="custom-class" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class', 'rounded-lg', 'transition-all');
    });
  });

  describe('Animation and Transitions', () => {
    it('applies transition classes', () => {
      render(<BurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('transition-all', 'duration-200');
    });

    it('applies hover effects', () => {
      render(<BurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('hover:bg-gray-800/70');
    });

    it('handles state change animations', async () => {
      const { rerender } = render(<BurgerMenu {...defaultProps} isOpen={false} />);

      expect(screen.getByTestId('menu-icon')).toBeInTheDocument();

      rerender(<BurgerMenu {...defaultProps} isOpen={true} />);

      await waitFor(() => {
        expect(screen.getByTestId('x-icon')).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles rapid toggle clicks', () => {
      const mockOnToggle = jest.fn();
      render(<BurgerMenu {...defaultProps} onToggle={mockOnToggle} />);

      const button = screen.getByRole('button');

      // Rapidly click multiple times
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(mockOnToggle).toHaveBeenCalledTimes(3);
    });

    it('handles undefined onToggle gracefully', () => {
      expect(() => {
        render(<BurgerMenu isOpen={false} onToggle={undefined as any} />);
      }).not.toThrow();
    });

    it('renders correctly with all props combined', () => {
      render(
        <BurgerMenu
          isOpen={true}
          onToggle={jest.fn()}
          variant="integrated"
          size="lg"
          className="custom-class"
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-3', 'bg-transparent', 'custom-class');
      expect(screen.getByTestId('x-icon')).toHaveClass('w-6', 'h-6');
    });
  });
});

describe('NavigationBurgerMenu', () => {
  const defaultProps = {
    isOpen: false,
    onToggle: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders BurgerMenu component', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);
      expect(screen.getByTestId('menu-icon')).toBeInTheDocument();
    });

    it('displays title when provided', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="Test Title" />);
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });

    it('does not display title when not provided', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);
      expect(screen.queryByText(/Test Title/)).not.toBeInTheDocument();
    });

    it('applies custom className to container', () => {
      render(<NavigationBurgerMenu {...defaultProps} className="custom-nav-class" />);

      const container = screen.getByRole('button').parentElement;
      expect(container).toHaveClass('custom-nav-class');
    });
  });

  describe('BurgerMenu Integration', () => {
    it('passes isOpen prop to BurgerMenu', () => {
      render(<NavigationBurgerMenu {...defaultProps} isOpen={true} />);
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });

    it('passes onToggle prop to BurgerMenu', () => {
      const mockOnToggle = jest.fn();
      render(<NavigationBurgerMenu {...defaultProps} onToggle={mockOnToggle} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnToggle).toHaveBeenCalledTimes(1);
    });

    it('uses integrated variant for BurgerMenu', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-transparent');
    });

    it('uses medium size for BurgerMenu', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('p-2');

      const icon = screen.getByTestId('menu-icon');
      expect(icon).toHaveClass('w-5', 'h-5');
    });
  });

  describe('Layout and Styling', () => {
    it('applies correct container classes', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="Test Title" />);

      const container = screen.getByRole('button').parentElement;
      expect(container).toHaveClass('flex', 'items-center', 'gap-3');
    });

    it('applies correct title styling', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="Test Title" />);

      const title = screen.getByText('Test Title');
      expect(title).toHaveClass('text-lg', 'font-medium', 'text-gray-200', 'hidden', 'sm:block');
    });

    it('hides title on small screens', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="Test Title" />);

      const title = screen.getByText('Test Title');
      expect(title).toHaveClass('hidden', 'sm:block');
    });
  });

  describe('Responsive Behavior', () => {
    it('shows title only on larger screens', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="Test Title" />);

      const title = screen.getByText('Test Title');
      expect(title).toHaveClass('hidden', 'sm:block');
    });

    it('maintains burger menu visibility on all screen sizes', () => {
      render(<NavigationBurgerMenu {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).not.toHaveClass('hidden');
    });
  });

  describe('State Changes', () => {
    it('updates burger menu state correctly', () => {
      const { rerender } = render(
        <NavigationBurgerMenu {...defaultProps} isOpen={false} title="Navigation" />
      );

      expect(screen.getByTestId('menu-icon')).toBeInTheDocument();

      rerender(<NavigationBurgerMenu {...defaultProps} isOpen={true} title="Navigation" />);

      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });

    it('maintains title visibility during state changes', () => {
      const { rerender } = render(
        <NavigationBurgerMenu {...defaultProps} isOpen={false} title="Navigation" />
      );

      expect(screen.getByText('Navigation')).toBeInTheDocument();

      rerender(<NavigationBurgerMenu {...defaultProps} isOpen={true} title="Navigation" />);

      expect(screen.getByText('Navigation')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('maintains burger menu accessibility features', () => {
      render(<NavigationBurgerMenu {...defaultProps} isOpen={false} />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Open menu');
    });

    it('updates accessibility labels correctly', () => {
      const { rerender } = render(<NavigationBurgerMenu {...defaultProps} isOpen={false} />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Open menu');

      rerender(<NavigationBurgerMenu {...defaultProps} isOpen={true} />);

      expect(button).toHaveAttribute('aria-label', 'Close menu');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty title gracefully', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="" />);
      // Empty title should not render a span element
      const spans = screen.queryAllByText('', { exact: false });
      const titleSpans = spans.filter(span => span.tagName === 'SPAN');
      expect(titleSpans.length).toBe(0);
    });

    it('handles whitespace-only title', () => {
      render(<NavigationBurgerMenu {...defaultProps} title="   " />);
      // Should render the whitespace title (React trims it, but it still renders a span)
      const spans = screen.getAllByRole('generic').filter(el => el.tagName === 'SPAN');
      expect(spans.length).toBeGreaterThan(0);
    });

    it('handles very long titles', () => {
      const longTitle = 'This is a very long title that might cause layout issues in some cases';
      render(<NavigationBurgerMenu {...defaultProps} title={longTitle} />);
      expect(screen.getByText(longTitle)).toBeInTheDocument();
    });

    it('works without any optional props', () => {
      expect(() => {
        render(<NavigationBurgerMenu isOpen={false} onToggle={jest.fn()} />);
      }).not.toThrow();
    });

    it('handles all props together', () => {
      const mockOnToggle = jest.fn();
      render(
        <NavigationBurgerMenu
          isOpen={true}
          onToggle={mockOnToggle}
          title="Complete Navigation"
          className="complete-nav-class"
        />
      );

      expect(screen.getByText('Complete Navigation')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();

      const container = screen.getByRole('button').parentElement;
      expect(container).toHaveClass('complete-nav-class');

      const button = screen.getByRole('button');
      fireEvent.click(button);
      expect(mockOnToggle).toHaveBeenCalledTimes(1);
    });
  });
});
