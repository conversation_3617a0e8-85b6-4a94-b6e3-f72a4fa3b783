import { InputComponentProps, sizeClasses, variantClasses } from '../types';
import React, { useState } from 'react';

import InfoTooltip from '../InfoTooltip';
import { PhoneInput } from '../../ui/PhoneInput';
import { PhoneValidationResult } from '@/utils/phoneValidation';
import RequiredFieldIndicator from '../RequiredFieldIndicator';
import Tooltip from '@mui/material/Tooltip';
import { cn } from '@/lib/utils';
import { useTheme } from '../styles/themeHelpers';

interface StyledInputProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement>,
    InputComponentProps {
  multiline?: boolean;
  rows?: number;
  infoTooltip?: {
    title: string;
    description: string;
  };
  placeholder?: string;
  inputType?: 'text' | 'phone';
  onPhoneValidationChange?: (validation: PhoneValidationResult) => void;
  required?: boolean;
}

const StyledInput: React.FC<StyledInputProps> = ({
  label,
  multiline = false,
  rows = 6,
  error,
  suggestions = [],
  onSuggestionClick,
  variant = 'dark',
  icon,
  className,
  tooltip,
  disabled = false,
  inputSize = 'md',
  infoTooltip,
  placeholder = 'Type to search or Press Enter to add new',
  inputType = 'text',
  onPhoneValidationChange,
  required = false,
  ...props
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  // Use the theme hook instead of manual theme management
  const currentTheme = useTheme(variant);

  const handleSuggestionClick = (suggestion: string) => {
    if (!disabled && onSuggestionClick) {
      onSuggestionClick(suggestion);
      setShowSuggestions(false);
    }
  };

  // Use the current theme from the hook
  const effectiveVariant = currentTheme || variant;

  const inputContent = (
    <div className={cn('relative', className)}>
      {(label || icon || infoTooltip) && (
        <div className="flex items-center gap-3 mb-3">
          {icon && (
            <span
              className={cn(
                'flex items-center justify-center',
                variantClasses[effectiveVariant].icon
              )}
            >
              {icon}
            </span>
          )}
          {label && (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <label
                  className={cn(
                    'block text-sm font-medium',
                    variantClasses[effectiveVariant].label
                  )}
                >
                  {label}
                </label>
                {infoTooltip && (
                  <InfoTooltip title={infoTooltip.title} description={infoTooltip.description} />
                )}
              </div>
              {required && <RequiredFieldIndicator variant="text" position="right" />}
            </div>
          )}
        </div>
      )}
      <div className="relative">
        {multiline ? (
          <textarea
            rows={rows}
            className={cn(
              'w-full rounded-lg border transition-all duration-200 outline-none',
              'disabled:cursor-not-allowed disabled:opacity-50',
              'resize-y',
              'px-4 py-2 text-sm',
              variantClasses[effectiveVariant].input
            )}
            disabled={disabled}
            placeholder={placeholder}
            {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
          />
        ) : inputType === 'phone' ? (
          <PhoneInput
            value={(props.value as string) || ''}
            onChange={(value: string) => {
              if (props.onChange) {
                const event = {
                  target: { value },
                } as React.ChangeEvent<HTMLInputElement>;
                props.onChange(event);
              }
            }}
            onValidationChange={onPhoneValidationChange}
            placeholder={placeholder}
            disabled={disabled}
            error={error}
            variant={effectiveVariant}
            className={className}
          />
        ) : (
          <div
            className={cn(
              'w-full rounded-lg border transition-all duration-200 outline-none',
              'disabled:cursor-not-allowed disabled:opacity-50',
              'flex items-center justify-between gap-2',
              sizeClasses[inputSize],
              variantClasses[effectiveVariant].input
            )}
          >
            <input
              type="text"
              className={cn(
                'w-full bg-transparent border-none outline-none',
                variantClasses[effectiveVariant].search
              )}
              placeholder={placeholder}
              disabled={disabled}
              onFocus={() => !disabled && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
            />
          </div>
        )}
      </div>
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
      {!disabled && showSuggestions && suggestions.length > 0 && (
        <div
          className={cn(
            'absolute top-full left-0 right-0 mt-1 max-h-[200px] overflow-y-auto rounded-lg shadow-lg z-50 border',
            variantClasses[effectiveVariant].suggestions
          )}
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className={cn(
                'px-4 py-2 cursor-pointer outline-none',
                variantClasses[effectiveVariant].item
              )}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return tooltip ? <Tooltip title={tooltip}>{inputContent}</Tooltip> : inputContent;
};

export default StyledInput;
