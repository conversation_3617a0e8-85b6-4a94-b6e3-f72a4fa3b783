import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import StyledInput from '../StyledInput';

// Mock the theme helper
jest.mock('@/components/common/styles/themeHelpers', () => ({
  useTheme: jest.fn().mockReturnValue('dark'),
}));

// Mock the PhoneInput component
jest.mock('@/components/ui/PhoneInput', () => ({
  PhoneInput: ({
    onChange,
    onValidationChange,
    value,
    placeholder,
    disabled,
    error,
    variant,
  }: any) => (
    <input
      data-testid="phone-input"
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      data-error={error}
      data-variant={variant}
    />
  ),
}));

// Mock InfoTooltip component
jest.mock('@/components/common/InfoTooltip', () => ({
  __esModule: true,
  default: ({ title, description }: { title: string; description: string }) => (
    <div data-testid="info-tooltip" title={title}>
      {description}
    </div>
  ),
}));

// Mock RequiredFieldIndicator component
jest.mock('@/components/common/RequiredFieldIndicator', () => ({
  __esModule: true,
  default: ({ variant, position }: { variant: string; position: string }) => (
    <span data-testid="required-indicator" data-variant={variant} data-position={position}>
      Required
    </span>
  ),
}));

// Mock Material-UI Tooltip
jest.mock('@mui/material/Tooltip', () => ({
  __esModule: true,
  default: ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div data-testid="mui-tooltip" title={title}>
      {children}
    </div>
  ),
}));

describe('StyledInput', () => {
  const defaultProps = {
    label: 'Test Label',
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders text input by default', () => {
      render(<StyledInput {...defaultProps} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByText('Test Label')).toBeInTheDocument();
    });

    test('renders textarea when multiline is true', () => {
      render(<StyledInput {...defaultProps} multiline={true} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
      // Check if it's actually a textarea by looking for the rows attribute or checking tagName
      const textElement = screen.getByRole('textbox');
      expect(textElement.tagName.toLowerCase()).toBe('textarea');
    });

    test('renders phone input when inputType is phone', () => {
      render(<StyledInput {...defaultProps} inputType="phone" />);

      expect(screen.getByTestId('phone-input')).toBeInTheDocument();
    });
  });

  describe('Required Field Functionality', () => {
    test('does not show required indicator by default', () => {
      render(<StyledInput {...defaultProps} />);

      expect(screen.queryByTestId('required-indicator')).not.toBeInTheDocument();
    });

    test('shows required indicator when required is true', () => {
      render(<StyledInput {...defaultProps} required={true} />);

      expect(screen.getByTestId('required-indicator')).toBeInTheDocument();
    });

    test('required indicator has correct props', () => {
      render(<StyledInput {...defaultProps} required={true} />);

      const indicator = screen.getByTestId('required-indicator');
      expect(indicator).toHaveAttribute('data-variant', 'text');
      expect(indicator).toHaveAttribute('data-position', 'right');
    });

    test('required indicator appears only when label is present', () => {
      render(<StyledInput value="" onChange={jest.fn()} required={true} />);

      expect(screen.queryByTestId('required-indicator')).not.toBeInTheDocument();
    });

    test('required indicator is positioned correctly with label', () => {
      render(<StyledInput {...defaultProps} required={true} />);

      const label = screen.getByText('Test Label');
      const indicator = screen.getByTestId('required-indicator');

      expect(label).toBeInTheDocument();
      expect(indicator).toBeInTheDocument();
    });
  });

  describe('InfoTooltip Integration', () => {
    const infoTooltipProps = {
      title: 'Tooltip Title',
      description: 'Tooltip Description',
    };

    test('renders info tooltip when provided', () => {
      render(<StyledInput {...defaultProps} infoTooltip={infoTooltipProps} />);

      expect(screen.getByTestId('info-tooltip')).toBeInTheDocument();
    });

    test('info tooltip and required indicator can coexist', () => {
      render(<StyledInput {...defaultProps} infoTooltip={infoTooltipProps} required={true} />);

      expect(screen.getByTestId('info-tooltip')).toBeInTheDocument();
      expect(screen.getByTestId('required-indicator')).toBeInTheDocument();
    });
  });

  describe('Input Interactions', () => {
    test('calls onChange when input value changes', () => {
      const handleChange = jest.fn();
      render(<StyledInput {...defaultProps} onChange={handleChange} />);

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'test value' } });

      // Check that onChange was called
      expect(handleChange).toHaveBeenCalled();

      // Get the actual call arguments
      const call = handleChange.mock.calls[0][0];
      expect(call.target).toBeDefined();
      expect(call.type).toBe('change');
    });

    test('input is disabled when disabled prop is true', () => {
      render(<StyledInput {...defaultProps} disabled={true} />);

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    test('shows placeholder text', () => {
      const placeholder = 'Enter some text';
      render(<StyledInput {...defaultProps} placeholder={placeholder} />);

      expect(screen.getByPlaceholderText(placeholder)).toBeInTheDocument();
    });
  });

  describe('Suggestions Functionality', () => {
    const suggestions = ['Option 1', 'Option 2', 'Option 3'];
    const mockOnSuggestionClick = jest.fn();

    test('shows suggestions when input is focused and suggestions are provided', async () => {
      render(
        <StyledInput
          {...defaultProps}
          suggestions={suggestions}
          onSuggestionClick={mockOnSuggestionClick}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      await waitFor(() => {
        suggestions.forEach(suggestion => {
          expect(screen.getByText(suggestion)).toBeInTheDocument();
        });
      });
    });

    test('calls onSuggestionClick when suggestion is clicked', async () => {
      render(
        <StyledInput
          {...defaultProps}
          suggestions={suggestions}
          onSuggestionClick={mockOnSuggestionClick}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      await waitFor(() => {
        const suggestion = screen.getByText('Option 1');
        fireEvent.click(suggestion);
      });

      expect(mockOnSuggestionClick).toHaveBeenCalledWith('Option 1');
    });

    test('hides suggestions when input loses focus', async () => {
      render(
        <StyledInput
          {...defaultProps}
          suggestions={suggestions}
          onSuggestionClick={mockOnSuggestionClick}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      fireEvent.blur(input);

      // Wait for the blur timeout (200ms in the component)
      await waitFor(
        () => {
          expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
        },
        { timeout: 300 }
      );
    });

    test('does not show suggestions when disabled', () => {
      render(
        <StyledInput
          {...defaultProps}
          suggestions={suggestions}
          onSuggestionClick={mockOnSuggestionClick}
          disabled={true}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    });
  });

  describe('Error Display', () => {
    test('shows error message when error prop is provided', () => {
      const errorMessage = 'This field is required';
      render(<StyledInput {...defaultProps} error={errorMessage} />);

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toHaveClass('text-red-500');
    });
  });

  describe('Phone Input Specific Tests', () => {
    test('passes phone validation callback to PhoneInput', () => {
      const mockValidationCallback = jest.fn();
      render(
        <StyledInput
          {...defaultProps}
          inputType="phone"
          onPhoneValidationChange={mockValidationCallback}
        />
      );

      expect(screen.getByTestId('phone-input')).toBeInTheDocument();
    });

    test('handles phone input value changes', () => {
      const handleChange = jest.fn();
      render(<StyledInput {...defaultProps} inputType="phone" onChange={handleChange} />);

      const phoneInput = screen.getByTestId('phone-input');
      fireEvent.change(phoneInput, { target: { value: '+1234567890' } });

      // The PhoneInput mock will trigger onChange with the value
      expect(handleChange).toHaveBeenCalled();
    });
  });

  describe('Textarea Specific Tests', () => {
    test('sets correct number of rows for textarea', () => {
      const rows = 4;
      render(<StyledInput {...defaultProps} multiline={true} rows={rows} />);

      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveAttribute('rows', rows.toString());
    });

    test('uses default rows when not specified', () => {
      render(<StyledInput {...defaultProps} multiline={true} />);

      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveAttribute('rows', '6'); // Default from component
    });
  });

  describe('Tooltip Integration', () => {
    test('wraps input in tooltip when tooltip prop is provided', () => {
      const tooltipText = 'Helpful tooltip';
      render(<StyledInput {...defaultProps} tooltip={tooltipText} />);

      expect(screen.getByTestId('mui-tooltip')).toBeInTheDocument();
      expect(screen.getByTestId('mui-tooltip')).toHaveAttribute('title', tooltipText);
    });

    test('does not render tooltip wrapper when tooltip prop is not provided', () => {
      render(<StyledInput {...defaultProps} />);

      expect(screen.queryByTestId('mui-tooltip')).not.toBeInTheDocument();
    });
  });

  describe('Icon Display', () => {
    test('renders icon when provided', () => {
      const TestIcon = () => <span data-testid="test-icon">Icon</span>;
      render(<StyledInput {...defaultProps} icon={<TestIcon />} />);

      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('associates label with input correctly', () => {
      render(<StyledInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const label = screen.getByText('Test Label');

      // In the actual component, this would be done via htmlFor/id association
      // For this test, we just verify both elements are present
      expect(input).toBeInTheDocument();
      expect(label).toBeInTheDocument();
    });

    test('required indicator has proper aria-label', () => {
      render(<StyledInput {...defaultProps} required={true} />);

      const indicator = screen.getByTestId('required-indicator');
      // The actual RequiredFieldIndicator component should have aria-label="Required field"
      expect(indicator).toBeInTheDocument();
    });
  });
});
