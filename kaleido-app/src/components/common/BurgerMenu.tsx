'use client';

import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import React from 'react';

interface BurgerMenuProps {
  isOpen: boolean;
  onToggle: () => void;
  variant?: 'default' | 'integrated' | 'floating';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const BurgerMenu: React.FC<BurgerMenuProps> = ({
  isOpen,
  onToggle,
  variant = 'default',
  className = '',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const buttonSizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'integrated':
        return 'bg-transparent hover:bg-white/10 border-none text-gray-300 hover:text-white';
      case 'floating':
        return 'bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 text-gray-300 hover:bg-gray-800/70';
      default:
        return 'bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 text-gray-300 hover:bg-gray-800/70';
    }
  };

  return (
    <button
      onClick={onToggle}
      className={`
        ${buttonSizeClasses[size]} 
        rounded-lg transition-all duration-200 
        ${getVariantClasses()}
        ${className}
      `}
      aria-label={isOpen ? 'Close menu' : 'Open menu'}
    >
      <AnimatePresence mode="wait" initial={false}>
        <motion.div
          key={isOpen ? 'close' : 'menu'}
          initial={{ opacity: 0, rotate: -90 }}
          animate={{ opacity: 1, rotate: 0 }}
          exit={{ opacity: 0, rotate: 90 }}
          transition={{ duration: 0.15 }}
        >
          {isOpen ? <X className={sizeClasses[size]} /> : <Menu className={sizeClasses[size]} />}
        </motion.div>
      </AnimatePresence>
    </button>
  );
};

// Additional component for burger menu in header/navigation context
interface NavigationBurgerMenuProps {
  isOpen: boolean;
  onToggle: () => void;
  title?: string;
  className?: string;
}

export const NavigationBurgerMenu: React.FC<NavigationBurgerMenuProps> = ({
  isOpen,
  onToggle,
  title,
  className = '',
}) => {
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <BurgerMenu isOpen={isOpen} onToggle={onToggle} variant="integrated" size="md" />
      {title && <span className="text-lg font-medium text-gray-200 hidden sm:block">{title}</span>}
    </div>
  );
};
