import React from 'react';

export type Variant = 'light' | 'dark';
export type Size = 'sm' | 'md' | 'lg';

export const sizeClasses = {
  sm: 'text-xs min-h-[40px] px-4 py-2 flex items-center',
  md: 'text-sm min-h-[44px] px-4 py-2 flex items-center',
  lg: 'text-sm min-h-[48px] px-5 py-3 flex items-center',
} as const;

export const variantClasses = {
  light: {
    input:
      'bg-white/70 border-gray-300/30 text-gray-900 placeholder:text-gray-500 focus:border-blue-500/50 focus:bg-white/80 focus:ring-2 focus:ring-blue-500/20 hover:border-opacity-80 backdrop-blur-xl',
    label: 'text-gray-900',
    icon: 'text-gray-700',
    content: 'bg-white/80 border-gray-200/30 text-gray-900 backdrop-blur-xl shadow-2xl',
    item: 'hover:bg-gray-100/60 text-gray-900 backdrop-blur-xl transition-colors duration-150 px-3 py-2 cursor-pointer border-b border-gray-200/40 last:border-b-0',
    search: 'text-gray-900 placeholder:text-gray-500',
    separator: 'border-gray-200/40',
    chip: 'bg-white/70 text-gray-900 backdrop-blur-xl border border-gray-200/40',
    chipDelete: 'hover:bg-gray-100/60 transition-colors duration-150',
    suggestions: 'bg-white/80 border-gray-200/30 text-gray-900 backdrop-blur-xl shadow-2xl',
  },
  dark: {
    input:
      'bg-[var(--input-bg)] border-[var(--input-border)] text-[var(--input-text)] placeholder:text-[var(--input-placeholder)] focus:border-[var(--input-border)] focus:bg-[var(--input-bg)] focus:ring-2 focus:ring-blue-400/20 hover:border-opacity-80 backdrop-blur-lg',
    label: 'text-[var(--input-text)]',
    icon: 'text-[var(--input-text)]',
    content:
      'bg-[var(--dropdown-bg)] border-[var(--dropdown-border)] text-[var(--dropdown-text)] backdrop-blur-lg',
    item: 'hover:bg-[var(--dropdown-hover-bg)] text-[var(--dropdown-text)] backdrop-blur-lg transition-colors duration-150 px-3 py-2 cursor-pointer',
    search: 'text-[var(--dropdown-text)] placeholder:text-[var(--input-placeholder)]',
    separator: 'border-[var(--dropdown-border)]',
    chip: 'bg-[var(--dropdown-bg)] text-[var(--dropdown-text)] backdrop-blur-lg border border-[var(--dropdown-border)]',
    chipDelete: 'text-white hover:bg-[var(--dropdown-hover-bg)] transition-colors duration-150',
    suggestions:
      'bg-[var(--dropdown-bg)] border-[var(--dropdown-border)] text-[var(--dropdown-text)] backdrop-blur-lg shadow-lg',
  },
} as const;

export interface BaseComponentProps {
  variant?: Variant;
  icon?: React.ReactNode;
  className?: string;
  tooltip?: string;
  disabled?: boolean;
  inputSize?: Size;
}

export interface Option {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

export interface InputComponentProps extends BaseComponentProps {
  label?: string;
  error?: string;
  suggestions?: string[];
  onSuggestionClick?: (suggestion: string) => void;
}

export interface SelectComponentProps extends BaseComponentProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  allowCustomValues?: boolean;
}
