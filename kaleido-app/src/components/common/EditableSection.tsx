import { Pencil } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '../ui/button';

interface EditableSectionProps {
  title?: string;
  onSave?: () => void;
  viewMode: React.ReactNode;
  editMode: React.ReactNode;
  className?: string;
  isEmpty?: boolean;
}

export function EditableSection({
  title,
  onSave,
  viewMode,
  editMode,
  className = '',
  isEmpty = false,
}: EditableSectionProps) {
  const [isEditing, setIsEditing] = useState(isEmpty);

  const handleSave = () => {
    onSave?.();
    setIsEditing(false);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {isEditing ? (
        <div className="p-6 rounded-xl bg-white/50 border border-gray-200 shadow-sm space-y-6">
          {title && <h3 className="text-lg  text-gray-900">{title}</h3>}
          {editMode}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsEditing(false)}
              disabled={isEmpty}
              className="text-gray-900 border-gray-300 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </div>
      ) : (
        <div className="p-4 rounded-lg bg-white/50 border border-gray-200 shadow-sm relative group">
          <button
            onClick={() => setIsEditing(true)}
            className="absolute right-3 top-3 p-1 rounded-full bg-blue-50 text-blue-500 hover:bg-blue-100 opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Edit section"
          >
            <Pencil className="w-4 h-4" />
          </button>
          {viewMode}
        </div>
      )}
    </div>
  );
}
