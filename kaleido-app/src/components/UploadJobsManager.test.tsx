import { act, render, screen, waitFor } from '@testing-library/react';

import { UploadJobsManager } from './UploadJobsManager';

// Mock console.log to verify logging
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock ReactDOM.createPortal
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: any) => node,
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock the stores
jest.mock('@/stores/unifiedJobStore', () => ({
  useUploadJobsStore: jest.fn(),
  useJobsStore: jest.fn(),
  useJobStore: {
    getState: jest.fn(() => ({
      fetchJobCriteria: jest.fn(),
    })),
  },
}));

describe('UploadJobsManager - Job Completion Handling', () => {
  const { useUploadJobsStore, useJobsStore } = require('@/stores/unifiedJobStore');

  beforeEach(() => {
    jest.clearAllMocks();
    mockConsoleLog.mockClear();
    mockLocalStorage.getItem.mockReturnValue('[]');
    jest.clearAllTimers();
    jest.useFakeTimers();

    // Mock the jobs store
    useJobsStore.mockReturnValue({
      refreshJobs: jest.fn(),
      invalidateJobsCache: jest.fn(),
    });

    // Default mock for upload jobs store
    useUploadJobsStore.mockReturnValue({
      jobs: {},
      activeJobs: [],
      removeJob: jest.fn(),
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('should handle upload job completion successfully without page refresh', async () => {
    const completedJob = {
      jobId: 'upload-job-1',
      status: 'completed' as const,
      progress: 100,
      totalFiles: 5,
      processedFiles: 5,
      result: {
        data: {
          files: [{ originalname: 'resume1.pdf' }, { originalname: 'resume2.pdf' }],
        },
        errors: [],
      },
      createdAt: new Date().toISOString(),
    };

    // Start with active job
    useUploadJobsStore.mockReturnValue({
      jobs: {
        'upload-job-1': { ...completedJob, status: 'active' },
      },
      activeJobs: ['upload-job-1'],
      removeJob: jest.fn(),
    });

    const { rerender } = render(<UploadJobsManager />);

    // Update to completed job
    await act(async () => {
      useUploadJobsStore.mockReturnValue({
        jobs: {
          'upload-job-1': completedJob,
        },
        activeJobs: [],
        removeJob: jest.fn(),
      });
      rerender(<UploadJobsManager />);
    });

    // Fast-forward through the completion timeout (1000ms) and refresh timeout (2000ms)
    await act(async () => {
      jest.advanceTimersByTime(1000); // First timeout for completion detection
    });

    await act(async () => {
      jest.advanceTimersByTime(2000); // Second timeout for page refresh
    });

    // Verify job completion was handled properly
    await waitFor(
      () => {
        // Check that the job shows as completed - look for the specific completion text
        const completionTexts = screen.getAllByText((content, element) => {
          return (
            element !== null &&
            element.textContent !== null &&
            element.textContent.includes('Completed') &&
            element.textContent.includes('5')
          );
        });
        expect(completionTexts.length).toBeGreaterThan(0);
      },
      { timeout: 1000 }
    );
  });

  it('should handle upload job completion with errors without page refresh', async () => {
    const completedJobWithErrors = {
      jobId: 'upload-job-2',
      status: 'completed_with_errors' as const,
      progress: 100,
      totalFiles: 3,
      processedFiles: 3,
      result: {
        data: {
          files: [{ originalname: 'resume1.pdf' }],
          failedUploads: [{ originalname: 'resume2.pdf', error: 'Invalid format' }],
        },
        errors: [{ file: 'resume2.pdf', error: 'Invalid format' }],
      },
      createdAt: new Date().toISOString(),
    };

    // Start with active job
    useUploadJobsStore.mockReturnValue({
      jobs: {
        'upload-job-2': { ...completedJobWithErrors, status: 'active' },
      },
      activeJobs: ['upload-job-2'],
      removeJob: jest.fn(),
    });

    const { rerender } = render(<UploadJobsManager />);

    // Update to completed job with errors
    await act(async () => {
      useUploadJobsStore.mockReturnValue({
        jobs: {
          'upload-job-2': completedJobWithErrors,
        },
        activeJobs: [],
        removeJob: jest.fn(),
      });
      rerender(<UploadJobsManager />);
    });

    // Fast-forward through the completion timeout (1000ms) and refresh timeout (2000ms)
    await act(async () => {
      jest.advanceTimersByTime(1000); // First timeout for completion detection
    });

    await act(async () => {
      jest.advanceTimersByTime(2000); // Second timeout for page refresh
    });

    // Wait for the completion to be handled - just verify the component renders correctly
    await waitFor(
      () => {
        // Check that the component shows the completed job with errors
        const errorElement = document.querySelector('.text-orange-500');
        expect(errorElement).toBeInTheDocument();
      },
      { timeout: 1000 }
    );
  });

  it('should not trigger completion handling for jobs still in progress', async () => {
    const activeJob = {
      jobId: 'upload-job-3',
      status: 'active' as const,
      progress: 50,
      totalFiles: 4,
      processedFiles: 2,
      createdAt: new Date().toISOString(),
    };

    useUploadJobsStore.mockReturnValue({
      jobs: {
        'upload-job-3': activeJob,
      },
      activeJobs: ['upload-job-3'],
      removeJob: jest.fn(),
    });

    render(<UploadJobsManager />);

    // Fast-forward time and ensure no completion handling was triggered
    await act(async () => {
      jest.advanceTimersByTime(5000); // Advance time but job is still active
    });

    expect(mockConsoleLog).not.toHaveBeenCalledWith('Upload job completed successfully');
  });
});
