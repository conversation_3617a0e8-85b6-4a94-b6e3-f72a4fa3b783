import React, { Component, ErrorInfo, ReactNode } from 'react';
import { InfiniteLoopError } from '@/hooks/useInfiniteLoopDetector';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorCount: number;
}

export class InfiniteLoopErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): State | null {
    // Only handle InfiniteLoopError or errors that look like infinite loops
    if (
      error instanceof InfiniteLoopError ||
      error.name === 'InfiniteLoopError' ||
      error.message.toLowerCase().includes('infinite loop')
    ) {
      return {
        hasError: true,
        error,
        errorCount: 0,
      };
    }
    return null;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to console
    console.error('Infinite Loop Error Boundary caught:', error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Track error in analytics if available
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'infinite_loop_error_boundary', {
        event_category: 'error',
        event_label: error.message,
        value: 1,
      });
    }
  }

  handleReset = () => {
    // Clear any stored state that might be causing the loop
    if (typeof window !== 'undefined') {
      // Clear specific problematic cache keys
      const keysToRemove = ['api_cache_/jobs/', 'temp_form_data', 'pending_requests'];

      Object.keys(localStorage).forEach(key => {
        if (keysToRemove.some(pattern => key.includes(pattern))) {
          localStorage.removeItem(key);
        }
      });
    }

    // Reset the error boundary
    this.setState({
      hasError: false,
      error: null,
      errorCount: 0,
    });

    // Reload the page after a short delay
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md w-full">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Too Many Requests Detected</h2>
            <p className="text-sm text-red-600 mb-4">
              The application detected rapid repeated requests which may indicate an issue. This is
              a safety mechanism to prevent performance problems.
            </p>
            <div className="bg-red-100 rounded p-3 mb-4">
              <p className="text-xs text-red-700 font-mono">{this.state.error.message}</p>
            </div>
            <button
              onClick={this.handleReset}
              className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
