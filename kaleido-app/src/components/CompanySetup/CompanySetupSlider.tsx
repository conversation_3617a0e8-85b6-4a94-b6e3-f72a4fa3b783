'use client';

import React, { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  Building2,
  Globe,
  Info,
  Mail,
  MapPin,
  Upload,
  User,
  Users,
  Wand2,
  X,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import Confetti from 'react-confetti';

import useUser from '@/hooks/useUser';
import apiHelper from '@/lib/apiHelper';

import { showToast } from '../Toaster';
import { Button } from '../ui/button';
import { PhoneInput } from '../ui/PhoneInput';

interface CompanySetupSliderProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (companyData: FormData) => void;
  hasError?: boolean;
  errorMessage?: string;
  initialData?: Partial<FormData>;
}

interface Slide {
  id: string;
  title: string;
  subtitle: string;
  fields: Array<keyof FormData>;
  image: string;
  component?: React.ComponentType;
}

interface CompanySummaryResponse {
  summary: string;
}

// Sample company values
const companyValuesOptions = [
  'Innovation',
  'Teamwork',
  'Customer Focus',
  'Integrity',
  'Excellence',
  'Diversity & Inclusion',
  'Work-Life Balance',
  'Sustainability',
  'Transparency',
  'Accountability',
  'Growth Mindset',
  'Social Responsibility',
];

// Sample cultural fit options
const culturalFitOptions = [
  'Collaborative',
  'Fast-paced',
  'Results-oriented',
  'Creative',
  'Detail-oriented',
  'Self-motivated',
  'Adaptable',
  'Analytical',
  'Entrepreneurial',
];

const slides: Slide[] = [
  {
    id: 'basics',
    title: 'Company Basics',
    subtitle: 'Tell us about your company',
    fields: ['companyName', 'companyWebsite', 'description', 'logo'],
    image: '/images/company-setup/full/company-full-1.webp',
  },
  {
    id: 'details',
    title: 'Company Details',
    subtitle: 'Help us understand your company better',
    fields: ['size', 'industry', 'cultureFitDescription'],
    image: '/images/company-setup/full/company-full-2.webp',
  },
  {
    id: 'contact',
    title: 'Contact Information',
    subtitle: 'Your contact information',
    fields: ['contactName', 'contactEmail', 'phoneNumber'],
    image: '/images/company-setup/full/company-full-3.webp',
  },
];

const companySizes = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1000+ employees',
];

interface CompletionState {
  show: boolean;
  animate: boolean;
}

interface FormData {
  companyName: string;
  companyWebsite: string;
  industry: string;
  size: string;
  location: string;
  contactName: string;
  contactEmail: string;
  phoneNumber: string;
  logo: string;
  description: string;
  cultureFitDescription?: string;
  companyValues?: string[];
}

export const CompanySetupSlider: React.FC<CompanySetupSliderProps> = ({
  onClose,
  onSubmit,
  initialData = {},
  hasError = false,
  errorMessage,
}) => {
  const { user } = useUser();
  const router = useRouter();
  const clientId = user?.userId || user?.sub || user?.clientId || '';
  const userEmail = user?.email || '';

  const [formData, setFormData] = useState<FormData>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('companySetupData');
      const savedData = saved ? JSON.parse(saved) : {};

      return {
        companyName: '',
        companyWebsite: '',
        industry: '',
        size: '',
        location: '',
        contactName: '',
        contactEmail: savedData.contactEmail || userEmail || '',
        phoneNumber: '',
        logo: '',
        description: '',
        cultureFitDescription: '',
        companyValues: [],
        ...savedData,
        ...initialData,
        clientId,
      };
    }
    return {
      companyName: '',
      companyWebsite: '',
      industry: '',
      size: '',
      location: '',
      contactName: '',
      contactEmail: userEmail || '',
      phoneNumber: '',
      logo: '',
      description: '',
      cultureFitDescription: '',
      companyValues: [],
      ...initialData,
      clientId,
    };
  });

  const [currentSlide, setCurrentSlide] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('companySetupSlide');
      return saved ? parseInt(saved, 10) : 0;
    }
    return 0;
  });

  const [emailError, setEmailError] = useState<string | null>(null);
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [completion, setCompletion] = useState<CompletionState>({
    show: false,
    animate: false,
  });
  const [isFetchingSummary, setIsFetchingSummary] = useState(false);
  const [isValidatingEmail, setIsValidatingEmail] = useState(false);

  // List of common public email domains that should be rejected
  // DISABLED: Email domain restrictions have been removed
  // const publicEmailDomains = [
  //   'gmail.com',
  //   'yahoo.com',
  //   'hotmail.com',
  //   'outlook.com',
  //   'aol.com',
  //   'icloud.com',
  //   'mail.com',
  //   'protonmail.com',
  //   'zoho.com',
  //   'yandex.com',
  //   'gmx.com',
  //   'live.com',
  //   'msn.com',
  //   'me.com',
  //   'inbox.com',
  //   'mail.ru',
  //   'hotmail.co.uk',
  //   'hotmail.fr',
  //   'yahoo.co.uk',
  //   'yahoo.fr',
  //   'googlemail.com',
  // ];

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('companySetupData', JSON.stringify(formData));
    }
  }, [formData]);

  // Save current slide to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('companySetupSlide', currentSlide.toString());
    }
  }, [currentSlide]);

  // Function to validate if an email is a work email (not a public email domain)
  // DISABLED: Company email validation has been disabled to allow all email domains
  const isWorkEmail = (email: string): boolean => {
    if (!email || !email.includes('@')) return false;

    // Validation disabled - accepting all valid email formats
    return true;
  };

  const handleInputChange = (field: keyof FormData, value: string | string[]) => {
    if (field && value !== undefined) {
      // Clear errors when any input changes
      setEmailError(null);
      setPhoneError(null);

      // If the field is contactEmail, validate that it's a work email
      // DISABLED: Email validation check has been removed to allow all email domains

      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Handle phone number validation
  const handlePhoneValidation = (validation: { isValid: boolean; error?: string }) => {
    if (!validation.isValid && validation.error) {
      setPhoneError(validation.error);
    } else {
      setPhoneError(null);
    }
  };

  // Initialize form data with initial values
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
      }));
    }
  }, [initialData]);

  // Pre-populate email when user data becomes available
  useEffect(() => {
    if (userEmail && !formData.contactEmail) {
      setFormData(prev => ({
        ...prev,
        contactEmail: userEmail,
      }));
    }
  }, [userEmail, formData.contactEmail]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    const formDataObj = new FormData();
    formDataObj.append('file', file);

    try {
      const { imageUrl } = await apiHelper.post('/companies/upload-logo', formDataObj);
      handleInputChange('logo', imageUrl);
      showToast({
        message: 'Company logo uploaded successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      showToast({
        message: 'Failed to upload company logo. Please try again.',
        isSuccess: false,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFetchSummary = async () => {
    if (!formData.companyWebsite) {
      showToast({
        message: 'Please enter a company website URL first',
        isSuccess: false,
      });
      return;
    }

    try {
      setIsFetchingSummary(true);
      const response = await apiHelper.post<CompanySummaryResponse>('/jobs/summarize', {
        url: formData.companyWebsite,
      });

      if (response.summary) {
        handleInputChange('description', response.summary);
        showToast({
          message: 'Company description fetched successfully',
          isSuccess: true,
        });
      }
    } catch (error) {
      console.error('Error fetching summary:', error);
      showToast({
        message: 'Failed to fetch company description. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsFetchingSummary(false);
    }
  };

  const handleNext = async () => {
    // If we're on the last slide, submit the company data
    if (currentSlide === slides.length - 1) {
      try {
        // Final validation of email before submission
        // DISABLED: Email validation has been removed to allow all email domains

        setIsValidatingEmail(true);
        await onSubmit(formData);
        setCompletion({ show: true, animate: true });
      } catch (error: any) {
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to create company. Please try again.';

        // If the error is related to email, set the email error and go back to the contact slide
        if (errorMessage.toLowerCase().includes('email')) {
          setEmailError(errorMessage);
          // Find the index of the slide containing contactEmail
          const contactSlideIndex = slides.findIndex(slide =>
            slide.fields.includes('contactEmail')
          );
          setCurrentSlide(contactSlideIndex);
        } else {
          showToast({
            message: errorMessage,
            isSuccess: false,
          });
        }
        setCompletion({ show: false, animate: false });
        return;
      } finally {
        setIsValidatingEmail(false);
      }
    } else {
      // Simply move to the next slide without updating the company data
      // The data will be saved when the user completes the entire form
      setCurrentSlide(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentSlide > 0) {
      setCurrentSlide(prev => prev - 1);
    }
  };

  const handleComplete = async () => {
    // If there's an error, go back to the contact slide
    if (hasError) {
      // Find the index of the contact slide
      const contactSlideIndex = slides.findIndex(slide => slide.id === 'contact');
      setCurrentSlide(contactSlideIndex);
      return;
    }

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('companySetupData');
      localStorage.removeItem('companySetupSlide');
    }
    setCompletion({ show: false, animate: false });
    onClose();
    router.replace('/dashboard');
  };

  const isCurrentSlideValid = () => {
    // If there's an error and we're on the last slide, prevent moving forward
    if (hasError && currentSlide === slides.length - 1) {
      return false;
    }

    // If there's an email or phone error, the slide is not valid
    if (emailError || phoneError) {
      return false;
    }

    const currentFields = slides[currentSlide].fields;

    return currentFields.every(field => {
      // Fields that are optional
      if (field === 'logo' || field === 'cultureFitDescription' || field === 'companyValues')
        return true;

      // Special validation for contactEmail
      if (field === 'contactEmail') {
        const email = formData[field] as string;
        // DISABLED: Email validation - just check if email exists
        return !!email;
      }

      // Special validation for phoneNumber
      if (field === 'phoneNumber') {
        const phone = formData[field] as string;
        return phone && !phoneError; // Valid if has value and no error
      }

      const value = formData[field];
      if (Array.isArray(value)) {
        return true; // Arrays are always valid (can be empty)
      }

      return value ? true : false;
    });
  };

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-gray-50 to-blue-50/30 backdrop-blur-3xl">
      {completion.show && <Confetti numberOfPieces={200} recycle={false} />}

      <AnimatePresence mode="wait">
        {completion.show ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 z-50"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, ease: 'easeOut' }}
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: `url(${slides[slides.length - 1].image})`,
              }}
            >
              <div className="absolute inset-0 bg-black/50" />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="absolute inset-0 flex flex-col items-center justify-center text-white"
              >
                <div className="max-w-2xl mx-auto text-center px-4 flex flex-col items-center justify-center">
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                    className="w-20 h-20 rounded-full bg-white flex items-center justify-center mb-6 shadow-lg"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="40"
                      height="40"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-pink-600"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </motion.div>
                  <h1 className="flex flex-col items-center gap-2">
                    <span className="text-2xl font-medium text-white text-center">Welcome to</span>
                    <span className="text-5xl font-bold text-white relative text-center">
                      Kaleido Talent
                    </span>
                  </h1>
                  <p className="text-sm md:text-base text-white/90 mt-6 mb-8 text-center max-w-lg">
                    Your company profile has been set up successfully. Get ready to transform your
                    hiring process with AI-powered tools and insights.
                  </p>
                  <div className="flex justify-center">
                    <Button
                      onClick={handleComplete}
                      className="flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-pink-600 hover:bg-pink-700 text-white transition-all"
                    >
                      <span>Let's Get Started</span>
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="relative h-screen w-full"
          >
            {/* Full-screen background image */}
            <div className="absolute inset-0 w-full h-full">
              <div
                className="absolute inset-0 bg-cover bg-center w-full h-full"
                style={{
                  backgroundImage: `url(${slides[currentSlide].image})`,
                  backgroundColor: 'rgb(55, 65, 81)',
                }}
              >
                <div className="absolute inset-0 bg-black/30" />
              </div>
            </div>

            {/* Close button */}
            <div className="absolute top-4 right-4 z-50">
              <Button
                variant="ghost"
                className="p-2 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all shadow-sm"
                onClick={() => {
                  onClose();
                  router.replace('/dashboard');
                }}
              >
                <X className="w-4 h-4 text-white" />
              </Button>
            </div>

            {/* Form Panel - Glassy effect */}
            <div className="relative w-full md:w-[500px] h-full overflow-hidden flex flex-col z-10 bg-white/25 backdrop-blur-md border-r border-white/20 shadow-xl">
              {/* Kaleido logo in top right of left panel */}
              <div className="absolute top-4 right-4 z-10">
                <Image
                  src="/images/logos/kaleido-logo-only.webp"
                  alt="Kaleido Logo"
                  width={40}
                  height={40}
                  className="opacity-80"
                />
              </div>

              <div className="flex-1 overflow-y-auto p-4 md:p-12">
                <div className="flex flex-col max-w-md mx-auto w-full">
                  <div className="mb-8">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="flex items-center justify-center px-3 py-1 rounded-full bg-pink-50/90 backdrop-blur-sm border border-pink-100/50 text-xs font-medium text-pink-700">
                        Step {currentSlide + 1} of {slides.length}
                      </div>
                    </div>
                    <h1 className="text-xl md:text-2xl font-semibold mb-2 text-white drop-shadow-sm">
                      {slides[currentSlide].title}
                    </h1>
                    <p className="text-white/80 text-xs md:text-sm mb-6">
                      {slides[currentSlide].subtitle}
                    </p>

                    {/* Display error message if provided */}
                    {hasError && errorMessage && (
                      <div className="mb-4 p-3 rounded-lg bg-red-500/20 backdrop-blur-sm border border-red-400/30">
                        <p className="text-red-100 text-sm font-medium">{errorMessage}</p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-6 pb-6">
                    {slides[currentSlide].component ? (
                      <div>
                        {React.createElement(slides[currentSlide].component as React.ComponentType)}
                      </div>
                    ) : (
                      slides[currentSlide].fields.map(field => (
                        <div key={field} className="relative">
                          {field === 'logo' ? (
                            <div className="relative">
                              <label className="block text-xs font-medium text-white mb-1">
                                Company Logo
                              </label>
                              <input
                                type="file"
                                name="file"
                                accept="image/*"
                                onChange={handleFileUpload}
                                className="hidden"
                                id="logo-upload"
                                disabled={uploading}
                              />
                              <label
                                htmlFor="logo-upload"
                                className="flex items-center justify-center w-full h-28 border border-white/20 rounded-lg hover:border-pink-400 transition-all cursor-pointer bg-white/60 backdrop-blur-sm group shadow-sm"
                              >
                                {formData[field] ? (
                                  <div className="relative w-full h-full rounded-lg overflow-hidden">
                                    <Image
                                      src={formData[field]}
                                      alt="Company logo"
                                      fill
                                      className="object-contain p-2"
                                    />
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                                      <div className="bg-white/20 backdrop-blur-sm rounded-full p-1.5 shadow-md">
                                        <Upload className="w-4 h-4 text-pink-300" />
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex flex-col items-center text-gray-700 group-hover:text-pink-600 transition-colors">
                                    <div className="w-10 h-10 rounded-full bg-white/30 flex items-center justify-center mb-2 group-hover:bg-pink-500/20 transition-colors">
                                      <Upload className="w-4 h-4 text-gray-700 group-hover:text-pink-600" />
                                    </div>
                                    <span className="text-xs">
                                      {uploading ? 'Uploading...' : 'Upload company logo'}
                                    </span>
                                  </div>
                                )}
                              </label>
                            </div>
                          ) : field === 'description' ? (
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <label className="block text-xs font-medium text-white mb-1">
                                  Company Description
                                </label>
                                <div className="flex gap-2 mb-2">
                                  {formData.companyWebsite && (
                                    <Button
                                      onClick={handleFetchSummary}
                                      disabled={isFetchingSummary || !formData.companyWebsite}
                                      className="flex items-center gap-1.5 text-xs bg-white/30 hover:bg-white/40 border border-white/20 text-gray-900 hover:shadow-sm transition-all rounded-lg py-1.5 h-auto backdrop-blur-sm"
                                    >
                                      <Wand2 className="w-3 h-3 text-pink-800" />
                                      {isFetchingSummary ? 'Fetching...' : 'Auto-generate'}
                                    </Button>
                                  )}
                                </div>
                              </div>
                              <div className="relative">
                                <textarea
                                  value={formData[field] || ''}
                                  onChange={e => {
                                    handleInputChange(field as keyof FormData, e.target.value);
                                  }}
                                  placeholder="Describe what your company does..."
                                  className="w-full h-40 px-4 py-3 text-sm text-black/90 rounded-lg border border-white/20 focus:border-pink-400 focus:ring-1 focus:ring-pink-400 bg-white/60 backdrop-blur-sm transition-all resize-none placeholder:text-black/60 placeholder:text-xs shadow-sm"
                                />
                                <div className="absolute bottom-3 right-3 text-black/70">
                                  <Info className="h-4 w-4" />
                                </div>
                              </div>
                            </div>
                          ) : field === 'size' ? (
                            <div>
                              <label className="block text-xs font-medium text-white mb-1">
                                Company Size
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <Users className="h-4 w-4 text-black/70" />
                                </div>
                                <select
                                  id="company-size"
                                  name="company-size"
                                  aria-label="Company Size"
                                  value={formData[field] || ''}
                                  onChange={e =>
                                    handleInputChange(field as keyof FormData, e.target.value)
                                  }
                                  className="w-full pl-10 pr-4 py-2.5 text-sm text-black/90 rounded-lg border border-white/20 focus:border-pink-400 focus:ring-1 focus:ring-pink-400 bg-white/60 backdrop-blur-sm transition-all appearance-none shadow-sm"
                                >
                                  <option value="">Select company size</option>
                                  {companySizes.map(size => (
                                    <option key={size} value={size}>
                                      {size}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            </div>
                          ) : field === 'cultureFitDescription' ? (
                            <div className="space-y-2">
                              <label className="block text-xs font-medium text-white mb-1">
                                Company Culture Description
                              </label>
                              <div className="relative">
                                <textarea
                                  value={formData[field] || ''}
                                  onChange={e => {
                                    handleInputChange(field as keyof FormData, e.target.value);
                                  }}
                                  placeholder="Describe your company culture and what makes a candidate a good cultural fit..."
                                  className="w-full h-40 px-4 py-3 text-sm text-black/90 rounded-lg border border-white/20 focus:border-pink-400 focus:ring-1 focus:ring-pink-400 bg-white/60 backdrop-blur-sm transition-all resize-none placeholder:text-black/60 placeholder:text-xs shadow-sm"
                                />
                                <div className="absolute bottom-3 right-3 text-black/70">
                                  <User className="h-4 w-4" />
                                </div>
                              </div>
                            </div>
                          ) : field === 'companyValues' ? (
                            <div>
                              <label className="block text-xs font-medium text-white mb-1">
                                Company Values
                              </label>
                              <div className="relative">
                                <div className="absolute top-3 left-3 pointer-events-none">
                                  <Users className="h-4 w-4 text-black/70" />
                                </div>
                                <select
                                  id="company-values"
                                  name="company-values"
                                  aria-label="Company Values"
                                  multiple
                                  value={Array.isArray(formData[field]) ? formData[field] : []}
                                  onChange={e => {
                                    const values = Array.from(
                                      e.target.selectedOptions,
                                      option => option.value
                                    );
                                    handleInputChange(field as keyof FormData, values);
                                  }}
                                  className="w-full pl-10 pr-4 py-2 text-sm text-black/90 rounded-lg border border-white/20 focus:border-pink-400 focus:ring-1 focus:ring-pink-400 bg-white/60 backdrop-blur-sm transition-all h-32 shadow-sm"
                                >
                                  {companyValuesOptions.map(value => (
                                    <option key={value} value={value}>
                                      {value}
                                    </option>
                                  ))}
                                </select>
                                <p className="text-xs text-black/70 mt-1 italic">
                                  Hold Ctrl/Cmd to select multiple values
                                </p>
                              </div>
                            </div>
                          ) : field === 'phoneNumber' ? (
                            <div>
                              <label className="block text-xs font-medium text-white mb-1">
                                Phone Number
                              </label>
                              <PhoneInput
                                value={(formData[field] as string) || ''}
                                onChange={(value: string) => handleInputChange(field, value)}
                                onValidationChange={handlePhoneValidation}
                                placeholder="Enter phone number"
                                error={phoneError || undefined}
                                required
                              />
                            </div>
                          ) : (
                            <div>
                              <label className="block text-xs font-medium text-white mb-1">
                                {String(field).charAt(0).toUpperCase() +
                                  String(field)
                                    .slice(1)
                                    .replace(/([A-Z])/g, ' $1')}
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  {field === 'companyName' && (
                                    <Building2 className="h-4 w-4 text-black/70" />
                                  )}
                                  {field === 'companyWebsite' && (
                                    <Globe className="h-4 w-4 text-black/70" />
                                  )}
                                  {field === 'industry' && (
                                    <Users className="h-4 w-4 text-black/70" />
                                  )}
                                  {field === 'location' && (
                                    <MapPin className="h-4 w-4 text-black/70" />
                                  )}
                                  {field === 'contactEmail' && (
                                    <Mail className="h-4 w-4 text-black/70" />
                                  )}
                                </div>
                                <input
                                  type="text"
                                  value={(formData[field] as string) || ''}
                                  onChange={e => handleInputChange(field, e.target.value)}
                                  placeholder={`Enter ${
                                    String(field).charAt(0).toUpperCase() +
                                    String(field)
                                      .slice(1)
                                      .replace(/([A-Z])/g, ' $1')
                                  }`}
                                  className="w-full pl-10 pr-4 py-2.5 text-sm text-black/90 rounded-lg border border-white/20 focus:border-pink-400 focus:ring-1 focus:ring-pink-400 bg-white/60 backdrop-blur-sm transition-all placeholder:text-black/60 placeholder:text-xs shadow-sm"
                                />
                              </div>
                              {field === 'contactEmail' && emailError && (
                                <p className="text-red-500 text-xs mt-1">{emailError}</p>
                              )}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>

              {/* Navigation Buttons - Modern design with glassy effect */}
              <div className="sticky bottom-0 left-0 right-0 bg-white/5 backdrop-blur-md border-t border-white/10 p-4 md:p-5 mt-auto shadow-sm">
                <div className="flex justify-between items-center max-w-md mx-auto gap-3">
                  {currentSlide > 0 && (
                    <Button
                      onClick={handleBack}
                      variant="outline"
                      className="flex items-center justify-center gap-1.5 px-4 py-2 flex-1 bg-white/30 hover:bg-white/40 transition-all text-gray-900 border-white/20 rounded-lg h-9 backdrop-blur-sm"
                    >
                      <ArrowLeft className="w-3.5 h-3.5" />
                      <span className="text-xs">Back</span>
                    </Button>
                  )}
                  <Button
                    onClick={handleNext}
                    disabled={!isCurrentSlideValid() || isValidatingEmail}
                    className={`flex-1 bg-pink-600 text-white hover:bg-pink-700 flex items-center justify-center gap-1.5 px-4 py-2 rounded-lg transition-all disabled:opacity-50 disabled:hover:bg-pink-600 disabled:cursor-not-allowed ${!currentSlide && 'w-full'} h-9 shadow-sm backdrop-blur-sm`}
                  >
                    <span className="text-xs">
                      {isValidatingEmail
                        ? 'Validating...'
                        : currentSlide === slides.length - 1
                          ? 'Complete'
                          : 'Next'}
                    </span>
                    <ArrowRight className="w-3.5 h-3.5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Simple indicator showing current step */}
            <div className="absolute bottom-8 right-8 z-10">
              <div className="flex items-center gap-2">
                {slides.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all ${
                      index === currentSlide ? 'bg-pink-600 w-6' : 'bg-white/60'
                    }`}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
