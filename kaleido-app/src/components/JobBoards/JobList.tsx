'use client';

import { useRouter } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { IJob } from '@/entities/interfaces';
import { jobDataArrayToIJobArray } from '@/stores/unifiedJobStore';

// Utility function to generate a random color
export const getGradientClass = () => {
  const gradients = [
    'bg-gradient-to-r from-purple-400 via-pink-500 to-red-500',
    'bg-gradient-to-r from-green-400 via-blue-500 to-purple-500',
    'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500',
    'bg-gradient-to-r from-teal-400 via-cyan-500 to-blue-500',
    'bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500',
  ];
  return gradients[Math.floor(Math.random() * gradients.length)];
};

const JobItem = ({ job }: { job: IJob }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/job-boards/${job.id}`);
  };

  return (
    <div
      className="flex items-center justify-between p-4 border-b cursor-pointer hover:bg-gray-50"
      onClick={handleClick}
    >
      <div className="flex items-center">
        <div
          className={`mr-4 rounded-full w-10 h-10 flex items-center justify-center text-white font-bold  text-2xl ${getGradientClass()}`}
        >
          {job.companyName.charAt(0).toUpperCase()}
        </div>
        <div>
          <h3 className="font-semibold">{job.jobType}</h3>
          <p className="text-sm text-gray-500">{job.companyName}</p>
        </div>
      </div>
      <div className="text-right">
        <span className="text-sm text-gray-500">{job.location[0]}</span>
        <p className="text-sm font-semibold">{job.salaryRange}</p>
      </div>
    </div>
  );
};

const JobListInner = () => {
  const { jobs: jobsData, error } = useJobs();
  const jobs = jobDataArrayToIJobArray(jobsData);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (!showContent) return <ColorfulSmokeyOrbLoader />;
  if (error) return <p>Error: {error}</p>;

  return (
    <section className="mx-auto">
      <h2 className="text-2xl font-bold mb-4">Latest Jobs</h2>
      <div className="bg-white rounded-lg shadow">
        {jobs.map(job => (
          <JobItem key={job.id} job={job} />
        ))}
      </div>
    </section>
  );
};

const JobList = () => {
  return (
    <Suspense fallback={<ColorfulSmokeyOrbLoader />}>
      <JobListInner />
    </Suspense>
  );
};

export default JobList;
