import { showToast } from '@/components/Toaster';
import StyledInput from '@/components/common/styledInputs/StyledInput';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import { teamService } from '@/services/team.service';
import {
  CompanyInvitation,
  CompanyMember,
  CompanyMemberRole,
  CompanyMemberStatus,
  InvitationStatus,
  getDefaultPermissions,
} from '@/types/team.types';
import { Check, Clock, Edit2, Mail, Plus, Shield, Trash2, UserPlus, Users, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface TeamManagementProps {
  companyId: string;
}

const TeamManagement: React.FC<TeamManagementProps> = ({ companyId }) => {
  const [members, setMembers] = useState<CompanyMember[]>([]);
  const [invitations, setInvitations] = useState<CompanyInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [editingMember, setEditingMember] = useState<string | null>(null);
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: CompanyMemberRole.MEMBER,
    message: '',
  });
  const [teamSettings, setTeamSettings] = useState({
    allowedEmailDomains: [] as string[],
    autoJoinEnabled: false,
    defaultAutoJoinRole: CompanyMemberRole.MEMBER,
  });
  const [newDomain, setNewDomain] = useState('');

  useEffect(() => {
    fetchTeamData();
  }, [companyId]);

  const fetchTeamData = async () => {
    try {
      setLoading(true);
      const [membersData, invitationsData] = await Promise.all([
        teamService.getCompanyMembers(companyId),
        teamService.getCompanyInvitations(companyId),
      ]);
      setMembers(membersData);
      setInvitations(invitationsData);
    } catch (error) {
      showToast({ message: 'Failed to load team data', isSuccess: false });
    } finally {
      setLoading(false);
    }
  };

  const handleInviteMember = async () => {
    try {
      await teamService.inviteTeamMember(companyId, {
        email: inviteForm.email,
        role: inviteForm.role,
        message: inviteForm.message,
        permissions: getDefaultPermissions(inviteForm.role),
      });
      showToast({ message: 'Invitation sent successfully', isSuccess: true });
      setShowInviteModal(false);
      setInviteForm({ email: '', role: CompanyMemberRole.MEMBER, message: '' });
      fetchTeamData();
    } catch (error: any) {
      showToast({ message: error.message || 'Failed to send invitation', isSuccess: false });
    }
  };

  const handleUpdateMember = async (member: CompanyMember, updates: Partial<CompanyMember>) => {
    try {
      await teamService.updateTeamMember(companyId, member.clientId, updates);
      showToast({ message: 'Member updated successfully', isSuccess: true });
      setEditingMember(null);
      fetchTeamData();
    } catch (error) {
      showToast({ message: 'Failed to update member', isSuccess: false });
    }
  };

  const handleRemoveMember = async (member: CompanyMember) => {
    if (!confirm(`Are you sure you want to remove ${member.email} from the team?`)) return;

    try {
      await teamService.removeTeamMember(companyId, member.clientId);
      showToast({ message: 'Member removed successfully', isSuccess: true });
      fetchTeamData();
    } catch (error) {
      showToast({ message: 'Failed to remove member', isSuccess: false });
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      await teamService.cancelInvitation(companyId, invitationId);
      showToast({ message: 'Invitation cancelled', isSuccess: true });
      fetchTeamData();
    } catch (error) {
      showToast({ message: 'Failed to cancel invitation', isSuccess: false });
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    try {
      await teamService.resendInvitation(companyId, invitationId);
      showToast({ message: 'Invitation resent', isSuccess: true });
    } catch (error) {
      showToast({ message: 'Failed to resend invitation', isSuccess: false });
    }
  };

  const handleAddDomain = () => {
    if (newDomain && !teamSettings.allowedEmailDomains.includes(newDomain)) {
      const updatedSettings = {
        ...teamSettings,
        allowedEmailDomains: [...teamSettings.allowedEmailDomains, newDomain],
      };
      setTeamSettings(updatedSettings);
      setNewDomain('');
      // Save to backend
      teamService.updateTeamSettings(companyId, updatedSettings);
    }
  };

  const handleRemoveDomain = (domain: string) => {
    const updatedSettings = {
      ...teamSettings,
      allowedEmailDomains: teamSettings.allowedEmailDomains.filter(d => d !== domain),
    };
    setTeamSettings(updatedSettings);
    // Save to backend
    teamService.updateTeamSettings(companyId, updatedSettings);
  };

  const getRoleBadgeColor = (role: CompanyMemberRole) => {
    switch (role) {
      case CompanyMemberRole.OWNER:
        return 'bg-purple-500/20 text-purple-300 border-purple-500/20';
      case CompanyMemberRole.ADMIN:
        return 'bg-blue-500/20 text-blue-300 border-blue-500/20';
      case CompanyMemberRole.MEMBER:
        return 'bg-green-500/20 text-green-300 border-green-500/20';
      case CompanyMemberRole.VIEWER:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/20';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/20';
    }
  };

  const getStatusBadgeColor = (status: CompanyMemberStatus | InvitationStatus) => {
    switch (status) {
      case CompanyMemberStatus.ACTIVE:
        return 'bg-green-500/20 text-green-300 border-green-500/20';
      case CompanyMemberStatus.INVITED:
      case InvitationStatus.PENDING:
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/20';
      case CompanyMemberStatus.SUSPENDED:
      case InvitationStatus.EXPIRED:
      case InvitationStatus.CANCELLED:
        return 'bg-red-500/20 text-red-300 border-red-500/20';
      case InvitationStatus.ACCEPTED:
        return 'bg-blue-500/20 text-blue-300 border-blue-500/20';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/20';
    }
  };

  const roleOptions = [
    { value: CompanyMemberRole.ADMIN, label: 'Admin' },
    { value: CompanyMemberRole.MEMBER, label: 'Member' },
    { value: CompanyMemberRole.VIEWER, label: 'Viewer' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 180px)' }}>
        <div className="space-y-4 pb-20">
          {/* Domain Tags - Only show when enabled and has domains */}
          {teamSettings.autoJoinEnabled && teamSettings.allowedEmailDomains.length > 0 && (
            <div className="flex flex-wrap gap-2 px-2">
              {teamSettings.allowedEmailDomains.map(domain => (
                <span
                  key={domain}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/10 text-white/90 border border-white/20"
                >
                  {domain}
                  <button
                    onClick={() => handleRemoveDomain(domain)}
                    className="ml-2 text-white/60 hover:text-white"
                    title={`Remove domain ${domain}`}
                    aria-label={`Remove domain ${domain}`}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          )}

          {/* Email Domain Auto-join Controls */}
          <div className="flex items-center justify-end">
            <div className="flex items-center space-x-3 px-4 py-3 bg-primary/10 backdrop-blur-lg rounded-lg border border-white/5">
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-purple-400" />
                <span className="text-sm font-medium text-white/90">
                  Allow team members with approved email domains to auto-join
                </span>
                <input
                  type="checkbox"
                  checked={teamSettings.autoJoinEnabled}
                  onChange={e => {
                    const updated = { ...teamSettings, autoJoinEnabled: e.target.checked };
                    setTeamSettings(updated);
                    teamService.updateTeamSettings(companyId, updated);
                  }}
                  title="Enable email domain auto-join"
                  aria-label="Enable email domain auto-join"
                  className="w-4 h-4 text-indigo-600 bg-white/10 border-white/20 rounded focus:ring-indigo-500 focus:ring-offset-0"
                />
              </div>

              {teamSettings.autoJoinEnabled && (
                <div className="flex items-center space-x-2">
                  <StyledInput
                    value={newDomain}
                    onChange={e => setNewDomain(e.target.value)}
                    placeholder="domain.com"
                    className="w-32 text-sm"
                  />
                  <button
                    onClick={handleAddDomain}
                    className="px-3 py-2 bg-white/10 text-white/70 text-sm font-medium rounded border border-white/20 hover:bg-white/20 hover:text-white/90 transition-all"
                  >
                    Add Domain
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Team Management Table */}
          <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-full">
                    <Users className="w-6 h-6 text-blue-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-white">Team Management</h2>
                    <span className="text-sm text-white/60 font-normal">
                      {members.length} members, {invitations.length} pending
                    </span>
                  </div>
                </div>

                <button
                  onClick={() => setShowInviteModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-sm font-medium rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all shadow-lg"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Invite Member
                </button>
              </div>

              {/* Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-left py-3 px-4 text-sm font-medium text-white/70">
                        Member
                      </th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-white/70">
                        Role
                      </th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-white/70">
                        Status
                      </th>
                      <th className="text-left py-3 px-4 text-sm font-medium text-white/70">
                        Details
                      </th>
                      <th className="text-right py-3 px-4 text-sm font-medium text-white/70">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/5">
                    {/* Active Members */}
                    {members.map(member => (
                      <tr key={member.id} className="hover:bg-white/5 transition-colors">
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-white">
                                {member.email.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-white">{member.email}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          {editingMember === member.id ? (
                            <select
                              value={member.role}
                              onChange={e =>
                                handleUpdateMember(member, {
                                  role: e.target.value as CompanyMemberRole,
                                })
                              }
                              title={`Change role for ${member.email}`}
                              aria-label={`Change role for ${member.email}`}
                              className="text-sm bg-white/10 border border-white/20 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-white"
                            >
                              {roleOptions.map(option => (
                                <option
                                  key={option.value}
                                  value={option.value}
                                  className="bg-gray-800"
                                >
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          ) : (
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleBadgeColor(member.role)}`}
                            >
                              {member.role}
                            </span>
                          )}
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(member.status)}`}
                          >
                            {member.status}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span className="text-xs text-white/50">Member</span>
                        </td>
                        <td className="py-4 px-4 text-right">
                          {member.role !== CompanyMemberRole.OWNER && (
                            <div className="flex items-center justify-end space-x-2">
                              {editingMember === member.id ? (
                                <button
                                  onClick={() => setEditingMember(null)}
                                  className="text-green-400 hover:text-green-300 p-1"
                                  title="Save changes"
                                  aria-label="Save changes"
                                >
                                  <Check className="w-4 h-4" />
                                </button>
                              ) : (
                                <>
                                  <button
                                    onClick={() => setEditingMember(member.id)}
                                    className="text-gray-400 hover:text-white transition-colors p-1"
                                    title={`Edit ${member.email}`}
                                    aria-label={`Edit ${member.email}`}
                                  >
                                    <Edit2 className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={() => handleRemoveMember(member)}
                                    className="text-red-400 hover:text-red-300 transition-colors p-1"
                                    title={`Remove ${member.email}`}
                                    aria-label={`Remove ${member.email}`}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}

                    {/* Pending Invitations */}
                    {invitations.map(invitation => (
                      <tr key={invitation.id} className="hover:bg-white/5 transition-colors">
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center border border-yellow-500/30">
                              <Mail className="w-4 h-4 text-yellow-400" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-white">{invitation.email}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleBadgeColor(invitation.role)}`}
                          >
                            {invitation.role}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border bg-yellow-500/20 text-yellow-300 border-yellow-500/20">
                            PENDING
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span className="text-xs text-white/50 flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            Expires {new Date(invitation.expiresAt).toLocaleDateString()}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleResendInvitation(invitation.id)}
                              className="text-sm text-indigo-400 hover:text-indigo-300 transition-colors px-2 py-1"
                            >
                              Resend
                            </button>
                            <button
                              onClick={() => handleCancelInvitation(invitation.id)}
                              className="text-sm text-red-400 hover:text-red-300 transition-colors px-2 py-1"
                            >
                              Cancel
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}

                    {/* Empty state */}
                    {members.length === 0 && invitations.length === 0 && (
                      <tr>
                        <td colSpan={5} className="py-12 text-center">
                          <UserPlus className="w-12 h-12 text-white/30 mx-auto mb-4" />
                          <p className="text-white/60">
                            No team members yet. Invite your first team member!
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-2xl shadow-xl max-w-md w-full border border-white/10">
            <div className="p-6 border-b border-white/10">
              <h3 className="text-lg font-semibold text-white">Invite Team Member</h3>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="text-white/90 mb-2 text-sm">Email Address</label>
                <StyledInput
                  value={inviteForm.email}
                  onChange={e => setInviteForm({ ...inviteForm, email: e.target.value })}
                  placeholder="<EMAIL>"
                  type="email"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-white/90 mb-2 text-sm">Role</label>
                <StyledSelect
                  value={inviteForm.role}
                  onChange={value =>
                    setInviteForm({ ...inviteForm, role: value as CompanyMemberRole })
                  }
                  options={roleOptions}
                  className="mt-1"
                  showListOnEmpty
                />
              </div>
              <div>
                <label className="text-white/90 mb-2 text-sm">Message (Optional)</label>
                <textarea
                  value={inviteForm.message}
                  onChange={e => setInviteForm({ ...inviteForm, message: e.target.value })}
                  rows={3}
                  className="mt-1 block w-full bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20"
                  placeholder="Add a personal message to the invitation..."
                />
              </div>
            </div>
            <div className="px-6 py-4 bg-black/20 border-t border-white/10 flex justify-end space-x-3">
              <button
                onClick={() => setShowInviteModal(false)}
                className="px-4 py-2 text-sm font-medium text-white/70 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleInviteMember}
                className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all"
              >
                Send Invitation
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TeamManagement;
