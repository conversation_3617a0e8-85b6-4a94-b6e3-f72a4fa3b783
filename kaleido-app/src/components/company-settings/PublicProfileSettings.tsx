'use client';

import React, { useEffect, useState } from 'react';

import {
  ChevronDown,
  Eye,
  FootprintsIcon,
  Globe,
  Hexagon,
  ImageIcon,
  Layers,
  Palette,
  Send,
  Sparkles,
} from 'lucide-react';

import StyledInput from '@/components/common/styledInputs/StyledInput';
import ColorPicker from '@/components/company/ColorPicker';
import ImageSelector from '@/components/company/ImageSelector';
import MediaUpload from '@/components/company/MediaUpload';
import ProfileLayoutSettings from '@/components/company/ProfileLayoutSettings';
import SaveButton from '@/components/company/SaveButton';
import SettingsCard from '@/components/company/SettingsCard';
import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';

// Helper function to convert company name to slug format
function slugify(text: string) {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
}

export type ProfileStyleSettings = {
  layoutPreference: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  heroImage: string;
  footerImage: string;
  featuredImages: string[];
  customCss: string;
  logo?: string;
  companyName?: string;
  industry?: string;
};

type PublicProfileSettingsProps = {
  initialSettings: ProfileStyleSettings;
  onChange: (field: string, value: string | string[]) => void;
  companyId: string;
  clientId: string;
  handleSave: () => Promise<void>;
  handleCancel: () => void;
  isEditing: boolean;
};

// Custom settings section component with its own collapsible state
interface SettingsSectionProps {
  title: string;
  icon: React.ReactNode;
  iconBgColor: string;
  iconColor: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  gradientHeader?: React.ReactNode;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({
  title,
  icon,
  iconBgColor,
  children,
  defaultOpen = false,
  gradientHeader,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="overflow-hidden rounded-xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
      {gradientHeader}
      <div className="p-4">
        <div
          className="flex items-center justify-between w-full cursor-pointer"
          onClick={() => setIsOpen(!isOpen)}
        >
          <h2 className="text-base font-bold text-white flex items-center gap-2">
            <div className={`p-1.5 ${iconBgColor} rounded-full`}>{icon}</div>
            {title}
          </h2>
          <div className="p-1 rounded-md bg-white/10 hover:bg-white/20 transition-colors">
            <ChevronDown
              className={`h-4 w-4 text-white transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            />
          </div>
        </div>

        {isOpen && <div className="mt-3 pt-3 border-t border-white/10">{children}</div>}
      </div>
    </div>
  );
};

const PublicProfileSettings: React.FC<PublicProfileSettingsProps> = ({
  initialSettings,
  onChange,
  companyId,
  clientId,
  handleSave,
  handleCancel,
  isEditing,
}) => {
  const [previewUrl, setPreviewUrl] = useState('');
  const [isPublishing, setIsPublishing] = useState(false);

  useEffect(() => {
    // Set preview URL for the company profile
    if (initialSettings.companyName) {
      setPreviewUrl(`/company-profile/${slugify(initialSettings.companyName)}`);
    }
  }, [initialSettings.companyName]);

  const handlePublishProfile = async () => {
    try {
      setIsPublishing(true);
      await apiHelper.post(`/companies/${companyId}/publish`, {
        isPublished: true,
      });
      showToast({
        message: 'Company profile published successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error publishing company profile:', error);
      showToast({
        message: 'Failed to publish company profile',
        isSuccess: false,
      });
    } finally {
      setIsPublishing(false);
    }
  };

  // Helper function to save changes for a specific section
  const handleSaveSection = async (section: string) => {
    try {
      // Save the changes to the backend
      await apiHelper.patch(`/companies`, {
        companyId,
        primaryColor: initialSettings.primaryColor,
        secondaryColor: initialSettings.secondaryColor,
        accentColor: initialSettings.accentColor,
        layoutPreference: initialSettings.layoutPreference,
        heroImage: initialSettings.heroImage,
        footerImage: initialSettings.footerImage,
        featuredImages: initialSettings.featuredImages,
        customCss: initialSettings.customCss,
      });

      showToast({
        message: `${section} settings saved successfully`,
        isSuccess: true,
      });
    } catch (error) {
      console.error(`Error saving ${section} settings:`, error);
      showToast({
        message: `Failed to save ${section} settings`,
        isSuccess: false,
      });
    }
  };

  // Color gradient header for the color section - using a function to ensure it updates with color changes
  const renderColorGradientHeader = () => {
    return (
      <div
        className="h-10 rounded-t-xl relative overflow-hidden"
        style={
          {
            '--primary-color': initialSettings.primaryColor,
            '--secondary-color': initialSettings.secondaryColor,
            '--accent-color': initialSettings.accentColor,
            background: `linear-gradient(to right, var(--primary-color), var(--secondary-color), var(--accent-color))`,
          } as React.CSSProperties
        }
      >
        <div className="absolute inset-0 opacity-80 mix-blend-overlay">
          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
            <defs>
              <pattern id="smallGrid" width="8" height="8" patternUnits="userSpaceOnUse">
                <path
                  d="M 8 0 L 0 0 0 8"
                  fill="none"
                  stroke="white"
                  strokeWidth="0.5"
                  strokeOpacity="0.15"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#smallGrid)" />
          </svg>
        </div>
      </div>
    );
  };

  // Get the current color gradient header
  const ColorGradientHeader = renderColorGradientHeader();

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto max-h-[calc(100vh-180px)]">
        <div className="space-y-3 pb-20">
          {/* Visibility Control - Not Collapsible */}
          <div className="overflow-hidden rounded-xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-base font-bold text-white flex items-center gap-2">
                  <div className="p-1.5 bg-blue-500/20 rounded-full">
                    <Eye className="w-4 h-4 text-blue-400" />
                  </div>
                  Visibility Control
                </h2>
              </div>

              <p className="text-white/80 text-xs mb-3">
                Control the visibility of your company profile and access your public profile.
              </p>

              <div className="flex flex-wrap gap-2">
                {previewUrl && (
                  <a
                    href={previewUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center gap-1.5 px-3 py-1.5 bg-white/10 hover:bg-white/15
                      border border-white/20 rounded-md text-white text-sm transition-colors"
                  >
                    <Globe className="w-3.5 h-3.5" />
                    View Public Profile
                  </a>
                )}

                <button
                  type="button"
                  onClick={handlePublishProfile}
                  disabled={isPublishing}
                  className="flex items-center justify-center gap-1.5 px-3 py-1.5 bg-gradient-to-r
                    from-blue-600 to-indigo-600 hover:opacity-90 rounded-md text-white text-sm transition-colors"
                >
                  {isPublishing ? (
                    <>
                      <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-white"></div>
                      Publishing...
                    </>
                  ) : (
                    <>
                      <Send className="w-3.5 h-3.5" />
                      Publish Profile
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Layout Selection */}
          <SettingsSection
            title="Layout Selection"
            icon={<Layers className="w-4 h-4 text-indigo-400" />}
            iconBgColor="bg-indigo-500/20"
            iconColor="text-indigo-400"
            defaultOpen={true}
          >
            <p className="text-white/80 text-xs mb-3">
              Choose a landing page style that best showcases your brand.
            </p>

            <ProfileLayoutSettings
              companyId={companyId}
              clientId={clientId}
              currentLayout={initialSettings.layoutPreference || 'modern'}
              onLayoutChange={layout => {
                onChange('layoutPreference', layout);
              }}
            />
          </SettingsSection>

          {/* Color Scheme */}
          <SettingsSection
            title="Color Scheme"
            icon={<Palette className="w-4 h-4 text-purple-400" />}
            iconBgColor="bg-purple-500/20"
            iconColor="text-purple-400"
            gradientHeader={ColorGradientHeader}
          >
            <p className="text-white/80 text-xs mb-3">
              Define the color palette that reflects your brand identity.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              {[
                { id: 'primaryColor', label: 'Primary Color' },
                { id: 'secondaryColor', label: 'Secondary Color' },
                { id: 'accentColor', label: 'Accent Color' },
              ].map(color => (
                <SettingsCard key={color.id} className="bg-transparent border-none">
                  <ColorPicker
                    id={color.id}
                    label={color.label}
                    color={initialSettings[color.id as keyof typeof initialSettings] as string}
                    onChange={value => onChange(color.id, value)}
                  />
                </SettingsCard>
              ))}
            </div>

            <div className="flex justify-end">
              <SaveButton onClick={() => handleSaveSection('Color scheme')} text="Save Colors" />
            </div>
          </SettingsSection>

          {/* Media Uploads */}
          <SettingsSection
            title="Media Assets"
            icon={<ImageIcon className="w-4 h-4 text-sky-400" />}
            iconBgColor="bg-sky-500/20"
            iconColor="text-sky-400"
          >
            <p className="text-white/80 text-xs mb-3">Upload images that represent your company.</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
              {[
                {
                  id: 'logo',
                  label: 'Company Logo',
                  placeholder: 'Enter logo URL',
                  emptyIcon: <Hexagon className="h-8 w-8 text-white/20 mx-auto mb-1" />,
                  emptyText: 'No logo uploaded',
                  altText: initialSettings.companyName || 'Company Logo',
                  objectFit: 'object-contain',
                },
                {
                  id: 'heroImage',
                  label: 'Hero Image',
                  placeholder: 'Enter hero image URL',
                  emptyIcon: <ImageIcon className="h-8 w-8 text-white/20 mx-auto mb-1" />,
                  emptyText: 'No hero image',
                  altText: 'Hero',
                  objectFit: 'object-cover',
                },
              ].map(media => (
                <SettingsCard key={media.id} className="bg-transparent border-none">
                  <MediaUpload
                    label={media.label}
                    url={initialSettings[media.id as keyof typeof initialSettings] as string}
                    placeholder={media.placeholder}
                    emptyIcon={media.emptyIcon}
                    emptyText={media.emptyText}
                    altText={media.altText}
                    objectFit={media.objectFit}
                    onChange={value => onChange(media.id, value)}
                  />
                </SettingsCard>
              ))}
            </div>

            <div className="flex justify-end">
              <SaveButton onClick={() => handleSaveSection('Media assets')} text="Save Media" />
            </div>
          </SettingsSection>

          {/* Hero Background Selection */}
          <SettingsSection
            title="Hero Background Selection"
            icon={<ImageIcon className="w-4 h-4 text-emerald-400" />}
            iconBgColor="bg-emerald-500/20"
            iconColor="text-emerald-400"
          >
            <p className="text-white/80 text-xs mb-3">
              Choose a background image for your company profile hero section.
            </p>

            <div className="mb-3">
              <SettingsCard className="bg-transparent border-none">
                <ImageSelector
                  label="Hero Background"
                  currentImage={initialSettings.heroImage}
                  onChange={value => onChange('heroImage', value)}
                  imageBasePath="/images/templates"
                  imageCount={41}
                  imagePrefix="bg"
                  modalTitle="Select Hero Background Image"
                  emptyText="Click to select a hero background image"
                />
              </SettingsCard>
            </div>

            <div className="flex justify-end">
              <SaveButton
                onClick={() => handleSaveSection('Hero background')}
                text="Save Background"
              />
            </div>
          </SettingsSection>

          {/* Footer Image Selection */}
          <SettingsSection
            title="Footer Image Selection"
            icon={<FootprintsIcon className="w-4 h-4 text-blue-400" />}
            iconBgColor="bg-blue-500/20"
            iconColor="text-blue-400"
          >
            <p className="text-white/80 text-xs mb-3">
              Choose an image for your company profile footer section.
            </p>

            <div className="mb-3">
              <SettingsCard className="bg-transparent border-none">
                <ImageSelector
                  label="Footer Image"
                  currentImage={initialSettings.footerImage}
                  onChange={value => onChange('footerImage', value)}
                  imageBasePath="/images/templates"
                  imageCount={41}
                  imagePrefix="bg"
                  modalTitle="Select Footer Image"
                  emptyText="Click to select a footer image"
                />
              </SettingsCard>
            </div>

            <div className="flex justify-end">
              <SaveButton
                onClick={() => handleSaveSection('Footer image')}
                text="Save Footer Image"
              />
            </div>
          </SettingsSection>

          {/* Advanced Customization */}
          <SettingsSection
            title="Advanced Customization"
            icon={<Sparkles className="w-4 h-4 text-emerald-400" />}
            iconBgColor="bg-emerald-500/20"
            iconColor="text-emerald-400"
          >
            <p className="text-white/80 text-xs mb-3">
              Add custom CSS to further customize your company profile.
            </p>

            <SettingsCard className="mb-3">
              <div className="flex items-center justify-between bg-white/30 rounded-t-md px-3 py-1.5 border-b border-primary/10">
                <span className="text-white/70 text-xs font-mono">custom.css</span>
                <div className="flex space-x-1.5">
                  <span className="w-2 h-2 rounded-full bg-red-500/70"></span>
                  <span className="w-2 h-2 rounded-full bg-yellow-500/70"></span>
                  <span className="w-2 h-2 rounded-full bg-green-500/70"></span>
                </div>
              </div>
              <StyledInput
                multiline
                rows={5}
                placeholder="/* Enter your custom CSS here */"
                value={initialSettings.customCss}
                onChange={e => onChange('customCss', e.target.value)}
                className="w-full font-mono text-xs bg-white/10 rounded-b-md border-0"
              />
            </SettingsCard>

            <div className="flex justify-end">
              <SaveButton
                onClick={() => handleSaveSection('Advanced customization')}
                text="Save CSS"
              />
            </div>
          </SettingsSection>
        </div>

        {/* Publish Profile Button - Fixed at the bottom */}
        <div className="flex justify-end pt-4 border-t border-white/10 fixed bottom-0 left-0 right-0 bg-primary/90 backdrop-blur-lg p-4 z-10 w-full shadow-lg">
          <SaveButton
            onClick={handlePublishProfile}
            text="Publish Profile"
            isLoading={isPublishing}
            icon={<Send className="w-3.5 h-3.5" />}
          />
        </div>
      </div>
    </div>
  );
};

export default PublicProfileSettings;
