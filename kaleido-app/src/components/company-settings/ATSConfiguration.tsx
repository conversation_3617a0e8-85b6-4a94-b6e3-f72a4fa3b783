import React, { useState } from 'react';

import {
  AtSign,
  Building2,
  CheckCircle2,
  Database,
  Globe,
  Loader2,
  Shield,
  XCircle,
} from 'lucide-react';
import Image from 'next/image';

import StyledInput from '@/components/common/styledInputs/StyledInput';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import { ATSConfig } from '@/pages/company-settings';

interface ATSConfigurationProps {
  formData: {
    atsProvider: string;
    atsApiKey: string;
    atsSubdomain: string;
    atsCompanyId: string;
    atsAuthType: string;
    atsConfig: Record<string, any>;
  };
  handleChange: (field: string, value: string) => void;
  handleSave: () => Promise<void>;
  handleCancel: () => void;
  isTestingConnection: boolean;
  atsConfigurations: ATSConfig[];
  testConnection: (config: {
    provider: string;
    apiKey: string;
    subdomain?: string;
    companyId?: string;
    authType?: string;
  }) => Promise<boolean>;
  isEditing: boolean;
}

const ATSConfiguration: React.FC<ATSConfigurationProps> = ({
  formData,
  handleChange,
  handleSave,
  handleCancel,
  isTestingConnection,
  atsConfigurations,
  testConnection,
  isEditing,
}) => {
  const [connectionTestResult, setConnectionTestResult] = useState<{
    success?: boolean;
    message?: string;
  }>({});

  const handleTestConnection = async () => {
    if (!formData.atsProvider || !formData.atsApiKey) return;

    // Reset previous test results
    setConnectionTestResult({});

    // Special handling for Lever tokens
    const selectedAts = atsConfigurations.find(ats => ats.id === formData.atsProvider);
    const isLever = selectedAts?.name === 'Lever';

    if (isLever) {
      // If token starts with eyJ (JWT format) but using Basic auth
      if (formData.atsApiKey.startsWith('eyJ') && formData.atsAuthType === 'Basic') {
        // Automatically switch to Bearer auth for JWT tokens
        handleChange('atsAuthType', 'Bearer');
      }
      // If token doesn't look like JWT but using Bearer auth
      else if (!formData.atsApiKey.startsWith('eyJ') && formData.atsAuthType === 'Bearer') {
        setConnectionTestResult({
          success: false,
          message:
            "Your token doesn't look like a JWT token but you've selected Bearer authentication. If this is a legacy API key, try Basic authentication instead.",
        });
        return;
      }
    }

    try {
      // Add provider name to the config to ensure backend can identify it correctly
      const config = {
        provider: formData.atsProvider,
        providerName: selectedAts?.name || '', // Add provider name to help with identification
        apiKey: formData.atsApiKey,
        subdomain: formData.atsSubdomain,
        companyId: formData.atsCompanyId,
        authType: formData.atsAuthType,
      };

      const success = await testConnection(config);

      setConnectionTestResult({
        success,
        message: success
          ? 'Connection successful! Your API key is valid.'
          : 'Connection failed. Please check your credentials.',
      });
    } catch (error) {
      setConnectionTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred',
      });
    }
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 xl:grid-cols-5 gap-10">
        {/* Left column - ATS Provider Selection */}
        <div className="xl:col-span-3 space-y-8">
          <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
            <div className="p-6 md:p-8">
              <div className="space-y-6">
                {/* ATS Provider and Auth Type in the same row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col">
                    <label className="text-white mb-2 font-small flex items-center gap-2">
                      <Database className="w-5 h-5 text-white/70" />
                      ATS Provider
                    </label>
                    <StyledSelect
                      options={atsConfigurations.map(ats => ({
                        value: ats.id,
                        label: ats.name,
                        icon: ats.logoUrl && (
                          <Image
                            src={ats.logoUrl}
                            alt={`${ats.name} logo`}
                            width={20}
                            height={20}
                            className="w-5 h-5 mr-2 object-contain"
                          />
                        ),
                      }))}
                      value={formData.atsProvider}
                      onChange={value => handleChange('atsProvider', value)}
                      placeholder="Select ATS provider"
                      className="w-full"
                    />
                  </div>

                  {/* Auth Type Selection in the same row */}
                  {formData.atsProvider &&
                  atsConfigurations.find(ats => ats.id === formData.atsProvider)
                    ?.supportedAuthTypes &&
                  atsConfigurations.find(ats => ats.id === formData.atsProvider)?.supportedAuthTypes
                    ?.length > 1 ? (
                    <div className="flex flex-col">
                      <label className="text-white mb-2 font-small flex items-center gap-2">
                        <Shield className="w-5 h-5 text-white/70" />
                        Authentication Type
                      </label>
                      <StyledSelect
                        options={
                          atsConfigurations
                            .find(ats => ats.id === formData.atsProvider)
                            ?.supportedAuthTypes?.map(authType => ({
                              value: authType,
                              label:
                                authType === 'Basic' ? 'Basic (Username/Password)' : 'Bearer Token',
                            })) || []
                        }
                        value={
                          formData.atsAuthType ||
                          atsConfigurations.find(ats => ats.id === formData.atsProvider)
                            ?.authType ||
                          'Basic'
                        }
                        onChange={value => handleChange('atsAuthType', value)}
                        placeholder="Select authentication type"
                        className="w-full"
                      />
                      <p className="text-xs text-white/50 mt-1">
                        {formData.atsAuthType === 'Bearer' ||
                        (!formData.atsAuthType &&
                          atsConfigurations.find(ats => ats.id === formData.atsProvider)
                            ?.authType === 'Bearer')
                          ? 'Use Bearer for JWT/OAuth tokens that start with "eyJ..."'
                          : 'Use Basic for standard API keys'}
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col md:opacity-50">
                      <label className="text-white mb-2 font-small flex items-center gap-2">
                        <Shield className="w-5 h-5 text-white/70" />
                        Authentication Type
                      </label>
                      <StyledSelect
                        options={[]}
                        value=""
                        onChange={() => {}}
                        placeholder="Select ATS provider first"
                        className="w-full"
                        disabled={true}
                      />
                    </div>
                  )}
                </div>

                {formData.atsProvider && (
                  <>
                    <div className="flex flex-col">
                      <label className="text-white mb-2 font-small flex items-center gap-2">
                        <AtSign className="w-5 h-5 text-white/70" />
                        API Key
                      </label>
                      {/* Changed from StyledInput to textarea to see all content */}
                      <textarea
                        value={formData.atsApiKey}
                        onChange={e => handleChange('atsApiKey', e.target.value)}
                        placeholder="Enter your API key"
                        className="w-full h-[300px] bg-white/5 border border-white/10 rounded-lg text-white px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 placeholder-white/40"
                      />
                    </div>

                    {atsConfigurations
                      .find(ats => ats.id === formData.atsProvider)
                      ?.baseUrl.includes('<subdomain>') && (
                      <div className="flex flex-col">
                        <label className="text-white mb-2 font-small flex items-center gap-2">
                          <Globe className="w-5 h-5 text-white/70" />
                          Subdomain
                        </label>
                        <StyledInput
                          value={formData.atsSubdomain}
                          onChange={e => handleChange('atsSubdomain', e.target.value)}
                          placeholder="Enter your ATS subdomain"
                          className="w-full"
                        />
                      </div>
                    )}

                    {atsConfigurations
                      .find(ats => ats.id === formData.atsProvider)
                      ?.baseUrl.includes('{companyId}') && (
                      <div className="flex flex-col">
                        <label className="text-white mb-2 font-small flex items-center gap-2">
                          <Building2 className="w-5 h-5 text-white/70" />
                          Company ID
                        </label>
                        <StyledInput
                          value={formData.atsCompanyId}
                          onChange={e => handleChange('atsCompanyId', e.target.value)}
                          placeholder="Enter your ATS company ID"
                          className="w-full"
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right column - ATS Configuration Information */}
        <div className="xl:col-span-2 space-y-8">
          <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
            <div className="p-6 md:p-8">
              {formData.atsProvider ? (
                <div className="space-y-4">
                  {atsConfigurations.map(ats => {
                    if (ats.id === formData.atsProvider) {
                      const baseUrl = ats.baseUrl
                        .replace('<subdomain>', formData.atsSubdomain || '<subdomain>')
                        .replace('{companyId}', formData.atsCompanyId || '{companyId}');

                      return (
                        <div key={ats.id} className="space-y-4">
                          {/* ATS Provider Logo */}
                          {ats.fullLogoUrl && (
                            <div className="flex justify-center">
                              <Image
                                src={ats.fullLogoUrl}
                                alt={`${ats.name} logo`}
                                width={120}
                                height={48}
                                className="max-h-12 object-contain"
                              />
                            </div>
                          )}
                          <div className="bg-white/5 p-4 rounded-lg">
                            <p className="text-white/80 text-sm mb-2">Base URL:</p>
                            <code className="text-blue-300 text-xs block p-2 bg-black/30 rounded-md overflow-x-auto">
                              {baseUrl}
                            </code>
                          </div>
                          <div className="bg-white/5 p-4 rounded-lg">
                            <p className="text-white/80 text-sm mb-2">Job Endpoint:</p>
                            <code className="text-green-300 text-xs block p-2 bg-black/30 rounded-md overflow-x-auto">
                              {baseUrl}
                              {ats.jobEndpoint}
                            </code>
                          </div>
                          <div className="bg-white/5 p-4 rounded-lg">
                            <p className="text-white/80 text-sm mb-2">Applicant Endpoint:</p>
                            <code className="text-purple-300 text-xs block p-2 bg-black/30 rounded-md overflow-x-auto">
                              {baseUrl}
                              {ats.applicantEndpoint}
                            </code>
                          </div>
                          <div className="bg-white/5 p-4 rounded-lg">
                            <p className="text-white/80 text-sm mb-2">Auth Type:</p>
                            <div className="flex gap-2">
                              {/* Updated to show both auth types with one highlighted based on selection */}
                              <span
                                className={`text-white text-sm px-3 py-1 rounded-md ${formData.atsAuthType === 'Basic' ? 'bg-purple-700/50 border border-purple-500/50' : 'bg-black/30'}`}
                              >
                                Basic
                              </span>
                              <span
                                className={`text-white text-sm px-3 py-1 rounded-md ${formData.atsAuthType === 'Bearer' ? 'bg-purple-700/50 border border-purple-500/50' : 'bg-black/30'}`}
                              >
                                Bearer
                              </span>
                            </div>
                          </div>

                          {formData.atsApiKey && (
                            <div className="mt-6 space-y-4">
                              <button
                                className="w-full flex items-center justify-center gap-2 py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white text-sm transition-colors"
                                onClick={handleTestConnection}
                                disabled={isTestingConnection}
                              >
                                {isTestingConnection ? (
                                  <>
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                    Testing Connection...
                                  </>
                                ) : (
                                  <>
                                    <Database className="w-4 h-4" />
                                    Test Connection
                                  </>
                                )}
                              </button>

                              {connectionTestResult.success !== undefined && (
                                <div
                                  className={`p-3 rounded-lg ${
                                    connectionTestResult.success
                                      ? 'bg-green-500/20 text-green-300'
                                      : 'bg-red-500/20 text-red-300'
                                  } flex items-center gap-2 text-sm`}
                                >
                                  {connectionTestResult.success ? (
                                    <CheckCircle2 className="w-5 h-5 flex-shrink-0" />
                                  ) : (
                                    <XCircle className="w-5 h-5 flex-shrink-0" />
                                  )}
                                  <span>{connectionTestResult.message}</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-4 text-center">
                  <Database className="w-12 h-12 text-white/20 mb-3" />
                  <p className="text-white/60 text-sm mb-4">
                    Select an ATS provider to see integration details
                  </p>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 w-full mt-2">
                    {atsConfigurations.map(ats => (
                      <div
                        key={ats.id}
                        className="flex flex-col items-center p-3 bg-white/5 rounded-lg hover:bg-white/10 cursor-pointer transition-colors"
                        onClick={() => handleChange('atsProvider', ats.id)}
                      >
                        {ats.logoUrl && (
                          <Image
                            src={ats.logoUrl}
                            alt={`${ats.name} logo`}
                            width={32}
                            height={32}
                            className="h-8 w-auto object-contain mb-2"
                          />
                        )}
                        <span className="text-xs text-white/80">{ats.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Save button for ATS configuration */}
      <div className="flex justify-end pt-4 mt-6 border-t border-white/10 sticky bottom-0 bg-primary/20 backdrop-blur-lg p-4 rounded-lg">
        <div className="flex gap-3">
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors flex items-center gap-2"
          >
            <Database className="w-4 h-4" />
            Save ATS Configuration
          </button>
        </div>
      </div>
    </div>
  );
};

export default ATSConfiguration;
