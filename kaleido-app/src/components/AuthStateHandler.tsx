'use client';

import { showToast } from '@/components/Toaster';
import { useUser } from '@/hooks/useUser';
import { getAuthStateHandlerPublicPaths, isPublicRoute } from '@/types/publicRoutes';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface AuthStateHandlerProps {
  children: React.ReactNode;
}

/**
 * Component that handles authentication state and provides graceful handling
 * for unauthenticated users and authentication errors
 */
export default function AuthStateHandler({ children }: AuthStateHandlerProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [hasShownWarning, setHasShownWarning] = useState(false);
  
  // Always call hooks in the same order
  const { user, isLoading, error } = useUser();
  
  // Check if we're on a public route
  const isOnPublicRoute = isPublicRoute(pathname);

  useEffect(() => {
    // Skip all auth checks on public routes
    if (isOnPublicRoute) return;
    
    // Skip if still loading
    if (isLoading) return;

    // Handle authentication errors
    if (error && !hasShownWarning) {
      console.error('Authentication error:', error);

      showToast({
        message: 'Authentication error occurred. Please try logging in again.',
        isSuccess: false,
      });

      setHasShownWarning(true);

      // Redirect to login after a delay
      setTimeout(() => {
        router.push('/api/auth/login');
      }, 2000);

      return;
    }

    // Check if user should be authenticated but isn't
    const currentPath = window.location.pathname;
    const publicPaths = getAuthStateHandlerPublicPaths();
    const isProtectedRoute = !publicPaths.some(
      publicPath => currentPath === publicPath || currentPath.startsWith(publicPath + '/')
    );

    if (isProtectedRoute && !user && !isLoading && !hasShownWarning) {
      console.warn('User not authenticated on protected route:', currentPath);

      showToast({
        message: 'Please log in to access this page',
        isSuccess: false,
      });

      setHasShownWarning(true);

      // Redirect to login with return URL
      const returnTo = encodeURIComponent(currentPath + window.location.search);
      setTimeout(() => {
        router.push(`/api/auth/login?returnTo=${returnTo}`);
      }, 1500);
    }
  }, [user, isLoading, error, hasShownWarning, router, isOnPublicRoute]);

  // Listen for auth session changes
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_session' && e.newValue === null) {
        // Auth session was cleared

        if (!hasShownWarning) {
          showToast({
            message: 'Your session has ended. Please log in again.',
            isSuccess: false,
          });
          setHasShownWarning(true);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [hasShownWarning]);

  // Reset warning flag when user successfully authenticates
  useEffect(() => {
    if (user && hasShownWarning) {
      setHasShownWarning(false);
    }
  }, [user, hasShownWarning]);

  return <>{children}</>;
}
