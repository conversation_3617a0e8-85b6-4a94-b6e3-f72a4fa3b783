'use client';

import apiHelper from '@/lib/apiHelper';
import { AuthCacheManager } from '@/lib/auth-cache-manager';
import { authService } from '@/services/auth.service';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface PostLoginRoleHandlerProps {
  children: React.ReactNode;
}

/**
 * Industry-standard post-login role handler
 * Syncs roles to Auth0 JWT claims for secure, server-verified access control
 */
export const PostLoginRoleHandler: React.FC<PostLoginRoleHandlerProps> = ({ children }) => {
  const { user, isLoading } = useUser();
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(true);
  const cacheManager = AuthCacheManager.getInstance();

  useEffect(() => {
    const syncUserRole = async () => {
      // Skip on auth pages
      const currentPath = window.location.pathname;
      if (['/session-expired', '/api/auth'].some(path => currentPath.startsWith(path))) {
        setIsProcessing(false);
        return;
      }

      // Wait for Auth0
      if (isLoading) return;

      // No user = not authenticated
      if (!user?.sub) {
        setIsProcessing(false);
        return;
      }

      try {
        // Check if role exists in JWT claims (industry standard)
        const session = await authService.getSession();

        if (session?.role) {
          setIsProcessing(false);
          return;
        }

        // Check if user has pendingRole in their profile (from Auth0)
        if (user.pendingRole && Object.values(UserRole).includes(user.pendingRole as UserRole)) {
          try {
            // Try to sync to Auth0 app_metadata
            await apiHelper.post('/auth/sync-role', { role: user.pendingRole });
          } catch (error: any) {
            // If we get a 403, it likely means the user already has a role
            // This is fine, just log it and continue
            if (error?.status === 403) {
            } else {
              console.warn('Failed to sync role to Auth0:', error?.message);
            }
          }

          // Store in localStorage for immediate use regardless of sync result
          localStorage.setItem(
            `userRole_${user.sub}`,
            JSON.stringify({
              role: user.pendingRole,
              clientId: user.sub,
              timestamp: Date.now(),
            })
          );

          // Update cache with new role
          cacheManager.updateCachedRole(user.pendingRole as UserRole);

          setIsProcessing(false);
          return;
        }

        // Check for pending role selection (from landing page)
        const pendingRole = localStorage.getItem('pendingUserRole');
        const pendingTimestamp = localStorage.getItem('pendingUserRoleTimestamp');

        if (pendingRole && pendingTimestamp) {
          const isRecent = Date.now() - parseInt(pendingTimestamp) < 10 * 60 * 1000; // 10 min

          if (isRecent && Object.values(UserRole).includes(pendingRole as UserRole)) {
            try {
              // Try to sync to Auth0 (will be in JWT on next refresh)
              await apiHelper.post('/auth/sync-role', { role: pendingRole });
            } catch (error: any) {
              // If we get a 403, it likely means the user already has a role
              // This is fine, just log it and continue
              if (error?.status === 403) {
              } else {
                console.warn('Failed to sync role to Auth0:', error?.message);
              }
            }

            // Store in localStorage for immediate use regardless of sync result
            localStorage.setItem(
              `userRole_${user.sub}`,
              JSON.stringify({
                role: pendingRole,
                clientId: user.sub,
                timestamp: Date.now(),
              })
            );

            // Clear pending role
            localStorage.removeItem('pendingUserRole');
            localStorage.removeItem('pendingUserRoleTimestamp');

            // Update cache with new role
            cacheManager.updateCachedRole(pendingRole as UserRole);

            // Note: Role will be in JWT on next page refresh
            setIsProcessing(false);
            return;
          }
        }

        // Check backend for existing role
        try {
          const roleResponse = await apiHelper.get(`/roles/${user.sub}`);
          if (roleResponse.role) {
            try {
              // Try to sync to Auth0
              await apiHelper.post('/auth/sync-role', { role: roleResponse.role });
            } catch (syncError: any) {
              // If we get a 403, it likely means the user already has a role
              // This is fine, just log it and continue
              if (syncError?.status === 403) {
              } else {
                console.warn('Failed to sync role to Auth0:', syncError?.message);
              }
            }

            // Store in localStorage for immediate use regardless of sync result
            localStorage.setItem(
              `userRole_${user.sub}`,
              JSON.stringify({
                role: roleResponse.role,
                clientId: user.sub,
                timestamp: Date.now(),
              })
            );

            // Update cache with new role
            cacheManager.updateCachedRole(roleResponse.role as UserRole);

            // Note: Role will be in JWT on next page refresh
            setIsProcessing(false);
            return;
          }
        } catch (error) {
          // Failed to get role from backend, this is fine for new users
        }

        // No role found anywhere - redirect to selection
        setIsProcessing(false);
        router.push('/?selectRole=true');
      } catch (error) {
        console.error('PostLoginRoleHandler: Error:', error);
        setIsProcessing(false);
      }
    };

    syncUserRole();
  }, [user, isLoading, router]);

  // Don't block rendering - role checks happen in components
  return <>{children}</>;
};
