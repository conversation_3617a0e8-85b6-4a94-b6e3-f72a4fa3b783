import '@testing-library/jest-dom';
import { render, waitFor } from '@testing-library/react';

import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';
import { PostLoginRoleHandler } from '../PostLoginRoleHandler';

// Mock dependencies
jest.mock('@auth0/nextjs-auth0/client');
jest.mock('@/lib/apiHelper', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
  },
  waitForAuthSession: jest.fn().mockResolvedValue(true),
  markAuthInitializing: jest.fn(),
}));
jest.mock('../PendingDataProcessor', () => ({
  __esModule: true,
  default: () => null,
}));
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));
jest.mock('@/lib/auth-cache-manager', () => ({
  AuthCacheManager: {
    getInstance: () => ({
      cacheAuthData: jest.fn(),
      getCachedAuth: jest.fn(),
      clearCache: jest.fn(),
      updateCachedRole: jest.fn(),
    }),
  },
}));
jest.mock('@/services/auth.service', () => ({
  authService: {
    checkAndUpdateRole: jest.fn(),
    setUserRole: jest.fn(),
    getSession: jest.fn().mockResolvedValue(null),
  },
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;

describe('PostLoginRoleHandler - Role Sync Flow', () => {
  const mockUser = {
    sub: 'auth0|test123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  let originalConsoleWarn: typeof console.warn;

  beforeAll(() => {
    // Save original console.warn
    originalConsoleWarn = console.warn;
    // Mock console.warn to suppress warnings in tests
    console.warn = jest.fn();
  });

  afterAll(() => {
    // Restore original console.warn
    console.warn = originalConsoleWarn;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockRouterPush.mockClear();
    localStorage.clear();
    sessionStorage.clear();

    // Mock window.location
    delete (window as any).location;
    (window as any).location = {
      href: '/',
      pathname: '/',
      search: '',
      hash: '',
    };

    // Set auth session ready
    sessionStorage.setItem('auth_session_ready', 'true');
    localStorage.setItem('auth_session', JSON.stringify({ accessToken: 'test-token' }));
  });

  describe('Successful Role Sync', () => {
    it('should sync employer role from localStorage to backend', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found')); // No existing role
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.EMPLOYER,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledWith('/auth/sync-role', {
          role: UserRole.EMPLOYER,
        });
      });

      // Verify localStorage was updated
      await waitFor(() => {
        const storedRole = localStorage.getItem(`userRole_${mockUser.sub}`);
        expect(storedRole).toBeTruthy();
        const roleData = JSON.parse(storedRole!);
        expect(roleData.role).toBe(UserRole.EMPLOYER);
      });
    });

    it('should sync job seeker role from localStorage', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.JOB_SEEKER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.JOB_SEEKER,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledWith('/auth/sync-role', {
          role: UserRole.JOB_SEEKER,
        });
      });
    });

    it('should handle user with pendingRole from sessionStorage', async () => {
      // Setup - Role from Auth0 user profile
      mockUseUser.mockReturnValue({
        user: { ...mockUser, pendingRole: UserRole.EMPLOYER },
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.EMPLOYER,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledWith('/auth/sync-role', {
          role: UserRole.EMPLOYER,
        });
      });
    });
  });

  describe('Sync Failures', () => {
    it('should use existing backend role when sync fails due to different role', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found')); // No existing role
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.EMPLOYER,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalled();
      });

      // Should use the backend role
      await waitFor(() => {
        const storedRole = localStorage.getItem(`userRole_${mockUser.sub}`);
        expect(storedRole).toBeTruthy();
        const roleData = JSON.parse(storedRole!);
        expect(roleData.role).toBe(UserRole.EMPLOYER); // localStorage role synced
      });
    });

    it('should redirect to role selection when sync fails with no backend role', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));
      mockApiHelper.post.mockResolvedValueOnce({
        success: false,
        message: 'Failed to sync role',
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert
      await waitFor(() => {
        expect(mockRouterPush).toHaveBeenCalledWith('/?selectRole=true');
      });
    });

    it('should handle network errors gracefully', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockResolvedValueOnce({ role: UserRole.EMPLOYER }); // Has backend role
      mockApiHelper.post.mockRejectedValueOnce(new Error('Network error'));

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should fall back to backend role
      await waitFor(() => {
        const storedRole = localStorage.getItem(`userRole_${mockUser.sub}`);
        expect(storedRole).toBeTruthy();
        const roleData = JSON.parse(storedRole!);
        expect(roleData.role).toBe(UserRole.EMPLOYER);
      });
    });
  });

  describe('Role Validation', () => {
    it('should ignore expired role in localStorage', async () => {
      // Clear mocks to ensure clean state
      mockApiHelper.post.mockClear();
      mockApiHelper.get.mockClear();

      // Setup - Role from 1 hour ago
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', (Date.now() - 60 * 60 * 1000).toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      // Mock that the user has a role in the backend
      mockApiHelper.get.mockResolvedValueOnce({ role: UserRole.EMPLOYER });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Wait for the component to process
      await waitFor(() => {
        expect(mockApiHelper.get).toHaveBeenCalledWith(`/roles/${mockUser.sub}`);
      });

      // Since the backend returns a role, it should sync that role (not the expired pending role)
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledWith('/auth/sync-role', {
          role: UserRole.EMPLOYER, // This is from the backend, not the expired localStorage
        });
      });

      // The expired pending role should still be in localStorage (not cleared since it wasn't used)
      expect(localStorage.getItem('pendingUserRole')).toBe(UserRole.EMPLOYER);
    });

    it('should clean up old localStorage data', async () => {
      // Setup - Old timestamp (more than 10 minutes)
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', (Date.now() - 11 * 60 * 1000).toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockResolvedValueOnce({ role: UserRole.EMPLOYER });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should not use the old pending role for sync
      await waitFor(() => {
        expect(mockApiHelper.post).not.toHaveBeenCalledWith('/auth/sync-role', expect.any(Object));
      });
    });

    it('should validate role is in UserRole enum', async () => {
      // Setup - Invalid role
      localStorage.setItem('pendingUserRole', 'INVALID_ROLE');
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Wait for the component to process
      await waitFor(() => {
        // The component should either redirect or not sync invalid roles
        expect(mockRouterPush).toHaveBeenCalledWith('/?selectRole=true');
      });

      // Should not sync invalid roles
      expect(mockApiHelper.post).not.toHaveBeenCalledWith('/auth/sync-role', {
        role: 'INVALID_ROLE',
      });
    });
  });

  describe('Edge Cases', () => {
    it('should skip processing for whitelisted admin emails', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);

      const adminUser = {
        ...mockUser,
        email: '<EMAIL>', // Assuming this is whitelisted
      };

      mockUseUser.mockReturnValue({
        user: adminUser,
        isLoading: false,
        error: undefined,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should not make any API calls
      await waitFor(
        () => {
          expect(mockApiHelper.get).not.toHaveBeenCalled();
          expect(mockApiHelper.post).not.toHaveBeenCalled();
        },
        { timeout: 1000 }
      );
    });

    it('should not process when auth session is not ready', async () => {
      // Setup
      sessionStorage.removeItem('auth_session_ready');
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should not make API calls
      await waitFor(
        () => {
          expect(mockApiHelper.post).not.toHaveBeenCalled();
        },
        { timeout: 1000 }
      );
    });

    it('should handle user without sub/userId', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);

      const invalidUser = {
        email: '<EMAIL>',
        // Missing sub
      };

      mockUseUser.mockReturnValue({
        user: invalidUser as any,
        isLoading: false,
        error: undefined,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should not process
      await waitFor(
        () => {
          expect(mockApiHelper.post).not.toHaveBeenCalled();
        },
        { timeout: 1000 }
      );
    });

    it('should only process once per user session', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.EMPLOYER,
      });

      // Execute - First render
      const { rerender } = render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledTimes(1);
      });

      // Execute - Second render
      rerender(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - Should not make additional calls
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledTimes(1); // Still only 1
      });
    });
  });

  describe('Cookie Cleanup', () => {
    it('should clean up pending role cookies after sync', async () => {
      // Setup
      localStorage.setItem('pendingUserRole', UserRole.EMPLOYER);
      localStorage.setItem('pendingUserRoleTimestamp', Date.now().toString());
      document.cookie = `pendingRole_${mockUser.sub}=test`;

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
        error: undefined,
      });

      mockApiHelper.get.mockRejectedValueOnce(new Error('Not found'));
      mockApiHelper.post.mockResolvedValueOnce({
        success: true,
        message: 'Role synced successfully',
        role: UserRole.EMPLOYER,
      });

      // Execute
      render(
        <PostLoginRoleHandler>
          <div>Test Child</div>
        </PostLoginRoleHandler>
      );

      // Assert - The component successfully syncs and sets the role
      await waitFor(() => {
        expect(mockApiHelper.post).toHaveBeenCalledWith('/auth/sync-role', expect.any(Object));
      });

      // Verify the role was stored in localStorage
      await waitFor(() => {
        const storedRole = localStorage.getItem(`userRole_${mockUser.sub}`);
        expect(storedRole).toBeTruthy();
        const roleData = JSON.parse(storedRole!);
        expect(roleData.role).toBe(UserRole.EMPLOYER);
      });
    });
  });
});
