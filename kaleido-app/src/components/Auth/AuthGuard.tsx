'use client';

import { useUser } from '@/hooks/useUser';
import { useRouter } from 'next/router';
import { ReactNode, useEffect } from 'react';

import { markAuthInitializing } from '@/lib/apiHelper';
import { getClientPublicRoutes } from '@/types/publicRoutes';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Enhanced guard that ensures Auth0 has finished loading and properly initializes auth session
 * This prevents components from making API calls before Auth0 is ready
 */
export default function AuthGuard({ children, fallback = null }: AuthGuardProps) {
  const { user, isLoading, error } = useUser();
  const router = useRouter();

  // Get public routes from centralized definition
  const publicRoutes = getClientPublicRoutes();

  // Check if current route is public
  const isPublicRoute = publicRoutes.some(
    route => router.pathname === route || router.pathname.startsWith(route + '/')
  );

  useEffect(() => {
    // Skip auth initialization for public routes
    if (isPublicRoute) {
      return;
    }

    // Mark auth as initializing when the component mounts
    markAuthInitializing();

    // Set up auth session ready flag when auth is complete
    if (!isLoading && typeof window !== 'undefined') {
      // Set a flag to indicate auth session is ready
      sessionStorage.setItem('auth_session_ready', 'true');

      // If we have a user, store the auth session data
      if (user?.sub) {
        // Check if we already have auth session in localStorage
        const existingSession = localStorage.getItem('auth_session');
        if (!existingSession) {
          // Create a basic auth session structure if it doesn't exist
          // This will be populated by the actual Auth0 token when API calls are made
          const basicSession = {
            user: user,
            accessToken: null, // Will be set by actual Auth0 token
            expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours from now
          };
          localStorage.setItem('auth_session', JSON.stringify(basicSession));
        }
      }
    }
  }, [isLoading, user, isPublicRoute]);

  // For public routes, render immediately without auth checks
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // If Auth0 is still loading, don't render children
  if (isLoading) {
    return <>{fallback}</>;
  }

  // If there's an Auth0 error, check if it requires logout
  if (error) {
    console.error('Auth0 error in AuthGuard:', error);

    // Check if this is a session expiration error
    if (error.message?.includes('401') || error.message?.includes('expired')) {
      // Clear all auth data and redirect to logout
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
        document.cookie.split(';').forEach(cookie => {
          const eqPos = cookie.indexOf('=');
          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        });
        window.location.href = '/api/auth/logout';
        return <>{fallback}</>;
      }
    }

    return <>{fallback}</>;
  }

  // Auth0 has finished loading (user exists or doesn't exist), safe to render children
  return <>{children}</>;
}
