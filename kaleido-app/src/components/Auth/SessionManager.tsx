import { useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { authService } from '@/services/auth.service';

/**
 * Global session manager component
 * Monitors auth state and handles session expiration
 */
export const SessionManager: React.FC = () => {
  const { user, error, isLoading } = useUser();

  useEffect(() => {
    if (!isLoading && error) {
      console.error('Auth error detected:', error);

      // Check if this is an authentication error
      if (
        error.message?.includes('expired') ||
        error.message?.includes('refresh') ||
        error.message?.includes('authentication')
      ) {
        authService.clearSessionCache();

        // Get current path for returnTo
        const currentPath = window.location.pathname + window.location.search;
        window.location.href = `/api/auth/login?returnTo=${encodeURIComponent(currentPath)}`;
      }
    }
  }, [error, isLoading]);

  // Set up periodic session check
  useEffect(() => {
    if (!user) return;

    const checkSession = async () => {
      try {
        const session = await authService.getSession();
        if (!session) {
        }
      } catch (error) {
        console.error('Session check error:', error);
      }
    };

    // Check session every 5 minutes
    const interval = setInterval(checkSession, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user]);

  return null;
};
