import React from 'react';
import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Button,
  Hr,
  Img,
  Link,
} from '@react-email/components';

interface TeamInvitationEmailProps {
  inviterName: string;
  companyName: string;
  recipientEmail: string;
  role: string;
  invitationUrl: string;
  expiresAt: Date;
  message?: string;
}

const TeamInvitationEmail: React.FC<TeamInvitationEmailProps> = ({
  inviterName,
  companyName,
  recipientEmail,
  role,
  invitationUrl,
  expiresAt,
  message,
}) => {
  const expirationDate = new Date(expiresAt).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Img
              src="https://res.cloudinary.com/dlvntmjgj/image/upload/v1734125461/logokaleido-removebg-preview_f0eehw.png"
              width="150"
              height="50"
              alt="Kaleido Talent"
              style={{ margin: '0 auto' }}
            />
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={heading}>You're invited to join {companyName}!</Text>

            <Text style={paragraph}>Hi there,</Text>

            <Text style={paragraph}>
              {inviterName} has invited you to join <strong>{companyName}</strong> as a{' '}
              <strong>{role}</strong> on Kaleido Talent.
            </Text>

            {message && (
              <Section style={messageBox}>
                <Text style={messageText}>
                  <strong>Message from {inviterName}:</strong>
                </Text>
                <Text style={messageText}>"{message}"</Text>
              </Section>
            )}

            <Text style={paragraph}>By accepting this invitation, you'll be able to:</Text>

            <ul style={list}>
              {role === 'admin' && (
                <>
                  <li>Manage job postings and candidates</li>
                  <li>Invite and manage team members</li>
                  <li>View analytics and reports</li>
                  <li>Configure company settings</li>
                </>
              )}
              {role === 'member' && (
                <>
                  <li>View and manage job postings</li>
                  <li>Review and manage candidates</li>
                  <li>Access analytics and reports</li>
                </>
              )}
              {role === 'viewer' && (
                <>
                  <li>View job postings</li>
                  <li>View candidate profiles</li>
                  <li>Access analytics reports</li>
                </>
              )}
            </ul>

            <Section style={buttonContainer}>
              <Button href={invitationUrl} style={button}>
                Accept Invitation
              </Button>
            </Section>

            <Text style={smallText}>Or copy and paste this URL into your browser:</Text>
            <Link href={invitationUrl} style={link}>
              {invitationUrl}
            </Link>

            <Hr style={divider} />

            <Text style={warningText}>
              This invitation will expire on <strong>{expirationDate}</strong>.
            </Text>

            <Text style={smallText}>
              If you didn't expect this invitation, you can safely ignore this email.
            </Text>
          </Section>

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              © {new Date().getFullYear()} Kaleido Talent. All rights reserved.
            </Text>
            <Text style={footerText}>
              <Link href="https://kaleidotalent.com" style={footerLink}>
                kaleidotalent.com
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  width: '560px',
};

const header = {
  padding: '24px',
  backgroundColor: '#ffffff',
  borderRadius: '8px 8px 0 0',
  textAlign: 'center' as const,
};

const content = {
  padding: '24px',
  backgroundColor: '#ffffff',
  borderRadius: '0 0 8px 8px',
};

const heading = {
  fontSize: '24px',
  fontWeight: '600',
  color: '#1a1a1a',
  margin: '0 0 20px',
  textAlign: 'center' as const,
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
  color: '#404040',
  margin: '0 0 16px',
};

const list = {
  fontSize: '16px',
  lineHeight: '26px',
  color: '#404040',
  margin: '16px 0',
  paddingLeft: '20px',
};

const messageBox = {
  backgroundColor: '#f3f4f6',
  borderRadius: '8px',
  padding: '16px',
  margin: '20px 0',
};

const messageText = {
  fontSize: '15px',
  lineHeight: '24px',
  color: '#404040',
  margin: '0 0 8px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#4f46e5',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 32px',
};

const link = {
  color: '#4f46e5',
  textDecoration: 'underline',
  fontSize: '14px',
  wordBreak: 'break-all' as const,
};

const smallText = {
  fontSize: '14px',
  lineHeight: '24px',
  color: '#666666',
  margin: '0 0 8px',
};

const warningText = {
  fontSize: '14px',
  lineHeight: '24px',
  color: '#ea580c',
  margin: '16px 0 8px',
};

const divider = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};

const footer = {
  padding: '24px',
  textAlign: 'center' as const,
};

const footerText = {
  fontSize: '12px',
  lineHeight: '20px',
  color: '#666666',
  margin: '0',
};

const footerLink = {
  color: '#4f46e5',
  textDecoration: 'underline',
};

export default TeamInvitationEmail;
