import { fireEvent, render, screen } from '@testing-library/react';

import ErrorModal from './ErrorModal';

// Mock the stores
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: () => ({
    refreshJobs: jest.fn(),
    invalidateJobsCache: jest.fn(),
  }),
}));

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

// Mock createPortal to render in the same DOM
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (children: any) => children,
}));

// Mock showToast
jest.mock('@/components/Toaster', () => ({
  showToast: jest.fn(),
}));

describe('Error Handling Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ErrorModal User-Friendly Messages', () => {
    it('should convert 404 errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="404 Not Found" title="Error" />);

      // Should show user-friendly message for 404 errors
      expect(screen.getByText(/couldn't find what you're looking for/)).toBeInTheDocument();
      expect(screen.queryByText('404')).not.toBeInTheDocument();
    });

    it('should convert network errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="Network error occurred" title="Error" />);

      // Should show user-friendly message for network errors
      expect(screen.getByText(/trouble connecting to our servers/)).toBeInTheDocument();
      expect(screen.queryByText('Network error')).not.toBeInTheDocument();
    });

    it('should convert server errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="500 Internal Server Error" title="Error" />);

      // Should show user-friendly message for server errors
      expect(screen.getByText(/servers are experiencing some issues/)).toBeInTheDocument();
      expect(screen.queryByText('500')).not.toBeInTheDocument();
    });

    it('should convert timeout errors to user-friendly messages', () => {
      render(<ErrorModal isOpen={true} message="Request timed out" title="Error" />);

      // Should show user-friendly message for timeout errors
      expect(screen.getByText(/taking longer than expected/)).toBeInTheDocument();
      expect(screen.queryByText('timed out')).not.toBeInTheDocument();
    });

    it('should show default message for unknown errors', () => {
      render(<ErrorModal isOpen={true} message="Some random technical error" title="Error" />);

      // Should show default user-friendly message
      expect(screen.getByText(/having trouble loading this page/)).toBeInTheDocument();
      expect(screen.queryByText('technical error')).not.toBeInTheDocument();
    });
  });

  describe('ErrorModal Features', () => {
    it('should have proper accessibility attributes', () => {
      render(<ErrorModal isOpen={true} message="Test error" title="Error" />);

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('type', 'button');
      });
    });

    it('should display error icon and proper styling', () => {
      render(<ErrorModal isOpen={true} message="Test error" title="Error" />);

      // Should have the alert circle icon
      const alertIcon = screen.getByRole('img', { hidden: true });
      expect(alertIcon).toBeInTheDocument();
    });

    it('should handle custom retry function', () => {
      const mockRetry = jest.fn();
      const mockClose = jest.fn();

      render(
        <ErrorModal
          isOpen={true}
          message="Test error"
          title="Error"
          onRetry={mockRetry}
          onClose={mockClose}
        />
      );

      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      expect(mockRetry).toHaveBeenCalledTimes(1);
      expect(mockClose).toHaveBeenCalledTimes(1);
    });

    it('should show/hide continue button based on props', () => {
      const { rerender } = render(
        <ErrorModal
          isOpen={true}
          message="Test error"
          title="Error"
          showContinueButton={true}
          onClose={jest.fn()}
        />
      );

      rerender(
        <ErrorModal
          isOpen={true}
          message="Test error"
          title="Error"
          showContinueButton={false}
          onClose={jest.fn()}
        />
      );
    });
  });
});
