'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Target,
  TrendingUp,
  Star,
  Zap,
  Clock,
  Trophy,
  CheckCircle2,
  AlertCircle,
  ArrowRight,
  Sparkles,
  Shield,
  Rocket,
  Calendar,
  Flag,
  ChevronRight,
  LightbulbIcon,
  Activity,
  Gauge,
  Award,
  Briefcase,
  GraduationCap,
  Users,
  Brain,
  Heart,
  BarChart3,
  ArrowUpRight,
  Timer,
  Info,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface Recommendation {
  action: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  expectedOutcome: string;
  timeframe: string;
}

interface AiInsights {
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: Recommendation[];
  confidenceScore?: number;
}

interface ModernRecommendationsProps {
  aiInsights: AiInsights;
  className?: string;
}

const priorityConfig = {
  HIGH: {
    icon: <Flag className="w-4 h-4" />,
    color: 'text-red-400',
    bg: 'bg-red-500/10',
    border: 'border-red-500/20',
    badge: 'bg-gradient-to-r from-red-500 to-orange-500',
    glow: 'shadow-red-500/20',
  },
  MEDIUM: {
    icon: <AlertCircle className="w-4 h-4" />,
    color: 'text-amber-400',
    bg: 'bg-amber-500/10',
    border: 'border-amber-500/20',
    badge: 'bg-gradient-to-r from-amber-500 to-yellow-500',
    glow: 'shadow-amber-500/20',
  },
  LOW: {
    icon: <Info className="w-4 h-4" />,
    color: 'text-blue-400',
    bg: 'bg-blue-500/10',
    border: 'border-blue-500/20',
    badge: 'bg-gradient-to-r from-blue-500 to-cyan-500',
    glow: 'shadow-blue-500/20',
  },
};

const timeframeIcons: Record<string, React.ReactNode> = {
  immediate: <Zap className="w-4 h-4" />,
  'short-term': <Clock className="w-4 h-4" />,
  'medium-term': <Calendar className="w-4 h-4" />,
  'long-term': <Target className="w-4 h-4" />,
};

export default function ModernRecommendations({
  aiInsights,
  className = '',
}: ModernRecommendationsProps) {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'actions'>('overview');
  const [expandedActions, setExpandedActions] = useState<Set<number>>(new Set());

  const getTimeframeIcon = (timeframe: string) => {
    const lowerTimeframe = timeframe.toLowerCase();
    for (const [key, icon] of Object.entries(timeframeIcons)) {
      if (lowerTimeframe.includes(key)) return icon;
    }
    return <Timer className="w-4 h-4" />;
  };

  const totalInsights =
    aiInsights.strengths.length + aiInsights.opportunities.length + aiInsights.challenges.length;

  const tabs = [
    { id: 'overview', label: 'Quick Overview', icon: <Sparkles className="w-4 h-4" /> },
    { id: 'actions', label: 'Action Plan', icon: <Rocket className="w-4 h-4" /> },
  ];

  return (
    <div className={cn('space-y-6', className)}>
      {/* Modern Tab Navigation */}
      <div className="flex items-center justify-center mb-8">
        <div className="bg-white/5 backdrop-blur-sm rounded-full p-1 flex">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as 'overview' | 'actions')}
              className={cn(
                'relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300',
                selectedTab === tab.id ? 'text-white' : 'text-gray-400 hover:text-gray-200'
              )}
            >
              {selectedTab === tab.id && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                />
              )}
              <span className="relative">{tab.icon}</span>
              <span className="relative text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      <AnimatePresence mode="wait">
        {selectedTab === 'overview' ? (
          <motion.div
            key="overview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="space-y-6"
          >
            {/* Confidence Score */}
            {aiInsights.confidenceScore !== undefined && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.1 }}
                className="text-center mb-8"
              >
                <div className="inline-flex items-center space-x-3 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm">
                  <Gauge className="w-5 h-5 text-purple-400" />
                  <span className="text-sm text-gray-300">Analysis Confidence</span>
                  <span className="text-lg font-bold text-white">
                    {aiInsights.confidenceScore}%
                  </span>
                </div>
              </motion.div>
            )}

            {/* Overview Grid */}
            <div className="grid md:grid-cols-3 gap-6">
              {/* Strengths */}
              {aiInsights.strengths.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Card className="relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/5 border-emerald-500/20 h-full">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-500/20 to-transparent rounded-full blur-3xl" />

                    <div className="relative p-6">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-500 shadow-lg">
                          <Star className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Your Strengths</h3>
                          <p className="text-sm text-gray-400">
                            {aiInsights.strengths.length} identified
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {aiInsights.strengths.slice(0, 3).map((strength, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            className="flex items-start space-x-3 group"
                          >
                            <CheckCircle2 className="w-5 h-5 text-emerald-400 mt-0.5 flex-shrink-0" />
                            <p className="text-sm text-gray-300 leading-relaxed">{strength}</p>
                          </motion.div>
                        ))}
                        {aiInsights.strengths.length > 3 && (
                          <p className="text-xs text-gray-500 pl-8">
                            +{aiInsights.strengths.length - 3} more
                          </p>
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}

              {/* Opportunities */}
              {aiInsights.opportunities.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Card className="relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-cyan-500/5 border-blue-500/20 h-full">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full blur-3xl" />

                    <div className="relative p-6">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg">
                          <TrendingUp className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Opportunities</h3>
                          <p className="text-sm text-gray-400">
                            {aiInsights.opportunities.length} available
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {aiInsights.opportunities.slice(0, 3).map((opportunity, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.4 + index * 0.1 }}
                            className="flex items-start space-x-3 group"
                          >
                            <ArrowUpRight className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                            <p className="text-sm text-gray-300 leading-relaxed">{opportunity}</p>
                          </motion.div>
                        ))}
                        {aiInsights.opportunities.length > 3 && (
                          <p className="text-xs text-gray-500 pl-8">
                            +{aiInsights.opportunities.length - 3} more
                          </p>
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}

              {/* Challenges */}
              {aiInsights.challenges.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="relative overflow-hidden bg-gradient-to-br from-orange-500/5 to-red-500/5 border-orange-500/20 h-full">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-transparent rounded-full blur-3xl" />

                    <div className="relative p-6">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-500 shadow-lg">
                          <Shield className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">Challenges</h3>
                          <p className="text-sm text-gray-400">
                            {aiInsights.challenges.length} to address
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {aiInsights.challenges.slice(0, 3).map((challenge, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.5 + index * 0.1 }}
                            className="flex items-start space-x-3 group"
                          >
                            <AlertCircle className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
                            <p className="text-sm text-gray-300 leading-relaxed">{challenge}</p>
                          </motion.div>
                        ))}
                        {aiInsights.challenges.length > 3 && (
                          <p className="text-xs text-gray-500 pl-8">
                            +{aiInsights.challenges.length - 3} more
                          </p>
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}
            </div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="grid grid-cols-3 gap-4 mt-8"
            >
              <div className="text-center p-4 rounded-xl bg-white/5 backdrop-blur-sm">
                <Activity className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{totalInsights}</p>
                <p className="text-xs text-gray-400">Total Insights</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-white/5 backdrop-blur-sm">
                <Target className="w-8 h-8 text-pink-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{aiInsights.recommendations.length}</p>
                <p className="text-xs text-gray-400">Action Items</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-white/5 backdrop-blur-sm">
                <Trophy className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">
                  {aiInsights.recommendations.filter(r => r.priority === 'HIGH').length}
                </p>
                <p className="text-xs text-gray-400">High Priority</p>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="actions"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="space-y-4"
          >
            {/* Action Plan Header */}
            <div className="text-center mb-8">
              <h3 className="text-2xl font-light text-white mb-2">Your Personalized Action Plan</h3>
              <p className="text-gray-400">
                {aiInsights.recommendations.length} strategic recommendations prioritized for
                maximum impact
              </p>
            </div>

            {/* Recommendations List */}
            {aiInsights.recommendations.map((rec, index) => {
              const priority = priorityConfig[rec.priority];
              const isExpanded = expandedActions.has(index);

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="group"
                >
                  <Card
                    className={cn(
                      'relative overflow-hidden transition-all duration-300',
                      priority.bg,
                      priority.border,
                      isExpanded && 'shadow-xl ' + priority.glow
                    )}
                  >
                    {/* Priority Gradient Bar */}
                    <div className={cn('absolute left-0 top-0 bottom-0 w-1', priority.badge)} />

                    <div
                      className="p-6 cursor-pointer"
                      onClick={() => {
                        setExpandedActions(prev => {
                          const newSet = new Set(prev);
                          if (isExpanded) {
                            newSet.delete(index);
                          } else {
                            newSet.add(index);
                          }
                          return newSet;
                        });
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 pr-4">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className={cn('p-2 rounded-lg', priority.bg)}>{priority.icon}</div>
                            <Badge className={cn('text-xs px-3 py-1 rounded-full', priority.badge)}>
                              {rec.priority} PRIORITY
                            </Badge>
                            <div className="flex items-center space-x-1 text-gray-400">
                              {getTimeframeIcon(rec.timeframe)}
                              <span className="text-sm">{rec.timeframe}</span>
                            </div>
                          </div>

                          <h4 className="text-lg font-semibold text-white mb-2 pr-8">
                            {rec.action}
                          </h4>

                          <AnimatePresence>
                            {isExpanded && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                              >
                                <div className="mt-4 pt-4 border-t border-white/10">
                                  <div className="flex items-start space-x-3">
                                    <LightbulbIcon className="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0" />
                                    <div>
                                      <p className="text-sm font-medium text-gray-300 mb-1">
                                        Expected Outcome
                                      </p>
                                      <p className="text-sm text-gray-400 leading-relaxed">
                                        {rec.expectedOutcome}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>

                        <motion.div
                          animate={{ rotate: isExpanded ? 90 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="flex-shrink-0 mt-1"
                        >
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </motion.div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              );
            })}

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: aiInsights.recommendations.length * 0.1 + 0.2 }}
              className="mt-8 text-center"
            >
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <Award className="w-4 h-4 mr-2" />
                Start Your Journey
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
