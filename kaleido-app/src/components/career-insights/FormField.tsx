import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { StructuredField } from '@/lib/career-insights/structured-questions';
import { cn } from '@/lib/utils';
import { useJobSeekerStore } from '@/stores/jobSeekerStore';
import { AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface FormFieldProps {
  field: StructuredField;
  value: string | string[];
  onChange: (fieldId: string, value: string | string[]) => void;
  error?: string;
}

export const FormField: React.FC<FormFieldProps> = ({ field, value, onChange, error }) => {
  const formData = useJobSeekerStore(state => state.formData);
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customValue, setCustomValue] = useState('');

  // Helper function to get the current job title from experience
  const getCurrentJobTitle = () => {
    if (!formData.experience || formData.experience.length === 0) return null;

    // Sort experiences by date (current jobs first, then by most recent end date)
    const sortedExperience = [...formData.experience].sort((a, b) => {
      // Current jobs (no end date) come first
      if (!a.endDate && b.endDate) return -1;
      if (a.endDate && !b.endDate) return 1;
      if (!a.endDate && !b.endDate) {
        // Both are current, sort by start date (most recent first)
        if (a.startDate && b.startDate) {
          return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
        }
        return 0;
      }

      // Both have end dates, sort by end date (most recent first)
      if (a.endDate && b.endDate) {
        return new Date(b.endDate).getTime() - new Date(a.endDate).getTime();
      }

      return 0;
    });

    return sortedExperience[0]?.title || null;
  };

  useEffect(() => {
    // Handle prefilling with user data for select fields
    if (field.type === 'select' && field.prefillWithUserData && !value && formData) {
      if (field.id === 'currentRole') {
        const currentJobTitle = getCurrentJobTitle();
        if (currentJobTitle) {
          onChange(field.id, 'use-profile');
        }
      }
    }
  }, [field, formData, value, onChange]);

  const handleChange = (newValue: string | string[]) => {
    onChange(field.id, newValue);
  };

  switch (field.type) {
    case 'radio':
      return (
        <div className="space-y-3">
          <div>
            <Label className="text-white text-base font-medium">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {field.helperText && <p className="text-sm text-gray-400 mt-1">{field.helperText}</p>}
          </div>
          <RadioGroup
            value={value as string}
            onValueChange={val => handleChange(val)}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4"
          >
            {field.options?.map(option => (
              <div
                key={option.value}
                className={cn(
                  'flex items-start space-x-3 p-4 sm:p-5 rounded-xl border transition-all cursor-pointer',
                  value === option.value
                    ? 'border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20'
                    : 'border-white/10 hover:border-white/20 hover:bg-white/5'
                )}
                onClick={() => handleChange(option.value)}
              >
                <RadioGroupItem
                  value={option.value}
                  id={`${field.id}-${option.value}`}
                  className="mt-1 text-purple-500"
                />
                <Label
                  htmlFor={`${field.id}-${option.value}`}
                  className="flex flex-col cursor-pointer flex-1"
                >
                  <span className="text-white font-medium text-base">{option.label}</span>
                  {option.description && (
                    <span className="text-sm text-gray-400 mt-1 leading-relaxed">
                      {option.description}
                    </span>
                  )}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm mt-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
      );

    case 'checkbox':
      return (
        <div className="space-y-3">
          <div>
            <Label className="text-white text-base font-medium">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {field.helperText && <p className="text-sm text-gray-400 mt-1">{field.helperText}</p>}
          </div>
          {field.options && field.options.length > 0 ? (
            <div className="space-y-3">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {field.options.map(option => (
                  <label
                    key={option.value}
                    htmlFor={`${field.id}-${option.value}`}
                    className={cn(
                      'flex items-start space-x-3 p-4 sm:p-5 rounded-xl border cursor-pointer transition-all transform hover:scale-[1.02] group',
                      Array.isArray(value) && value.includes(option.value)
                        ? 'border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20'
                        : 'border-white/10 hover:border-white/20 hover:bg-white/5'
                    )}
                  >
                    <div className="relative mt-1 flex-shrink-0">
                      <input
                        type="checkbox"
                        className="peer sr-only"
                        id={`${field.id}-${option.value}`}
                        checked={Array.isArray(value) && value.includes(option.value)}
                        onChange={e => {
                          const currentValues = Array.isArray(value) ? value : [];
                          if (e.target.checked) {
                            handleChange([...currentValues, option.value]);
                          } else {
                            handleChange(currentValues.filter(v => v !== option.value));
                          }
                        }}
                      />
                      <div
                        className={cn(
                          'w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center',
                          Array.isArray(value) && value.includes(option.value)
                            ? 'bg-purple-500/20 border-purple-500'
                            : 'bg-transparent border-white/30 group-hover:border-white/40'
                        )}
                      >
                        <svg
                          className={cn(
                            'w-3 h-3 text-white transition-opacity duration-200',
                            Array.isArray(value) && value.includes(option.value)
                              ? 'opacity-100'
                              : 'opacity-0'
                          )}
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="3"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="text-white font-medium text-base">{option.label}</div>
                      {option.description && (
                        <div className="text-sm text-gray-400 mt-1 leading-relaxed">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </label>
                ))}
              </div>

              {field.allowCustom && (
                <div className="mt-4 pt-4 border-t border-white/10">
                  <div className="space-y-3">
                    <label className="text-sm text-gray-400">Add your own options</label>
                    <input
                      type="text"
                      className="w-full px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Type to add custom option and press Enter..."
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          const newValue = e.currentTarget.value.trim();
                          const currentValues = Array.isArray(value) ? value : [];
                          if (!currentValues.includes(newValue)) {
                            handleChange([...currentValues, newValue]);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    {/* Show custom values */}
                    {Array.isArray(value) &&
                      value.filter(v => !field.options?.find(opt => opt.value === v)).length >
                        0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {value
                            .filter(v => !field.options?.find(opt => opt.value === v))
                            .map(customVal => (
                              <span
                                key={customVal}
                                className="inline-flex items-center gap-1 px-3 py-1 bg-purple-500/20 border border-purple-500/30 rounded-full text-sm text-white"
                              >
                                {customVal}
                                <button
                                  type="button"
                                  onClick={() => {
                                    const currentValues = Array.isArray(value) ? value : [];
                                    handleChange(currentValues.filter(v => v !== customVal));
                                  }}
                                  className="ml-1 text-white/60 hover:text-white"
                                >
                                  ×
                                </button>
                              </span>
                            ))}
                        </div>
                      )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              <input
                type="text"
                className="w-full px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Type to add option and press Enter..."
                onKeyDown={e => {
                  if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                    const newValue = e.currentTarget.value.trim();
                    const currentValues = Array.isArray(value) ? value : [];
                    if (!currentValues.includes(newValue)) {
                      handleChange([...currentValues, newValue]);
                    }
                    e.currentTarget.value = '';
                  }
                }}
              />
              {Array.isArray(value) && value.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {value.map(val => (
                    <span
                      key={val}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-purple-500/20 border border-purple-500/30 rounded-full text-sm text-white"
                    >
                      {val}
                      <button
                        type="button"
                        onClick={() => {
                          const currentValues = Array.isArray(value) ? value : [];
                          handleChange(currentValues.filter(v => v !== val));
                        }}
                        className="ml-1 text-white/60 hover:text-white"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm mt-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
      );

    case 'textarea':
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-white text-base font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.helperText && <p className="text-sm text-gray-400">{field.helperText}</p>}
          <Textarea
            id={field.id}
            value={value as string}
            onChange={e => handleChange(e.target.value)}
            className="bg-white/5 border-white/10 text-white placeholder:text-gray-500 min-h-[100px]"
            placeholder={field.placeholder}
            rows={4}
          />
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm mt-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
      );

    case 'select':
      const selectValue = value as string;
      const shouldShowCustom =
        selectValue === 'custom' ||
        (field.allowCustom &&
          selectValue &&
          !field.options?.find(opt => opt.value === selectValue));

      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-white text-base font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.helperText && <p className="text-sm text-gray-400">{field.helperText}</p>}

          <div className="space-y-3">
            <Select
              value={shouldShowCustom ? 'custom' : selectValue}
              onValueChange={val => {
                if (val === 'custom') {
                  setShowCustomInput(true);
                  handleChange(customValue || '');
                } else if (val === 'use-profile') {
                  const currentJobTitle = getCurrentJobTitle();
                  setShowCustomInput(false);
                  handleChange(currentJobTitle || '');
                } else {
                  setShowCustomInput(false);
                  handleChange(val);
                }
              }}
            >
              <SelectTrigger className="bg-white/5 border-white/10 text-white">
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span>{option.label}</span>
                      {option.description && (
                        <span className="text-xs text-gray-500">{option.description}</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {(shouldShowCustom || showCustomInput) && (
              <Input
                type="text"
                value={shouldShowCustom && !showCustomInput ? selectValue : customValue}
                onChange={e => {
                  setCustomValue(e.target.value);
                  handleChange(e.target.value);
                }}
                className="bg-white/5 border-white/10 text-white placeholder:text-gray-500"
                placeholder={field.placeholder}
                autoFocus
              />
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm mt-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
      );

    case 'text':
    default:
      return (
        <div className="space-y-2">
          <Label htmlFor={field.id} className="text-white text-base font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.helperText && <p className="text-sm text-gray-400">{field.helperText}</p>}
          <Input
            id={field.id}
            type="text"
            value={value as string}
            onChange={e => handleChange(e.target.value)}
            className="bg-white/5 border-white/10 text-white placeholder:text-gray-500"
            placeholder={field.placeholder}
          />
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm mt-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>
      );
  }
};
