import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { FormField } from '../FormField';
import { StructuredField } from '@/lib/career-insights/structured-questions';

// Mock the stores and utilities
jest.mock('@/stores/jobSeekerStore', () => ({
  useJobSeekerStore: jest.fn(() => ({
    formData: {
      experience: [
        {
          title: 'Software Engineer',
          startDate: '2023-01-01',
          endDate: null, // Current job
        },
      ],
    },
  })),
}));

jest.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

describe('FormField Component', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  describe('Radio Button Field', () => {
    const radioField: StructuredField = {
      id: 'targetRoleType',
      label: 'Role Category',
      type: 'radio',
      required: true,
      helperText: 'Choose the category that best matches your target role',
      options: [
        {
          value: 'engineering',
          label: 'Engineering',
          description: 'Software, DevOps, Data, etc.',
        },
        {
          value: 'product',
          label: 'Product',
          description: 'Product Management, Product Design',
        },
        {
          value: 'design',
          label: 'Design',
          description: 'UX/UI, Visual Design, Research',
        },
      ],
    };

    test('renders radio field with all options', () => {
      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Check if the field label is rendered
      expect(screen.getByText('Role Category')).toBeInTheDocument();
      expect(
        screen.getByText('Choose the category that best matches your target role')
      ).toBeInTheDocument();

      // Check if all options are rendered
      expect(screen.getByText('Engineering')).toBeInTheDocument();
      expect(screen.getByText('Software, DevOps, Data, etc.')).toBeInTheDocument();
      expect(screen.getByText('Product')).toBeInTheDocument();
      expect(screen.getByText('Product Management, Product Design')).toBeInTheDocument();
      expect(screen.getByText('Design')).toBeInTheDocument();
      expect(screen.getByText('UX/UI, Visual Design, Research')).toBeInTheDocument();
    });

    test('clicking on radio button container selects the option', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Find the container div for the "Engineering" option
      const engineeringContainer = screen
        .getByText('Engineering')
        .closest('div[class*="flex items-start space-x-3"]');
      expect(engineeringContainer).toBeInTheDocument();

      // Click on the container
      await user.click(engineeringContainer!);

      // Verify onChange was called with the correct value
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'engineering');
    });

    test('clicking on different parts of the container selects the option', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Test clicking on the label text
      const productLabel = screen.getByText('Product');
      await user.click(productLabel);
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'product');

      mockOnChange.mockClear();

      // Test clicking on the description text
      const designDescription = screen.getByText('UX/UI, Visual Design, Research');
      await user.click(designDescription);
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'design');
    });

    test('clicking on radio button icon selects the option', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Find the actual radio button input for "Engineering"
      const radioButton = screen.getByRole('radio', { name: /engineering/i });

      // Click on the radio button
      await user.click(radioButton);

      // Verify onChange was called
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'engineering');
    });

    test('displays correct visual state when option is selected', () => {
      render(<FormField field={radioField} value="product" onChange={mockOnChange} />);

      // Find the container for the selected option
      const productContainer = screen
        .getByText('Product')
        .closest('div[class*="flex items-start space-x-3"]');
      expect(productContainer).toHaveClass('border-purple-500', 'bg-purple-500/10');

      // Find containers for unselected options
      const engineeringContainer = screen
        .getByText('Engineering')
        .closest('div[class*="flex items-start space-x-3"]');
      expect(engineeringContainer).toHaveClass('border-white/10');
      expect(engineeringContainer).not.toHaveClass('border-purple-500');
    });

    test('multiple clicks on the same option only trigger onChange once per click', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      const engineeringContainer = screen
        .getByText('Engineering')
        .closest('div[class*="flex items-start space-x-3"]');

      // Click twice on the same option
      await user.click(engineeringContainer!);
      await user.click(engineeringContainer!);

      // Should be called twice (once per click)
      expect(mockOnChange).toHaveBeenCalledTimes(2);
      expect(mockOnChange).toHaveBeenNthCalledWith(1, 'targetRoleType', 'engineering');
      expect(mockOnChange).toHaveBeenNthCalledWith(2, 'targetRoleType', 'engineering');
    });

    test('clicking on different options changes selection', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      const engineeringContainer = screen
        .getByText('Engineering')
        .closest('div[class*="flex items-start space-x-3"]');
      const productContainer = screen
        .getByText('Product')
        .closest('div[class*="flex items-start space-x-3"]');

      // Click on Engineering first
      await user.click(engineeringContainer!);
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'engineering');

      mockOnChange.mockClear();

      // Click on Product
      await user.click(productContainer!);
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'product');
    });

    test('displays error message when provided', () => {
      const errorMessage = 'Please select a role category';

      render(
        <FormField field={radioField} value="" onChange={mockOnChange} error={errorMessage} />
      );

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      // Check for the error container with red text
      const errorContainer = screen.getByText(errorMessage).closest('div');
      expect(errorContainer).toHaveClass('text-red-400');
    });

    test('does not display error message when no error provided', () => {
      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Should not find any error-related elements
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    test('handles required field indicator', () => {
      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Check for required asterisk
      expect(screen.getByText('*')).toBeInTheDocument();
    });

    test('handles non-required field', () => {
      const nonRequiredField = { ...radioField, required: false };

      render(<FormField field={nonRequiredField} value="" onChange={mockOnChange} />);

      // Should not have required asterisk
      expect(screen.queryByText('*')).not.toBeInTheDocument();
    });

    test('container has proper cursor-pointer styling', () => {
      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      const engineeringContainer = screen
        .getByText('Engineering')
        .closest('div[class*="flex items-start space-x-3"]');
      expect(engineeringContainer).toHaveClass('cursor-pointer');
    });

    test('keyboard navigation works correctly', async () => {
      const user = userEvent.setup();

      render(<FormField field={radioField} value="" onChange={mockOnChange} />);

      // Focus on the first radio button
      const firstRadio = screen.getByRole('radio', { name: /engineering/i });
      firstRadio.focus();

      // Press space to select
      await user.keyboard(' ');
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'engineering');

      mockOnChange.mockClear();

      // Use arrow keys to navigate
      await user.keyboard('{ArrowDown}');
      await user.keyboard(' ');
      expect(mockOnChange).toHaveBeenCalledWith('targetRoleType', 'product');
    });
  });

  describe('Text Field', () => {
    const textField: StructuredField = {
      id: 'targetRole',
      label: 'Target Role',
      type: 'text',
      required: true,
      placeholder: 'e.g., Senior Software Engineer',
    };

    test('renders text field correctly', () => {
      render(<FormField field={textField} value="" onChange={mockOnChange} />);

      expect(screen.getByDisplayValue('')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('e.g., Senior Software Engineer')).toBeInTheDocument();
      expect(screen.getByText('Target Role')).toBeInTheDocument();
    });

    test('calls onChange when text input changes', async () => {
      render(<FormField field={textField} value="" onChange={mockOnChange} />);

      const input = screen.getByPlaceholderText('e.g., Senior Software Engineer');

      // Use fireEvent to trigger a change with complete value
      fireEvent.change(input, { target: { value: 'Senior Developer' } });

      expect(mockOnChange).toHaveBeenCalledTimes(1);
      expect(mockOnChange).toHaveBeenCalledWith('targetRole', 'Senior Developer');
    });
  });

  describe('Textarea Field', () => {
    const textareaField: StructuredField = {
      id: 'description',
      label: 'Description',
      type: 'textarea',
      required: false,
      placeholder: 'Enter description...',
      helperText: 'Provide additional details',
    };

    test('renders textarea field correctly', () => {
      render(<FormField field={textareaField} value="" onChange={mockOnChange} />);

      expect(screen.getByLabelText('Description')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter description...')).toBeInTheDocument();
      expect(screen.getByText('Provide additional details')).toBeInTheDocument();
    });

    test('calls onChange when textarea content changes', async () => {
      render(<FormField field={textareaField} value="" onChange={mockOnChange} />);

      const textarea = screen.getByPlaceholderText('Enter description...');

      // Use fireEvent to trigger a change with complete value
      fireEvent.change(textarea, { target: { value: 'This is a test description' } });

      expect(mockOnChange).toHaveBeenCalledTimes(1);
      expect(mockOnChange).toHaveBeenCalledWith('description', 'This is a test description');
    });
  });
});
