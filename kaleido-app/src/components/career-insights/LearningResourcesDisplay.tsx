'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  ExternalLink,
  Star,
  Clock,
  DollarSign,
  GraduationCap,
  Play,
  FileText,
  Award,
  Link as LinkIcon,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LearningResource } from '@/stores/careerInsightsStore';
import { cn } from '@/lib/utils';

interface LearningResourcesDisplayProps {
  resources: LearningResource[];
  title?: string;
  className?: string;
}

const resourceTypeConfig = {
  COURSE: {
    icon: <GraduationCap className="w-4 h-4" />,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    textColor: 'text-blue-400',
  },
  TUTORIAL: {
    icon: <Play className="w-4 h-4" />,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20',
    textColor: 'text-green-400',
  },
  CERTIFICATION: {
    icon: <Award className="w-4 h-4" />,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/20',
    textColor: 'text-purple-400',
  },
  BOOK: {
    icon: <BookOpen className="w-4 h-4" />,
    color: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-500/10',
    borderColor: 'border-orange-500/20',
    textColor: 'text-orange-400',
  },
  LINK: {
    icon: <LinkIcon className="w-4 h-4" />,
    color: 'from-indigo-500 to-purple-500',
    bgColor: 'bg-indigo-500/10',
    borderColor: 'border-indigo-500/20',
    textColor: 'text-indigo-400',
  },
};

const getCostColor = (cost: string) => {
  const lowerCost = cost.toLowerCase();
  if (lowerCost.includes('free') || lowerCost === '0' || lowerCost === '$0') {
    return 'text-green-400';
  }
  if (lowerCost.includes('$') && (lowerCost.includes('10') || lowerCost.includes('20'))) {
    return 'text-yellow-400';
  }
  return 'text-red-400';
};

export default function LearningResourcesDisplay({
  resources,
  title = 'Learning Resources',
  className = '',
}: LearningResourcesDisplayProps) {
  if (!resources || resources.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center space-x-2 mb-4">
        <BookOpen className="w-5 h-5 text-purple-400" />
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <Badge variant="outline" className="text-xs border-white/20 text-gray-400">
          {resources.length} resources
        </Badge>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {resources.map((resource, index) => {
          const typeConfig = resourceTypeConfig[resource.type] || resourceTypeConfig.LINK;

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
            >
              <Card
                className={cn(
                  'relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                  typeConfig.bgColor,
                  typeConfig.borderColor,
                  'border backdrop-blur-sm'
                )}
              >
                {/* Type indicator line */}
                <div
                  className={cn(
                    'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r',
                    typeConfig.color
                  )}
                />

                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={cn('p-2 rounded-lg', typeConfig.bgColor)}>
                        <div className={typeConfig.textColor}>{typeConfig.icon}</div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-white text-sm mb-1 line-clamp-2">
                          {resource.title}
                        </h4>
                        <p className="text-xs text-gray-400 mb-2">{resource.provider}</p>

                        <div className="flex items-center space-x-3 text-xs">
                          <div className="flex items-center space-x-1 text-gray-400">
                            <Clock className="w-3 h-3" />
                            <span>{resource.estimatedDuration}</span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <DollarSign className="w-3 h-3 text-gray-400" />
                            <span className={getCostColor(resource.cost)}>{resource.cost}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Badge
                      variant="outline"
                      className={cn('text-xs ml-2', typeConfig.borderColor, typeConfig.textColor)}
                    >
                      {resource.type}
                    </Badge>
                  </div>

                  {resource.url && (
                    <Button
                      onClick={() => window.open(resource.url, '_blank')}
                      variant="outline"
                      size="sm"
                      className={cn(
                        'w-full text-white border-white/20 hover:bg-white/10 transition-all duration-200',
                        'group'
                      )}
                    >
                      <ExternalLink className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform" />
                      Access Resource
                    </Button>
                  )}
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Quick summary */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: resources.length * 0.1 + 0.2 }}
        className="mt-6 p-4 rounded-lg bg-white/5 border border-white/10"
      >
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 text-gray-400">
              <Star className="w-4 h-4 text-yellow-400" />
              <span>
                {resources.filter(r => r.cost.toLowerCase().includes('free')).length} Free Resources
              </span>
            </div>
            <div className="flex items-center space-x-1 text-gray-400">
              <Award className="w-4 h-4 text-purple-400" />
              <span>{resources.filter(r => r.type === 'CERTIFICATION').length} Certifications</span>
            </div>
          </div>

          <div className="text-xs text-gray-500">Curated learning path for maximum impact</div>
        </div>
      </motion.div>
    </div>
  );
}
