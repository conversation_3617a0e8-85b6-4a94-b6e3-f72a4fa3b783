'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  Target,
  CheckCircle,
  ArrowRight,
  Briefcase,
  BarChart3,
  Award,
  ChevronDown,
  ChevronRight,
  MapPin,
  Zap,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CareerPath, CareerStep } from '@/stores/careerInsightsStore';
import { cn } from '@/lib/utils';

interface CareerPathDisplayProps {
  paths: CareerPath[];
  currentPosition?: string;
  immediateSteps?: string[];
  className?: string;
}

const difficultyConfig = {
  EASY: {
    color: 'text-green-400',
    bg: 'bg-green-500/10',
    border: 'border-green-500/20',
    label: 'Easy Transition',
  },
  MODERATE: {
    color: 'text-yellow-400',
    bg: 'bg-yellow-500/10',
    border: 'border-yellow-500/20',
    label: 'Moderate Effort',
  },
  DIFFICULT: {
    color: 'text-red-400',
    bg: 'bg-red-500/10',
    border: 'border-red-500/20',
    label: 'Challenging Path',
  },
};

const formatSalary = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0,
  }).format(amount);
};

const getPotentialIcon = (potential: 'HIGH' | 'MEDIUM' | 'LOW') => {
  switch (potential) {
    case 'HIGH':
      return <TrendingUp className="w-4 h-4 text-green-400" />;
    case 'MEDIUM':
      return <BarChart3 className="w-4 h-4 text-yellow-400" />;
    case 'LOW':
      return <Target className="w-4 h-4 text-red-400" />;
    default:
      return <Target className="w-4 h-4 text-gray-400" />;
  }
};

export default function CareerPathDisplay({
  paths,
  currentPosition,
  immediateSteps = [],
  className = '',
}: CareerPathDisplayProps) {
  const [expandedPaths, setExpandedPaths] = useState<Set<number>>(new Set());
  const [selectedStep, setSelectedStep] = useState<number | null>(null);

  if (!paths || paths.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20">
            <MapPin className="w-5 h-5 text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">Recommended Career Paths</h3>
            <p className="text-sm text-gray-400">
              {paths.length} path{paths.length !== 1 ? 's' : ''} tailored to your profile
            </p>
          </div>
        </div>

        {currentPosition && (
          <Badge variant="outline" className="text-sm border-white/20 text-gray-300">
            Starting from: {currentPosition}
          </Badge>
        )}
      </div>

      {/* Immediate Steps */}
      {immediateSteps.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-br from-blue-500/5 to-cyan-500/5 border-blue-500/20">
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Zap className="w-5 h-5 text-blue-400" />
                <h4 className="text-lg font-semibold text-white">Quick Wins - Start Today</h4>
              </div>
              <div className="grid gap-3 md:grid-cols-2">
                {immediateSteps.slice(0, 6).map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    className="flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
                  >
                    <div className="w-2 h-2 rounded-full bg-blue-400" />
                    <span className="text-sm text-gray-200">{step}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* Career Paths */}
      <div className="space-y-4">
        {paths.map((path, pathIndex) => {
          const isExpanded = expandedPaths.has(pathIndex);
          const difficultyStyle =
            difficultyConfig[path.difficultyLevel] || difficultyConfig.MODERATE;

          return (
            <motion.div
              key={pathIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: pathIndex * 0.2, duration: 0.5 }}
            >
              <Card
                className={cn(
                  'relative overflow-hidden transition-all duration-300',
                  isExpanded && 'shadow-2xl ring-2 ring-purple-500/20'
                )}
              >
                {/* Path Header */}
                <div
                  className="p-6 cursor-pointer hover:bg-white/5 transition-colors"
                  onClick={() => {
                    setExpandedPaths(prev => {
                      const newSet = new Set(prev);
                      if (isExpanded) {
                        newSet.delete(pathIndex);
                      } else {
                        newSet.add(pathIndex);
                      }
                      return newSet;
                    });
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20">
                        <Briefcase className="w-6 h-6 text-purple-400" />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-semibold text-white">{path.pathName}</h4>
                          <Badge
                            variant="outline"
                            className={cn('text-xs', difficultyStyle.border, difficultyStyle.color)}
                          >
                            {difficultyStyle.label}
                          </Badge>
                        </div>

                        <div className="flex items-center space-x-6 text-sm text-gray-400">
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4" />
                            <span>{path.totalDuration}</span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <Target className="w-4 h-4" />
                            <span>{path.successProbability}% success rate</span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <Award className="w-4 h-4" />
                            <span>{path.steps.length} steps</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </motion.div>
                  </div>

                  {/* Success Probability Bar */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-gray-400">Success Probability</span>
                      <span className="text-xs text-white font-medium">
                        {path.successProbability}%
                      </span>
                    </div>
                    <Progress value={path.successProbability} className="h-2 bg-white/10" />
                  </div>
                </div>

                {/* Expanded Path Details */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.5, ease: [0.23, 1, 0.32, 1] }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 border-t border-white/10">
                        <div className="pt-6">
                          <h5 className="text-sm font-medium text-white mb-4 flex items-center">
                            <MapPin className="w-4 h-4 mr-2 text-purple-400" />
                            Career Journey Steps
                          </h5>

                          <div className="space-y-4">
                            {path.steps.map((step, stepIndex) => (
                              <motion.div
                                key={stepIndex}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: stepIndex * 0.1, duration: 0.5 }}
                                className={cn(
                                  'relative p-4 rounded-lg border transition-all duration-200 cursor-pointer',
                                  selectedStep === stepIndex
                                    ? 'bg-purple-500/10 border-purple-500/30'
                                    : 'bg-white/5 border-white/10 hover:bg-white/10'
                                )}
                                onClick={() =>
                                  setSelectedStep(selectedStep === stepIndex ? null : stepIndex)
                                }
                              >
                                {/* Step connector line */}
                                {stepIndex < path.steps.length - 1 && (
                                  <div className="absolute left-6 top-16 w-0.5 h-8 bg-gradient-to-b from-purple-400/50 to-transparent" />
                                )}

                                <div className="flex items-start space-x-4">
                                  <div className="flex-shrink-0">
                                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                                      <span className="text-xs font-bold text-white">
                                        {stepIndex + 1}
                                      </span>
                                    </div>
                                  </div>

                                  <div className="flex-1">
                                    <div className="flex items-center justify-between mb-2">
                                      <h6 className="font-medium text-white">{step.title}</h6>
                                      <Badge
                                        variant="outline"
                                        className="text-xs border-white/20 text-gray-400"
                                      >
                                        {step.timeline}
                                      </Badge>
                                    </div>

                                    <p className="text-sm text-gray-300 mb-3">{step.description}</p>

                                    <div className="grid md:grid-cols-2 gap-4">
                                      {/* Salary Range */}
                                      <div className="flex items-center space-x-2 text-sm">
                                        <DollarSign className="w-4 h-4 text-green-400" />
                                        <span className="text-gray-400">Salary:</span>
                                        <span className="text-green-400 font-medium">
                                          {formatSalary(step.averageSalaryRange.min)} -{' '}
                                          {formatSalary(step.averageSalaryRange.max)}
                                        </span>
                                      </div>

                                      {/* Market Demand */}
                                      <div className="flex items-center space-x-2 text-sm">
                                        <Users className="w-4 h-4 text-blue-400" />
                                        <span className="text-gray-400">Demand:</span>
                                        <div className="flex items-center space-x-2">
                                          <div className="w-16 bg-white/10 rounded-full h-1.5">
                                            <div
                                              className="bg-gradient-to-r from-blue-500 to-cyan-500 h-1.5 rounded-full"
                                              style={{ width: `${step.marketDemand}%` }}
                                            />
                                          </div>
                                          <span className="text-blue-400 text-xs">
                                            {step.marketDemand}%
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Growth Potential & Requirements */}
                                    <div className="mt-3 flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        {getPotentialIcon(step.growthPotential)}
                                        <span className="text-xs text-gray-400">
                                          {step.growthPotential.toLowerCase()} growth potential
                                        </span>
                                      </div>

                                      <div className="flex items-center space-x-1">
                                        <CheckCircle className="w-4 h-4 text-purple-400" />
                                        <span className="text-xs text-gray-400">
                                          {step.requirements.length} requirements
                                        </span>
                                      </div>
                                    </div>

                                    {/* Requirements (show when selected) */}
                                    <AnimatePresence>
                                      {selectedStep === stepIndex && (
                                        <motion.div
                                          initial={{ opacity: 0, height: 0 }}
                                          animate={{ opacity: 1, height: 'auto' }}
                                          exit={{ opacity: 0, height: 0 }}
                                          transition={{ duration: 0.3 }}
                                          className="mt-4 pt-3 border-t border-white/10"
                                        >
                                          <h6 className="text-xs font-medium text-white mb-2">
                                            Key Requirements:
                                          </h6>
                                          <div className="space-y-1">
                                            {step.requirements.map((req, reqIndex) => (
                                              <div
                                                key={reqIndex}
                                                className="flex items-center space-x-2 text-xs text-gray-300"
                                              >
                                                <CheckCircle className="w-3 h-3 text-green-400" />
                                                <span>{req}</span>
                                              </div>
                                            ))}
                                          </div>
                                        </motion.div>
                                      )}
                                    </AnimatePresence>
                                  </div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
