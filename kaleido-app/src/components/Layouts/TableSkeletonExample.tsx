'use client';

import { useEffect, useState } from 'react';

import GenericTable from '@/components/Layouts/GenericTable';

// Example data type
interface ExampleData {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
}

// Example data
const mockData: ExampleData[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'User',
    status: 'Inactive',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Editor',
    status: 'Active',
  },
];

// Example columns
const columns = [
  { key: 'name', label: 'Name' },
  { key: 'email', label: 'Email' },
  { key: 'role', label: 'Role' },
  { key: 'status', label: 'Status' },
];

const TableSkeletonExample = () => {
  const [data, setData] = useState<ExampleData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setData(mockData);
      setIsLoading(false);
    }, 2000); // Simulate 2 second loading time

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Table with Skeleton Loading</h1>

      <div className="mb-4">
        <button
          onClick={() => {
            setIsLoading(true);
            setTimeout(() => {
              setIsLoading(false);
            }, 2000);
          }}
          className="px-4 py-2 bg-purple-600 text-white rounded-md"
        >
          Reload with Skeleton
        </button>
      </div>

      <GenericTable
        data={data}
        columns={columns}
        isLoading={isLoading}
        onRowClick={row => () => console.log(row)}
      />
    </div>
  );
};

export default TableSkeletonExample;
