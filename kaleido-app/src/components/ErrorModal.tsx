'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';
import Image from 'next/image';

import { useJobsStore } from '@/stores/unifiedJobStore';

// Convert technical error messages to user-friendly ones
const getUserFriendlyMessage = (error?: string): string => {
  if (!error)
    return "We're having trouble loading this page. Don't worry, our team has been notified and is working on it.";

  const lowerError = error.toLowerCase();

  // Database constraint errors (duplicate account/email)
  if (
    lowerError.includes('duplicate key') ||
    lowerError.includes('already exists') ||
    lowerError.includes('account already exists')
  ) {
    return 'It looks like you already have an account with us. Please try refreshing the page or contact support if the issue persists.';
  }

  // Network/connection errors
  if (
    lowerError.includes('network') ||
    lowerError.includes('fetch') ||
    lowerError.includes('connection')
  ) {
    return "We're having trouble connecting to our servers. Please check your internet connection and try again.";
  }

  // Server errors (5xx)
  if (
    lowerError.includes('500') ||
    lowerError.includes('502') ||
    lowerError.includes('503') ||
    lowerError.includes('504') ||
    lowerError.includes('server error')
  ) {
    return "Our servers are experiencing some issues. We're working to fix this as quickly as possible.";
  }

  // Not found errors (404)
  if (lowerError.includes('404') || lowerError.includes('not found')) {
    return "We couldn't find what you're looking for. The page may have been moved or no longer exists.";
  }

  // Permission/auth errors (401, 403)
  if (
    lowerError.includes('401') ||
    lowerError.includes('403') ||
    lowerError.includes('unauthorized') ||
    lowerError.includes('forbidden')
  ) {
    return "You don't have permission to access this content. Please try logging in again.";
  }

  // Timeout errors
  if (lowerError.includes('timeout') || lowerError.includes('timed out')) {
    return 'The request is taking longer than expected. Please try again in a moment.';
  }

  // Generic fallback for any other technical errors
  return "We're having trouble loading this page. Don't worry, our team has been notified and is working on it.";
};

interface ErrorModalProps {
  isOpen: boolean;
  onClose?: () => void;
  message?: string;
  title?: string;
  showContinueButton?: boolean;
  onRetry?: () => void;
}

const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  message,
  title = 'Oops! Something went wrong',
  showContinueButton = true,
  onRetry,
}) => {
  // Use user-friendly message conversion
  const userFriendlyMessage = getUserFriendlyMessage(message);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Select a random image from company setup images
  const randomImage = React.useMemo(() => {
    const images = [
      '/images/company-setup/company-1.webp',
      '/images/company-setup/company-2.webp',
      '/images/company-setup/company-3.webp',
      '/images/company-setup/company-4.webp',
    ];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  }, [isOpen]); // Re-randomize when modal opens

  // Create portal container on mount
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const container = document.createElement('div');
      container.id = 'error-modal-container';
      document.body.appendChild(container);
      setPortalContainer(container);

      return () => {
        document.body.removeChild(container);
      };
    }
  }, []);

  // Handle refresh/retry button click
  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Refresh data through Zustand stores instead of full page reload
      invalidateJobsCache();
      refreshJobs(true); // Force refresh

      // Clear any cached data
      if (typeof localStorage !== 'undefined') {
        const keys = Object.keys(localStorage);
        const cacheKeys = keys.filter(
          key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
        );
        cacheKeys.forEach(key => localStorage.removeItem(key));
      }
    }

    // Close the modal after refreshing
    if (onClose) {
      onClose();
    }
  };

  if (!isOpen || !portalContainer) return null;

  return createPortal(
    <AnimatePresence>
      <div className="fixed inset-0 z-[9999] overflow-y-auto">
        {/* Backdrop with blur effect */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-xl"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-4xl overflow-hidden rounded-3xl relative shadow-2xl"
            onClick={e => e.stopPropagation()}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 h-full">
              {/* Left side - Image */}
              <div className="relative h-64 md:h-full">
                <Image
                  src={randomImage}
                  alt="Error"
                  fill
                  style={{ objectFit: 'cover' }}
                  className="brightness-[0.85]"
                  priority
                />
              </div>

              {/* Right side - Content */}
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl p-8 flex flex-col">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-4">
                    <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h2>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-8">{userFriendlyMessage}</p>

                <div className="mt-auto flex flex-col sm:flex-row gap-4">
                  <button
                    type="button"
                    onClick={handleRefresh}
                    className="flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>,
    portalContainer
  );
};

export default ErrorModal;
