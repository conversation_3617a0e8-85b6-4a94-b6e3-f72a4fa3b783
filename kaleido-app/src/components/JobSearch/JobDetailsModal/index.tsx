import { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Info } from 'lucide-react';

import ActivityHistory from '@/components/shared/ActivityHistory';
import WithdrawApplicationModal from '@/components/shared/WithdrawApplicationModal';
import { showToast } from '@/components/Toaster';
import { useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import { getJobApplicationStatus } from '@/services/jobApplication.service';
import { CandidateStatus } from '@/types/candidate.types';
import { DetailedJobInfo, Job } from '@/types/job';

import StatusTimeline from '../../shared/StatusTimeline';
import ActionButton from './ActionButton';
import AtsJobDetailsDrawer from './AtsJobDetailsDrawer';
import CandidateList from './CandidateList';
import { JobContent } from './JobContent';
import { ModalHeader } from './ModalHeader';
import { TLDRPopup } from './TLDRPopup';

interface JobDetailsModalProps {
  job: DetailedJobInfo;
  isOpen: boolean;
  isApplied?: boolean;
  hideApplyButton?: boolean;
  customFooter?: React.ReactNode;
  showScoutButton?: boolean;
}

export const JobDetailsModal = ({
  job,
  isOpen,
  isApplied,
  hideApplyButton,
}: JobDetailsModalProps) => {
  const { handleApply, onCloseModal, onCloseTldr, showTldr } = useJobSearch();
  const [activeTab, setActiveTab] = useState<string>('description');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  // State to hold the application status and activity history
  const [applicationStatus, setApplicationStatus] = useState<CandidateStatus>(
    job.alreadyApplied ? CandidateStatus.APPLIED : CandidateStatus.NEW
  );
  const [statusTimeline, setStatusTimeline] = useState<any[]>([]);
  const [activityHistory, setActivityHistory] = useState<any[]>([]);
  const [emailCorrespondence, setEmailCorrespondence] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false); // Used to track loading state

  // Fetch the application status from the backend
  useEffect(() => {
    const fetchApplicationStatus = async () => {
      setIsLoading(true);
      try {
        const status = await getJobApplicationStatus(job.id);

        // Always use the main status field from the API as the authoritative source
        if (status.status) {
          setApplicationStatus(status.status as unknown as CandidateStatus);
        }

        // Set the status timeline if available (for historical display only)
        if (status.statusTimeline && status.statusTimeline.length > 0) {
          setStatusTimeline(status.statusTimeline);
        }

        // Set the activity history if available
        if (status.activityHistory && status.activityHistory.length > 0) {
          setActivityHistory(status.activityHistory);
        }

        // Set the email correspondence if available
        if ((status as any).emailCorrespondence && (status as any).emailCorrespondence.length > 0) {
          setEmailCorrespondence((status as any).emailCorrespondence);
        }
      } catch (error) {
        console.error('Error fetching application status:', error);
        // Keep the default status if there's an error
      } finally {
        setIsLoading(false);
      }
    };

    if (activeTab === 'status') {
      fetchApplicationStatus();
    }
  }, [job.id, activeTab]);

  // Handle action completion (interview/offer accept/decline)
  const handleActionComplete = () => {
    // Refetch the application status to get updated data
    if (activeTab === 'status') {
      const fetchApplicationStatus = async () => {
        setIsLoading(true);
        try {
          const status = await getJobApplicationStatus(job.id);

          // Always use the main status field from the API as the authoritative source
          if (status.status) {
            setApplicationStatus(status.status as unknown as CandidateStatus);
          }

          if (status.statusTimeline && status.statusTimeline.length > 0) {
            setStatusTimeline(status.statusTimeline);
          }

          if (status.activityHistory && status.activityHistory.length > 0) {
            setActivityHistory(status.activityHistory);
          }

          if (
            (status as any).emailCorrespondence &&
            (status as any).emailCorrespondence.length > 0
          ) {
            setEmailCorrespondence((status as any).emailCorrespondence);
          }
        } catch (error) {
          console.error('Error refetching application status:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchApplicationStatus();
    }
  };

  const handleApplySuccess = async () => {
    await handleApply(job as unknown as Job);
    onCloseModal();
  };

  // Open withdraw modal
  const handleWithdrawApplication = () => {
    setShowWithdrawModal(true);
  };

  // Handle withdraw success
  const handleWithdrawSuccess = () => {
    showToast({
      message: 'Application withdrawn successfully',
      isSuccess: true,
    });

    setShowWithdrawModal(false);
    onCloseModal();

    // Refresh the page to ensure all job data is updated
    // This ensures the job search results reflect the new application status
    setTimeout(() => {
      window.location.reload();
    }, 1000); // Small delay to let the toast show
  };

  // Handle withdraw error
  const handleWithdrawError = (error: string) => {
    console.error('Error withdrawing application:', error);
    showToast({
      message: 'Failed to withdraw application',
      isSuccess: false,
    });
  };

  if (!isOpen) {
    return null;
  }

  // Animation variants for tab transitions
  const tabContentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        ease: 'easeIn',
      },
    },
  };

  return (
    <div className="fixed inset-0 z-[99999] flex items-center justify-center bg-black/5 backdrop-blur-sm">
      <div className="bg-gray-900/95 w-[95vw] max-w-7xl rounded-xl border border-white/10 shadow-xl my-8 flex flex-col max-h-[90vh]">
        <ModalHeader
          {...job}
          onClose={onCloseModal}
          onApply={hideApplyButton ? undefined : handleApplySuccess}
          jobId={job.id}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          isApplied={isApplied || job.alreadyApplied}
          onWithdraw={handleWithdrawApplication}
        />

        {/* Tab Content with proper scrolling */}
        <div className="flex-1 overflow-hidden max-h-[calc(100%-140px)]">
          <AnimatePresence mode="wait">
            {activeTab === 'description' ? (
              <motion.div
                key="description"
                className="h-full overflow-y-auto custom-scrollbar"
                variants={tabContentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <JobContent {...job} />
              </motion.div>
            ) : (
              <motion.div
                key="status"
                className="h-full overflow-y-auto custom-scrollbar p-6 space-y-6"
                variants={tabContentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                  </div>
                ) : (
                  <div className="bg-purple-500/5 backdrop-blur-md rounded-xl p-6 border border-white/10">
                    <p className="text-md font-bold text-white mb-6">Application Status</p>

                    <div className="mb-8">
                      <StatusTimeline
                        currentStatus={applicationStatus}
                        size="lg"
                        showAllStatuses={false}
                        isJobSeeker={true}
                        statusTimelineData={
                          statusTimeline.length > 0
                            ? statusTimeline.map((item, index, array) => ({
                                newStatus: item.status,
                                previousStatus: index > 0 ? array[index - 1].status : 'APPLIED',
                                timestamp: item.timestamp,
                                description: item.note || '',
                              }))
                            : []
                        }
                      />
                    </div>

                    {activityHistory.length > 0 && (
                      <div className="mb-8">
                        <ActivityHistory
                          activities={activityHistory}
                          itemsPerPage={4}
                          showActions={true}
                          onActionComplete={handleActionComplete}
                        />
                      </div>
                    )}

                    {emailCorrespondence.length > 0 && (
                      <div className="mb-8">
                        <div className="bg-black/30 rounded-lg border border-white/5 overflow-hidden">
                          <div className="p-4 border-b border-white/5">
                            <h3 className="text-sm font-medium text-white/80">
                              Email Correspondence
                            </h3>
                          </div>
                          <div className="max-h-64 overflow-y-auto">
                            {emailCorrespondence.map((email, index) => (
                              <div
                                key={email.id}
                                className={`p-4 ${index !== emailCorrespondence.length - 1 ? 'border-b border-white/5' : ''}`}
                              >
                                <div className="flex justify-between items-start mb-2">
                                  <h4 className="text-sm font-medium text-white/90">
                                    {email.subject}
                                  </h4>
                                  <span className="text-xs text-white/50">
                                    {new Date(email.timestamp).toLocaleDateString()}
                                  </span>
                                </div>
                                <p className="text-xs text-white/70 mb-2">
                                  {email.emailType.toUpperCase()}
                                </p>
                                {email.requiresAction && !email.actionCompleted && (
                                  <div className="flex gap-2 mt-2">
                                    <span className="text-xs px-2 py-1 bg-orange-600/20 text-orange-300 rounded">
                                      Action Required
                                    </span>
                                  </div>
                                )}
                                {email.actionCompleted && (
                                  <div className="flex gap-2 mt-2">
                                    <span className="text-xs px-2 py-1 bg-green-600/20 text-green-300 rounded">
                                      Action Completed
                                    </span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="bg-black/30 rounded-lg p-4 border border-white/5">
                      <div className="flex items-center gap-2 mb-2">
                        <Info size={16} className="text-purple-400" />
                        <h3 className="text-sm font-medium text-white/80">Status Information</h3>
                      </div>
                      <p className="text-sm text-white/60">
                        Track your application progress in real-time. You'll be notified of any
                        status updates.
                      </p>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* No footer to ensure content is fully visible */}

        {showTldr && job.tldr && (
          <TLDRPopup
            tldr={job.tldr}
            onClose={onCloseTldr}
            onApply={handleApplySuccess}
            jobId={job.id}
            isApplied={isApplied}
          />
        )}
      </div>

      {/* Withdraw Application Modal */}
      <WithdrawApplicationModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onSuccess={handleWithdrawSuccess}
        onError={handleWithdrawError}
        jobId={job.id}
        jobTitle={job.jobType}
        companyName={job.companyName}
      />
    </div>
  );
};

export { ActionButton, AtsJobDetailsDrawer, CandidateList };

export default AtsJobDetailsDrawer;
