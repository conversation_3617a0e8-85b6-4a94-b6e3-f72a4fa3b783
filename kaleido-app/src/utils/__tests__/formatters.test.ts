import { formatNumberWithK } from '../formatters';

describe('formatNumberWithK', () => {
  describe('Numbers below 1000', () => {
    test('should return number as string with locale formatting for numbers below 1000', () => {
      expect(formatNumberWithK(0)).toBe('0');
      expect(formatNumberWithK(1)).toBe('1');
      expect(formatNumberWithK(99)).toBe('99');
      expect(formatNumberWithK(999)).toBe('999');
    });
  });

  describe('Numbers at exactly 1000', () => {
    test('should format 1000 as "1k"', () => {
      expect(formatNumberWithK(1000)).toBe('1k');
    });

    test('should format multiples of 1000 without decimals', () => {
      expect(formatNumberWithK(2000)).toBe('2k');
      expect(formatNumberWithK(5000)).toBe('5k');
      expect(formatNumberWithK(10000)).toBe('10k');
      expect(formatNumberWithK(100000)).toBe('100k');
    });
  });

  describe('Numbers with decimal places when divided by 1000', () => {
    test('should format numbers with one decimal place when needed', () => {
      expect(formatNumberWithK(1500)).toBe('1.5k');
      expect(formatNumberWithK(2500)).toBe('2.5k');
      expect(formatNumberWithK(4750)).toBe('4.8k'); // 4750/1000 = 4.75, rounded to 4.8
      expect(formatNumberWithK(12300)).toBe('12.3k');
    });

    test('should round decimal places appropriately', () => {
      expect(formatNumberWithK(1234)).toBe('1.2k'); // 1.234 rounded to 1.2
      expect(formatNumberWithK(1987)).toBe('2.0k'); // 1.987 rounded to 2.0
      expect(formatNumberWithK(1950)).toBe('1.9k'); // 1.95 rounded to 1.9
      expect(formatNumberWithK(1949)).toBe('1.9k'); // 1.949 rounded to 1.9
    });
  });

  describe('Large numbers', () => {
    test('should handle very large numbers', () => {
      expect(formatNumberWithK(1000000)).toBe('1000k');
      expect(formatNumberWithK(1500000)).toBe('1500k');
      expect(formatNumberWithK(999999)).toBe('1000.0k'); // 999.999 rounded to 1000.0
    });
  });

  describe('Edge cases', () => {
    test('should handle negative numbers', () => {
      expect(formatNumberWithK(-500)).toBe('-500');
      expect(formatNumberWithK(-1000)).toBe('-1k');
      expect(formatNumberWithK(-1500)).toBe('-1.5k');
    });

    test('should handle decimal inputs', () => {
      expect(formatNumberWithK(999.9)).toBe('999.9'); // Below 1000, uses toLocaleString
      expect(formatNumberWithK(1000.5)).toBe('1.0k'); // 1.0005k rounds to 1.0k
      expect(formatNumberWithK(1500.7)).toBe('1.5k'); // 1.5007k rounds to 1.5k
    });

    test('should handle zero and very small numbers', () => {
      expect(formatNumberWithK(0)).toBe('0');
      expect(formatNumberWithK(0.1)).toBe('0.1');
      expect(formatNumberWithK(0.9)).toBe('0.9');
    });
  });

  describe('Real-world salary examples', () => {
    test('should format common salary ranges correctly', () => {
      // Entry level salaries
      expect(formatNumberWithK(30000)).toBe('30k');
      expect(formatNumberWithK(35000)).toBe('35k');
      expect(formatNumberWithK(45000)).toBe('45k');

      // Mid-level salaries
      expect(formatNumberWithK(60000)).toBe('60k');
      expect(formatNumberWithK(75000)).toBe('75k');
      expect(formatNumberWithK(85000)).toBe('85k');

      // Senior level salaries
      expect(formatNumberWithK(100000)).toBe('100k');
      expect(formatNumberWithK(120000)).toBe('120k');
      expect(formatNumberWithK(150000)).toBe('150k');

      // Executive level salaries
      expect(formatNumberWithK(200000)).toBe('200k');
      expect(formatNumberWithK(250000)).toBe('250k');
      expect(formatNumberWithK(500000)).toBe('500k');
    });

    test('should handle partial thousands in salaries', () => {
      expect(formatNumberWithK(32500)).toBe('32.5k');
      expect(formatNumberWithK(67500)).toBe('67.5k');
      expect(formatNumberWithK(87250)).toBe('87.3k'); // 87.25 rounded to 87.3
      expect(formatNumberWithK(112750)).toBe('112.8k'); // 112.75 rounded to 112.8
    });
  });

  describe('Locale-specific formatting for numbers below 1000', () => {
    test('should use toLocaleString for numbers below 1000', () => {
      // Mock toLocaleString to verify it's being called
      const mockToLocaleString = jest.fn().mockReturnValue('mocked-locale-string');
      const originalToLocaleString = Number.prototype.toLocaleString;
      Number.prototype.toLocaleString = mockToLocaleString;

      formatNumberWithK(500);
      expect(mockToLocaleString).toHaveBeenCalled();

      // Restore original method
      Number.prototype.toLocaleString = originalToLocaleString;
    });
  });
});
