/**
 * Converts a display string to a key format
 * Example: "Brand Manager" -> "brand_manager"
 */
export const formatToKey = (display: string): string => {
  if (!display) return '';
  return display
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .trim()
    .replace(/\s+/g, '_'); // Replace spaces with underscores
};

/**
 * Converts a key format to display format
 * Example: "brand_manager" -> "Brand Manager"
 */
export const formatToDisplay = (key: string): string => {
  if (!key) return '';
  return key
    .split(/[_\s]/) // Split by underscore or space
    .map(word => {
      if (!word) return '';
      // Capitalize first letter of each word
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .filter(Boolean) // Remove empty strings
    .join(' '); // Join with spaces
};

/**
 * Creates options array for select components
 */
export const createSelectOptions = (items: string[]) => {
  return items.map(item => ({
    value: formatTo<PERSON>ey(item),
    label: item,
  }));
};

/**
 * Finds the current value in options array
 */
export const findCurrentValue = (
  options: { value: string; label: string }[],
  currentValue: string
) => {
  return options.find(
    option => option.value === currentValue || option.label === formatToDisplay(currentValue)
  );
};

/**
 * Converts a key format to sentence case
 * Example: "brand_manager" -> "Brand Manager"
 */
export const formatToSentenceCase = (text?: string) => {
  if (!text) return '';
  return text
    .split(/[\s_-]/) // Split by space, underscore, or hyphen
    .map(word => {
      // Handle empty strings that might result from multiple delimiters
      if (!word) return '';
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .filter(word => word) // Remove empty strings
    .join('-'); // Join with hyphens for hyphenated terms
};

/**
 * Converts a key format to display case
 * Example: "brand_manager" -> "Brand Manager"
 */
export const formatToDisplayCase = (text?: string) => {
  if (!text) return '';
  return text
    .split(/[\s_-]/)
    .map(word => {
      if (!word) return '';
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .filter(word => word)
    .join(' ');
};

/**
 * Creates an array of formatted values
 * Example: ["Brand Manager", "Software Engineer"] -> ["Brand Manager", "Software Engineer"]
 */
export const formatArrayValues = (values?: string[] | string): string[] => {
  if (!values) return [];
  if (typeof values === 'string') return [formatToSentenceCase(values)];
  return values.map(value => formatToSentenceCase(value));
};

/**
 * Formats a job description
 * Example: { jobType: "Full-Time", industry: "Technology", typeOfHiring: "Direct", typeOfJob: "Software Engineer", experience: "5 years" } -> { jobType: "Full-Time", industry: "Technology", typeOfHiring: "Direct", typeOfJob: "Software Engineer", experience: "5 years" }
 */
export const formatJobDescription = (data: any) => {
  // Handle arrays that need value/label conversion
  const arrayFields = ['companyValues', 'culturalFit', 'softSkills', 'benefits'];

  const formatted = { ...data };

  arrayFields.forEach(field => {
    if (Array.isArray(formatted[field])) {
      formatted[field] = formatted[field].map((item: string) => {
        // If the item contains underscores, it's likely in key format and needs to be converted
        if (item.includes('_')) {
          return formatToDisplay(item);
        }
        return item;
      });
    }
  });

  return formatted;
};

export const formatEnumValue = (value: string) => {
  return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
};

// Add this helper function to standardize formatting across the app
export const formatToHumanReadable = (value: string | string[] | undefined): string => {
  if (!value) return '';

  const formatSingleValue = (str: string): string => {
    // Preserve original capitalization and spacing, just trim whitespace
    return str.trim();
  };

  if (Array.isArray(value)) {
    return value.map(formatSingleValue).join(', ');
  }

  if (typeof value === 'string' && value.includes(',')) {
    return value
      .split(',')
      .map(item => formatSingleValue(item))
      .join(', ');
  }

  return formatSingleValue(value);
};

/**
 * Formats any field value for display in tables and lists
 * Handles underscore-separated words, full caps, and regular text
 * Preserves currency and salary values
 */
export const formatTableValue = (value: any): string => {
  if (!value) {
    return '-';
  }

  if (typeof value === 'string') {
    // Skip formatting for values starting with currency codes (3 uppercase letters)
    if (/^[A-Z]{3}\b/.test(value)) {
      return value;
    }

    // Handle underscore-separated words
    if (value.includes('_')) {
      return value
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    }

    // Handle regular text and full caps
    return value
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  return String(value);
};

/**
 * Formats a job title by extracting the first part before the first "|" character.
 * If no "|" is present, returns the original job title.
 *
 * @param jobTitle - The full job title string
 * @returns The formatted job title
 *
 * @example
 * // Returns "Software Engineer, Mentor"
 * formatJobTitle("Software Engineer, Mentor | xMicrosoft, xCitrix | Backend Developer")
 */
export const formatJobTitle = (jobTitle: string): string => {
  if (!jobTitle) return '';

  const parts = jobTitle.split('|');
  return parts[0].trim();
};

/**
 * Formats numeric values with "k" notation for thousands
 * Example: 4000 -> "4k", 4500 -> "4.5k", 50000 -> "50k"
 *
 * @param amount - The numeric amount to format
 * @returns Formatted string with "k" notation for thousands
 *
 * @example
 * formatNumberWithK(4000) // "4k"
 * formatNumberWithK(4500) // "4.5k"
 * formatNumberWithK(999) // "999"
 */
export const formatNumberWithK = (amount: number): string => {
  if (Math.abs(amount) >= 1000) {
    const thousands = amount / 1000;
    // Check if the result is a whole number
    const isWholeNumber = thousands % 1 === 0;
    const formatted = thousands.toFixed(isWholeNumber ? 0 : 1);
    return `${formatted}k`;
  }
  return amount.toLocaleString();
};
