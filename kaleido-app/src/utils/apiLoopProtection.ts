import { InfiniteLoopError } from '@/hooks/useInfiniteLoopDetector';

interface ApiCallTracker {
  endpoint: string;
  timestamps: number[];
}

class ApiLoopDetector {
  private trackers: Map<string, ApiCallTracker> = new Map();
  private maxCalls = 10;
  private timeWindow = 2000; // 2 seconds
  private errorReported = false;

  /**
   * Track an API call and detect potential infinite loops
   */
  trackCall(endpoint: string, method: string = 'GET'): void {
    const key = `${method}:${endpoint}`;
    const now = Date.now();

    if (!this.trackers.has(key)) {
      this.trackers.set(key, {
        endpoint: key,
        timestamps: [],
      });
    }

    const tracker = this.trackers.get(key)!;

    // Remove old timestamps
    tracker.timestamps = tracker.timestamps.filter(ts => now - ts < this.timeWindow);

    // Add current timestamp
    tracker.timestamps.push(now);

    // Check for infinite loop
    if (tracker.timestamps.length >= this.maxCalls) {
      // Clear the tracker to prevent further errors
      tracker.timestamps = [];

      // Only report once per session
      if (!this.errorReported) {
        this.errorReported = true;

        // Log to console
        console.error(
          `API Infinite Loop Detected: ${key} called ${this.maxCalls} times in ${this.timeWindow}ms`
        );

        // You can also send this to your error tracking service
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'api_infinite_loop', {
            event_category: 'error',
            event_label: key,
            value: tracker.timestamps.length,
          });
        }
      }

      throw new InfiniteLoopError(this.maxCalls, this.timeWindow, key);
    }
  }

  /**
   * Reset tracking for a specific endpoint
   */
  reset(endpoint?: string, method: string = 'GET'): void {
    if (endpoint) {
      const key = `${method}:${endpoint}`;
      this.trackers.delete(key);
    } else {
      this.trackers.clear();
    }
    this.errorReported = false;
  }

  /**
   * Get current tracking stats
   */
  getStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    const now = Date.now();

    this.trackers.forEach((tracker, key) => {
      const recentCalls = tracker.timestamps.filter(ts => now - ts < this.timeWindow);
      if (recentCalls.length > 0) {
        stats[key] = recentCalls.length;
      }
    });

    return stats;
  }
}

// Singleton instance
export const apiLoopDetector = new ApiLoopDetector();

/**
 * Axios interceptor to add loop detection
 */
export function createAxiosLoopDetectionInterceptor() {
  return {
    request: (config: any) => {
      try {
        const method = config.method?.toUpperCase() || 'GET';
        const url = config.url || '';

        // Extract relative path from full URL
        const urlPath = url.replace(/^https?:\/\/[^\/]+/, '');

        apiLoopDetector.trackCall(urlPath, method);
      } catch (error) {
        if (error instanceof InfiniteLoopError) {
          // Cancel the request
          const cancelToken = new AbortController();
          cancelToken.abort();
          config.signal = cancelToken.signal;

          // Show user-friendly error
          if (typeof window !== 'undefined') {
            const message = 'Too many requests detected. Please refresh the page.';

            // Try to show toast if available
            const event = new CustomEvent('show-toast', {
              detail: { message, isSuccess: false },
            });
            window.dispatchEvent(event);
          }

          // Re-throw to stop the request
          throw error;
        }
      }

      return config;
    },

    error: (error: any) => {
      return Promise.reject(error);
    },
  };
}
