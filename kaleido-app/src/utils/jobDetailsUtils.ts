import apiHelper from '@/lib/apiHelper';
import { IJob } from '@/entities/interfaces';

export interface JobDetailsData extends IJob {
  // Add any additional fields that come from the details endpoint
  videoJDs?: any;
  cultureFitQuestions?: any;
  [key: string]: any;
}

/**
 * Fetch job details from the appropriate endpoint based on context
 * @param jobId - The job ID to fetch
 * @param detailsOnly - Whether to fetch from the lightweight details endpoint
 * @returns The job details data
 */
export async function fetchJobDetails(
  jobId: string,
  detailsOnly: boolean = false
): Promise<JobDetailsData> {
  try {
    const endpoint = detailsOnly ? `/jobs/${jobId}/details` : `/jobs/${jobId}`;
    const response = await apiHelper.get(endpoint);

    // Handle response format variations
    const jobData = response.job || response;

    // Ensure consistent data structure
    return {
      ...jobData,
      id: jobData.id || jobId,
      // Add default values for required IJob fields if missing
      matchedCandidatesCount: jobData.matchedCandidatesCount || 0,
      candidatesCount: jobData.candidatesCount || 0,
    } as JobDetailsData;
  } catch (error) {
    console.error('Error fetching job details:', error);
    throw error;
  }
}

/**
 * Check if job data needs refreshing based on last fetch time
 * @param lastFetchTime - The timestamp of the last fetch
 * @param cacheTimeout - The cache timeout in milliseconds
 * @returns Whether the data should be refreshed
 */
export function shouldRefreshJobData(
  lastFetchTime: number,
  cacheTimeout: number = 5 * 60 * 1000
): boolean {
  return Date.now() - lastFetchTime > cacheTimeout;
}
