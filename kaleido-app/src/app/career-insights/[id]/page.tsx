'use client';

import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
  BarChart3,
  BookOpen,
  Briefcase,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  Eye,
  Share2,
  Sparkles,
  Target,
  TrendingUp,
} from 'lucide-react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import AnalysisRenderer from '@/components/career-insights/AnalysisRenderer';
import CareerPathDisplay from '@/components/career-insights/CareerPathDisplay';
import LearningResourcesDisplay from '@/components/career-insights/LearningResourcesDisplay';
import ModernRecommendations from '@/components/career-insights/ModernRecommendations';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import CareerInsightsPdf from '@/components/pdf/CareerInsightsPdf';
import BaseLayout from '@/components/steps/layout/BaseLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InsightType } from '@/lib/career-insights/config';
import { cn } from '@/lib/utils';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { UserRole } from '@/types/roles';
import { PDFDownloadLink } from '@react-pdf/renderer';

const insightTypeConfig = {
  [InsightType.SKILL_GAP_ANALYSIS]: {
    icon: Target,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
  },
  [InsightType.CAREER_PATH_RECOMMENDATION]: {
    icon: TrendingUp,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-500/10',
  },
  [InsightType.MARKET_TREND_ANALYSIS]: {
    icon: BarChart3,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
  },
  [InsightType.ROLE_TRANSITION_GUIDANCE]: {
    icon: Briefcase,
    color: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-500/10',
  },
  [InsightType.COMPENSATION_BENCHMARK]: {
    icon: DollarSign,
    color: 'from-yellow-500 to-amber-500',
    bgColor: 'bg-yellow-500/10',
  },
};

export default function CareerInsightDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [activeTab, setActiveTab] = useState('analysis');

  const { currentInsight, isLoading, fetchInsightById } = useCareerInsightsStore();

  useEffect(() => {
    if (id) {
      fetchInsightById(id).catch(error => {
        console.error('Failed to fetch insight:', error);
      });
    }
  }, [id, fetchInsightById]);

  if (isLoading || !currentInsight) {
    return (
      <BaseLayout userRole={UserRole.JOB_SEEKER}>
        <div className="flex-1 flex items-center justify-center">
          <ColourfulLoader />
        </div>
      </BaseLayout>
    );
  }

  const config = insightTypeConfig[currentInsight.type];
  const Icon = config.icon;

  return (
    <BaseLayout userRole={UserRole.JOB_SEEKER}>
      <div className="flex-1 px-4 py-6 md:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Button
            variant="ghost"
            onClick={() => router.push('/career-insights')}
            className="mb-4 text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Insights
          </Button>

          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <div className={`p-4 rounded-lg bg-gradient-to-br ${config.color} shadow-lg`}>
                <Icon className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{currentInsight.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {new Date(currentInsight.createdAt).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <Eye className="w-4 h-4 mr-1" />
                    {currentInsight.viewCount} views
                  </span>
                  {currentInsight.validUntil && (
                    <Badge variant="outline" className="text-gray-300 border-gray-600">
                      Valid until {new Date(currentInsight.validUntil).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="text-white border-white/20 hover:bg-white/10"
                onClick={() => {
                  // Share functionality
                  if (navigator.share) {
                    navigator.share({
                      title: currentInsight.title,
                      text: currentInsight.summary,
                      url: window.location.href,
                    });
                  } else {
                    navigator.clipboard.writeText(window.location.href);
                    alert('Link copied to clipboard!');
                  }
                }}
              >
                <Share2 className="w-4 h-4" />
              </Button>
              <PDFDownloadLink
                document={<CareerInsightsPdf insight={currentInsight} />}
                fileName={`CareerInsights_${currentInsight.type}_${new Date().toISOString().split('T')[0]}.pdf`}
              >
                {({ loading }) => (
                  <Button
                    variant="outline"
                    size="icon"
                    className="text-white border-white/20 hover:bg-white/10"
                    disabled={loading}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                )}
              </PDFDownloadLink>
            </div>
          </div>
        </motion.div>

        {/* Summary Card */}
        {/* Summary Card only show if summary has actual content */}
        {currentInsight.summary &&
          currentInsight.summary !== 'Processing your career insight...' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mb-8"
            >
              <Card className={`${config.bgColor} border-white/10 backdrop-blur-sm`}>
                <div className="p-6">
                  <h2 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <Sparkles className="w-5 h-5 mr-2 text-yellow-400" />
                    Key Insights
                  </h2>
                  <p className="text-gray-300 leading-relaxed">{currentInsight.summary}</p>
                </div>
              </Card>
            </motion.div>
          )}

        {/* Detailed Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <div className="flex items-center justify-center mb-8">
              <div className="bg-white/5 backdrop-blur-sm rounded-full p-1 flex relative">
                <TabsList className="bg-transparent border-0 p-0 flex">
                  <TabsTrigger
                    value="analysis"
                    className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                  >
                    {activeTab === 'analysis' && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      />
                    )}
                    <BookOpen className="w-4 h-4 relative" />
                    <span className="text-sm font-medium relative">Detailed Analysis</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="recommendations"
                    className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                  >
                    {activeTab === 'recommendations' && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      />
                    )}
                    <Target className="w-4 h-4 relative" />
                    <span className="text-sm font-medium relative">Recommendations</span>
                  </TabsTrigger>
                  {currentInsight.type === InsightType.SKILL_GAP_ANALYSIS && (
                    <TabsTrigger
                      value="skills"
                      className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                    >
                      {activeTab === 'skills' && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        />
                      )}
                      <BarChart3 className="w-4 h-4 relative" />
                      <span className="text-sm font-medium relative">Skills Gap</span>
                    </TabsTrigger>
                  )}
                  {currentInsight.type === InsightType.CAREER_PATH_RECOMMENDATION && (
                    <TabsTrigger
                      value="career-paths"
                      className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                    >
                      {activeTab === 'career-paths' && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        />
                      )}
                      <TrendingUp className="w-4 h-4 relative" />
                      <span className="text-sm font-medium relative">Career Paths</span>
                    </TabsTrigger>
                  )}
                  {currentInsight.type === InsightType.ROLE_TRANSITION_GUIDANCE && (
                    <TabsTrigger
                      value="transition"
                      className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                    >
                      {activeTab === 'transition' && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        />
                      )}
                      <Briefcase className="w-4 h-4 relative" />
                      <span className="text-sm font-medium relative">Transition Plan</span>
                    </TabsTrigger>
                  )}
                </TabsList>
              </div>
            </div>

            <TabsContent value="analysis" className="space-y-6">
              <AnalysisRenderer
                content={currentInsight.detailedAnalysis || 'Analysis content will appear here...'}
              />
            </TabsContent>

            <TabsContent value="recommendations" className="space-y-6">
              {currentInsight.aiInsights && (
                <ModernRecommendations aiInsights={currentInsight.aiInsights} />
              )}
            </TabsContent>

            {currentInsight.type === InsightType.SKILL_GAP_ANALYSIS &&
              currentInsight.skillGapAnalysis && (
                <TabsContent value="skills" className="space-y-6">
                  <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-white mb-4">Skills Analysis</h3>
                      <div className="mb-6">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-300">Overall Readiness</span>
                          <span className="text-white font-semibold">
                            {currentInsight.skillGapAnalysis.overallReadinessScore}%
                          </span>
                        </div>
                        <Progress
                          value={currentInsight.skillGapAnalysis.overallReadinessScore}
                          className="h-2 bg-white/10"
                        />
                      </div>

                      {currentInsight.skillGapAnalysis.skillGaps?.map(
                        (skill: any, index: number) => (
                          <div key={index} className="mb-6 p-4 bg-white/5 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-white">{skill.skillName}</h4>
                              <Badge
                                variant={
                                  skill.priority === 'HIGH'
                                    ? 'destructive'
                                    : skill.priority === 'MEDIUM'
                                      ? 'default'
                                      : 'secondary'
                                }
                              >
                                {skill.priority} Priority
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-300 mb-2">
                              Current: {skill.currentLevel} → Target: {skill.targetLevel}
                            </div>
                            <div className="flex items-center justify-between mb-3">
                              <p className="text-xs text-gray-400">
                                Estimated time: {skill.timeToAchieve}
                              </p>
                              <div className="flex items-center">
                                <span className="text-xs text-gray-400 mr-2">Market Demand:</span>
                                <div className="w-16 bg-white/10 rounded-full h-1.5">
                                  <div
                                    className="bg-gradient-to-r from-green-500 to-emerald-500 h-1.5 rounded-full"
                                    style={{ width: `${skill.marketDemand || 0}%` }}
                                  />
                                </div>
                                <span className="text-xs text-green-400 ml-2">
                                  {skill.marketDemand || 0}%
                                </span>
                              </div>
                            </div>

                            {/* Learning Resources */}
                            {skill.learningResources && skill.learningResources.length > 0 && (
                              <div className="mt-4">
                                <h5 className="text-sm font-medium text-white mb-2 flex items-center">
                                  <BookOpen className="w-4 h-4 mr-2 text-purple-400" />
                                  Learning Resources
                                </h5>
                                <div className="space-y-2">
                                  {skill.learningResources.map(
                                    (resource: any, resIndex: number) => (
                                      <div
                                        key={resIndex}
                                        className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                                      >
                                        <div className="flex items-center space-x-3">
                                          <div className="p-2 rounded-lg bg-purple-500/20">
                                            <BookOpen className="w-4 h-4 text-purple-400" />
                                          </div>
                                          <div>
                                            <h6 className="text-sm font-medium text-white">
                                              {resource.title}
                                            </h6>
                                            <div className="flex items-center space-x-3 text-xs text-gray-400">
                                              <span>{resource.provider}</span>
                                              <span>•</span>
                                              <span>{resource.estimatedDuration}</span>
                                              <span>•</span>
                                              <span className="text-green-400">
                                                {resource.cost}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                        {resource.url && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-white border-white/20 hover:bg-white/10"
                                            onClick={() => window.open(resource.url, '_blank')}
                                          >
                                            <ArrowLeft className="w-4 h-4 mr-1 rotate-180" />
                                            Visit
                                          </Button>
                                        )}
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  </Card>
                </TabsContent>
              )}

            {/* Career Path Recommendations Tab */}
            {currentInsight.type === InsightType.CAREER_PATH_RECOMMENDATION &&
              currentInsight.careerPathRecommendation && (
                <TabsContent value="career-paths" className="space-y-6">
                  <CareerPathDisplay
                    paths={currentInsight.careerPathRecommendation.recommendedPaths}
                    currentPosition={currentInsight.careerPathRecommendation.currentPosition}
                    immediateSteps={currentInsight.careerPathRecommendation.immediateNextSteps}
                  />
                </TabsContent>
              )}

            {/* Role Transition Tab */}
            {currentInsight.type === InsightType.ROLE_TRANSITION_GUIDANCE &&
              currentInsight.roleTransitionGuidance && (
                <TabsContent value="transition" className="space-y-6">
                  <div className="grid gap-6">
                    {/* Transition Overview */}
                    <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                          <Target className="w-5 h-5 mr-2 text-purple-400" />
                          Transition Overview
                        </h3>

                        <div className="grid md:grid-cols-3 gap-4 mb-6">
                          <div className="text-center p-4 rounded-lg bg-white/5">
                            <Briefcase className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-400">From</p>
                            <p className="font-medium text-white">
                              {currentInsight.roleTransitionGuidance.fromRole}
                            </p>
                          </div>

                          <div className="flex items-center justify-center">
                            <ArrowRight className="w-8 h-8 text-purple-400" />
                          </div>

                          <div className="text-center p-4 rounded-lg bg-white/5">
                            <Target className="w-8 h-8 text-green-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-400">To</p>
                            <p className="font-medium text-white">
                              {currentInsight.roleTransitionGuidance.toRole}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-center mb-6">
                          <Badge
                            variant="outline"
                            className={cn(
                              'text-sm px-4 py-2',
                              currentInsight.roleTransitionGuidance.transitionDifficulty === 'EASY'
                                ? 'border-green-500/30 text-green-400'
                                : currentInsight.roleTransitionGuidance.transitionDifficulty ===
                                    'MODERATE'
                                  ? 'border-yellow-500/30 text-yellow-400'
                                  : 'border-red-500/30 text-red-400'
                            )}
                          >
                            {currentInsight.roleTransitionGuidance.transitionDifficulty} Transition
                          </Badge>
                        </div>

                        {/* Transferable Skills */}
                        {currentInsight.roleTransitionGuidance.transferableSkills.length > 0 && (
                          <div className="mb-6">
                            <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                              <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                              Your Transferable Skills
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {currentInsight.roleTransitionGuidance.transferableSkills.map(
                                (skill, index) => (
                                  <Badge
                                    key={index}
                                    variant="outline"
                                    className="bg-green-500/10 border-green-500/30 text-green-300"
                                  >
                                    {skill}
                                  </Badge>
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </Card>

                    {/* New Skills Required */}
                    {currentInsight.roleTransitionGuidance.newSkillsRequired.length > 0 && (
                      <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                        <div className="p-6">
                          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                            <BookOpen className="w-5 h-5 mr-2 text-purple-400" />
                            Skills to Develop
                          </h3>

                          <div className="space-y-6">
                            {currentInsight.roleTransitionGuidance.newSkillsRequired.map(
                              (skill, index) => (
                                <div
                                  key={index}
                                  className="p-4 rounded-lg bg-white/5 border border-white/10"
                                >
                                  <div className="flex items-center justify-between mb-3">
                                    <h4 className="font-semibold text-white">{skill.skillName}</h4>
                                    <Badge
                                      variant={
                                        skill.priority === 'HIGH'
                                          ? 'destructive'
                                          : skill.priority === 'MEDIUM'
                                            ? 'default'
                                            : 'secondary'
                                      }
                                    >
                                      {skill.priority} Priority
                                    </Badge>
                                  </div>

                                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                                    <div className="text-sm text-gray-300">
                                      <span className="text-gray-400">Level:</span>{' '}
                                      {skill.currentLevel} → {skill.targetLevel}
                                    </div>
                                    <div className="text-sm text-gray-300">
                                      <span className="text-gray-400">Time needed:</span>{' '}
                                      {skill.timeToAchieve}
                                    </div>
                                  </div>

                                  {skill.learningResources &&
                                    skill.learningResources.length > 0 && (
                                      <LearningResourcesDisplay
                                        resources={skill.learningResources}
                                        title={`Resources for ${skill.skillName}`}
                                      />
                                    )}
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      </Card>
                    )}

                    {/* Transition Plan */}
                    {currentInsight.roleTransitionGuidance.transitionPlan.length > 0 && (
                      <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                        <div className="p-6">
                          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                            <Calendar className="w-5 h-5 mr-2 text-purple-400" />
                            Your Transition Roadmap
                          </h3>

                          <div className="space-y-6">
                            {currentInsight.roleTransitionGuidance.transitionPlan.map(
                              (phase, index) => (
                                <div key={index} className="relative">
                                  {index <
                                    currentInsight.roleTransitionGuidance.transitionPlan.length -
                                      1 && (
                                    <div className="absolute left-6 top-16 w-0.5 h-16 bg-gradient-to-b from-purple-400/50 to-transparent" />
                                  )}

                                  <div className="flex items-start space-x-4">
                                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0">
                                      <span className="text-sm font-bold text-white">
                                        {index + 1}
                                      </span>
                                    </div>

                                    <div className="flex-1 p-4 rounded-lg bg-white/5">
                                      <div className="flex items-center justify-between mb-2">
                                        <h4 className="font-semibold text-white">{phase.phase}</h4>
                                        <Badge
                                          variant="outline"
                                          className="text-xs border-white/20 text-gray-400"
                                        >
                                          {phase.duration}
                                        </Badge>
                                      </div>

                                      <div className="grid md:grid-cols-2 gap-4">
                                        <div>
                                          <h5 className="text-sm font-medium text-white mb-2">
                                            Actions
                                          </h5>
                                          <ul className="space-y-1">
                                            {phase.actions.map((action, actionIndex) => (
                                              <li
                                                key={actionIndex}
                                                className="text-sm text-gray-300 flex items-start"
                                              >
                                                <CheckCircle className="w-3 h-3 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                                                {action}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>

                                        <div>
                                          <h5 className="text-sm font-medium text-white mb-2">
                                            Milestones
                                          </h5>
                                          <ul className="space-y-1">
                                            {phase.milestones.map((milestone, milestoneIndex) => (
                                              <li
                                                key={milestoneIndex}
                                                className="text-sm text-gray-300 flex items-start"
                                              >
                                                <Target className="w-3 h-3 text-purple-400 mr-2 mt-0.5 flex-shrink-0" />
                                                {milestone}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      </Card>
                    )}
                  </div>
                </TabsContent>
              )}
          </Tabs>
        </motion.div>
      </div>
    </BaseLayout>
  );
}
