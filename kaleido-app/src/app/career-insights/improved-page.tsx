'use client';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Archive,
  ArrowRight,
  Briefcase,
  ChevronDown,
  ChevronRight,
  Clock,
  Eye,
  FileText,
  Plus,
  Sparkles,
  Target,
  TrendingUp,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import BaseLayout from '@/components/steps/layout/BaseLayout';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { InsightStatus, InsightType, insightTypeConfig } from '@/lib/career-insights/config';
import { cn } from '@/lib/utils';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { UserRole } from '@/types/roles';

interface GroupedInsights {
  [key: string]: {
    latest: any;
    history: any[];
    totalViews: number;
  };
}

export default function ImprovedCareerInsightsPage() {
  const router = useRouter();
  const { insights, isLoading, fetchInsights } = useCareerInsightsStore();
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchInsights().catch(error => {
      console.error('Failed to fetch insights:', error);
    });
  }, [fetchInsights]);

  // Group insights by type and sort by creation date
  const groupedInsights = useMemo(() => {
    const groups: GroupedInsights = {};

    insights.forEach(insight => {
      if (!groups[insight.type]) {
        groups[insight.type] = {
          latest: null,
          history: [],
          totalViews: 0,
        };
      }

      groups[insight.type].totalViews += insight.viewCount || 0;

      if (
        !groups[insight.type].latest ||
        new Date(insight.createdAt) > new Date(groups[insight.type].latest.createdAt)
      ) {
        if (groups[insight.type].latest) {
          groups[insight.type].history.push(groups[insight.type].latest);
        }
        groups[insight.type].latest = insight;
      } else {
        groups[insight.type].history.push(insight);
      }
    });

    // Sort history by date descending
    Object.values(groups).forEach(group => {
      group.history.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    });

    return groups;
  }, [insights]);

  const toggleGroup = (type: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(type)) {
      newExpanded.delete(type);
    } else {
      newExpanded.add(type);
    }
    setExpandedGroups(newExpanded);
  };

  const handleCreateInsight = (type: InsightType) => {
    router.push(`/career-insights/create?type=${type}`);
  };

  const handleViewInsight = (id: string) => {
    router.push(`/career-insights/${id}`);
  };

  const getStatusBadge = (status: InsightStatus) => {
    const statusConfig = {
      [InsightStatus.READY]: {
        label: 'Ready',
        className: 'bg-green-500/20 text-green-400 border-green-500/30',
      },
      [InsightStatus.PROCESSING]: {
        label: 'Processing',
        className: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      },
      [InsightStatus.DRAFT]: {
        label: 'Draft',
        className: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
      },
      [InsightStatus.ARCHIVED]: {
        label: 'Archived',
        className: 'bg-red-500/20 text-red-400 border-red-500/30',
      },
    };

    const config = statusConfig[status] || statusConfig[InsightStatus.DRAFT];
    return (
      <Badge variant="outline" className={cn('text-xs', config.className)}>
        {config.label}
      </Badge>
    );
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diffInMs = now.getTime() - then.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  return (
    <BaseLayout userRole={UserRole.JOB_SEEKER}>
      <div className="flex-1 px-4 py-6 md:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="mb-6">
            <h1 className="text-md font-medium text-white/90 mb-1">Career Insights Dashboard</h1>
            <p className="text-sm text-white/70">
              AI-powered insights to accelerate your career growth and unlock opportunities
            </p>
          </div>

          {insights.length > 0 && (
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <span className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-pink-400" />
                {insights.length} Total Insights
              </span>
              <span className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-pink-400" />
                {insights.reduce((sum, i) => sum + (i.viewCount || 0), 0)} Total Views
              </span>
            </div>
          )}
        </motion.div>

        {/* Quick Actions - Image Cards at Top */}
        <div className="mb-8">
          <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
            <Plus className="w-4 h-4 text-pink-400 mr-2" />
            Generate New Insights
            <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Skill Gap Analysis Card */}
            <Card
              className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
              onClick={() => handleCreateInsight(InsightType.SKILL_GAP_ANALYSIS)}
            >
              <div className="relative h-32 overflow-hidden">
                <Image
                  src="/images/insights/skill_gap_analysis_revised_1.png"
                  alt="Skill Gap Analysis"
                  fill
                  className="object-cover object-[center_20%] transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center text-white">
                  <Target className="w-4 h-4 text-pink-400 mr-2" />
                  Skill Gap Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-xs text-white/70">
                  Identify skill gaps and get personalized learning recommendations
                </CardDescription>
              </CardContent>
            </Card>

            {/* Career Path Card */}
            <Card
              className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
              onClick={() => handleCreateInsight(InsightType.CAREER_PATH_RECOMMENDATION)}
            >
              <div className="relative h-32 overflow-hidden">
                <Image
                  src="/images/insights/career_development.png"
                  alt="Career Path"
                  fill
                  className="object-cover object-[center_20%] transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center text-white">
                  <TrendingUp className="w-4 h-4 text-pink-400 mr-2" />
                  Career Path
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-xs text-white/70">
                  Discover career paths aligned with your skills and goals
                </CardDescription>
              </CardContent>
            </Card>

            {/* Market Trend Analysis Card */}
            <Card
              className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
              onClick={() => handleCreateInsight(InsightType.MARKET_TREND_ANALYSIS)}
            >
              <div className="relative h-32 overflow-hidden">
                <Image
                  src="/images/insights/performance_analytics_revised.png"
                  alt="Market Trend Analysis"
                  fill
                  className="object-cover object-[center_20%] transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center text-white">
                  <FileText className="w-4 h-4 text-pink-400 mr-2" />
                  Market Trends
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-xs text-white/70">
                  Stay updated with industry trends and opportunities
                </CardDescription>
              </CardContent>
            </Card>

            {/* Role Transition Guidance Card */}
            <Card
              className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
              onClick={() => handleCreateInsight(InsightType.ROLE_TRANSITION_GUIDANCE)}
            >
              <div className="relative h-32 overflow-hidden">
                <Image
                  src="/images/insights/professional_training_revised.png"
                  alt="Role Transition Guidance"
                  fill
                  className="object-cover object-[center_20%] transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center text-white">
                  <Users className="w-4 h-4 text-pink-400 mr-2" />
                  Role Transition
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-xs text-white/70">
                  Get guidance for transitioning to your dream role
                </CardDescription>
              </CardContent>
            </Card>

            {/* Compensation Benchmark Card */}
            <Card
              className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
              onClick={() => handleCreateInsight(InsightType.COMPENSATION_BENCHMARK)}
            >
              <div className="relative h-32 overflow-hidden">
                <Image
                  src="/images/insights/team_collaboration_revised.png"
                  alt="Compensation Benchmark"
                  fill
                  className="object-cover object-[center_20%] transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center text-white">
                  <Briefcase className="w-4 h-4 text-pink-400 mr-2" />
                  Compensation Benchmark
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-xs text-white/70">
                  Understand your market value and salary benchmarks
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <ColourfulLoader />
          </div>
        )}

        {/* Existing Insights - Compact Modern List */}
        {!isLoading && Object.keys(groupedInsights).length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-12"
          >
            <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
              <Archive className="w-4 h-4 text-pink-400 mr-2" />
              Your Insights History
              <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
            </h2>

            <div className="overflow-hidden rounded-xl border border-white/5 backdrop-blur-sm bg-purple-900/5">
              <div className="divide-y divide-white/10">
                {Object.entries(groupedInsights).map(([type, group], index) => {
                  const config = insightTypeConfig[type as InsightType];
                  const Icon = config.icon;
                  const isExpanded = expandedGroups.has(type);
                  const hasHistory = group.history.length > 0;

                  return (
                    <motion.div
                      key={type}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      {/* Main Insight Row */}
                      <div
                        className="group hover:bg-purple-500/5 transition-all duration-200 cursor-pointer"
                        onClick={() => handleViewInsight(group.latest.id)}
                      >
                        <div className="p-4 sm:p-5">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
                              {/* Icon */}
                              <div
                                className={`p-2 rounded-lg bg-gradient-to-br ${config.color} shadow-md shrink-0`}
                              >
                                <Icon className="w-5 h-5 text-white" />
                              </div>

                              {/* Content */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3 className="text-sm font-medium text-white truncate">
                                    {group.latest.title}
                                  </h3>
                                  {getStatusBadge(group.latest.status)}
                                </div>
                                <div className="flex items-center gap-3 text-xs text-gray-400">
                                  <span>{formatTimeAgo(group.latest.createdAt)}</span>
                                  <span>•</span>
                                  <span className="flex items-center">
                                    <Eye className="w-3 h-3 mr-1" />
                                    {group.latest.viewCount} views
                                  </span>
                                  {hasHistory && (
                                    <>
                                      <span>•</span>
                                      <span className="flex items-center">
                                        <Clock className="w-3 h-3 mr-1" />
                                        {group.history.length} versions
                                      </span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-2 ml-4">
                              {hasHistory && (
                                <button
                                  type="button"
                                  onClick={e => {
                                    e.stopPropagation();
                                    toggleGroup(type);
                                  }}
                                  className="p-1.5 hover:bg-white/10 rounded-lg transition-colors"
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="w-4 h-4 text-gray-400" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4 text-gray-400" />
                                  )}
                                </button>
                              )}
                              <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-pink-400 transition-colors" />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* History Section - Nested */}
                      <AnimatePresence>
                        {isExpanded && hasHistory && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden bg-black/10"
                          >
                            <div className="pl-14 pr-4 py-2 space-y-1">
                              {group.history.map(insight => (
                                <div
                                  key={insight.id}
                                  onClick={() => handleViewInsight(insight.id)}
                                  className="p-3 rounded-lg hover:bg-white/5 transition-colors cursor-pointer group/item"
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center gap-2 mb-1">
                                        <span className="text-xs text-white/80 truncate">
                                          {insight.title}
                                        </span>
                                        {getStatusBadge(insight.status)}
                                      </div>
                                      <div className="flex items-center gap-3 text-xs text-gray-500">
                                        <span>{formatTimeAgo(insight.createdAt)}</span>
                                        <span>•</span>
                                        <span>{insight.viewCount} views</span>
                                      </div>
                                    </div>
                                    <ChevronRight className="w-3 h-3 text-gray-500 group-hover/item:text-gray-400 ml-2" />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>
        )}

        {/* Empty State */}
        {!isLoading && insights.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center py-12"
          >
            <div className="p-6 rounded-full bg-purple-500/10 w-fit mx-auto mb-4">
              <Sparkles className="w-12 h-12 text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No insights yet</h3>
            <p className="text-gray-400 mb-6">Start by creating your first career insight above</p>
          </motion.div>
        )}
      </div>
    </BaseLayout>
  );
}
