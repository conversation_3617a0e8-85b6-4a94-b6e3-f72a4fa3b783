import { jest } from '@jest/globals';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';

import { ICandidate } from '@/entities/interfaces';
import apiHelper from '@/lib/apiHelper';

// Import the interfaces from the page file
interface JobCandidatesResponse {
  job: {
    id: string;
    jobType: string;
    department: string;
    companyName: string;
    topCandidateThreshold: number;
    secondTierCandidateThreshold: number;
    status: string;
  };
  candidates: {
    topTier: ICandidate[];
    secondTier: ICandidate[];
    others: ICandidate[];
    unranked: ICandidate[];
    shortlisted: ICandidate[];
  };
  stats: {
    totalCandidates: number;
    topTierCount: number;
    secondTierCount: number;
    othersCount: number;
    unrankedCount: number;
    shortlistedCount: number;
  };
}

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock apiHelper
jest.mock('@/lib/apiHelper', () => {
  const mockGet = jest.fn();
  return {
    __esModule: true,
    default: {
      get: mockGet,
      resetCircuitBreaker: jest.fn(),
    },
  };
});

// Type cast all mock implementations to 'any' to bypass TypeScript checks

// Mock the SimplifiedCandidatesComposition2 component
jest.mock('@/components/MatchRank/compositions/SimplifiedCandidatesComposition2', () => ({
  SimplifiedCandidatesComposition2: function MockSimplifiedCandidatesComposition2({
    candidates,
  }: {
    candidates: any;
  }) {
    // Handle both flat array and grouped candidates structure
    const candidatesList = Array.isArray(candidates)
      ? candidates
      : [
          ...(candidates.shortlisted || []),
          ...(candidates.topTier || []),
          ...(candidates.secondTier || []),
          ...(candidates.others || []),
          ...(candidates.unranked || []),
        ];

    return (
      <div data-testid="matched-candidates-composition">
        <div data-testid="candidates-count">{candidatesList.length} candidates</div>
        {candidatesList.map((candidate: any) => (
          <div key={candidate.id} data-testid={`candidate-${candidate.id}`}>
            {candidate.fullName || 'Unknown'} - {candidate.tier}
          </div>
        ))}
      </div>
    );
  },
}));

// Mock other components
jest.mock('@/components/Layouts/ColourfulLoader', () => {
  return function MockColourfulLoader() {
    return <div data-testid="loading">Loading...</div>;
  };
});

jest.mock('@/components/Toaster', () => ({
  showToast: jest.fn(),
}));

// Mock GenericStatusManager and its configs
jest.mock('@/components/shared/GenericStatusManager/GenericStatusManager', () => ({
  GenericStatusManager: function MockGenericStatusManager() {
    return <div data-testid="generic-status-manager" />;
  },
}));

jest.mock('@/components/shared/GenericStatusManager/configs/matchRankConfig', () => ({
  createMatchRankConfig: jest.fn(() => ({})),
}));

jest.mock('@/components/shared/GenericStatusManager/configs/scoutConfig', () => ({
  createScoutConfig: jest.fn(() => ({})),
}));

jest.mock('@/components/shared/GenericStatusManager/configs/uploadConfig', () => ({
  createUploadConfig: jest.fn(() => ({})),
}));

// Mock all the additional components used in the real CandidatesPage
jest.mock('@/components/ATSManager', () => ({
  ATSManager: function MockATSManager({ onComplete }: { onComplete?: (jobId: string) => void }) {
    return <div data-testid="ats-manager" />;
  },
}));

jest.mock('@/components/common/FullPageBg', () => ({
  FullPageBgLayout: function MockFullPageBgLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="full-page-bg">{children}</div>;
  },
}));

jest.mock('@/components/common/NavigationButton', () => ({
  NavigationButton: function MockNavigationButton({
    icon,
    children,
    onClick,
  }: {
    icon: any;
    children: React.ReactNode;
    onClick: () => void;
  }) {
    return (
      <button type="button" data-testid="navigation-button" onClick={onClick}>
        {children}
      </button>
    );
  },
}));

// Mock the JobCandidatesStats component
jest.mock('./components/JobCandidatesStats', () => {
  return function MockJobCandidatesStats({ stats }: { stats: any }) {
    return <div data-testid="job-candidates-stats">{stats.totalCandidates} total</div>;
  };
});

// Mock the unified job store
const mockJobStore = {
  currentJob: null,
  candidates: null,
  stats: null,
  isLoading: false,
  currentPage: 1,
  totalPages: 1,
  filters: {},
  setSelectedJob: jest.fn(),
  fetchJobById: jest.fn(),
  fetchCandidates: jest.fn().mockResolvedValue(undefined),
  setCurrentPage: jest.fn(),
  setFilters: jest.fn(),
  onWorkerComplete: jest.fn(),
  onCandidateStatusChange: jest.fn(),
  reset: jest.fn(),
  fetchCandidateById: jest.fn().mockResolvedValue(undefined),
  fetchJobCriteria: jest.fn().mockResolvedValue(undefined),
  updateCandidateStatusOptimistic: jest.fn(),
  silentDataSync: jest.fn(),
};

// Create a mock implementation that returns different values
const createMockJobStore = () => {
  const storeState = { ...mockJobStore };

  return jest.fn(selector => {
    if (selector) {
      return selector(storeState);
    }
    return storeState;
  });
};

// Mock additional stores used in the page
jest.mock('@/stores/matchRankJobsStore', () => ({
  useMatchRankJobsStore: jest.fn(() => ({
    jobs: [],
    activeJobs: [],
    updateJob: jest.fn(),
  })),
}));

jest.mock('@/stores/scoutJobsStore', () => ({
  useScoutJobsStore: jest.fn(() => ({
    jobs: [],
    activeJobs: [],
    updateJob: jest.fn(),
  })),
}));

jest.mock('@/stores/scoutedCandidatesStore', () => ({
  useScoutedCandidatesStore: jest.fn(() => ({
    fetchScoutedCandidates: jest.fn(),
  })),
}));

jest.mock('@/stores/unifiedJobStore', () => {
  const useJobStore = (selector?: any) => {
    if (selector) {
      return selector(mockJobStore);
    }
    return mockJobStore;
  };

  return {
    useJobStore,
    useUploadJobsStore: jest.fn(() => ({
      jobs: [],
      activeJobs: [],
      updateJob: jest.fn(),
    })),
  };
});

// Import the real component after all mocks are set up
import CandidatesPage from './page';

describe('CandidatesPage - API Integration', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockSearchParams = new URLSearchParams('page=1');

  const mockApiResponse: JobCandidatesResponse = {
    job: {
      id: 'test-job-id',
      jobType: 'Software Engineer',
      department: 'Engineering',
      companyName: 'Test Company',
      topCandidateThreshold: 80,
      secondTierCandidateThreshold: 60,
      status: 'ACTIVE',
    },
    candidates: {
      topTier: [
        {
          id: 'candidate-1',
          fullName: 'John Doe',
          jobTitle: 'Senior Developer',
          tier: 'TOP',
          evaluation: { matchScore: 85 },
          jobStats: {
            topCandidateThreshold: 80,
            secondTierCandidateThreshold: 60,
          },
        } as unknown as ICandidate,
      ],
      secondTier: [
        {
          id: 'candidate-2',
          fullName: 'Jane Smith',
          jobTitle: 'Frontend Developer',
          tier: 'SECOND',
          evaluation: { matchScore: 65 },
          jobStats: {
            topCandidateThreshold: 80,
            secondTierCandidateThreshold: 60,
          },
        } as unknown as ICandidate,
      ],
      others: [
        {
          id: 'candidate-3',
          fullName: 'Bob Johnson',
          jobTitle: 'Junior Developer',
          tier: 'OTHER',
          evaluation: { matchScore: 45 },
          jobStats: {
            topCandidateThreshold: 80,
            secondTierCandidateThreshold: 60,
          },
        } as unknown as ICandidate,
      ],
      unranked: [],
      shortlisted: [],
    },
    stats: {
      totalCandidates: 3,
      topTierCount: 1,
      secondTierCount: 1,
      othersCount: 1,
      unrankedCount: 0,
      shortlistedCount: 0,
    },
  };

  const mockFlatArrayResponse: ICandidate[] = [
    {
      id: 'candidate-1',
      fullName: 'John Doe',
      tier: 'TOP',
      evaluation: { matchScore: 85 },
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    } as unknown as ICandidate,
    {
      id: 'candidate-2',
      fullName: 'Jane Smith',
      tier: 'SECOND',
      evaluation: { matchScore: 65 },
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    } as unknown as ICandidate,
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    (useParams as any).mockReturnValue({ id: 'test-job-id' });
    (useRouter as any).mockReturnValue(mockRouter);
    (useSearchParams as any).mockReturnValue(mockSearchParams);

    // Reset mock store
    Object.keys(mockJobStore).forEach(key => {
      if (typeof mockJobStore[key] === 'function') {
        mockJobStore[key].mockClear();
      }
    });

    // Reset default store state
    mockJobStore.currentJob = null;
    mockJobStore.candidates = null;
    mockJobStore.stats = null;
    mockJobStore.isLoading = false;
    mockJobStore.currentPage = 1;
    mockJobStore.totalPages = 1;
    mockJobStore.filters = {};
  });

  describe('Candidate Navigation and Redirect Logic', () => {
    it('should redirect to edit page when no candidates exist in any tier', async () => {
      const emptyCandidates = {
        topTier: [],
        secondTier: [],
        others: [],
        unranked: [],
        shortlisted: [],
      };

      // Mock the store to return empty candidates data
      mockJobStore.currentJob = { id: 'test-job-id', jobTitle: 'Test Job' };
      mockJobStore.candidates = {
        'test-job-id': emptyCandidates,
      };
      mockJobStore.stats = {
        totalCandidates: 0,
        topTierCount: 0,
        secondTierCount: 0,
        othersCount: 0,
        unrankedCount: 0,
        shortlistedCount: 0,
      };
      mockJobStore.isLoading = false;

      render(<CandidatesPage />);

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      });

      // Wait for the redirect to happen
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/jobs/test-job-id/edit');
      });
    });

    it('should not redirect when candidates exist in topTier', async () => {
      const candidatesWithTopTier = {
        topTier: [{ id: 'candidate-1', name: 'John Doe' }],
        secondTier: [],
        others: [],
        unranked: [],
        shortlisted: [],
      };

      mockJobStore.currentJob = { id: 'test-job-id', jobTitle: 'Test Job' };
      mockJobStore.candidates = {
        'test-job-id': candidatesWithTopTier,
      };
      mockJobStore.stats = {
        totalCandidates: 1,
        topTierCount: 1,
        secondTierCount: 0,
        othersCount: 0,
        unrankedCount: 0,
        shortlistedCount: 0,
      };
      mockJobStore.isLoading = false;

      render(<CandidatesPage />);

      // Wait a bit to ensure no redirect happens
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockRouter.push).not.toHaveBeenCalledWith('/jobs/test-job-id/edit');
    });

    it('should not redirect when candidates exist in secondTier only', async () => {
      const candidatesWithSecondTier = {
        topTier: [],
        secondTier: [{ id: 'candidate-2', name: 'Jane Smith' }],
        others: [],
        unranked: [],
        shortlisted: [],
      };

      mockJobStore.currentJob = { id: 'test-job-id', jobTitle: 'Test Job' };
      mockJobStore.candidates = {
        'test-job-id': candidatesWithSecondTier,
      };
      mockJobStore.stats = {
        totalCandidates: 1,
        topTierCount: 0,
        secondTierCount: 1,
        othersCount: 0,
        unrankedCount: 0,
        shortlistedCount: 0,
      };
      mockJobStore.isLoading = false;

      render(<CandidatesPage />);

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockRouter.push).not.toHaveBeenCalledWith('/jobs/test-job-id/edit');
    });

    it('should not redirect when candidates exist in others only', async () => {
      const candidatesWithOthers = {
        topTier: [],
        secondTier: [],
        others: [{ id: 'candidate-3', name: 'Bob Johnson' }],
        unranked: [],
        shortlisted: [],
      };

      mockJobStore.currentJob = { id: 'test-job-id', jobTitle: 'Test Job' };
      mockJobStore.candidates = {
        'test-job-id': candidatesWithOthers,
      };
      mockJobStore.stats = {
        totalCandidates: 1,
        topTierCount: 0,
        secondTierCount: 0,
        othersCount: 1,
        unrankedCount: 0,
        shortlistedCount: 0,
      };
      mockJobStore.isLoading = false;

      render(<CandidatesPage />);

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockRouter.push).not.toHaveBeenCalledWith('/jobs/test-job-id/edit');
    });

    it('should not redirect when candidates exist in unranked only', async () => {
      const candidatesWithUnranked = {
        topTier: [],
        secondTier: [],
        others: [],
        unranked: [{ id: 'candidate-4', name: 'Alice Brown' }],
        shortlisted: [],
      };

      mockJobStore.currentJob = { id: 'test-job-id', jobTitle: 'Test Job' };
      mockJobStore.candidates = {
        'test-job-id': candidatesWithUnranked,
      };
      mockJobStore.stats = {
        totalCandidates: 1,
        topTierCount: 0,
        secondTierCount: 0,
        othersCount: 0,
        unrankedCount: 1,
        shortlistedCount: 0,
      };
      mockJobStore.isLoading = false;

      render(<CandidatesPage />);

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockRouter.push).not.toHaveBeenCalledWith('/jobs/test-job-id/edit');
    });
  });

  it('should handle structured API response with job context', async () => {
    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    // Wait for component to render with data
    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify candidates are flattened and displayed
    expect(screen.getByTestId('candidates-count')).toHaveTextContent('3 candidates');
    expect(screen.getByTestId('candidate-candidate-1')).toHaveTextContent('John Doe - TOP');
    expect(screen.getByTestId('candidate-candidate-2')).toHaveTextContent('Jane Smith - SECOND');
    expect(screen.getByTestId('candidate-candidate-3')).toHaveTextContent('Bob Johnson - OTHER');
  });

  it('should handle flat array API response', async () => {
    // Create a mock response that looks like the grouped structure but with flat array data
    const flatArrayApiResponse = {
      topTier: [mockFlatArrayResponse[0]],
      secondTier: [mockFlatArrayResponse[1]],
      others: [],
      unranked: [],
      shortlisted: [],
    };

    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': flatArrayApiResponse,
    };
    mockJobStore.stats = {
      totalCandidates: 2,
      topTierCount: 1,
      secondTierCount: 1,
      othersCount: 0,
      unrankedCount: 0,
      shortlistedCount: 0,
    };
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify candidates are displayed
    expect(screen.getByTestId('candidates-count')).toHaveTextContent('2 candidates');
    expect(screen.getByTestId('candidate-candidate-1')).toHaveTextContent('John Doe - TOP');
    expect(screen.getByTestId('candidate-candidate-2')).toHaveTextContent('Jane Smith - SECOND');
  });

  it('should extract job thresholds from candidates jobStats', async () => {
    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // The component should receive candidates with jobStats containing thresholds
    // This is verified by the component rendering correctly with the threshold data
    expect(screen.getByTestId('candidate-candidate-1')).toBeInTheDocument();
  });

  it('should handle API call without pagination parameters', async () => {
    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify the store's fetchCandidates was called during component initialization
    expect(mockJobStore.fetchCandidates).toHaveBeenCalledWith('test-job-id', 1);
  });

  it('should handle API errors gracefully', async () => {
    const mockError = new Error('Failed to load candidates');
    (apiHelper.get as any).mockRejectedValue(mockError);

    render(<CandidatesPage />);

    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    // Should not crash and should handle error state
    expect(screen.queryByTestId('matched-candidates-composition')).not.toBeInTheDocument();
  });

  it('should handle empty candidates response', async () => {
    const emptyResponse = {
      topTier: [],
      secondTier: [],
      others: [],
      unranked: [],
      shortlisted: [],
    };

    // Set up the store to return empty data
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': emptyResponse,
    };
    mockJobStore.stats = {
      totalCandidates: 0,
      topTierCount: 0,
      secondTierCount: 0,
      othersCount: 0,
      unrankedCount: 0,
      shortlistedCount: 0,
    };
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    expect(screen.getByTestId('candidates-count')).toHaveTextContent('0 candidates');
  });

  it('should pass correct props to MatchedCandidatesComposition', async () => {
    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify the component receives flattened candidates array
    expect(screen.getByTestId('candidates-count')).toHaveTextContent('3 candidates');
  });

  it('should handle tier and status filters in API call', async () => {
    const mockSearchParamsWithFilters = new URLSearchParams('page=1&tier=TOP&status=ACTIVE');
    (useSearchParams as any).mockReturnValue(mockSearchParamsWithFilters);

    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify the store's fetchCandidates was called during component initialization
    expect(mockJobStore.fetchCandidates).toHaveBeenCalledWith('test-job-id', 1);
  });

  it('should verify performance optimization - single API call', async () => {
    // Set up the store to return data after loading
    mockJobStore.currentJob = mockApiResponse.job;
    mockJobStore.candidates = {
      'test-job-id': mockApiResponse.candidates,
    };
    mockJobStore.stats = mockApiResponse.stats;
    mockJobStore.isLoading = false;

    render(<CandidatesPage />);

    // Wait for loading to disappear first
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
    });

    // Verify only one API call is made (no additional calls for thresholds, job context, etc.)
    // Note: The actual API call is made by the store, not directly by the component
    // So we verify the component renders correctly with the mocked store data
    expect(screen.getByTestId('matched-candidates-composition')).toBeInTheDocument();
  });
});
