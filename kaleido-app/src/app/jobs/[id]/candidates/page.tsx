'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { ATSManager } from '@/components/ATSManager';
import { FullPageBgLayout } from '@/components/common/FullPageBg';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { SimplifiedCandidatesComposition2 } from '@/components/MatchRank/compositions/SimplifiedCandidatesComposition2';
import { createMatchRankConfig } from '@/components/shared/GenericStatusManager/configs/matchRankConfig';
import { createScoutConfig } from '@/components/shared/GenericStatusManager/configs/scoutConfig';
import { createUploadConfig } from '@/components/shared/GenericStatusManager/configs/uploadConfig';
import { GenericStatusManager } from '@/components/shared/GenericStatusManager/GenericStatusManager';
import { showToast } from '@/components/Toaster';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import { useScoutJobsStore } from '@/stores/scoutJobsStore';
import { useJobStore, useUploadJobsStore } from '@/stores/unifiedJobStore';

export default function CandidatesPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params?.id as string;

  // Local loading state
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Use unified job store with optimistic updates
  const store = useJobStore();
  const currentJob = useJobStore(state => state.currentJob);
  const candidatesData = useJobStore(state => state.candidates);
  const stats = useJobStore(state => state.stats);
  const isLoading = useJobStore(state => state.isLoading);
  const currentPage = useJobStore(state => state.currentPage);
  const totalPages = useJobStore(state => state.totalPages);
  const filters = useJobStore(state => state.filters);

  // Get methods from store directly to avoid TypeScript selector issues
  const setSelectedJob = store.setSelectedJob;
  const fetchJobById = store.fetchJobById;
  const fetchCandidates = store.fetchCandidates;
  const fetchCandidateById = store.fetchCandidateById;
  const fetchJobCriteria = store.fetchJobCriteria;
  const setCurrentPage = store.setCurrentPage;
  const setFilters = store.setFilters;
  const onWorkerComplete = store.onWorkerComplete;
  const onCandidateStatusChange = store.onCandidateStatusChange;
  const updateCandidateStatusOptimistic = store.updateCandidateStatusOptimistic;
  const silentDataSync = store.silentDataSync;
  const reset = store.reset;

  // Get candidates for current job
  const candidates = candidatesData?.[jobId] || null;

  // Status manager stores
  const {
    jobs: matchRankJobs,
    activeJobs: activeMatchRankJobs,
    updateJobStatus: updateMatchRankJob,
    removeJob: removeMatchRankJob,
  } = useMatchRankJobsStore();
  const {
    jobs: uploadJobs,
    activeJobs: activeUploadJobs,
    updateJobStatus: updateUploadJob,
    removeJob: removeUploadJob,
  } = useUploadJobsStore();
  const {
    jobs: scoutJobs,
    activeJobs: activeScoutJobs,
    updateJobStatus: updateScoutJob,
    removeJob: removeScoutJob,
  } = useScoutJobsStore();

  // Convert MatchRankJob to StatusJob format
  const convertedMatchRankJobs = Object.fromEntries(
    Object.entries(matchRankJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.error,
        message: job.message,
        createdAt: job.createdAt,
        notifiedCompletion: job.notifiedCompletion,
        errorCount: job.errorCount,
        metadata: job.metadata || {},
      },
    ])
  );

  // Convert UploadJob to StatusJob format
  const convertedUploadJobs = Object.fromEntries(
    Object.entries(uploadJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.message,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.createdAt, // Use createdAt as fallback since updatedAt may not exist
        totalFiles: job.totalFiles,
        processedFiles: job.processedFiles,
        failedCount: job.failedCount,
      },
    ])
  );

  // Convert ScoutJob to StatusJob format
  const convertedScoutJobs = Object.fromEntries(
    Object.entries(scoutJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.message,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.createdAt, // Use createdAt as fallback since updatedAt may not exist
        totalProfiles: job.totalProfiles,
        processedProfiles: job.processedProfiles,
        successCount: job.successCount,
        failedCount: job.failedCount,
        duplicateCount: job.duplicateCount,
        results: job.results,
        duplicateInfo: job.duplicateInfo,
        data: job.data,
        metadata: job.metadata || {},
      },
    ])
  );

  // Initialize and load data
  useEffect(() => {
    const loadData = async () => {
      if (!jobId) {
        setIsInitialLoading(false);
        return;
      }

      try {
        setSelectedJob(jobId);

        // Only load candidates data - job data comes with candidates response
        await fetchCandidates(jobId, 1);
      } catch (error) {
        console.error('Error loading candidates data:', error);
        showToast({
          message: 'Failed to load candidates data',
          isSuccess: false,
        });
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadData();

    // Cleanup on unmount
    return () => {
      reset();
    };
  }, [jobId]); // Only depend on jobId to prevent infinite loops

  // Handle page change
  const handlePageChange = useCallback(
    async (page: number) => {
      if (jobId) {
        setCurrentPage(page);
        await fetchCandidates(jobId, page, filters);
      }
    },
    [jobId, filters, setCurrentPage, fetchCandidates]
  );

  // Handle back navigation
  const backToJobs = useCallback(() => {
    router.push(`/jobs`);
  }, [router]);

  // Handle navigation to edit page
  const handleEditClick = useCallback(() => {
    const returnTo = `/jobs/${jobId}/candidates`;
    router.push(`/jobs/${jobId}/edit?returnTo=${encodeURIComponent(returnTo)}`);
  }, [router, jobId]);

  // Handle redirect to edit if no candidates
  useEffect(() => {
    if (!isInitialLoading && !isLoading && candidates) {
      // Check if all candidate groups are empty
      const hasAnyCandidates =
        candidates.topTier.length > 0 ||
        candidates.secondTier.length > 0 ||
        candidates.others.length > 0 ||
        candidates?.unranked?.length > 0 ||
        candidates.shortlisted.length > 0;

      if (!hasAnyCandidates) {
        router.push(`/jobs/${jobId}/edit`);
      }
    }
  }, [isInitialLoading, isLoading, candidates, router, jobId]);

  // Show ColourfulLoader while initial loading
  if (isInitialLoading) {
    return <ColourfulLoader text="Loading candidates..." />;
  }

  // Show error if no jobId
  if (!jobId) {
    return (
      <div className="text-white text-lg backdrop-blur-xl px-6 py-3 rounded-lg bg-white/10 border border-white/10 shadow-xl">
        Invalid job ID
      </div>
    );
  }

  // Show error if no job data after loading
  if (!currentJob) {
    return (
      <div className="text-white text-lg backdrop-blur-xl px-6 py-3 rounded-lg bg-white/10 border border-white/10 shadow-xl">
        Job not found
      </div>
    );
  }

  return (
    <FullPageBgLayout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Main Content - Full Width */}
        {candidates && (
          <SimplifiedCandidatesComposition2
            candidates={candidates as any}
            currentPage={typeof currentPage === 'number' ? currentPage : 1}
            onPageChange={handlePageChange}
            isAtsJob={false}
            jobId={jobId}
            fetchCandidateById={fetchCandidateById}
          />
        )}

        {/* Status Managers */}
        <ATSManager
          onComplete={async (jobId: string) => {
            await onWorkerComplete(jobId, 'ats');
            // Additional refresh to ensure UI is updated
            await fetchCandidates(jobId, currentPage, filters);
          }}
        />
        <GenericStatusManager
          jobs={convertedMatchRankJobs}
          activeJobs={activeMatchRankJobs}
          config={createMatchRankConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            updateMatchRankJob(jobId, updates.status, updates);
          }}
          onRemoveJob={removeMatchRankJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'matchrank');
            // Refresh job criteria to update recentCandidates and other data
            await fetchJobCriteria(actualJobId, true);
            // Also refresh the current candidates view
            await fetchCandidates(actualJobId, currentPage, filters);
          }}
        />
        <GenericStatusManager
          jobs={convertedScoutJobs}
          activeJobs={activeScoutJobs}
          config={createScoutConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            updateScoutJob(jobId, updates.status, updates);
          }}
          onRemoveJob={removeScoutJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'scout');
            // Refresh job criteria to update recentCandidates and other data
            await fetchJobCriteria(actualJobId, true);
            // Also refresh the current candidates view
            await fetchCandidates(actualJobId, currentPage, filters);
          }}
        />
        <GenericStatusManager
          jobs={convertedUploadJobs as any}
          activeJobs={activeUploadJobs}
          config={createUploadConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            updateUploadJob(jobId, updates.status, updates);
          }}
          onRemoveJob={removeUploadJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'upload');
            // Refresh job criteria to update recentCandidates and other data
            await fetchJobCriteria(actualJobId, true);
            // Also refresh the current candidates view
            await fetchCandidates(actualJobId, currentPage, filters);
          }}
        />
      </div>
    </FullPageBgLayout>
  );
}
