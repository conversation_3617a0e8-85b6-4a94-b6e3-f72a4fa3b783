'use client';

import React, { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';

export default function AdminRolePage() {
  const { user, isLoading } = useUser();
  const [message, setMessage] = useState<string>('');
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const router = useRouter();
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  useEffect(() => {
    if (!user || !user.sub) return;

    // Get current role from localStorage
    const roleData = localStorage.getItem(`userRole_${user.sub}`);
    if (roleData) {
      try {
        const { role } = JSON.parse(roleData);
        setCurrentRole(role);
      } catch (error) {
        console.error('Error parsing role data:', error);
      }
    }
  }, [user]);

  const setAdminRole = async () => {
    if (!user || !user.sub) {
      setMessage('User not logged in');
      return;
    }

    try {
      // Update role in database
      await apiHelper.put(`/roles/${user.sub}`, { role: UserRole.ADMIN });

      // Update local storage
      const roleData = { role: UserRole.ADMIN, clientId: user.sub };
      localStorage.setItem(`userRole_${user.sub}`, JSON.stringify(roleData));

      // Also set cookie for middleware access
      const expires = new Date();
      expires.setTime(expires.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
      document.cookie = `userRole_${user.sub}=${encodeURIComponent(JSON.stringify(roleData))};expires=${expires.toUTCString()};path=/;SameSite=Lax;Secure`;

      setMessage('Role updated to ADMIN successfully');
      setCurrentRole(UserRole.ADMIN);

      // Redirect to admin feature flags page after a short delay
      setTimeout(() => {
        router.push('/admin/feature-flags');
      }, 2000);
    } catch (error) {
      console.error('Error updating user role:', error);
      setMessage('Error updating role. See console for details.');
    }
  };

  const refreshPage = () => {
    // Refresh data through Zustand stores instead of full page reload
    invalidateJobsCache();
    refreshJobs(true); // Force refresh

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }

    setMessage('Data refreshed successfully!');
  };

  if (isLoading) {
    return <div className="p-8 text-white">Loading...</div>;
  }

  if (!user) {
    return <div className="p-8 text-white">Please log in to continue</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="bg-gradient-to-br from-purple-900/30 via-pink-900/30 to-blue-900/30 backdrop-blur-xl rounded-lg shadow-lg p-6 border border-white/10">
        <h1 className="text-2xl font-bold text-white mb-4">Admin Role Debug Page</h1>

        <div className="mb-6">
          <p className="text-white/80 mb-2">Current user: {user.name || user.email}</p>
          <p className="text-white/80 mb-4">
            Current role: <span className="font-semibold">{currentRole || 'Not set'}</span>
          </p>

          <div className="flex space-x-4">
            <button
              type="button"
              onClick={setAdminRole}
              className="px-4 py-2 bg-pink-700 hover:bg-pink-600 text-white rounded-md transition-colors"
            >
              Set Role to ADMIN
            </button>

            <button
              type="button"
              onClick={refreshPage}
              className="px-4 py-2 bg-blue-700 hover:bg-blue-600 text-white rounded-md transition-colors"
            >
              Refresh Page
            </button>
          </div>

          {message && <div className="mt-4 p-3 bg-white/10 rounded-md text-white">{message}</div>}
        </div>

        <div className="mt-8 border-t border-white/10 pt-4">
          <h2 className="text-xl font-semibold text-white mb-2">Instructions</h2>
          <p className="text-white/80">
            This page allows you to set your user role to ADMIN, which will give you access to admin
            features like Feature Flags. After setting your role, you will be redirected to the
            Feature Flags page.
          </p>
          <p className="text-white/80 mt-2">
            If you don't see the admin menu after setting your role, try refreshing the page or
            logging out and back in.
          </p>
        </div>
      </div>
    </div>
  );
}
