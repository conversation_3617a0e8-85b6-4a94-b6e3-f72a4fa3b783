'use client';

import { UserRole } from '@/types/roles';
import { WHITE_LIST_EMAILS } from '@/types/whitelists';
import { useUser } from '@auth0/nextjs-auth0/client';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Force dynamic rendering to prevent build-time errors
export const dynamic = 'force-dynamic';

interface AuthDebugInfo {
  auth0User: any;
  authSession: any;
  userRole: any;
  isWhitelisted: boolean;
  tokenExpiry: {
    expiresAt: number;
    currentTime: number;
    timeUntilExpiry: number;
    isExpired: boolean;
  } | null;
}

export default function AdminAuthDebugPage() {
  const { user, isLoading, error } = useUser();
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null);
  const [refreshCount, setRefreshCount] = useState(0);

  const collectDebugInfo = () => {
    if (!user) return;

    const authSession = localStorage.getItem('auth_session');
    let parsedAuthSession = null;
    let tokenExpiry = null;

    if (authSession) {
      try {
        parsedAuthSession = JSON.parse(authSession);
        if (parsedAuthSession.expiresAt) {
          const currentTime = Date.now();
          tokenExpiry = {
            expiresAt: parsedAuthSession.expiresAt,
            currentTime,
            timeUntilExpiry: parsedAuthSession.expiresAt - currentTime,
            isExpired: currentTime > parsedAuthSession.expiresAt,
          };
        }
      } catch (error) {
        console.error('Error parsing auth session:', error);
      }
    }

    let userRole = null;
    if (user.sub) {
      const roleData = localStorage.getItem(`userRole_${user.sub}`);
      if (roleData) {
        try {
          userRole = JSON.parse(roleData);
        } catch (error) {
          console.error('Error parsing user role:', error);
        }
      }
    }

    const isWhitelisted = WHITE_LIST_EMAILS.includes(user.email || '');

    setDebugInfo({
      auth0User: user,
      authSession: parsedAuthSession,
      userRole,
      isWhitelisted,
      tokenExpiry,
    });
  };

  useEffect(() => {
    if (!isLoading && user) {
      collectDebugInfo();
    }
  }, [user, isLoading, refreshCount]);

  const handleRefresh = () => {
    setRefreshCount(prev => prev + 1);
    collectDebugInfo();
  };

  const handleSetAdminRole = async () => {
    if (!user?.sub) return;

    try {
      // Update local storage
      const roleData = { role: UserRole.ADMIN, clientId: user.sub };
      localStorage.setItem(`userRole_${user.sub}`, JSON.stringify(roleData));

      // Also set cookie for middleware access
      const expires = new Date();
      expires.setTime(expires.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
      document.cookie = `userRole_${user.sub}=${encodeURIComponent(JSON.stringify(roleData))};expires=${expires.toUTCString()};path=/;SameSite=Lax;Secure`;

      alert('Admin role set in localStorage');
      handleRefresh();
    } catch (error) {
      console.error('Error setting admin role:', error);
      alert('Error setting admin role');
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);
    return `${hours}h ${minutes}m ${seconds}s`;
  };

  if (isLoading) {
    return <div className="p-8">Loading...</div>;
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
        <pre className="bg-red-50 p-4 rounded text-sm overflow-auto">
          {JSON.stringify(error, null, 2)}
        </pre>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Not Authenticated</h1>
        <Link href="/api/auth/login" className="bg-blue-500 text-white px-4 py-2 rounded">
          Login
        </Link>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Admin Authentication Debug</h1>

      <div className="mb-4 space-x-4">
        <button
          onClick={handleRefresh}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Refresh Debug Info
        </button>
        <button
          onClick={handleSetAdminRole}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Set Admin Role
        </button>
      </div>

      {debugInfo && (
        <div className="space-y-6">
          {/* User Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Email:</strong> {debugInfo.auth0User.email}
              </div>
              <div>
                <strong>Sub:</strong> {debugInfo.auth0User.sub}
              </div>
              <div>
                <strong>Name:</strong> {debugInfo.auth0User.name}
              </div>
              <div>
                <strong>Is Whitelisted:</strong>
                <span className={debugInfo.isWhitelisted ? 'text-green-600' : 'text-red-600'}>
                  {debugInfo.isWhitelisted ? ' Yes' : ' No'}
                </span>
              </div>
            </div>
          </div>

          {/* Role Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Role Information</h2>
            {debugInfo.userRole ? (
              <div>
                <strong>Current Role:</strong>
                <span
                  className={
                    debugInfo.userRole.role === 'admin' ? 'text-green-600' : 'text-blue-600'
                  }
                >
                  {debugInfo.userRole.role}
                </span>
                <pre className="mt-2 bg-gray-50 p-2 rounded text-sm">
                  {JSON.stringify(debugInfo.userRole, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-red-600">No role found in localStorage</div>
            )}
          </div>

          {/* Token Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Token Information</h2>
            {debugInfo.authSession ? (
              <div className="space-y-2">
                <div>
                  <strong>Has Access Token:</strong>{' '}
                  {debugInfo.authSession.accessToken ? 'Yes' : 'No'}
                </div>
                {debugInfo.tokenExpiry && (
                  <div className="space-y-1">
                    <div>
                      <strong>Expires At:</strong> {formatTime(debugInfo.tokenExpiry.expiresAt)}
                    </div>
                    <div>
                      <strong>Current Time:</strong> {formatTime(debugInfo.tokenExpiry.currentTime)}
                    </div>
                    <div>
                      <strong>Time Until Expiry:</strong>
                      <span
                        className={
                          debugInfo.tokenExpiry.isExpired ? 'text-red-600' : 'text-green-600'
                        }
                      >
                        {debugInfo.tokenExpiry.isExpired
                          ? ' EXPIRED'
                          : ` ${formatDuration(debugInfo.tokenExpiry.timeUntilExpiry)}`}
                      </span>
                    </div>
                    <div>
                      <strong>Is Expired:</strong>
                      <span
                        className={
                          debugInfo.tokenExpiry.isExpired ? 'text-red-600' : 'text-green-600'
                        }
                      >
                        {debugInfo.tokenExpiry.isExpired ? ' Yes' : ' No'}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-600">No auth session found in localStorage</div>
            )}
          </div>

          {/* Raw Data */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Raw Debug Data</h2>
            <pre className="bg-gray-50 p-4 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
